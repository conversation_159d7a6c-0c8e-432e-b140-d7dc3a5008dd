/* 集团实名登记服务 - 优化版本 */
import request from '@/utils/request';

/**
 * 实名信息查询
 * @param {Object} params 查询参数
 * @returns {Promise} 查询结果
 */
export async function queryGroupRealNameInfo(params) {
  // 注意: 在实际开发中，这里会调用真实的API接口
  // 在当前改造中，我们在组件内使用setTimeout模拟API调用
  try {
    const response = await request('portal/CrmRealNameController/queryCustRel.do', {
      data: params,
    });


    return response;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('查询集团实名信息异常:', error);
    throw error;
  }
}

/**
 * 实名信息新增/编辑
 * @param {Object} params 提交参数
 * @returns {Promise} 提交结果
 */
export async function editGroupRealNameInfo(params) {
  try {
    // 参数验证
    if (!params.groupId) {
      throw new Error('集团ID不能为空');
    }

    if (!params.realNameInfo) {
      throw new Error('实名信息不能为空');
    }

    const response = await request('portal/CrmRealNameController/addCustReal.do', {
      data: params,
    });


    return response;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('提交集团实名信息异常:', error);
    throw error;
  }
}


export async function queryGroupRealNameDetail(params) {
  // 注意: 在实际开发中，这里会调用真实的API接口
  // 在当前改造中，我们在组件内使用setTimeout模拟API调用
  return request('order_IGroupRealNameInfoOperCSV_query', {
    data: params,
  });
}


export async function saveGroupRealNameInfo(params) {
  // 注意: 在实际开发中，这里会调用真实的API接口
  // 在当前改造中，我们在组件内使用setTimeout模拟API调用
  return request('rule_IGroupRealNameCSV_saveGroupRealNameInfo', {
    data: params,
  });
}

/**
 * 地市查询
 * @param {number} parentDistrictId 父级地区ID，默认400（内蒙古）
 * @returns {Promise} 地市列表
 */
export async function queryDisticts(parentDistrictId = 400) {
  // 注意: 在实际开发中，这里会调用真实的API接口
  // 在当前改造中，我们在组件内使用setTimeout模拟API调用
  try {
    const response = await request('portal/EnterpriseAddressController/queryDisticts.do', {
      data: {
        parentDistrictId,
      },
    });


    return response;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('查询地市信息异常:', error);
    throw error;
  }
}

/**
 * 基本信息-集团法人信息
 * operType: DELETE-删除 ADD-删除 QUERY-查询 *必传
 * groupId: 集团编码  *必传
 * @param props
 * @returns {*}
 */
export function getLegalPersonInfo(props) {
  const { operType, groupId, ...restParams } = props;
  // 注意: 在实际开发中，这里会调用真实的API接口
  // 在当前改造中，我们在组件内使用setTimeout模拟API调用
  return request('portal/GroupLegalPersionController/legalPersonInfo.do', {
    method: 'post',
    data: {
      operType: 'QUERY',
      groupId,
      ...restParams,
    },
  });
}

/**
 * 获取客户经理组织信息
 * @param {Object} params 包含OPERATOR_ID的参数对象
 * @returns {Promise} 组织信息
 */
export async function getOrgInfoByOpId(params) {
  // 注意: 在实际开发中，这里会调用真实的API接口
  // 在当前改造中，我们在组件内使用setTimeout模拟API调用
  try {
    if (!params.OPERATOR_ID) {
      throw new Error('操作员ID不能为空');
    }

    const response = await request('portal/GroupCommonExtController/getOrgInfoByOpId.do', {
      data: params,
    });


    return response;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('获取客户经理组织信息异常:', error);
    throw error;
  }
}

/**
 * 数据验证工具函数
 */
export const validateRealNameData = {

  /**
   * 验证经办人信息
   * @param {Object} agentInfo 经办人信息
   * @returns {Object} 验证结果
   */
  validateAgentInfo(agentInfo) {
    const errors = [];

    if (!agentInfo.INDIVIDUAL_NAME) {
      errors.push('经办人姓名不能为空');
    }

    if (!agentInfo.IDEN_NR) {
      errors.push('经办人证件号码不能为空');
    } else if (!/^\d{17}[\dXx]$/.test(agentInfo.IDEN_NR)) {
      errors.push('经办人证件号码格式不正确');
    }

    if (!agentInfo.BILL_ID) {
      errors.push('经办人联系电话不能为空');
    } else if (!/^1[3-9]\d{9}$/.test(agentInfo.BILL_ID)) {
      errors.push('经办人联系电话格式不正确');
    }

    if (agentInfo.EMAIL && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(agentInfo.EMAIL)) {
      errors.push('经办人邮箱格式不正确');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * 验证责任人信息
   * @param {Object} ownerInfo 责任人信息
   * @returns {Object} 验证结果
   */
  validateOwnerInfo(ownerInfo) {
    const errors = [];

    if (!ownerInfo.INDIVIDUAL_NAME) {
      errors.push('责任人姓名不能为空');
    }

    if (!ownerInfo.IDEN_NR) {
      errors.push('责任人证件号码不能为空');
    } else if (!/^\d{17}[\dXx]$/.test(ownerInfo.IDEN_NR)) {
      errors.push('责任人证件号码格式不正确');
    }

    if (!ownerInfo.BILL_ID) {
      errors.push('责任人联系电话不能为空');
    } else if (!/^1[3-9]\d{9}$/.test(ownerInfo.BILL_ID)) {
      errors.push('责任人联系电话格式不正确');
    }

    if (ownerInfo.EMAIL && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(ownerInfo.EMAIL)) {
      errors.push('责任人邮箱格式不正确');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },
};
