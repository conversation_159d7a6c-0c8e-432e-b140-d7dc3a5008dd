.messageModal{
  :global{
    .ant-modal-header{
      display: none;
    }
  }
}
.OSSPageModalHeader{
  :global{
    .ant-switch-checked {
      background-color: #9bc144;
    }
  }
}
.OSSPageModal{
  :global{
    .ant-modal{
      margin: 0;
      padding-bottom: 0;
    }
  }
}
.historyModal{
  top: 20px;
  :global{
    .ant-modal-header{
      display: block;
    }
  }
}
// 用户列表
.userContainer{
  width: 280px;
  height: 100%;
  border-right: 1px solid #e8e8e8;
  .search{
    padding: 16px 16px 11px;
    border-bottom: 1px solid #e8e8e8;

    input{
      height: 28px;
      background: #f5f5f5;
      border: none;
    }
  }
  .userList{
    height: calc(100% - 60px);
    padding: 4px 8px;
    overflow-x: hidden;
    overflow-y: auto;
    :global{
      .ant-list-item{
        padding: 0;
      }
    }
  }
}

.userSearch{
  width: 216px;
  height: 200px;
  overflow-x: hidden;
  overflow-y: auto;
  .searchResult{
    display: flex;
    align-items: center;
    // height: 10%;
    padding: 6px 16px;
    border-bottom: 1px solid #e8e8e8;
  }
  .searchResult:last-child{
    border-bottom: none;
  }
  .userName{
    margin-left: 10px;
    color: #333;
    font-size: 14px;
  }
}

// 单个用户
.userItem{
  display: flex;
  padding: 12px;
  // border-bottom: 1px solid #F9F9F9;
  cursor: default;
  .userName{
    color: #333;
    font-size: 12px;
  }
  .lastMsg{
    width: 156px;
    overflow: hidden;
    color: #666;
    font-size: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .time{
    width: 36px;
    text-align: right;
  }
}

// 聊天窗口
.chatWindow{
  width: 100%;
  background: #f5f5f5;
  .header{
    display: flex;
    align-items: center;
    height: 56px;
    padding: 0 16px;
    border-bottom: 1px solid #e8e8e8;
  }
  .userName{
    margin-left: 10px;
    color: #333;
    font-size: 14px;
  }
}

.chatContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 56px - 190px);
  // background-color: #fff;

  .chatItems {
    position: relative;
    height: 100%;
    padding: 12px;
    overflow-y: auto;
    // background-color: #fff;

    .noMore{
      margin: 16px;
      color: #c9c9c9;
      font-size: 12px;
      text-align: center;
    }

    .chatloading {
      position: absolute;
      left: 50%;
      padding-top: 50px;
      text-align: center;
    }

    .chatItem {
      p {
        margin: 0;
        padding: 6px;
        background-color: #fff;
        border-radius: 4px;
      }
      .senderItem {
        position: relative;
        display: flex;
        align-items: flex-start;
        justify-content: flex-end;

        .avatarWrap {
          width: 48px;
          margin-left: 8px;
        }
      }
      .receiverItem {
        position: relative;
        display: flex;
        align-items: flex-start;

        .avatarWrap {
          width: 48px;
          // margin-right: 8px;
        }
      }

      .text-message-view {
        position: relative;
        width: 100%;
        min-height: 48px;

        .text-message-wrapper {
          padding: 0 8px 8px 8px;
          overflow: hidden;
          white-space: pre;
          text-align: left;
          word-wrap: break-word;
          word-break: break-all;
          cursor: default;

          .message-time {
            color: #333;
            font-size: 12px;

            &.sender {
              text-align: right;
            }

            &.receiver {
              text-align: left;
            }
          }

          &.sender {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
          }

          &.receiver {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
          }

          .bubble {
            display: flex;

            &.sender {
              align-items: flex-end;
              justify-content: flex-end;
            }

            &.receiver {
              align-items: flex-end;
              justify-content: flex-start;
            }

            .text-item {
              width: fit-content;
              max-width: 500px;
              padding: 8px 16px;
              overflow: hidden;
              font-size: 14px;
              white-space: break-spaces;
              text-overflow: ellipsis;
              background-color: #FFF;
              border-radius: 4px;
            }

            .image-item {
              max-width: 128px;
              max-height: 128px;
              padding: 8px;
              background-color: aliceblue;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .show-popmenu {
              width: 16px;
              height: 16px;
              cursor: pointer;
              opacity: 0;

              img {
                width: 100%;
                height: 100%;
              }

              &.sender {
                margin-right: 8px;
              }
              &.receiver {
                margin-left: 8px;
              }
            }
          }
        }
      }
    }
  }
}

.chatInput {
  position: relative;
  height: 190px;
  padding: 8px 8px 0;
  // background: #fff;
  border-top: 1px solid #e8e8e8;

  .icon{
    padding: 4px 8px;
  }
  textarea {
    flex: 1;
    background: #f5f5f5;
    border: none;
    outline: none;
    resize: none;

    &:focus {
      box-shadow: none;
    }

  }

  .chatSendBtn {
    position: absolute;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    margin: 8px;

    .tips{
      margin-right: 10px;
      color: #c9c9c9;
      font-size: 12px;
      letter-spacing: 1px;
    }
  }
}

.recordSearch{
  height: 100%;
  background: #FFF;

  .searchInput{
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;

    input{
      height: 28px;
      background: #f5f5f5;
      border: none;
    }
  }

  :global{
    .ant-tabs-bar{
      margin: 0;
      margin: 0 8px;
    }
    .ant-tabs-tab-active{
      color: #000;
    }
    .ant-tabs-ink-bar{
      background-color: #000;
    }
    .ant-input-suffix{
      display: none;
    }
  }
}

.chatHistory{
  padding: 12px;
  overflow-x: hidden;
  overflow-y: auto;
  background: #f5f5f5;
}

.historyItem{
  display: flex;
  margin-bottom: 4px;
  padding: 12px;
  background: #FFF;
  .avatarImg {
    margin-right: 20px;
  }
  .sendInfo{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
    color: #999;
    font-size: 12px;
  }
  .sendText{
    color: #333;
    font-size: 14px;
  }
}

.searchPart{
  padding: 12px;
  border-left: 1px solid #e8e8e8;

  .title{
    margin-bottom: 16px;
    color: #999;
    font-size: 14px;
  }
  .smallTitle{
    margin-bottom: 8px;
    color: #999;
    font-size: 12px;
  }
}

.minContainer {
  position: fixed;
  top: 100px;
  right: 10px;
  background-color: rgb(255, 255, 255);
  border-radius: 2px;
  box-shadow: rgba(0, 0, 0, 0.15) 0 4px 12px;
  .minContent{
    display: flex;
    justify-content: space-between;
    width: 200px;
    padding: 16px 24px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 22px;
    .title{
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
    }
  }
}
