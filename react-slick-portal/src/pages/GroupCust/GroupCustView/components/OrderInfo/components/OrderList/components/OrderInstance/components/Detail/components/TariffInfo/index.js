import React, { useState, useEffect, useRef } from 'react';
import { Card, message } from 'antd';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
// import style from './index.less';
import { getInstanceDetail } from '@/pages/GroupCust/GroupCustView/components/OrderInfo/components/OrderList/components/OrderInstance/services';

const Index = ({ size: { height }, ...otherProps }) => {
  // const [expandedRows, setExpandedRows] = useState([]);
  const { offerInst } = otherProps;
  const tableRef = useRef(null);
  const [planList, setPlanList] = useState([]);
  const [loading, setLoading] = useState(false);
  // 产品信息
  const queryOfferInst = () => {
    setLoading(true);
    getInstanceDetail({
      subscriberInsId: offerInst.SUBSCRIBER_INS_ID,
    }).then(res => {
      setLoading(false);
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.prodInfos) {
        setPlanList(resultObject?.rspParam?.busiInfo?.disInfos);
      } else {
        message.error(resultMsg);
      }
    });
  };

  /**
   * 渲染嵌套内容
   * @param record
   * @returns {Element}
   */
  // const expandedRowRender = (record, index, indent, expanded) => (
  //   <div className={`${style.expandedRowRender} ${expanded ? style.visible : style.hidden}`}>
  //     <Row>
  //       <Descriptions column={4} layout="vertical" title={record.OFFER_NAME}>
  //         {/* {record?.PRICEPLAN_PARAM_LIST?.map(item => (
  //           <Descriptions.Item label={item.CHA_SPEC_NAME}>{item.CHA_SPEC_VALUE}</Descriptions.Item>
  //         ))} */}
  //         <Descriptions.Item label="生失效时间">
  //           {record.VALID_DATE}-{record.EXPIRE_DATE}
  //         </Descriptions.Item>
  //       </Descriptions>
  //     </Row>
  //   </div>
  // );

  // // 展开嵌套内容
  // const handleExpand = key => {
  //   const newExpandedRows = [...expandedRows];
  //   if (newExpandedRows.includes(key)) {
  //     newExpandedRows.splice(newExpandedRows.indexOf(key), 1);
  //   } else {
  //     newExpandedRows.push(key);
  //   }
  //   setExpandedRows(newExpandedRows);
  // };

  useEffect(() => {
    queryOfferInst();
  }, []);

  // 资费变更
  // const handleChangeFee = record => {

  // };
  return (
    <>
      <Card title="资费信息" className="cute" style={{ minHeight: height }} bordered>
        <SlickTable
          style={{ marginTop: 20 }}
          ref={tableRef}
          loading={loading}
          // expandedRowKeys={expandedRows}
          // expandIcon={() => null} // 移除默认的第一列展开图标
          // expandIconAsCell={false} // 设置展开图标在表格中不显示为列
          // expandIconColumnIndex={1} // 设置展开图标的列索引为1（即序号列）
          rowKey={record => record.PRICE_PLAN_INS_ID}
          // rowClassName={record => (expandedRows.includes(record.PRICE_PLAN_INS_ID) ? `${style.expandedRow}` : '')}
          // expandedRowRender={expandedRowRender}
          dataSource={planList}
          columns={[
            {
              title: '资费实例标识',
              dataIndex: 'PRICE_PLAN_INS_ID',
              key: 'pricePlanInsId',
              render: text => (
                <div>
                  {/* <Icon
                    style={{ marginRight: '8px' }}
                    type={expandedRows.includes(record.PRICE_PLAN_INS_ID) ? 'caret-down' : 'caret-right'}
                    onClick={() => handleExpand(record.PRICE_PLAN_INS_ID)}
                  /> */}
                  {text}
                </div>
              ),
            },
            // {
            //   title: '资费状态',
            //   dataIndex: 'DATA_STATUS',
            //   key: 'dataStatus',
            //   render: text => (text === '1' ? '生效中' : '失效'),
            // },
            // {
            //   title: '用户计费号码',
            //   dataIndex: 'ACCESS_NUM',
            //   key: 'accessNum',
            //   render: () => offerInst.ACCESS_NUM,
            // },
            // {
            //   title: '开始时间',
            //   dataIndex: 'VALID_DATE',
            //   key: 'validDate',
            //   width: 150,
            // },
            {
              title: '商品名称',
              width: 250,
              render: (_, record) => <div>{record.OFFER_NAME}</div>,
            },
            {
              title: '手填资费',
              width: 150,
              render: (_, record) => {
                if (record?.PRICEPLAN_PARAM_LIST?.length !== 0) {
                  const text = record?.PRICEPLAN_PARAM_LIST?.filter(e => e.CHA_SPEC_NAME === '手填资费(元)')[0];
                  return (<div>{text?.CHA_SPEC_VALUE || ''}</div>);
                }
                return null;
              },
            },
            {
              title: '办理时间',
              dataIndex: 'CREATE_DATE',
              key: 'createDate',
              width: 150,
            },
            {
              title: '生失效时间',
              render: (_, record) => (<div>{record.VALID_DATE} - {record.EXPIRE_DATE}</div>),
            },

            /* {
              title: '办理人',
              dataIndex: 'CREATE_DATE',
              key: 'createDate',
              width: 150
            }, */
            {
              title: '办理人工号',
              dataIndex: 'CREATE_OP_NAME',
              key: 'createOpId',
              width: 150,
            },
            // {
            //   title: '操作',
            //   align: 'center',
            //   key: 'action',
            //   width: 230,
            //   render: (_, record) => <Button type="link" onClick={() => handleChangeFee(record)}>资费变更</Button>,
            // },
          ]}
        />
      </Card>
    </>
  );
};

export default connect(({ setting }) => ({
  size: setting.size,
}))(Index);
