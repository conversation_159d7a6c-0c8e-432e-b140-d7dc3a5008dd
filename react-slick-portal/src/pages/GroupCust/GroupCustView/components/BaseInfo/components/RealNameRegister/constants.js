/**
 * 集团实名制相关常量定义
 */

// 数据类型
export const DATA_TYPE = {
  AGENT: '1', // 经办人
  OWNER: '2', // 责任人
};

// 数据类型描述
export const DATA_TYPE_DESC = {
  [DATA_TYPE.AGENT]: '经办人',
  [DATA_TYPE.OWNER]: '责任人',
};

// 渠道代码
export const CHANNEL_CODE = {
  WEB: '1', // Web渠道
  MOBILE: '2', // 移动端
  COUNTER: '3', // 柜台
};

// 在线比对结果状态
export const ONLINE_COMPARE_STATUS = {
  SUCCESS: '0000', // 比对成功
  FAILED: '0001', // 比对失败
  ERROR: '9999', // 系统错误
};

// 信息核验结果状态
export const INFO_CHECK_STATUS = {
  SUCCESS: '1', // 核验成功
  FAILED: '0', // 核验失败
};

// 认证类型
export const LOG_VERIFY_TYPE = {
  PERSONAL: '0', // 个人
  USER: '1', // 使用人
  OWNER: '2', // 责任人
  AGENT: '3', // 经办人
  CUSTOMER: '4', // 客户
};

// 业务类型
export const BUSINESS_TYPE = {
  GROUP_REAL_NAME: '1', // 集团实名制
};

// 表单验证规则
export const FORM_RULES = {
  // 必填项规则
  REQUIRED: {
    required: true,
    message: '此项为必填项',
  },

  // 姓名验证规则
  NAME: [
    { required: true, message: '姓名不能为空' },
    { min: 2, max: 20, message: '姓名长度应在2-20个字符之间' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/, message: '姓名只能包含中文、英文和空格' },
  ],

  // 身份证号验证规则
  ID_CARD: [
    { required: true, message: '证件号码不能为空' },
    { pattern: /^\d{17}[\dXx]$/, message: '请输入正确的18位身份证号码' },
  ],

  // 手机号验证规则
  PHONE: [
    { required: true, message: '联系电话不能为空' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号码' },
  ],

  // 邮箱验证规则
  EMAIL: [
    { type: 'email', message: '请输入正确的邮箱地址' },
  ],

  // 地址验证规则
  ADDRESS: [
    { required: true, message: '地址不能为空' },
    { min: 5, max: 100, message: '地址长度应在5-100个字符之间' },
  ],

  // 地市验证规则
  CITY: [
    { required: true, message: '请选择地市' },
  ],
};

// 文件上传配置
export const FILE_UPLOAD_CONFIG = {
  // 授权书上传配置
  AUTH_LETTER: {
    accept: '.doc,.docx,.pdf,.jpg,.jpeg,.png',
    maxSize: 10 * 1024 * 1024, // 10MB
    fileType: '1',
  },
};

// 状态文本映射
export const STATUS_TEXT = {
  PENDING: '待处理',
  PROCESSING: '处理中',
  SUCCESS: '成功',
  FAILED: '失败',
  COMPLETED: '已完成',
  NOT_STARTED: '未开始',
};

// 提示信息
export const MESSAGES = {
  // 成功提示
  SUCCESS: {
    SCAN_SUCCESS: '身份证信息扫描成功',
    ONLINE_COMPARE_SUCCESS: '经办人在线比对验证通过',
    INFO_CHECK_SUCCESS: '责任人信息核验通过',
    COPY_INFO_SUCCESS: '经办人信息已复制到责任人',
    SUBMIT_SUCCESS: '集团实名制信息提交成功',
  },

  // 警告提示
  WARNING: {
    INCOMPLETE_AGENT_INFO: '请先完善经办人姓名和证件号码信息',
    INCOMPLETE_OWNER_INFO: '请先完善责任人姓名和证件号码信息',
    MISSING_SCAN_INFO: '请先进行身份证扫描获取照片信息',
    MISSING_ONLINE_COMPARE: '请先完成经办人在线比对验证',
    MISSING_INFO_CHECK: '请先完成责任人信息核验',
    INCOMPLETE_SCAN_INFO: '扫描信息不完整，请重新扫描身份证',
    INVALID_ID_CARD: '请输入正确的18位身份证号码',
  },

  // 错误提示
  ERROR: {
    SCAN_INFO_EMPTY: '扫描信息为空，请重新扫描',
    AUTH_LETTER_REQUIRED: '经办人与责任人不是同一人，必须上传授权书',
    NETWORK_ERROR: '网络连接失败，请检查网络后重试',
    SUBMIT_FAILED: '提交失败，请重试',
    FETCH_ORG_INFO_FAILED: '获取客户经理组织信息失败',
    FETCH_DISTRICTS_FAILED: '获取地市信息失败',
    FETCH_OWNER_INFO_FAILED: '获取集团法人信息失败',
    COPY_INFO_FAILED: '复制信息失败',
  },
};

// 默认值
export const DEFAULT_VALUES = {
  CERT_TYPE: '100001', // 默认证件类型：居民身份证
  NATION: 'CHN', // 默认国籍：中国
  CHANNEL_CODE: '1', // 默认渠道：Web
  CHECK_RESULT: '1', // 默认核验结果：成功
  PARENT_DISTRICT_ID: 400, // 默认父级地区ID：内蒙古
};

// 字段映射关系
export const FIELD_MAPPING = {
  // 经办人字段映射
  AGENT: {
    NAME: 'AGENT_NAME',
    MASK_NAME: 'AGENT_MASK_NAME',
    ID_NUM: 'AGENT_ID_NUM',
    MASK_ID_NUM: 'AGENT_MASK_ID_NUM',
    ADDR: 'AGENT_ADDR',
    MASK_ADDR: 'AGENT_MASK_ADDR',
    PHONE: 'AGENT_PHONE',
    EMAIL: 'AGENT_EMAIL',
    CITY: 'AGENT_CITY',
    DISTR: 'AGENT_DISTR',
    REMARK: 'AGENT_REMARK',
    AUTH_LETTER: 'AGENT_auth_letter',
  },

  // 责任人字段映射
  OWNER: {
    NAME: 'OWNER_NAME',
    ID_NUM: 'OWNER_ID_NUM',
    ADDR: 'OWNER_ADDR',
    PHONE: 'OWNER_PHONE',
    EMAIL: 'OWNER_EMAIL',
    CITY: 'OWNER_CITY',
    DISTR: 'OWNER_DISTR',
    REMARK: 'OWNER_REMARK',
    ID_TYPE: 'OWNER_ID_TYPE',
  },

  // 扫描相关字段
  SCAN: {
    BASE_STRING: 'AGGENT_BASE_STRING',
    IMAGE_STRING: 'AGGENT_IMAGE_STRING',
    NATION: 'CUST_NATION',
    TIMESTAMP: 'COMPARE_IMAGE_TIMESTAMP',
    IMAGE_TIMESTAMP: 'IMAGE_TIMESTAMP',
    PROTOCOL_ID: 'PROTOCOL_ID',
  },
};

// 步骤配置
export const STEPS = {
  LIST: 0, // 列表页
  EDIT: 1, // 编辑页
  DETAIL: 2, // 详情页
};
