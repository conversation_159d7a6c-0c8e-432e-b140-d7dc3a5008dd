import forEach from 'lodash/forEach';
import sortBy from 'lodash/sortBy';
import { getTodoRefreshTime, getDataDictByGroup, qryTasksNumbers } from '@/services/undo';
import icon1 from '@/pages/Dashboard/img/icon1.png';
import icon2 from '@/pages/Dashboard/img/icon2.png';
import icon3 from '@/pages/Dashboard/img/icon3.png';
import icon4 from '@/pages/Dashboard/img/icon4.png';
import icon5 from '@/pages/Dashboard/img/icon5.png';
import icon6 from '@/pages/Dashboard/img/icon6.png';
import icon7 from '@/pages/Dashboard/img/icon7.png';
import icon8 from '@/pages/Dashboard/img/icon8.png';

/**
 * 对qryTasksNumbers.do的回参进行序列化,提取count > 0 的并按照taskCount大小排序
 * [
 *  {
 *    taskName: '日常任务',
 *    taskCount: taskInfo.count,
 *    menuId: classData.MENU_RCRW_TODO,
 *    imgSrc: icon2,
 *  }
 * ]
 * @return {object[] | null}
 */
function serialize(data, classData) {
  let taskResultData = [];
  if (data && data.resultCode === '0') {
    const resultData = data.resultData.details;
    forEach(resultData, function(taskData) {
      if (taskData.status === 'TODO') {
        const { taskInfos } = taskData;
        let warningTotal = 0;
        forEach(taskInfos, function(taskData) {
          if (taskData.taskType === 'WARNING') {
            forEach(taskData.subTaskInfo, function(taskInfo) {
              // 预警任务
              warningTotal += taskInfo.count;
            });
          } else if (taskData.taskType === 'DAILY_TASK') {
            // 日常任务
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.taskType === 'DAILY') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '日常任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_RCRW_TODO,
                    imgSrc: icon2,
                  });
                }
                // self.$(".js-rcrw-num").text(taskInfo.count);
              }
            });
          } else if (taskData.taskType === 'MARKETING') {
            // 营销任务-集团营销，成员稳保
            let total = 0;
            forEach(taskData.subTaskInfo, function(taskInfo) {
              total += taskInfo.count;
            });
            if (total > 0) {
              taskResultData.push({
                taskName: '营销任务',
                taskCount: total,
                menuId: classData.MENU_YJRW_TODO,
                imgSrc: icon7,
              });
            }
            //  self.$(".js-yxrw-num").text(total);
          } else if (taskData.taskType === 'SERVICE_TASK') {
            // 服务任务-生日关怀
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.taskType === 'BIRTHDAY_SERVICE') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '服务任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_FWRW_TODO,
                    imgSrc: icon8,
                  });
                }
                // self.$(".js-fwrw-num").text(taskInfo.count);
              }
            });
          } else if (taskData.taskType === 'OPP_TASK') {
            // 商机任务
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.taskType === 'OPPORTUNITY') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '商机任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_SJRW_TODO,
                    imgSrc: icon3,
                  });
                }
                // self.$(".js-sjrw-num").text(taskInfo.count);
              }
            });
          }else if (taskData.taskType === 'OPP_PRE_TASK') {
            // 售前支撑任务
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.taskType === 'PRE_SALE_TASK') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '售前支撑任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_SJRW_PRESALE_TODO,
                    imgSrc: icon3,
                  });
                }
                // self.$(".js-sjrw-num").text(taskInfo.count);
              }
            });
          } else if (taskData.taskType === 'CUST_TYPE') {
            // 客户任务
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.taskType === 'CUST_SERVICE') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '客户任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_KHRW_TODO,
                    imgSrc: icon1,
                  });
                }
                // self.$(".js-khrw-num").text(taskInfo.count);
              }
            });
          } else if (taskData.taskType === 'VISIT_TASK') {
            // 拜访任务
            let count = 0;
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.count > 0) {
                count += taskInfo.count;
              }
            });
            if (count > 0) {
              taskResultData.push({
                taskName: '拜访任务',
                taskCount: count,
                menuId: classData.MENU_RCRW_TODO,
                imgSrc: icon7,
              });
            }
          } else if (taskData.taskType === 'ORDER_TYPE') {
            // 订单任务
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.taskType === 'ORDER_SERVICE') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '订单任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_DDRW_TODO,
                    imgSrc: icon6,
                  });
                }
                //  self.$(".js-ddrw-num").text(taskInfo.count);
              }
            });
          } else if (taskData.taskType === 'DISPATCH_TYPE') {
            // 调度任务
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.taskType === 'TIME_OUT_DISPATCH') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '超时调度任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_CSDDRW_TODO,
                    imgSrc: icon7,
                  });
                }
                //  self.$(".js-ddrw-num").text(taskInfo.count);
              }
            });
          } else if (taskData.taskType === 'CLOUD_TYPE') {
            // 云网任务
            forEach(taskData.subTaskInfo, function(taskInfo) {
              // ICP备案审核任务
              if (taskInfo.taskType === 'ICP_RECORD_SERVICE') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: 'ICP备案审核任务',
                    taskCount: taskInfo.count,
                    menuId: classData.WAIT_TODO_CLOUD,
                    imgSrc: icon7,
                  });
                }
              }
            });
          } else if (taskData.taskType === 'CPC_TYPE') {
            // 产商品任务

            let count = 0;
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.count > 0) {
                count += taskInfo.count;
              }
            });
            if (count > 0) {
              taskResultData.push({
                taskName: '产商品任务',
                taskCount: count,
                menuId: classData.MENU_CPC_TODO,
                imgSrc: icon7,
              });
            }
          } else if (taskData.taskType === 'AGREEMENT_TYPE') {
            // 合同任务
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.taskType === 'AGREEMENT_SERVICE') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '合同任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_HTRW_TODO,
                    imgSrc: icon5,
                  });
                }
                // self.$(".js-htrw-num").text(taskInfo.count);
              } else if (taskInfo.taskType === 'AGREEMENT_TEMPLET_SERVICE') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '合同模板任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_HTMBRW_TODO,
                    imgSrc: icon5,
                  });
                }
              }
            });
          } else if (taskData.taskType === 'SURVEY_TYPE') {
            // 查勘任务
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.taskType === 'SURVEY_TASKS_SERVICE') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '查勘任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_CKRW_TODO,
                    imgSrc: icon7,
                  });
                }
                // self.$(".js-ckrw-num").text(taskInfo.count);
              }
            });
          } else if (taskData.taskType === 'BPC_TYPE') {
            // 项目任务
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.taskType === 'BPC_SERVICE') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '项目任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_BPC_TODO,
                    imgSrc: icon7,
                  });
                }
                // self.$(".js-ckrw-num").text(taskInfo.count);
              }
            });
          } else if (taskData.taskType === 'JLB_TYPE') {
            // 接力棒任务
            forEach(taskData.subTaskInfo, function(taskInfo) {
              if (taskInfo.taskType === 'PROJECT_SERVICE') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '项目型商机任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_XMRW_TODO,
                    imgSrc: icon4,
                  });
                }
                // self.$(".js-xmrw-num").text(taskInfo.count);
              } else if (taskInfo.taskType === 'COLLABORATION_SERVICE') {
                if (taskInfo.count > 0) {
                  taskResultData.push({
                    taskName: '协作单任务',
                    taskCount: taskInfo.count,
                    menuId: classData.MENU_XZDRW_TODO,
                    imgSrc: icon8,
                  });
                }
                // self.$(".js-xzdrw-num").text(taskInfo.count);
              }
            });
          }
        });
        // self.$(".js-yjrw-num").text(warningTotal);
        if (warningTotal > 0) {
          taskResultData.push({
            taskName: '预警任务',
            taskCount: warningTotal,
            menuId: classData.MENU_YJRW_TODO,
            imgSrc: 'modules/workbench/images/icon8.png',
          });
        }
      }
    });
    // 按照taskCount大小排序
    taskResultData = sortBy(taskResultData, function(info) {
      return -parseInt(info.taskCount, 10);
    });

    return taskResultData;
  }
  return null;
}

const classData = {};

export default {
  namespace: 'undo',

  state: {
    taskResultData: [],
    undoCount: 0,
    authorityUndoList: [], // 已按照菜单权限过滤后的待办列表
  },

  effects: {
    *getInitData({ payload }, { call, put }) {
      const [refreshTime, ids, response3] = yield [
        call(getTodoRefreshTime, { defValue: payload.defaultInterval }),
        call(getDataDictByGroup),
        call(qryTasksNumbers),
      ];
      let result = {};
      if (refreshTime) {
        result.refreshTime = parseInt(refreshTime, 10);
      } else {
        result.refreshTime = parseInt(payload.defaultInterval, 10);
      }
      if (ids && ids.length > 0) {
        forEach(ids, function(item) {
          if (item.paramCode === 'MENU_YJRW_TODO') {
            classData.MENU_YJRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_YXRW_TODO') {
            classData.MENU_YXRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_RCRW_TODO') {
            classData.MENU_RCRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_FWRW_TODO') {
            classData.MENU_FWRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_KHRW_TODO') {
            classData.MENU_KHRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_SJRW_TODO') {
            classData.MENU_SJRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_HTRW_TODO') {
            classData.MENU_HTRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_DDRW_TODO') {
            classData.MENU_DDRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_CKRW_TODO') {
            classData.MENU_CKRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_XMRW_TODO') {
            classData.MENU_XMRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_XZDRW_TODO') {
            classData.MENU_XZDRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_HTMBRW_TODO') {
            classData.MENU_HTMBRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_BPC_TODO') {
            classData.MENU_BPC_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_CSDDRW_TODO') {
            classData.MENU_CSDDRW_TODO = item.paramValue;
          } else if (item.paramCode === 'MENU_CPC_TODO') {
            classData.MENU_CPC_TODO = item.paramValue;
          } else if (item.paramCode === 'WAIT_TODO_CLOUD') {
            classData.WAIT_TODO_CLOUD = item.paramValue;
          } else if(item.paramCode === 'MENU_SJRW_VISIT_TODO'){
            classData.MENU_SJRW_VISIT_TODO = item.paramValue;
          }else if(item.paramCode === 'MENU_SJRW_PRESALE_TODO'){
            classData.MENU_SJRW_PRESALE_TODO = item.paramValue;
          }
        });
      }
      if (response3 && response3.resultCode === '0') {
        const data = serialize(response3, classData);
        if (data !== null || data.length > 0) {
          result = { ...result, taskResultData: data };
          yield put({
            type: 'save',
            payload: result,
          });
        }
      }
    },
    *qryTasksNumbers(_, { call, put }) {
      const response = yield call(qryTasksNumbers);
      let result = {};
      if (response && response.resultCode === '0') {
        const data = serialize(response, classData);
        if (data !== null || data.length > 0) {
          result = { ...result, taskResultData: data, undoCount: (response.resultData?.details?.[0]?.count ?? 0) };
          yield put({
            type: 'save',
            payload: result,
          });
        }
      }
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
