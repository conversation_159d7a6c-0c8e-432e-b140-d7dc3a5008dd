import React, { useState, useEffect, useRef } from 'react';
import {
  G2,
  Chart,
  Geom,
  Axis,
  Tooltip,
  Coord,
  Label,
  Legend,
  View,
  Guide,
  Shape,
  Facet,
  Util,
} from 'bizcharts';
import { Card, Form, Row, Col, Select, Icon, Input, Button, Tag, Modal, Divider } from 'antd';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
import request from '@/utils/request';
import styles from './styles.less';
import { Bar, ChartCard } from '@/components/Charts';
import { useAntdTable } from '@umijs/hooks';
import { getPageSizeByCardHeight } from '@/utils/utils';
import { getItem } from '@/utils/utils';
let isExistNext  = true;
let allStatisticsData=[];
let firstStatisticsData = [];
function PersonTaskStatistics(props) {
  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 8 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 16 },
    },
  };
  const { form } = props;
  const { getFieldDecorator } = props.form;

  const columnsWidth = 90;
  const colModel = [
    {
      dataIndex: 'taskType',
      title: '任务大类',
      width: columnsWidth,
      render: (value, row, index) => {
        const obj = {
          children: value,
          props: {},
        };
        if (row.isParent) {
          obj.props.rowSpan = row.totalColSpan;
        } else {
          obj.props.rowSpan = 0;
        }
        return obj;
      }
    },
    {
      dataIndex: 'subTaskType',
      title: '任务小类',
      width: columnsWidth,
    },
    {
      dataIndex: 'addCount',
      title: '新增',
      width: columnsWidth,
    },
    {
      dataIndex: 'completeRate',
      title: '完成率',
      width: columnsWidth,
      // sorter: (a, b) => a.completeRate - b.completeRate,
      // sortOrder: true,
      // ellipsis: true,
    },
  ];
  const compare = function(obj1, obj2) {
    let val1 = parseInt(obj1.completeCount);
    let val2 = parseInt(obj2.completeCount);
    return val2 - val1;
  };
  const [current, setCurrent] = useState(1);
  const [size, setSize] = useState(getPageSizeByCardHeight(props.size.height - 64 - 8));
  const [queryDateMap, setQueryDateMap] = useState([]);
  const [statisticsData, setStatisticsData] = useState([]);
  const [titleInfo, setTitleInfo] = useState('');
  const [totalCount, setTotalCount] = useState(0);
  const [showColumns, setShowColumns] = useState(colModel);
  const [paramsObj, setParamsObj] = useState({});
  const [tableDataList, setTableDataList] = useState([]);
  const [list, setList] = useState([]);
  const [titleArray, setTitleArray] = useState([]);

  const resetForm = () => {
    form.resetFields();
  };


  useEffect(() => {
    setSize(getPageSizeByCardHeight(props.size.height - 64 - 8 - props.size.height * 0.4));
  }, [props.size.height]);
  useEffect(() => {

    let queryCode = {
      busiNbr: 'TaskView',
      propertyName: 'queryDate',
    };
    request('portal/DomainDataController/getValuesList.do', {
      data: { ...queryCode },
      method: 'POST',
    }).then(result => {
      const {orgInfo } = getItem('user');
      let assitTitleArray =[];
      assitTitleArray.push({
        taskType: '全部任务',
        id:'1'
      });
      setTitleArray(assitTitleArray);
      setQueryDateMap(result);
      props.form.setFieldsValue({
        dateDimension:'DAY_ADD',
      })
      search(props,{id:orgInfo.orgId,name:orgInfo.name});
    });
  }, []);

  const cols = {
    y: {
      min: 0,
      alias: "任务总数"
    },
    sales: {
      tickInterval: 20,
    },
  };
  return (
    <div>
      <Form>
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
          <Col span={6}>
            <Form.Item label="统计周期" {...formItemLayout}>
              {getFieldDecorator('dateDimension')(
                <Select
                  allowClear
                  placeholder="请选择"
                  style={{ width: '100%' }}
                  showSearch
                  optionFilterProp="children"
                >
                  {queryDateMap.map(item => (
                    <Select.Option key={item.value}>{item.name}</Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
          <Button
            type="primary"
            onClick={() => {
              search(props,titleArray[titleArray.length -1]);
            }}
          >
            查询
          </Button>
        </Row>
      </Form>
      <div style={{ border: '1px solid #E8E8E8', padding: '10px' }}>
        <div className={styles.leftTitle}>任务完成量统计</div>
          <div className={styles.statisticsTitle}>{
            titleArray.map((item, index)=>{
                 return  (
                   <span key={index} onClick={()=>clickTitle(item)} > {index!=0 ? ">>":""} {item.taskType} </span>
                 )
            })
          }</div>
        <div >
          <Chart height={props.size.height * 0.38} data={statisticsData} scale={cols} forceFit onPlotClick={(value) =>handleChartClick(value)}>
            <Axis name="x" />
            <Axis name="y" />
            <Tooltip
              crosshairs={{
                type: "y",
                name:'任务总数'
              }}
            />
            <Geom type="interval" position="x*y" />
          </Chart>
        </div>
        <SlickTable
          style={{ marginTop: 0}}
          rowKey={record => record.title}

          // columns={showColumns}
          // dataSource={tableDataList}
          // data={{
          //   pagination: {
          //     pageNumber: current,
          //     pageSize: size,
          //   },
          // }}

          data={{
            list:tableDataList,
            pagination: {
              current,
              pageSize:1000
            },
          }}
          columns={showColumns}
          // dataSource={tableDataList}
          // width={300}
          onChange={handleChange}
          scroll={{ y: props.size.height * 0.3 }}
        />
      </div>
    </div>
  );


  function handleChange  (pagination)  {
    const { current } = pagination;
    setCurrent(current);
  };
  function handleChartClick(e) {
    if(!isExistNext){
      return;
    }
    const data = e.data;
    if(data === undefined || data === null){
      return;
    }

    isExistNext  = false;
    let statistics = data._origin.data;
    let name  = returnTaskName(statistics.taskType);
    let assitTitleArray =titleArray;
    assitTitleArray.push({
      taskType: name,
      id:2
    });
    setTitleArray(assitTitleArray);
    let datas = [];
    tableDataList.forEach( function (data) {
        if (data.taskType == name && data.subTaskType != "总量") {
          datas.push({
            x:data.subTaskType,
            y:data.completeCount,
            data:data
          })

        }
    })
    setStatisticsData(datas);
  }

  function clickTitle(item){
    if(item.taskType != '全部任务'){
      return;
    }
    isExistNext = true;
    let assitTitleArray =[];
    if(titleArray[titleArray.length -1].id == item.id){
      return;
    }
    for (let i = 0; i <titleArray.length;i++) {
        assitTitleArray.push(titleArray[i]);
        if(assitTitleArray[i].id == item.id){
          break;
        }
    }
    setTitleArray(assitTitleArray);
    setStatisticsData(firstStatisticsData);
  }
  function search(props) {
    const { userInfo } = getItem('user');
    let dateDimension = props.form.getFieldValue('dateDimension');
    let queryCode = {
      userId: userInfo.userId,
      dateDimension: (dateDimension == undefined ) ? 'DAY_ADD' : dateDimension,
    };
    request('orgauth/WorkbenchController/queryTaskPersonalView.do', {
      data: { ...queryCode },
      method: 'POST',
    }).then(result => {

     const list = result.taskInfos;
      list.sort(compare);

      var totalAddCount = 0;
      var totalCompleteCount = 0;

      let datas = [];
      let tableDatas = [];
      let titleInfo = {};
      for (let i = 0; i < list.length; i++) {
          let rstData = list[i];
          let taskName = returnTaskName(rstData.taskType);
          datas.push({
            x: taskName,
            y:rstData.completeCount,
            data:rstData
          })

          var totalCount = 0;
          if (rstData.subTaskInfo) {
              let index = 0 ;
              // 记录同等数据有几条
              let totalColSpan = rstData.subTaskInfo.length+1;
              rstData.subTaskInfo.forEach( function (taskData) {
                  totalCount = totalCount + taskData.addCount;
                  totalCompleteCount +=  taskData.completeCount;
                  tableDatas.push({
                      taskType: taskName,
                      addCount: taskData.addCount,
                      completeCount: taskData.completeCount,
                      completeRate: taskData.completeRate,
                      subTaskType: returnTaskName(taskData.subTaskType),
                      isParent: index==0?true:false,
                      totalColSpan: index==0?totalColSpan:0
                  });
                  index++;
              })

              totalAddCount += totalCount;
              let total = {
                  taskType: taskName,
                  addCount: totalCount,
                  completeCount: rstData.completeCount,
                  subTaskType: "总量"
              }

              if (totalCount != 0 && rstData.completeCount >= totalCount) {
                  total.completeRate = "100.00%";
              } else if (totalCount != 0 && rstData.completeCount < totalCount) {
                  total.completeRate = ((rstData.completeCount / totalCount) * 100).toFixed(2) + "%";
              } else if (totalCount == 0 && rstData.completeCount > 0) {
                  total.completeRate = "100.00%"
              } else {
                  total.completeRate = "0.00%"
              }
              total.isParent = index==0?true:false;
              total.totalColSpan = index==0?totalColSpan:0
              index++;
              tableDatas.push(total);
          }
      }
      firstStatisticsData = datas;
      setStatisticsData(firstStatisticsData);

      setTableDataList(tableDatas);
    });
  }
  function formTableData(tableDatas){

  }
  function returnTaskName(taskType) {
    switch (taskType) {
      case 'SERVICE_TASK': return "服务任务";
      case 'OPP_TASK': return "商机任务";
      case 'VISIT_TASK': return "拜访任务";
      case 'WARNING': return "预警任务";
      case 'MARKETING': return "营销任务";
      case 'CUST_TYPE': return "客户任务";
      case 'ORDER_TYPE': return "订单任务";
      case 'AGREEMENT_TYPE': return "合同任务";
      case 'SURVEY_TYPE': return "查勘任务";
      case 'OPPORTUNITY': return "商机任务";
      case 'BIRTHDAY_SERVICE': return "生日关怀";
      case 'DAILY_TASK': return "日常任务";
      case 'VISIT_DAILY_TASK': return "日常拜访";
      case 'CLUE_TASK': return "线索拜访";
      case 'OPPT_TASK': return "商机拜访";
      case 'COLOR_LIGHT': return "红黄灯预警";
      case 'HIGH_DANGER': return "高危预警";
      case 'COMPLAIN': return "客户投诉";
      case 'DEBTS_RECYCLE': return "欠费回收";
      case 'ARREARS': return "欠费催缴";
      case 'DEALY_ARREARS': return "延迟催缴审批";
      case 'CONTRACT_TIME_OUT': return "合同到期";
      case 'PERSON_MARKETING': return "成员稳保";
      case 'SPECIAL_MARKETING': return "专线营销";
      case 'CUST_SERVICE': return "客户任务";
      case 'ORDER_SERVICE': return "订单任务";
      case 'AGREEMENT_SERVICE': return "合同任务";
      case 'SURVEY_TASKS_SERVICE': return "查勘任务";
      case 'CUST_CARD_UPDATE': return "客户三证一章修改";
      case 'CUST_CONTACT_UPDATE': return "客户联系信息修改";
      case 'CUST_INFO_UPDATE': return "客户资料修改";
      case 'CPC_TYPE': return "产商品任务";
      case 'CPC_SERVICE':return '产商品任务';
      default: return "未知";
  }

  }
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(PersonTaskStatistics));
