export default {
  namespace: 'desensitizationManage',
  state: {
    sysCode: [],
    behavior: 'disabled',
    tableData: {},
    refreshTag: 0,
  },

  effects: {},

  reducers: {
    saveSyscode(state, { payload: params }) {
      return {
        ...state,
        sysCode: params,
      };
    },
    saveBehavior(state, { payload: params }) {
      return {
        ...state,
        behavior: params,
      };
    },
    saveTableData(state, { payload: params }) {
      return {
        ...state,
        tableData: params,
      };
    },
    updateRefreshTag(state) {
      return {
        ...state,
        refreshTag: state.refreshTag + 1,
      };
    },

  },
};
