// 集团证件扫描地址
export const GRP_IDEN_SCAN_URL = '';
// 无纸化拍照地址
export const SCAN_URL = '';
// 无纸化系统地址
export const SONBR_ADDRESS = '';

/* 归属地重要集团 */
export const ENTERPRISE_IMPORT_FLAG = {
  0: '否',
  1: '是',
};

/* 客户状态 */
export const GROUP_STATUS = {
  2: '正使用',
  3: '沉默',
  4: '离网',
  5: '已销户',
  1: '未开通',
};

/* 证件类型 */
export const IDEN_CARD_TYPE = {
  100001: '居民个人身份证',
  100002: '户口薄',
  100005: '外国人证件',
  100007: '港澳居民来往内地通行证',
  100008: '台湾居民来往大陆通行证',
  100010: '军官证',
  100011: '警官证',
  100023: '外国人永久居留证',
  100024: '港澳台居民居住证',
  100026: '临时身份证',
  100027: '华侨护照',
  100028: '港澳居民来往内地通行证（非中国籍）',
  200002: '18位统一社会信用代码（除营业执照）',
  200006: '集团单位营业执照,集团单位营业执照',
  200010: '新版营业执照',
  300002: '事业单位法人证书',
  300006: '民办非企业单位登记证书',
};

/* 信用级别 */
export const GROUP_CREDIT_LEVEL = {
  1: '一级',
  2: '二级',
  3: '三级',
  4: '四级',
  5: '五级',
};

/* 集团类型 */
export const GROUP_TYPE = {
  1: '法人单位',
  2: '个体经营',
  3: '聚类客户',
  4: '个体企业',
};

/* CUSTGROUP_CLASSID */
export const CUSTGROUP_CLASSID = {
  1: 'A类客户',
  2: 'B类客户',
  3: 'C类客户',
  4: 'A1',
  5: 'A2',
  6: 'B1',
  7: 'B2',
  8: 'C',
  9: 'D',
  99: '未定级别',
};

/* 集团规模 */
export const ENTERPRISE_SIZE_CODE = {
  0: '特大型',
  1: '大型',
  2: '中型',
  3: '小微型',
};

/* 跨省类型 */
export const GROUP_SCOPE_TYPE = {
  1: '牵头省',
  2: '协作省',
};

/* 是否战略客户 */
export const IS_BRANCH_VALUE = {
  0: '否',
  1: '是',
};

/* 是否升级华为云平台 */
export const IS_SEND_MANAGE_PLAT = {
  0: '否',
  1: '是',
};

/* 小微企业 */
export const IS_SMALLMICRO = {
  0: '其他',
  1: '中小微企业',
  '': '其他',
};

/* 客户服务等级 */
export const GROUP_SERV_LEVEL = {
  1: '金',
  2: '银',
  3: '铜',
  4: '标',
};

/* 客户类型 */
export const GROUP_CUST_TYPE = {
  100000003: '团体客户',
  100000001: '集团客户',
};

/* 进出口客户 */
export const CMIOT_EXPORT_IMPORT_VALUE = {
  0: '否',
  1: '是',
};

export const IS_UP_WLW_GROUP = {
  0: '关闭',
  1: '开启',
};

/* 集团客户证件类型 */
export const GROUP_CARD_TYPE = {
  100019: '介绍信',
  200002: '18位统一社会信用代码（除营业执照）',
  200009: '集团单位组织机构代码证',
  200010: '新版营业执照（三合一）',
  300000: '军队代码',
  300001: '有偿服务许可证',
  300002: '事业单位法人证书',
  300004: '社团法人证书',
  300005: '宗教活动场所登记证',
  300006: '民办非企业单位登记证书',
  300007: '基金会法人登记证书',
  300008: '律师事务所执业许可证',
  300009: '单位证明',
  300010: '个人有效证件',
};

/* 是否通过在线公司核验 */
export const COMPARE_STATUS_NAME = {
  0: '未通过核验',
  1: '已通过核验',
};

/* 集团客户类型 */
export const GROUP_CUST_TYPE_CODE = {
  100000: '军队',
  100001: '政府机关',
  100002: '事业单位',
  100003: '企业',
  100004: '社会团体',
  100005: '民办非企业',
  100006: '基金会',
  100007: '律师事务所',
  100008: '外国文化中心',
  100009: '群团组织',
  100010: '村委会',
  100011: '个体工商户及小微企业',
};

/* 客户类型 - 证件类型 约束 */
export const custTypeAndCertTypeRules = {

  /* 军队 */
  100000: [
    { code: 300000, name: '军队代码' },
    { code: 300001, name: '有偿服务许可证' },
  ],

  /* 政府机关 */
  100001: [
    { code: 200002, name: '18位统一社会信用代码（除营业执照）' },
    { code: 200009, name: '集团单位组织机构代码证' },
  ],

  /* 事业单位 */
  100002: [
    { code: 200009, name: '集团单位组织机构代码证' },
    { code: 300002, name: '事业单位法人证书' },
  ],

  /* 企业 */
  100003: [
    { code: 200002, name: '18位统一社会信用代码（除营业执照）' },
    { code: 200009, name: '集团单位组织机构代码证' },
    { code: 200010, name: '新版营业执照（三合一）' },
  ],

  /* 社会团体 */
  100004: [
    { code: 200009, name: '集团单位组织机构代码证' },
    { code: 300004, name: '社团法人证书' },
    { code: 300005, name: '宗教活动场所登记证' },
  ],

  /* 民办非企业 */
  100005: [
    { code: 200009, name: '集团单位组织机构代码证' },
    { code: 300006, name: '民办非企业单位登记证书' },
  ],

  /* 基金会 */
  100006: [
    { code: 200009, name: '集团单位组织机构代码证' },
    { code: 300007, name: '基金会法人登记证书' },
  ],

  /* 律师事务所 */
  100007: [
    { code: 200009, name: '集团单位组织机构代码证' },
    { code: 300008, name: '律师事务所执业许可证' },
  ],

  /* 外国文化中心 */
  100008: [{ code: 200002, name: '18位统一社会信用代码（除营业执照）' }],

  /* 群团组织 */
  100009: [{ code: 200002, name: '18位统一社会信用代码（除营业执照）' }],

  /* 村委会 */
  100010: [
    { code: 200009, name: '集团单位组织机构代码证' },
    { code: 300009, name: '单位证明' },
  ],

  /* 个体工商户及小微企业 */
  100011: [
    { code: 200002, name: '18位统一社会信用代码（除营业执照）' },
    { code: 300010, name: '身份证' },
  ],
};

/* 经办人经办方式 */
export const CM_OPERATOR_MODE = {
  0: '本人办理',
  1: '委托他人办理',
};
export const CMIOT_REGION_VALUE = {
  4700: '呼伦贝尔市',
  4710: '呼和浩特市',
  4720: '包头市',
  4730: '乌海市',
  4740: '乌兰察布市',
  4750: '通辽市',
  4760: '赤峰市',
  4770: '鄂尔多斯市',
  4780: '巴彦淖尔市',
  4790: '锡林郭勒盟',
  4820: '兴安盟',
  4830: '阿拉善盟',
};

/* CMIOT 组织机构 */
export const CMIOT_ORG_VALUE = {
  60000000002248: '内蒙移动',
};

/* 合同类型 */
export const CONTRACT_TYPE = {
  0: '集团客户合同',
  1: '合作伙伴合同',
  2: '个人合同',
  9: '其他合同',
};

/* 合同级别 */
export const CONTRACT_LEVEL = {
  0: '普通',
  1: '重要',
  2: '保密',
};

/* 信誉度级别 */
export const CUST_CREDIT_LEVEL = {
  2: 'C级-后付费用户',
  20: '集团客户预付费0余额信控',
  21: '集团客户后付费3个月信控',
  22: '集团客户后付费6个月信控',
  23: '集团客户后付费12个月信控',
  24: '集团客户后付费15个月信控',
  3: 'C1级-单停一日后付费用户',
  30: '集团客户后付费1个月信控',
  4: 'C2级-单停三日后付费用户',
  5: 'B级-信誉度客户',
  6: 'A级-预付费客户',
  7: '中高端预付费（30元催缴）',
};

/* 账户类型 */
export const ACT_TYPE = {};

/* 纳税人资质属性 */
export const TAX_QUALIFICATION = {
  0: '小规模纳税人',
  1: '一般纳税人',
};

/* 发票打印类型 */
export const INVOICE_PRINT_TYPE = {
  0: {
    0: '预存打印发票',
    1: '月结打印发票',
    4: '增值税电子普通发票',
  },
  1: {
    0: '预存打印发票',
    2: '月结通用机打打发票',
    3: '月结增值税专票',
  },
};

/* 行业类型 */
export const CALLING_AREA_CODE = {
  1: '党政军',
  10: '中外合资',
  11: '国企',
  12: '其他',
  2: '公检法',
  3: '新闻媒体',
  4: '院校',
  5: '企事业',
  6: '外事机构',
  7: '外企',
  8: '私企',
  9: '民营',
};

/* 行业门类 */
export const GROUP_ARCHIVES_TRADE_TYPE = {
  11000: 'A、农、林、牧、渔业',
  11100: 'B、采矿业,B、采矿业',
  11200: 'C、制造业,C、制造业',
  11300: 'D、电力、燃气及水的生产和供应业',
  11400: 'E、建筑业',
  11500: 'F、交通运输、仓储和邮政业',
  11600: 'G、信息传输、计算机服务和软件业',
  11700: 'H、批发和零售业',
  11800: 'I、住宿和餐饮业',
  11900: 'J、金融业',
  12000: 'K、房地产业',
  12100: 'L、租赁和商务服务业',
  12200: 'M、科学研究、技术服务和地质勘查',
  12300: 'N、环境和公共设施管理业',
  12400: 'O、居民服务和其他服务业',
  12500: 'P、教育',
  12600: 'Q、卫生、社会保障和社会福利业',
  12700: 'R、文化、体育和娱乐业',
  12800: 'S、公共管理和社会组织',
  12900: 'T、国际组织',
  13000: '其它',
  13100: 'U、国防',
  14100: 'V、其他',
  15000: 'W、工业能源-客户树',
  15100: 'X、交通-客户树',
  16000: 'Y、互联网-客户树',
  16100: 'Z、金融-客户树',
  17000: 'AA、教育-客户树',
  17100: 'AB、医卫-客户树',
  18000: 'AC、农业文旅-客户树',
  18100: 'AD、党政-客户树',
  19000: 'AE、融合-客户树',
};
export const GROUP_JURISTIC_IDEN_TYPE = {
  100001: '身份证',
  100002: '户口薄',
  100003: '驾驶证',
  100005: '护照',
  100010: '军官证',
  100011: '警官证',
  100014: '社会保险号',
  100019: '介绍信',
};

/* 是否CMIOT */
export const IS_CMIOT = {
  0: '否',
  1: '是',
};

/* 集团级别 */
export const GROUP_LEVEL = {
  14: 'D1',
  15: 'D2',
  2: 'B类客户',
  3: 'C类客户',
  4: 'A1',
  5: 'A2',
  6: 'B1',
  7: 'B2',
  8: 'C',
  9: 'D',
  99: '未定级别',
};

/* 场景大类 */
export const clusteringScenario = {
  1: '楼宇',
  2: '泛住宿',
  3: '园区',
  4: '沿街商铺',
  6: '专业市场',
  7: '其他',
};

/* 场景子类 */
export const clustScenTypeTwo = {
  '01': '零售',
  '02': '餐饮',
  '03': '住宿',
  '04': '药店',
  '05': '自定义',
  '06': '其他',
};

/* 集团所在省份 */
export const CDR_TABLE_PROVINCE_CODE = {
  100: '北京',
  200: '广东',
  210: '上海',
  220: '天津',
  230: '重庆',
  240: '辽宁',
  250: '江苏',
  270: '湖北',
  280: '四川',
  290: '陕西',
  311: '河北',
  351: '山西',
  371: '河南',
  431: '吉林',
  451: '黑龙江',
  471: '内蒙',
  531: '山东',
  551: '安徽',
  571: '浙江',
  591: '福建',
  731: '湖南',
  771: '广西',
  791: '江西',
  851: '贵州',
  871: '云南',
  891: '西藏',
  898: '海南',
  931: '甘肃',
  951: '宁夏',
  971: '青海',
  991: '新疆',
};

/* 集团详细类型 */
export const GROUP_TYPE_DETAIL = {
  1: '机关法人',
  2: '企业法人',
  3: '事业单位法人',
  4: '社会团体法人',
  5: '其它法人',
};

/* 跨省类型 */
export const MULTI_PROVINCE = {
  1: '牵头省',
  2: '协作省',
};

/* 集团支付方式 */
export const GROUP_PAY_NAME = {
  1: '统一付费',
  2: '实报实销',
  3: '限额报销',
  4: '限额补助',
  5: '个人负责',
};

/**/
export const GROUP_REAL_NAME_QRY_STATUS = {
  DATA_ALL: '全部数据',
  DATA_EFF: '生效数据',
  DATA_WAIT: '等待无纸化回调',
};

/* 账户类型 */
export const ACCT_TYPE = {
  0: '预付费账户',
  1: '后付费账户',
  2: '信誉度账户',
  3: '一点支付类型的账户类型',
};

/* 账户状态 */
export const ACCT_STATUS = {
  0: '历史',
  1: '在用',
  2: '删除',
};

/* 账户级别 */
export const ACCT_CLASS = {
  1: '普通个人帐户',
  2: '集团付费帐户',
  3: 'VPMN集团帐户',
  4: '一点支付主办省账户',
  5: '一点支付协力省账户',
};

/* 付费方式 */
export const ACCT_PAY_TYPE = {
  1: '现金',
  10: 'POS刷卡',
  11: '第三方支付',
  26: '互联网支付',
  3: '支票',
  7: '互联网支付',
  8: '汇票',
  9: '银行转账',
};

/* 客户细分标识 */
export const CM_CUST_SEGMENT = {
  100000000: '个人客户',
  100000001: '政企客户',
  100000002: '家庭客户',
  100000003: '团体客户',
  100000004: '大型商业客户',
  100000005: '中型商业客户',
  100000006: '小型商业客户',
  100000007: '微型商业客户',
};

/* 集团客户级别 */

export const GROUP_CUST_LEVEL = {
  1: 'A',
  10: 'C1',
  11: 'C2',
  12: 'C3',
  13: 'C4',
  14: 'D1',
  15: 'D2',
  2: 'B',
  4: 'A1',
  5: 'A2',
  6: 'B1',
  7: 'B2',
  8: 'C',
  9: 'D',
  99: '未定级别',
};

/* 文件类型 */
export const FILE_TYPE = {
  doc: 'doc',
  未知: '未知',
  jpeg: 'jpeg',
  bmp: 'bmp',
  jpg: 'jpg',
  xlsx: 'xlsx',
  xls: 'xls',
  docx: 'docx',
};

/* 非结构化附件类型 */
export const UNSTRUCTURED_TYPE = {
  1: '集团客户实名登记证明材料',
  2: '目的地号码证明材料',
  3: '信息安全承诺书',
  4: '业务协议',
  5: '业务受理单',
  6: '号码审批清单',
  7: '工信部或管局审批文件',
  8: '其他',
  12: '客户证件扫描件',
  13: '集团专线延期账务停机（账务注销）的审批文件',
};

/* 非结构化业务类型 */
export const UNSTRUCTURED_BUSITYPE = {
  1: 'ims语音专线',
  2: '省内呼叫中心直连',
  3: '虚拟呼叫中心',
  4: '移动400',
};

/* 企业属性 */
export const ENTERPRISE_ATTR = {
  0: '企业',
  1: '个人',
  2: '企业性单位或个体工商户',
  3: '非企业性单位',
};

/* 预打印增值税资质 */
export const PRINT_QUALIFICATIONS = {
  0: '没有资质',
  1: '有资质',
};

/* 纳税人资料类型 */
export const TAXPAYER_DOC_TYPE = {
  0: '图片',
  1: '文件',
};

export const SCPID_TYPE = {
  1: 'IMS业务',
  2: '联合V网',
  3: 'VPMN',
  4: '多媒体桌面电话',
  5: '融合V网',
  6: '融合一号通',
  7: '融合总机',
  8: '固定会场会议',
  9: '客户端会议',
  10: '语音会议',
  101: '跨省V网',
};

/* VPMN集团类型 */
export const VPMN_GROUP_TYPE = {
  1: '个性化本地集团',
  2: '个性化全省集团',
  3: '普通本地集团',
  4: '普通全省集团',
  5: '全国集团',
  6: '本地化省级集团',
};

/* 特殊要求的集团类型 */
export const SPECIAL_GROUP_TYPE = {
  0: '普通VPMN集团',
  1: '特殊要求的VPMN集团',
};

/* 是否判断 */
export const IS_OR_NO_TYPE = {
  0: '否',
  1: '是',
};

/* 激活标志 */
export const ACTIVATE_TYPE = {
  0: '未激活',
  1: '激活',
};

/* 注销标志 */
export const REMOVE_TYPE = {
  0: '正常',
  2: '注销',
  3: '停用',
};

/* 地州翻译 */
export const REGION_ID_TYPE = {
  470: '呼伦贝尔',
  471: '呼和浩特',
  472: '包头',
  473: '乌海',
  474: '集宁',
  475: '通辽',
  476: '赤峰',
  477: '鄂尔多斯',
  478: '临河',
  479: '锡林浩特',
  482: '乌兰浩特',
  483: '阿拉善',
};

// 账户信息 - 账户级别映射
export const AcctClass = {
  1: '普通个人帐户',
  2: '集团付费帐户',
  3: 'VPMN集团帐户',
  4: '一点支付主办省账户',
  5: '一点支付协力省账户',
};

// 账户信息 - 信用级别
export const CreditLevel = {
  2: 'C级-后付费用户',
  20: '集团客户预付费0余额信控',
  21: '集团客户后付费3个月信控',
  22: '集团客户后付费6个月信控',
  23: '集团客户后付费12个月信控',
  24: '集团客户后付费15个月信控',
  3: 'C1级-单停一日后付费用户',
  30: '集团客户后付费1个月信控',
  4: 'C2级-单停三日后付费用户',
  5: 'B级-信誉度客户',
  6: 'A级-预付费客户',
  7: '中高端预付费（30元催缴）',
};

/* 角色类型  角色配置是固定的 */
export const CHARACTERS_TYPE = {
  3130000001: '合作伙伴-角色名称1',
  3130000002: '合作伙伴-角色名称2',
  3130000003: '渠道商-角色名称1',
  3130000004: '渠道商-角色名称2',
};

/* 黑名单数据状态 */
export const BLACK_DATA_STATUS = {
  0: '非黑名单客户',
  1: '黑名单客户',
};

export const CMIOT_REGION_VALUE_LIST = [
  {
    label: '呼伦贝尔市',
    value: '4700',
    isLeaf: false, // 第三级是叶子节点
  },
  {
    label: '呼和浩特市',
    value: '4710',
    isLeaf: false, // 第三级是叶子节点
  },
  {
    label: '包头市',
    value: '4720',
    isLeaf: false, // 第三级是叶子节点
  },
  {
    label: '乌海市',
    value: '4730',
    isLeaf: false, // 第三级是叶子节点
  },
  {
    label: '乌兰察布市',
    value: '4740',
    isLeaf: false, // 第三级是叶子节点
  },
  {
    label: '通辽市',
    value: '4750',
    isLeaf: false, // 第三级是叶子节点
  },
  {
    label: '赤峰市',
    value: '4760',
    isLeaf: false, // 第三级是叶子节点
  },
  {
    label: '鄂尔多斯市',
    value: '4770',
    isLeaf: false, // 第三级是叶子节点
  },
  {
    label: '巴彦淖尔市',
    value: '4780',
    isLeaf: false, // 第三级是叶子节点
  },
  {
    label: '锡林郭勒盟',
    value: '4790',
    isLeaf: false, // 第三级是叶子节点
  },
  {
    label: '兴安盟',
    value: '4820',
    isLeaf: false, // 第三级是叶子节点
  },
  {
    label: '阿拉善盟',
    value: '4830',
    isLeaf: false, // 第三级是叶子节点
  },
];
