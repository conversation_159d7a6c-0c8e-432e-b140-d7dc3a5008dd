import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Card, message } from 'antd';
import pick from 'lodash/pick';
import delay from 'lodash/delay';
import { getUserConfigMenu, getTempletById, saveTemplet } from '../services/customize';
import DndTable from '../components/DndTable';

function MobileTemplate({
  dispatch,
  user: {
    userInfo: { userId, roleId, moduleStyle },
  },
  appSysCode,
  editable,
  dataSource,
  goToStep,
  size: { height },
}) {
  const [templetId] = useState(() => (dataSource.status === 'undo' ? '-1' : dataSource.templetId));
  const [current, setCurrent] = useState([]);
  const [all, setAll] = useState([]);
  const [allFunc, setAllFunc] = useState([]);
  const [submiting, setSubmiting] = useState(false);
  const [currentLoading, setCurrentLoading] = useState(true);
  const [allLoading, setAllLoading] = useState(true);

  function handleSubmit(data) {
    const { portlets, ...rest } = data;
    if (Array.isArray(portlets) && portlets.length === 0) {
      message.warn('请先选择模块');
      return;
    }
    setSubmiting(true);
    saveTemplet({
      confType: '1', // 表示配置类型是工作台
      // type: 'user',
      templetId,
      sysPostId: roleId,
      sysUserId: userId,
      terminalType: '1200', // 终端类型 1000表示Pc端
      ...rest,
      moduleRels: portlets.map(item =>
        pick(item, ['menuId', 'menuName', 'urlAddr', 'templetModuleRelId','module','moduleIndex'])
      ), // 只有编辑时才有templetModuleRelId，否则不用传
      moduleStyle,
    })
      .then(res => {
        const { resultCode } = res;
        if (resultCode === '0') {
          message.success('保存模板成功');
          delay(() => {
            // 保证每次的值不一样 否则List不会刷新
            goToStep(1, { refresh: +new Date() });
          }, 300);
        } else {
          //message.error('保存模板失败');
        }
      })
      .finally(() => {
        setSubmiting(false);
      });
  }

  // 筛选出手机端的菜单
  useEffect(() => {
    getUserConfigMenu({ terminalType: 1200 }).then(res => {
      const all = [];
      const allFunc = [];
      if (!Array.isArray(res)) {
        message.error('获取当前用户有权限添加的portlet数据失败');
        return;
      }

      res.forEach(menu => {
        if (menu.menuCode.indexOf('TYMH_MENU_CARD') === 0) {
          all.push(
            pick(
              menu,
              'menuId',
              'parMenuId',
              'parMenuName',
              'urlAddr',
              'regionId',
              'systemCode',
              'menuName',
              'menuDesc',
              'menuLevel',
              'iconUrl',
              'menuCode',
              'menuModuleTypeName'
            )
          );
        }
      });
      res.forEach(menu => {
        if (menu.systemCode==='727004') {
          allFunc.push(
            pick(
              menu,
              'menuId',
              'parMenuId',
              'parMenuName',
              'urlAddr',
              'regionId',
              'systemCode',
              'menuName',
              'menuDesc',
              'menuLevel',
              'iconUrl',
              'menuCode',
              'menuModuleTypeName'
            )
          );
        }
      });
      if (all.length > 0) {
        setAll(all);
        setAllFunc(allFunc)
        setAllLoading(false);
      } else {
        message.error('无可选模块。请去菜单管理上传工作台模块，若已上传请给该用户分配模块权限。');
        delay(() => {
          goToStep(1);
        }, 300);
      }
    });
  }, [setAll,setAllFunc, appSysCode, goToStep]);

  // 如果模板id存在，则获取模板数据
  useEffect(() => {
    if (templetId !== '-1') {
      getTempletById({ templetId, terminalType: '1200' })
        .then(res => {
          //  moduleRels表示当前模板包含的portles
          const { resultObject = {}, resultCode } = res;
          const { moduleRels } = resultObject;
          if (resultCode !== '0' || !Array.isArray(moduleRels)) {
            message.error('获取模板数据失败');
          } else if(resultCode === '0' && !Array.isArray(moduleRels)){
            
          }else {
            setCurrent(moduleRels);
          }
        })
        .finally(() => {
          setCurrentLoading(false);
        });
    }
  }, [dispatch, templetId]);

  return (
    <>
      {/* <Card title="手机端模板配置" style={{ minHeight: height }} bordered loading={allLoading}> */ }
      {(all.length > 0 && templetId === '-1') ||
        (all.length > 0 && templetId !== '-1' && !currentLoading) ? (
          <DndTable
            current={current}
            currentMenu={(current.length===0||current[0].module===undefined)?[]:current[0].module.details}
            templetName={dataSource.status === 'undo' ? '' : dataSource.templetName}
            templetDesc={dataSource.status === 'undo' ? '' : dataSource.templetDesc}
            editable={editable}
            all={all}
            allFunc={allFunc}
            onConfirm={handleSubmit}
            onBack={() => {
              goToStep(1);
            }}
            loading={submiting}
          />
        ) : null}

      {/* </Card> */}
    </>
  );
}

export default connect(({ setting, menu, login }) => ({
  size: setting.size,
  user: login.user,
  appSysCode: menu.appSysCode,
}))(MobileTemplate);
