import React, { useState, useEffect } from 'react';
import { Row, Col, Skeleton, Empty } from 'antd';
import numeral from 'numeral';
import request from '@/utils/request';
import styles from './rank.less';
import { Bar } from '@/components/Charts';

const cache = {};

/**
 * 异步获取数据后，需要缓存起来。避免组件未销毁时重复发起请求
 */
function OppGrowthRate() {
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState([]);
  const [topList, setTopList] = useState([]);

  useEffect(() => {
    async function getInitData() {
      setLoading(true);
      // 查询增长趋势
      const response1 = await request('orgauth/WorkbenchController/qrySalesStatisticsIncrease.do', {
        method: 'get',
        data: { objType: 'OPP' },
      });

      // 查询排行榜数据 bjType :类型 LEAD:线索 OPP：商机
      const response2 = await request('orgauth/WorkbenchController/qryStatisticsOfBest.do', {
        method: 'get',
        data: { objType: 'OPP' },
      });
      if (
        response1 !== null &&
        response1.resultCode === '0' &&
        response1.resultObject.data.resultCode === '0'
      ) {
        let data = [];
        const rstData = response1.resultObject.data.resultObject;
        if (rstData) {
          rstData.forEach(item => data.push({ x: item.createDate, y: item.num }));
          // 所有项的y都是0 约等于“没记录”，避免柱状图出现“一条线”
          if (data.filter(item => item.y > 0).length === 0) {
            data = [];
          }
          cache.chartData = data;
          setChartData(data);
        }
      }

      if (
        response2 !== null &&
        response2.resultCode === '0' &&
        response2.resultObject.data.resultCode === '0'
      ) {
        const data = [];
        const rstData = response2.resultObject.data.resultObject.topList;
        if (rstData) {
          /**
           *  过滤掉0%，最多取7个
           *  预期取到的rstData格式如下
           *  [
           *    {
           *      createStaff: 2,
           *      createStaffName: '李可鹏',
           *      num: 22,
           *      numTotal: 76,
           *      percentage: '28.95%',
           *      percentageValue: '28.95%',
           *      ranks: 0,
           *      rowId: 1,
           *    }
           *  ];
           */
          rstData
            .filter(item => numeral(item.percentage)._value !== 0)
            .sort((a, b) => numeral(b.percentage)._value - numeral(a.percentage)._value)
            .slice(0, 7)
            .forEach((rankData, i) => {
              rankData.ranks = i;
              data.push(rankData);
            });
          cache.topList = data;
          setTopList(data);
        }
      }

      setLoading(false);
    }
    if (Object.keys(cache).length < 2) {
      getInitData();
    }
  }, []);

  let _chartData;
  let _topList;

  if (cache.chartData === undefined) {
    _chartData = chartData;
  } else {
    _chartData = cache.chartData;
  }
  if (cache.topList === undefined) {
    _topList = topList;
  } else {
    _topList = cache.topList;
  }
  return (
    <Skeleton active loading={loading} paragraph={{ rows: 7 }}>
      <Row gutter={24}>
        <Col span={18}>
          <div className={styles.salesBar}>
            {_chartData.length === 0 ? (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无数据"
                style={{ marginTop: 120 }}
              />
            ) : (
              <Bar
                height={292}
                title="商机增长趋势"
                data={_chartData}
                tooltipFormat={[
                  'x*y',
                  (x, y) => ({
                    name: `${x}月`,
                    value: `新增商机：${y}个`,
                  }),
                ]}
              />
            )}
          </div>
        </Col>
        <Col span={6}>
          <div className={styles.salesRank}>
            <h4 className={styles.rankingTitle}>商机转化率排名（本月）</h4>
            <ul className={styles.rankingList}>
              {_topList.length === 0 ? (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无排名"
                  style={{ marginTop: 120 }}
                />
              ) : (
                _topList.map((item, i) => (
                  <li key={item.createStaffName}>
                    <span className={`${styles.rankingItemNumber} ${i < 3 ? styles.active : ''}`}>
                      {i + 1}
                    </span>
                    <span className={styles.rankingItemTitle} title={item.createStaffName}>
                      {item.createStaffName}
                    </span>
                    <span>{item.percentage}</span>
                  </li>
                ))
              )}
            </ul>
          </div>
        </Col>
      </Row>
    </Skeleton>
  );
}

export default OppGrowthRate;
