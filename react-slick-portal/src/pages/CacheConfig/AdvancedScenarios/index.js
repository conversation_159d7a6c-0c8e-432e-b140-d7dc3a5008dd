import React, { useState } from 'react';
import { Card, Table, Button, Input, Row, Col, message, Modal } from 'antd';
import { selectCacheList, delByKeyType, batchDelete } from '@/services/cacheConfig';
import usePageTable from '@/hooks/usePageTable';
// import style from './SceneList.module.scss';
import NewRedis from './createRedis/index';
import EditRedis from './editRedis/index';

const PAGE_SIZE = 10;

const AdvancedScenarios = () => {
  const { list, pagination, loading, onFormChange, onSearch, getTableData, form } = usePageTable({
    pageSize: PAGE_SIZE,
    pageParamsKey: { index: 'pageNum', size: 'pageSize' },
    method: selectCacheList,
  });

  const [addVisible, setAddVisible] = useState(false);
  const [editVisible, setEditVisible] = useState({});
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const deleteScene = async record => {
    Modal.confirm({
      title: '确认删除吗',
      onOk: async () => {
        await delByKeyType({
          dataType: record.dataType,
          redisKey: record.redisKey,
        });
        message.success('删除成功！');
        onSearch();
      },
      onCancel() {},
    });
  };

  const deleteSelectedScenes = async () => {
    Modal.confirm({
      title: '确认批量删除吗',
      onOk: async () => {
        const keysToDelete = list.filter(item => selectedRowKeys.includes(item.redisKey)).map(item => ({
          dataType: item.dataType,
          redisKey: item.redisKey,
        }));
        await batchDelete(keysToDelete);
        message.success('批量删除成功！');
        setSelectedRowKeys([]);
        onSearch();
      },
      onCancel() {},
    });
  };

  const getEditModal = record => {
    setEditVisible({
      visible: true,
      record,
    });
  };

  const columns = [
    {
      title: '类型',
      dataIndex: 'dataType',
      width: '20%',
    },
    {
      title: '键',
      dataIndex: 'redisKey',
      width: '30%',
      ellipsis: true,
    },
    {
      title: '过期时间',
      dataIndex: 'expire',
      width: '20%',
      ellipsis: true,
    },
    {
      title: '元素总素',
      dataIndex: 'elCount',
      width: '10%',
    },
    {
      title: '操作',
      width: '20%',
      render: (value, record) => (
        <div>
          <Button
            type="link"
            onClick={() => getEditModal(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            onClick={() => deleteScene(record)}
          >
            删除
          </Button>
        </div>
      ),
    },
  ];

  return (
    <>
      <Card title="redis缓存刷新">
        <div>
          <div>
            <Row gutter={[16, 8]} align="middle">
              <Col span={1}>键：</Col>
              <Col span={5}>
                <Input
                  allowClear
                  onPressEnter={onSearch}
                  value={form.keyword}
                  onChange={e => {
                    onFormChange('keyword', e.target.value);
                  }}
                />
              </Col>
              <Col span={18} style={{ textAlign: 'right' }}>
                <Button
                  style={{ marginRight: 8 }}
                  onClick={() => {
                    setAddVisible(true);
                  }}
                >
                  新增
                </Button>
                <Button
                  type="primary"
                  style={{ marginRight: 8 }}
                  onClick={onSearch}
                >
                  查询
                </Button>
                <Button
                  type="danger"
                  onClick={deleteSelectedScenes}
                  disabled={selectedRowKeys.length === 0}
                >
                  批量删除
                </Button>
              </Col>
            </Row>
          </div>
        </div>
        <div className="panel">
          <Table
            bordered
            rowKey="redisKey"
            columns={columns}
            dataSource={list}
            loading={loading}
            size="middle"
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
            }}
            pagination={{
              ...pagination,
              pageSizeOptions: ['10', '50', '100', '200', '500'],
            }}
          />
        </div>
        {addVisible && (
          <NewRedis
            visible={addVisible}
            onCancel={() => {
              setAddVisible(false);
              getTableData();
            }}
          />
        )}
        {editVisible.visible && (
          <EditRedis
            visible={editVisible.visible}
            record={editVisible.record}
            onCancel={() => {
              setEditVisible({
                visible: false,
                record: {},
              });
              getTableData();
            }}
          />
        )}
      </Card>
    </>
  );
};

export default AdvancedScenarios;
