import request from '@/utils/request';

export function querySubGroupList(groupId) {
  return request('portal/SubGroupController/querySubEnterpriseByGroupId.do', {
    data: {
      parentId: groupId,
    },
  });
}

export function removeSubGroupRel(data) {
  return request('portal/SubGroupController/releaseEnterpriseRelationByParams.do', {
    data: {
      relationType: '1',
      ...data,
    },
  });
}
export function addSubGroupRel(data) {
  return request('portal/SubGroupController/saveSubEnterpriseRelation.do', {
    data: {
      ...data,
    },
  });
}

export function qryGroupEnterpriseInfo(data) {
  return request('portal/GroupEnterpriseController/qryGroupEnterpriseInfo.do', {
    method: 'POST',
    data,
  });
}
