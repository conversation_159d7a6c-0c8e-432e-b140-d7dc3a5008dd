// 使用端的服务
import { message } from 'antd';
import request from '@/utils/request';
import { SORT_TYPE } from '@/pages/Librarys/const';

/**
 * 查询用户上传的文件的统计信息
 * @returns
 */
export function queryPageUserUploadInfo(params) {
  const { current, pageSize = 10, knowledgeInfo } = params;
  return request('portal/KlKnowledgeInfoController/qryKlUserIntegralGrid.do', {
    method: 'post',
    data: {
      page: current,
      rowNum: pageSize,
      pageNum: current,
      pageSize,
      knowledgeInfo,
    },
  }).then(res => {
    if (Array.isArray(res?.list)) {
      return {
        total: res.total,
        data: res.list,
      };
    }
    return {
      total: 0,
      data: [],
    };
  });
}

/**
 * 本月创建TOP10查询
 * @returns
 */
export function qryCurrentMonthCreateTop(params) {
  const { knowledgeType } = params;
  return request('portal/KlKnowledgeInfoController/currentMonthCreateStatistical.do', {
    method: 'post',
    data: {
      pageNum: 1,
      pageSize: 10,
      anyProperties: {
        knowledgeClassType: knowledgeType, // 知识库类型
      },
    },
  }).then(res => {
    if (Array.isArray(res?.resultObject?.list)) {
      return res?.resultObject?.list;
    }
    return [];
  });
}

/**
 * 当月资料评分TOP10
 * @returns
 */
export function qryMonthScoreTop(params) {
  const { knowledgeType } = params;
  return request('portal/KlKnowledgeInfoController/currentMonthScoreStatistical.do', {
    method: 'post',
    data: {
      pageNum: 1,
      pageSize: 10,
      sortName: 'knowledgeSorce', // 积分排序
      sortOrder: 'desc', // 倒序
      anyProperties: {
        knowledgeClassType: knowledgeType, // 知识库类型
        isRegionId: 'N', // 是否带上用户区域条件
        isCurrentMonth: 'y', // 查询当月数据标识
        statusCd: '1001', // 状态：上架
      },
    },
  }).then(res => {
    if (Array.isArray(res?.resultObject?.list)) {
      return res?.resultObject?.list;
    }
    return [];
  });
}

/**
 * 最新上传
 * @returns
 */
export function qryMonthNewTop(params) {
  const { knowledgeType } = params;
  return request('portal/KlKnowledgeInfoController/qryKlKnowledgeInfoGrid.do', {
    method: 'post',
    data: {
      pageNum: 1,
      pageSize: 10,
      sortName: '', // 为空按时间倒序
      anyProperties: {
        isRegionId: 'N', // 是否带上用户区域条件
        knowledgeClassType: knowledgeType, // 知识库类型
        statusCd: '1001', // 状态：上架
      },
    },
  }).then(res => {
    if (Array.isArray(res?.list)) {
      return res?.list;
    }
    return [];
  });
}

/**
 * 本月访问TOP10查询
 * @returns
 */
export function qryCurrentMonthViewTop(params) {
  const { knowledgeType } = params;
  return request('portal/KlKnowledgeInfoController/currentMonthBrowseStatistical.do', {
    method: 'post',
    data: {
      pageNum: 1,
      pageSize: 10,
      anyProperties: {
        knowledgeClassType: knowledgeType, // 知识库类型
      },
    },
  }).then(res => {
    if (Array.isArray(res?.resultObject?.list)) {
      return res?.resultObject?.list;
    }
    return [];
  });
}

/**
 * 知识库ES服务查询
 * @returns
 */
export function qryESKlKnowledgeInfo(params) {
  const {
    current,
    pageSize,
    filters,
    sorter,
    sort,
    rangeCreateDate,
    filterVal,
    simpleFileType = [],
    fileTypeList = [],
    createStaffList = [],
    ...rest
  } = params;
  // 拼接文件类型列表，并去重
  const finalFileList = Array.from(new Set([...fileTypeList, ...simpleFileType]));
  const sortName = SORT_TYPE.find(item => item.value === sort)?.sortName;
  const sortOrder = SORT_TYPE.find(item => item.value === sort)?.sortOrder;

  // 处理创建日期
  if (Array.isArray(params.rangeCreateDate) && params.rangeCreateDate.length === 2) {
    rest.startCreateDate = params.rangeCreateDate[0].format('YYYY-MM-DD');
    rest.endCreateDate = params.rangeCreateDate[1].format('YYYY-MM-DD');
  }

  setTimeout(() => {
    document.getElementById('topBlock').scrollIntoView();
  }, 0);

  return request('portal/KlKnowledgeInfoController/qryESKlKnowledgeInfoPage.do', {
    method: 'post',
    data: {
      pageNum: current,
      pageSize,
      filterVal,
      sortName,
      sortOrder,
      anyProperties: {
        ...rest,
        createStaffList: createStaffList.map(item => item.id),
        fileTypeList: finalFileList,
      },
    },
  }).then(res => {
    if (Array.isArray(res?.resultObject?.list)) {
      return {
        total: res?.resultObject?.total,
        data: res?.resultObject?.list,
      };
    }
    return [];
  });
}

/**
 * 获取文件类型列表
 */
export async function qryFileTypeList() {
  return request('portal/DataDictController/getDataDictByGroup.do', {
    method: 'get',
    data: { groupCode: 'DOCUMENT_FORMAT_TYPE' },
  });
}

/**
 * 提交评分
 * @returns
 */
export function sendCommentScore(params) {
  return request('portal/KlKnowledgeScoreController/addKlKnowledgeScore.do', {
    method: 'post',
    data: {
      knowledgeInfoId: params.knowledgeInfoId, // 知识库id
      integrityScore: params.completeRate, // 完整性评分
      replicationScore: params.copyRate, // 复制性评分
      appliedScore: params.applyRate, // 应用性评分
      commentsName: params.commentsName, // 评分人（匿名评分时，该值默认传 匿名，否则不传值，后端自动记录当前操作用户）
      klKnowledgeEvaluations: [
        // 评价内容为空，则不传该节点
        {
          evaMessage: params.evaMessage, // 评价内容
          commentsName: params.commentsName, // 评价人 （匿名评分时，该值默认传 匿名，否则不传值，后端自动记录当前操作用户）
        },
      ],
    },
  }).then(res => {
    if (res.success === true) {
      message.success('评价成功！');
      return true;
    }
    // message.error('评价失败');
    return false;
  });
}

/**
 * 提交评价（针对某条评分的子评价）
 * @returns
 */
export function sendInnerCommentScore(params) {
  return request('portal/KlKnowledgeEvaluationController/addKnowledgeEvaluation.do', {
    method: 'post',
    data: {
      knowledgeScoreId: params.knowledgeScoreId, // 知识库id
      evaMessage: params.evaMessage, // 回复内容
      commentsName: params.commentsName, // 评分人（匿名评分时，该值默认传 匿名，否则不传值，后端自动记录当前操作用户）
      relaId: params.relaId, // 对应当前回复的评价的ID（父评价ID）
    },
  }).then(res => {
    if (res.success === true) {
      message.success('评价成功！');
      return true;
    }
    // message.error('评价失败');
    return false;
  });
}

/**
 * 知识库评分列表查询
 * @returns
 */
export function qryKlKnowledgeScoreList(params) {
  const { knowledgeInfoId } = params;
  return request('portal/KlKnowledgeScoreController/qryKlKnowledgeScoreGrid.do', {
    method: 'post',
    data: {
      knowledgeInfoId, // 知识库id
      pageNum: 1,
      pageSize: 1000,
    },
  }).then(res => {
    if (res && Array.isArray(res.list)) {
      return res.list;
    }
    return [];
  });
}

/**
 * 获取个人统计数据
 */
export async function getUserStatisticsInfo() {
  return request('portal/KlUserIntegralController/getUserStatisticsInfo.do', {
    method: 'get',
  }).then(res => res?.resultObject);
}

/**
 * 知识库热门资料列表查询
 * @returns
 */
export function qryKlKnowledgeInfoGrid(params) {
  const { knowledgeClassType, pageSize, knowledgeType = '', industryType = '' } = params;
  return request('portal/KlKnowledgeInfoController/qryKlKnowledgeInfoForUser.do', {
    method: 'post',
    data: {
      knowledgeClassType,
      knowledgeType,
      industryType,
      pageNum: 1,
      pageSize,
      sortName: 'browseCount',
      sortOrder: 'DESC',
    },
  }).then(res => {
    if (res && Array.isArray(res.list)) {
      return res.list;
    }
    return [];
  });
}

/**
 * 案例库：行业热点查询
 * @returns
 */
export function getKnowledgeIndustryOrder(params) {
  const { knowledgeClassType } = params;
  return request('portal/KlKnowledgeInfoController/getKnowledgeIndustryOrder.do', {
    method: 'post',
    data: {
      knowledgeClassType,
      pageNum: 1,
      pageSize: 4,
    },
  }).then(res => {
    if (res && Array.isArray(res.list)) {
      return res.list;
    }
    return [];
  });
}

/**
 * 浏览资料详情页面5s后发送请求增加积分
 * @returns
 */
export function addKlKnowledgeAction(params) {
  const { knowledgeInfoId } = params;
  return request('portal/KlKnowledgeActionController/addKlKnowledgeAction.do', {
    method: 'post',
    data: {
      knowledgeInfoId,
      actionType: '1100',
    },
  });
}

/**
 * 积分管理-积分服务查询
 * @returns
 */
export function qryKlKnowledgeActionGrid(params) {
  const { createTime, filters, sorter, ...rest } = params;
  const dateRange = {};
  if (Array.isArray(params.shelfTime) && params.shelfTime.length === 2) {
    dateRange.startShelfTime = params.shelfTime[0].format('YYYY-MM-DD HH:mm:ss');
    dateRange.endShelfTime = params.shelfTime[1].format('YYYY-MM-DD HH:mm:ss');
  }

  if (Array.isArray(params.createTime) && params.createTime.length === 2) {
    dateRange.startCreateDate = params.createTime[0].format('YYYY-MM-DD HH:mm:ss');
    dateRange.endCreateDate = params.createTime[1].format('YYYY-MM-DD HH:mm:ss');
  }

  return request('portal/KlKnowledgeActionController/qryKlKnowledgeActionGrid.do', {
    method: 'post',
    data: {
      ...rest,
      ...dateRange,
      pageNum: params.current,
    },
  }).then(res => {
    if (Array.isArray(res?.list)) {
      return {
        total: res?.total,
        data: res?.list,
      };
    }
    return [];
  });
}

/**
 * 获取集团四库链接
 * @returns
 */
export function getDictSsoUrl() {
  return request('portal/DictSsoLoginController/getDictSsoUrl.do', {
    method: 'post',
  });
}
