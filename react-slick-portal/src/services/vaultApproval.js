import request from '@/utils/request';

// 查询是否需要金库
export function queryIsVault(params) {
  return request('portal/GoldBankController/initParam.do', {
    data: params,
  });
}

// 创建金库审批
export function createVault(params) {
  return request('portal/GoldBankController/createAuthEvent.do', {
    data: params,
  });
}

// 金库认证
export function vaultAuthentication(params) {
  return request('portal/GoldBankController/authEvent.do', {
    data: params,
  });
}

// 刷新金库申请状态
export function refreshVaultValue(params) {
  return request('portal/GoldBankController/queryJKStatusByID.do', {
    data: params,
  });
}

// 金库短信重发
export function resendMessage(params) {
  return request('portal/GoldBankController/queryJKStatusByID.do', {
    data: params,
  });
}
