/* 红名单信息填写 */
import React, { useCallback, useState } from 'react';
import { But<PERSON>, Card, Col, DatePicker, Form, message, Row, Spin } from 'antd';
import moment from 'moment/moment';
import style from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/index.less';
import { disableEndDate, disableStartDate } from '@/utils/validator';
import { addRedListInfo, queryEcBadDebtAndOverdue } from '@/pages/GroupCust/GroupCustRedListManage/services';
import AuditModal from '@/pages/Approval/components/AuditModal';

// 常量配置
const RETRY_CONFIG = {
  MAX_RETRIES: 5,
  BASE_DELAY: 1000, // 1秒
  MAX_DELAY: 8000, // 最大8秒
};

const MESSAGES = {
  NO_GROUP_SELECTED: '请先选择需要添加红名单的集团，再进行提交！',
  NO_DEBT_DATA: '查询不到集团逾期欠费金额，无法提交！',
  SUBMIT_SUCCESS: '提交成功',
};

// 样式对象
const styles = {
  container: {
    maxWidth: '400px',
    margin: '50px auto',
    padding: '20px',
    border: '1px solid #ccc',
    borderRadius: '8px',
    textAlign: 'center',
  },
  formGroup: {
    marginBottom: '20px',
  },
  submitButton: {
    padding: '10px 20px',
    fontSize: '16px',
    cursor: 'pointer',
    backgroundColor: '#4CAF50',
    color: '#fff',
    border: 'none',
    borderRadius: '4px',
  },
  overlay: {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },
  popup: {
    backgroundColor: '#fff',
    padding: '20px',
    borderRadius: '8px',
    textAlign: 'center',
    width: '300px',
  },
  popupMessage: {
    marginBottom: '20px',
    fontSize: '16px',
    color: '#333',
  },
};

const RedList = props => {
  const { form, baseInfo, FatherProps } = props;
  const { getFieldDecorator } = form;
  const [isPopupVisible, setPopupVisible] = useState(false);
  const [popupMessage, setPopupMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [auditRecord, setAuditRecord] = useState('');

  // 计算重试延迟时间
  const getRetryDelay = useCallback(retryCount => Math.min(RETRY_CONFIG.BASE_DELAY * (2 * retryCount), RETRY_CONFIG.MAX_DELAY), []);

  // 显示弹窗消息
  const showPopup = useCallback(message1 => {
    setPopupMessage(message1);
    setPopupVisible(true);
  }, []);

  // 查询欠费金额的重试函数
  const queryEcBadDebtAndOverdueWithRetry = useCallback(
    async (retryCount = 0) => {
      try {
        const { resultObject } = await queryEcBadDebtAndOverdue({ groupId: baseInfo.GROUP_ID });

        if (resultObject?.rspParam?.busiInfo?.ecBadDebtResultCode === '3') {
          return resultObject?.rspParam?.busiInfo?.overdueBillFee;
        }

        if (retryCount < RETRY_CONFIG.MAX_RETRIES) {
          await new Promise(resolve => setTimeout(resolve, getRetryDelay(retryCount)));
          return queryEcBadDebtAndOverdueWithRetry(retryCount + 1);
        }

        return undefined;
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Query debt amount error:', error);
        if (retryCount < RETRY_CONFIG.MAX_RETRIES) {
          await new Promise(resolve => setTimeout(resolve, getRetryDelay(retryCount)));
          return queryEcBadDebtAndOverdueWithRetry(retryCount + 1);
        }
        throw error;
      }
    },
    [baseInfo.GROUP_ID, getRetryDelay]
  );

  // 提交表单
  const submit = useCallback(async () => {
    try {
      setLoading(true);

      // 表单验证
      const values = await new Promise((resolve, reject) => {
        form.validateFields((err, formValues) => {
          if (err) reject(err);
          resolve(formValues);
        });
      });

      // 检查是否选择集团
      if (!baseInfo.GROUP_ID) {
        showPopup(MESSAGES.NO_GROUP_SELECTED);
        return;
      }

      // 查询欠费金额
      const amountOwed = await queryEcBadDebtAndOverdueWithRetry();
      if (!amountOwed) {
        message.error(MESSAGES.NO_DEBT_DATA);
        return;
      }

      // 提交红名单信息
      const result = await addRedListInfo({
        groupId: baseInfo.GROUP_ID,
        groupName: baseInfo.GROUP_NAME,
        orgaEnterpriseId: baseInfo.ORGA_ENTERPRISE_ID,
        createDate: moment(values.createDate).format('YYYY-MM-DD'),
        expireDate: moment(values.expireDate).format('YYYY-MM-DD'),
        amountOwed,
      });

      if (result.resultCode === 'TRUE') {
        message.success(MESSAGES.SUBMIT_SUCCESS);
        FatherProps.goToStep(1);
      } else if (result?.resultCode === '110' || result?.resultCode === '180') {
        setApprovalModalVisible(true); // 显示弹窗
        setAuditRecord(result?.resultObject);
      } else {
        message.error(result.resultMsg);
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Submit error:', error);
      message.error(error.message || '提交失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [baseInfo, form, FatherProps, queryEcBadDebtAndOverdueWithRetry, showPopup]);

  // 关闭弹窗
  const closePopup = useCallback(() => {
    setPopupVisible(false);
    setPopupMessage('');
  }, []);

  // 返回上一步
  const goBack = useCallback(() => {
    FatherProps.goToStep(1);
  }, [FatherProps]);

  const currentDate = moment().format('YYYY-MM-DD');

  const approvalModalVisibleChange = value => {
    setApprovalModalVisible(value);
  };

  return (
    <Spin spinning={loading}>
      <Card title="红名单审核信息" className="cute">
        <div className={style.content}>
          <Form className="flow fix-label">
            <Row gutter={24} type="flex" justify="start">
              <Col span={6} style={{ marginRight: '20px' }}>
                <Form.Item label="生效时间">
                  {getFieldDecorator('createDate', {
                    initialValue: moment(currentDate),
                    rules: [
                      {
                        required: true,
                        message: '请选择生效时间!',
                      },
                    ],
                  })(
                    <DatePicker
                      format="YYYY-MM-DD"
                      allowClear={false}
                      disabled
                      disabledDate={disableStartDate(form.getFieldValue('expireDate'))}
                      style={{ width: '100%' }}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="失效时间">
                  {getFieldDecorator('expireDate', {
                    rules: [
                      {
                        required: true,
                        message: '请选择失效时间!',
                      },
                      {
                        validator: (_, value, callback) => {
                          const createDate = form.getFieldValue('createDate');
                          if (createDate && value) {
                            const monthsDiff = value.diff(createDate, 'months');
                            if (monthsDiff > 12) {
                              callback('失效时间不能超过生效时间12个月');
                            } else {
                              callback();
                            }
                          } else {
                            callback();
                          }
                        },
                      },
                    ],
                  })(
                    <DatePicker
                      format="YYYY-MM-DD"
                      allowClear
                      disabledDate={disableEndDate(form.getFieldValue('createDate'))}
                      style={{ width: '100%' }}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12} className="text-right">
                <Button type="primary" className={style.buttonStyle1} onClick={submit}>
                  提交
                </Button>
                {isPopupVisible && (
                  <div style={styles.overlay}>
                    <div style={styles.popup}>
                      <p style={styles.popupMessage}>{popupMessage}</p>
                      <Button type="primary" onClick={closePopup}>
                        确定
                      </Button>
                    </div>
                  </div>
                )}
              </Col>
              <Col span={12}>
                <Button onClick={goBack}>取消</Button>
              </Col>
            </Row>
          </Form>
        </div>
      </Card>
      <AuditModal approvalModalVisible={approvalModalVisible} approvalModalVisibleChange={approvalModalVisibleChange} auditRecord={auditRecord} />
    </Spin>
  );
};

export default Form.create()(RedList);
