/* 标准地址 */
import React, { useCallback, useEffect, useState } from 'react';
import { Card, Col, Form, Input, message, Row, Select } from 'antd';
import { connect } from 'dva';
import styles from '@/pages/GroupCust/GroupCustManage/components/Edit/index.less';
import { queryStandardAddress } from '@/pages/GroupCust/GroupCustManage/services';
import AsyncCascader from '@/components/AsyncCascader';
import MultiInputComponent from '@/components/MultiInputComponent';
import NineAddress from '@/components/NineAddress';

const FormItem = Form.Item;
const formItemLayout = {
  // labelAlign: 'left',
  // labelCol: {
  //   xs: { span: 24 },
  //   // sm: { span: 4 },
  // },
  // wrapperCol: {
  //   xs: { span: 24 },
  //   sm: { span: 16 },
  // },
};
const StandardAddr = props => {
  const { form, editable, formData, onValuesChange, dispatch, viewMode } = props;
  const { getFieldDecorator } = form;
  // 所属地市
  const [districts, setDistricts] = useState([]);
  // 区县
  const [counties, setCounties] = useState([]);
  // 镇
  const [towns, setTowns] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [openCard, setOpenCard] = useState(true); // 是否展开卡片
  // const showAddress = () => {};
  // const renderExtraContent = useMemo(() => {
  //   if (editable) {
  //     return (
  //       <>
  //         {/* <a onClick={showAddress} style={{ marginRight: '10px' }}>点击设置集团标准地址</a> */}
  //         {openCard ? <a onClick={() => setOpenCard(false)}>隐藏</a> : <a onClick={() => setOpenCard(true)}>展开</a>}
  //       </>
  //     );
  //   }
  //   return openCard ? <a onClick={() => setOpenCard(false)}>隐藏</a> : <a onClick={() => setOpenCard(true)}>展开</a>;
  // }, [editable, openCard]);

  // 初始化地市
  const getRegions = () => {
    queryStandardAddress({
      upAddress: '内蒙',
      upCol: 'PROVINCE_NAME',
      lowCol: 'CITY_NAME',
    }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        setDistricts(resultObject?.rspParam?.busiInfo?.outData);
      } else {
        setDistricts([]);
        message.warn(resultMsg);
      }
    });
  };
  // 切换地市,查询区县
  const handleChangeRegion = val => {
    queryStandardAddress({
      upAddress: val,
      upCol: 'CITY_NAME',
      lowCol: 'AREA_NAME',
    }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        setCounties(resultObject?.rspParam?.busiInfo?.outData);
        const params = {
          OSS_AREA_NAME: undefined,
          OSS_COUNTY_NAME: undefined,
        };
        form.setFieldsValue(params);
      } else {
        setCounties([]);
        message.warn(resultMsg);
      }
    });
  };
  // 切换区县
  const handleChangeCounty = val => {
    queryStandardAddress({
      upAddress: val,
      upCol: 'AREA_NAME',
      lowCol: 'COUNTY_NAME',
    }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        const params = {
          OSS_COUNTY_NAME: undefined,
        };
        form.setFieldsValue(params);
        setTowns(resultObject?.rspParam?.busiInfo?.outData);
      } else {
        setTowns([]);
        message.warn(resultMsg);
      }
    });
  };
  useEffect(() => {
    getRegions();
  }, []);
  const updateFormField = useCallback(
    data => {
      if (!data.field) return;
      form.setFieldsValue({
        [data.field]: data.value,
      });
    },
    [form]
  );
  useEffect(() => {
    dispatch({
      type: 'AddGroupCust/registerMethods',
      payload: {
        componentName: 'StandardAddr',
        methods: { updateFormField },
      },
    });

    return () => {
      dispatch({
        type: 'AddGroupCust/unregisterMethods',
        payload: { componentName: 'StandardAddr' },
      });
    };
  }, [dispatch]);

  // 查询区域信息
  const getLocation = (selected = []) => {
    const level = selected.length; // 当前层级
    let service;
    if (level === 0) {
      const params = {
        upAddress: '内蒙',
        upCol: 'PROVINCE_NAME',
        lowCol: 'CITY_NAME',
      };
      service = queryStandardAddress(params);
    }
    if (level === 1) {
      // （第2级或第3级）
      const areaCode = selected[selected.length - 1].value;
      const params = {
        upAddress: areaCode,
        upCol: 'CITY_NAME',
        lowCol: 'AREA_NAME',
      };
      service = queryStandardAddress(params);
    }
    if (level === 2) {
      const areaCode = selected[selected.length - 1].value;
      const params = {
        upAddress: areaCode,
        upCol: 'AREA_NAME',
        lowCol: 'COUNTY_NAME',
      };
      service = queryStandardAddress(params);
    }

    return service
      .then(res => {
        const { resultCode, resultObject, resultMsg } = res;
        if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
          return (resultObject?.rspParam?.busiInfo?.outData.map(item => ({
            label: item.ADDRESS,
            value: item.ADDRESS,
            isLeaf: level === 2, // 第三级是叶子节点
          })) || []);
        }
        message.error(resultMsg || '获取省份数据失败');
        return [];
      })
      .catch(() => {
        // console.error(err); // 可选：打印错误以便调试
        message.error('获取省份数据失败');
        return [];
      });
  };

  const handleSelect = selected => {
    if (!selected || selected.length === 0) return;
    const [row] = selected;
    // console.log('选择的九级地址信息', row);
    const addressInfo = {
      OSS_VILLAGE_NAME: row.road, // 道路
      OSS_DISTRICT_NAME: row.villages, // 所属小区
      OSS_BUILD_NAME: row.building, // 所属楼栋号
      OSS_FLOOR_NAME: row.floorNo, // 房间
      OSS_ADDRESS_ID: row.addressId, // 九级地址编号
      OSS_ADDR_COVER_STATE: row.coverState, // 九级地址资源覆盖程度
      fourthAddress: [row.road, row.villages, row.building, row.floorNo],
    };
    form.setFieldsValue(addressInfo);
  };
  return (
    <Card
      className="cute"
      title="标准信息"
      bodyStyle={{ padding: '0' }}
      // extra={renderExtraContent}
      headStyle={{ background: '#E7F2FB', padding: '0 20px' }}
    >
      {openCard && (
        <div className={styles.warp}>
          <Form className="flow2 fix-label" onValuesChange={onValuesChange}>
            <Row>
              <Col span={8} hidden>
                <FormItem label="所属省份" {...formItemLayout}>
                  {getFieldDecorator('OSS_PROVINCE_NAME', {
                    initialValue: formData.OSS_PROVINCE_NAME || '内蒙古',
                  })(<Input className={styles.inputStyle} placeholder="请输入" disabled={viewMode === 'view'} />)}
                </FormItem>
              </Col>
              {/* 地市：OSS_CITY_NAME 区县：OSS_AREA_NAME 所属街道 OSS_COUNTY_NAME */}
              <Col span={8}>
                <FormItem label="所属市区街" {...formItemLayout}>
                  {getFieldDecorator('OSS_CITY_NAME_INFO',
                    { initialValue: [] }
                  )(
                    <AsyncCascader
                      title="归属区域"
                      loadData={getLocation}
                      placeholder="请选择所属市区街"
                      displayRender={labels => labels.join('-')}
                      className={styles.inputStyle}
                      disabledAll={viewMode === 'view'}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="道路/小区/楼号/房号" {...formItemLayout}>
                  {getFieldDecorator('fourthAddress', {
                    initialValue: [],
                  })(
                    <MultiInputComponent fields={[{ label: '道路', field: 'road' }, { label: '小区', field: 'building' }, { label: '楼号', field: 'unit' }, { label: '房号', field: 'room' }]} disabled={viewMode === 'view'} />
                  )}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="九级地址" {...formItemLayout}>
                  {getFieldDecorator('NineAddress', {
                    // initialValue: selectedManager,
                  })(
                    <NineAddress
                      estroyPopupOnHide
                      pick="radio"
                      areaInfo={form.getFieldValue('OSS_CITY_NAME_INFO')}
                      label="addressName"
                      popupStyle={{ width: 600 }}
                      rowKey="addressName"
                      columns={[
                        {
                          title: '详细地址',
                          align: 'left',
                          dataIndex: 'addressName',
                          ellipsis: true,
                        },
                      ]}
                      onConfirm={e => handleSelect(e)}
                      custName={form.getFieldValue('NineAddress')}
                      disabled={viewMode === 'view'}
                    />
                  )}
                </FormItem>

              </Col>
              <Col span={8} hidden>
                <FormItem label="所属地市" {...formItemLayout}>
                  {getFieldDecorator('OSS_CITY_NAME')(
                    <Select placeholder="请选择" onChange={handleChangeRegion} className={styles.inputStyle} disabled={viewMode === 'view'}>
                      {districts?.map(e => (
                        <Select.Option value={e.ADDRESS} key={e.ADDRESS}>
                          {e.ADDRESS}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              </Col>
              <Col span={8} hidden>
                <FormItem label="所属区县" {...formItemLayout}>
                  {getFieldDecorator('OSS_AREA_NAME')(
                    <Select placeholder="请选择" onChange={handleChangeCounty} className={styles.inputStyle} disabled={viewMode === 'view'}>
                      {counties?.map(e => (
                        <Select.Option value={e.ADDRESS} key={e.ADDRESS}>
                          {e.ADDRESS}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={8} hidden>
                <FormItem label="所属街道" {...formItemLayout}>
                  {getFieldDecorator('OSS_COUNTY_NAME')(
                    <Select placeholder="请选择" disabled={viewMode === 'view' || !editable} className={styles.inputStyle}>
                      {towns?.map(e => (
                        <Select.Option value={e.ADDRESS} key={e.ADDRESS}>
                          {e.ADDRESS}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              </Col>
              <Col span={8} hidden>
                <FormItem label="所属道路" {...formItemLayout}>
                  {getFieldDecorator('OSS_VILLAGE_NAME', {})(<Input className={styles.inputStyle} placeholder="请输入" disabled={viewMode === 'view' || !editable} />)}
                </FormItem>
              </Col>
              <Col span={8} hidden>
                <FormItem label="所属小区" {...formItemLayout}>
                  {getFieldDecorator('OSS_DISTRICT_NAME', {})(<Input className={styles.inputStyle} placeholder="请输入" disabled={viewMode === 'view' || !editable} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={8} hidden>
                <FormItem label="所属楼栋号" {...formItemLayout}>
                  {getFieldDecorator('OSS_BUILD_NAME', {})(<Input className={styles.inputStyle} placeholder="请输入" disabled={viewMode === 'view' || !editable} />)}
                </FormItem>
              </Col>
              <Col span={8} hidden>
                <FormItem label="所属房号" {...formItemLayout}>
                  {getFieldDecorator('OSS_FLOOR_NAME', {})(<Input className={styles.inputStyle} placeholder="请输入" disabled={viewMode === 'view' || !editable} />)}
                </FormItem>
              </Col>
              <Col span={8} hidden>
                <FormItem label="九级地址" {...formItemLayout}>
                  {getFieldDecorator('OSS_NINE_ADDR_NAME', {})(<Input className={styles.inputStyle} placeholder="请输入" disabled={viewMode === 'view' || !editable} />)}
                </FormItem>
              </Col>
              <Col span={8} hidden>
                <FormItem label="九级地址编号" {...formItemLayout}>
                  {getFieldDecorator('OSS_ADDRESS_ID', {})(<Input className={styles.inputStyle} placeholder="请输入" disabled={viewMode === 'view' || !editable} />)}
                </FormItem>
              </Col>
              <Col span={8} hidden>
                <FormItem label="九级地址资源覆盖程度" {...formItemLayout}>
                  {getFieldDecorator('OSS_ADDR_COVER_STATE' || '', {})(<Input className={styles.inputStyle} placeholder="请输入" disabled={viewMode === 'view' || !editable} />)}
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
      )}
    </Card>
  );
};

export default connect(({ AddGroupCust }) => ({
  fieldStates: AddGroupCust.currentFormFieldState,
  formData: AddGroupCust.currentForm,
}))(Form.create()(StandardAddr));
