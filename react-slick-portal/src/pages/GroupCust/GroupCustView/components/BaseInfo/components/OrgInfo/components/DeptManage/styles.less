.deptManage {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .deptManageLeft {
    flex: 0 0 300px;
    min-width: 250px;
    max-width: 300px;
    height: 100%;
    overflow: hidden;

    :global {
      .ant-card {
        height: 100%;
      }
      
      .ant-card .ant-card-body {
        padding: 0px;
        overflow: auto;
        height: calc(100% - 57px);
      }
    }
  }

  .deptManageRight {
    flex: 1;
    height: 100%;
    overflow: auto;
    padding: 0 16px;
    display: flex;
    flex-direction: column;

    .deptMember {
      flex: 1;
      overflow: hidden;
      
      :global {
        .ant-card {
          height: 100%;
        }
        
        .ant-card .ant-card-body {
          padding: 0px;
          overflow: auto;
          height: calc(100% - 57px);
        }
      }

      .components {
        padding: 16px 0px;
      }
    }

    :global {
      .ant-card {
        overflow: hidden;
        margin-bottom: 16px;
      }
      
      .ant-table-wrapper {
        height: 100%;
      }
      
      .ant-table-scroll {
        overflow: auto;
      }
      
      .ant-table-header {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
        overflow: hidden !important;
      }
      
      .ant-table-body {
        overflow-x: auto !important;
        overflow-y: auto !important;
      }
    }
  }
}

.system_menu {
  margin: 0px 16px;


  .system_menu_tree {
    .system_menu_tree_edit {
      // display: none;
      padding-left: 8px;
      visibility: hidden;

      .icon {
        padding-right: 8px;
      }

      .icon:hover {
        color: #1890ff;
      }
    }
  }

  :global {
    .ant-tree li .ant-tree-node-content-wrapper:hover .ant-tree-title > span > span:nth-child(2) {
      // display: inline-block;
      visibility: visible;
    }
  }
}

.icon_hover:hover {
  color: #1890ff;
}
