import request from '@/utils/request';

// 历史订单列表查询
export function getOrderHistoryList(data) {
  return request('portal/CrmGroupOrderController/qry360HistoryOrder.do', {
    method: 'POST',
    data,
  });
}

// 撤单 cancelSdWanOrder
export function cancelSdWanOrder(data) {
  return request('portal/ContractController/qryContractByCustId.do', {
    method: 'POST',
    data,
  });
}

// 历史预受理列表
export function getUnfinishPreOrderList(data) {
  return request('portal/GroupOrderController/queryOrderFromOrderCenter.do', {
    method: 'POST',
    data,
  });
}
