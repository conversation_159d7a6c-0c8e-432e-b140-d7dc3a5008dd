import React from 'react';
import IconBase from '../IconBase';

// 首页图标
export const HomeIcon = (props) => (
  <IconBase viewBox="0 0 24 24" {...props}>
    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
  </IconBase>
);

// 返回图标
export const BackIcon = (props) => (
  <IconBase viewBox="0 0 24 24" {...props}>
    <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
  </IconBase>
);

// 前进图标
export const ForwardIcon = (props) => (
  <IconBase viewBox="0 0 24 24" {...props}>
    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
  </IconBase>
);

// 向上图标
export const UpIcon = (props) => (
  <IconBase viewBox="0 0 24 24" {...props}>
    <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
  </IconBase>
);

// 向下图标
export const DownIcon = (props) => (
  <IconBase viewBox="0 0 24 24" {...props}>
    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
  </IconBase>
);

// 向左图标
export const LeftIcon = (props) => (
  <IconBase viewBox="0 0 24 24" {...props}>
    <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
  </IconBase>
);

// 向右图标
export const RightIcon = (props) => (
  <IconBase viewBox="0 0 24 24" {...props}>
    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
  </IconBase>
);

// 菜单图标
export const MenuIcon = (props) => (
  <IconBase viewBox="0 0 24 24" {...props}>
    <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
  </IconBase>
);

// 展开图标
export const ExpandIcon = (props) => (
  <IconBase viewBox="0 0 24 24" {...props}>
    <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"/>
  </IconBase>
);

// 收起图标
export const CollapseIcon = (props) => (
  <IconBase viewBox="0 0 24 24" {...props}>
    <path d="M12 8l-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z"/>
  </IconBase>
);
