.bannerList {
  position: relative;
  width: 100%;
  margin-bottom: 11px;
  padding-top: 11px;

  .bulletin{
    height: 34px;
    padding: 6px 12px;
    background: #FFF7E7;
    border-radius: 2px;
  }

  .bulletinTitle{
    img{
      margin-top: -4px;
      padding-right: 6px;
    }
    span{
      color: #FAAF0C;
      font-weight: 400;
      font-size: 14px;
    }
  }

  .bulletinContent{
    margin-left: -30px;
    span{
      color: #555864;
      font-weight: 400;
      font-size: 14px !important;
      font-family: PingFangSC-Regular, PingFang SC, sans-serif;
      line-height: 23px;
      background: #FFF7E7 !important;
    }
  }
  .bulletinMore{
    right: -86px;
    text-align: right;
    span{
      color: #8F9198;
      font-weight: 400;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC, sans-serif;
      line-height: 22px;
      img{
        margin-top: -2px;
      }
    }
  }

  .conTitle {
    font-weight: 500;
    font-size: 24px;
  }

  .bannerBottom,
  .descList {
    max-width: 1200px;
    height: 184px;
    margin: 0 auto;
    padding-left: 0;
    border-radius: 4px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.07);
  }

  .descList {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;

    .line {
      display: block;
      width: 1px;
      height: 128px;
      background-color: #eee;
    }

    li {
      display: flex;
      flex: 1;
      flex-direction: row;
      justify-content: flex-start;
      height: 100%;
      padding: 26px 58px 34px 37px;
      overflow: hidden;
      text-align: justify;

      div.descCon {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        max-width: 299px;
      }

      span {
        color: #333;
      }

      .title {
        display: -webkit-box;
        width: 100%;
        font-weight: 400;
        font-size: 24px;
        text-overflow: ellipsis;

        /* ! autoprefixer: off */
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      }

      .desc {
        display: inline-block;
        display: -webkit-box;
        width: 100%;
        padding-top: 12px;
        overflow: hidden;
        color: #999;
        font-size: 18px;
        text-overflow: ellipsis;

        /* ! autoprefixer: off */
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
      }

      div.imgCon {
        width: 60px;
        padding: 5px;
        overflow: hidden;

        img {
          width: 100%;
        }
      }
    }
  }

  .showMore {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 50px 0 58px;
    color: #999;
    font-size: 14px;

    a {
      color: #999;
    }
  }

  :global(.slick-dots) {
    bottom: 28%;
    left: 10%;
    width: 200px;

    > li {
      float: left;
      width: 36px;
      background: #e7e7e7;

      button {
        width: 36px;
        background: #e7e7e7;
      }
    }

    :global(li.slick-active) {
      width: 36px;
      background: #3181f0;

      button {
        width: 36px;
        background: #3181f0;
      }
    }
  }
}
