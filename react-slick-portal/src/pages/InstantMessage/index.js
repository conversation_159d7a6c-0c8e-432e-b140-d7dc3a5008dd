import React, { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Modal, Spin, Icon, Switch, message } from 'antd';
import UserList from './UserList';
import ChatWindow from './ChatWindow';
import ChatRecords from './ChatRecords';
import styles from './index.less';
import { getItem } from '@/utils/utils';

import { updateUserStatus, queryUserStatus } from '@/services/chat';

const InstantMessage = props => {
  const { size: { height, width }, login, dispatch } = props;
  const { visible, messageLoading, historyVisible } = props.message;
  const { isOSSPage } = login;

  const [refresh, setRefresh] = useState(false);

  const closeModal = () => {
    dispatch({ type: 'message/changeVisible', payload: false });

    // 向即时通讯发送窗口关闭消息
    const imchatIframe = document.getElementById('iframe_imchat').contentWindow;
    imchatIframe.postMessage(
      {
        type: 'closeYXJModal',
      },
      '*',
    );
    console.log('send closeYXJModal PostMessage');
  };

  const refreshImchat = () => {
    setRefresh(true);
    document.getElementById('iframe_imchat').contentWindow.location.reload();
    setTimeout(() => {
      setRefresh(false);
    }, 2000);
  };

  const handleChatInitExp = (originData = {}) => {
    const mainData = originData.data || {};
    if (!mainData) {
      return;
    }
    const { type, data = {} } = mainData;

    // 监听打包消息
    if (type === 'initExp') {
      const { initExp = '', modalType } = data;

      if (!initExp || !modalType) {
        return;
      }
      dispatch({ type: 'librarys/receivePackMessage', payload: data });

      // 打包案例，拉起经验库新增页面
      if (modalType === 'casePack') {
        setTimeout(() => {
          // 设置消息内容
          dispatch({ type: 'librarys/handleOpenAdd', payload: { messageData: initExp } });
        }, 500);
      }
      console.log('receive openExperienceAdd  PostMessage， data：', data);
    }

    // 监听即时通讯未读消息数
    if (type === 'unreadTotal') {
      const { total = 0 } = data;
      dispatch({ type: 'notice/setChatNumber', payload: total });
      console.log('receive unreadTotal  PostMessage， data：', data);
    }
  };

  const [maximized, setMaximized] = useState(true);
  const [isOnline, setIsOnline] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const handleOnOffLineChange = async checked => {
    try {
      setIsLoading(true);
      const res = await updateUserStatus({
        userCode: getItem('user')?.userInfo?.userCode,
        userStatus: checked ? '1' : '0',
      });
      if (res.resultCode === '200') {
        setIsOnline(res.userStatus === '1');
      } else {
        message.error('更新用户状态失败');
      }
    } catch (error) {
      message.error('更新用户状态出错，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  const getUserStatus = async () => {
    const res = await queryUserStatus({
      userCode: getItem('user')?.userInfo?.userCode,
    });
    if (res.resultCode === '200') {
      setIsOnline(res.userStatus === '1');
    }
  };

  useEffect(() => {
    getUserStatus();
    window.addEventListener('message', handleChatInitExp);
    return () => {
      window.removeEventListener('message', handleChatInitExp);
    };
  }, []);

  return (
    <Spin spinning={messageLoading}>
      <Modal
        title="云小集"
        visible={visible}
        onCancel={closeModal}
        forceRender={login.isOSSPage}
        zIndex="1010"
        centered
        footer={null}
        bodyStyle={{ padding: '0', height }}
        width={width}
        wrapClassName={!isOSSPage ? styles.messageModal : styles.OSSPageModal}
      >
        {
          isOSSPage ? (
            <>
              <div
                className={styles.OSSPageModalHeader}
                style={{ position: 'absolute', top: '18px', right: '60px', fontSize: '14px' }}
              >
                <Switch
                  checkedChildren="在线"
                  unCheckedChildren="离线"
                  checked={isOnline}
                  loading={isLoading}
                  style={{ marginRight: '16px' }}
                  onChange={checked => handleOnOffLineChange(checked)}
                />
                <Icon
                  type="fullscreen-exit"
                  onClick={() => {
                    setMaximized(false);
                    closeModal();
                  }}
                  style={{ marginRight: '16px', cursor: 'pointer' }}
                />
                {
                  refresh ? <Icon type="loading" /> : <Icon onClick={refreshImchat} type="reload" />
                }
              </div>
              <iframe
                id="iframe_imchat"
                title="云小集"
                frameBorder="no"
                border="0"
                width="100%"
                height="100%"
                src="/littleSupport/?reactName=littleSupport&routerName=Home&needLayout=false&initExpType=outer"
              />
            </>
          ) : (
            <div style={{ display: 'flex', alignItems: 'center' }} id="messageModal">
              <UserList />
              <ChatWindow />
              {historyVisible && <ChatRecords />}
            </div>
          )
        }
      </Modal>
      {!visible && !maximized && (
        <div className={styles.minContainer}>
          <div className={styles.minContent}>
            <div className={styles.title}>云小集</div>
            <div>
              <Icon
                onClick={() => {
                  setMaximized(true);
                  dispatch({ type: 'message/changeVisible', payload: true });
                }}
                type="fullscreen"
                style={{ marginRight: '12px' }}
              />
              <Icon
                onClick={() => {
                  setMaximized(true);
                  closeModal();
                }}
                type="close"
              />
            </div>
          </div>
        </div>
      )}
    </Spin>
  );
};
export default connect(({ setting, message, login }) => ({
  size: setting.size,
  login,
  message,
}))(InstantMessage);
