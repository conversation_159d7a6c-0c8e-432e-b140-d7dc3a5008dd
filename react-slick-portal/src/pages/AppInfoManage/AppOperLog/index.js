import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Select,
  Icon,
  Input,
  Button,
  Tag,
  Modal,
  Divider,
  Skeleton,
  DatePicker,
} from 'antd';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
import request from '@/utils/request';
import AreaSelect from '@/components/AreaSelect';
import OperTab from './components/Tab';
const { Option } = Select;
const { RangePicker } = DatePicker;
import ComboGrid from '@/components/ComboGrid';
import moment from 'moment';

const namespace = 'appOperLog';

function appOperLog(props) {
  const { getFieldDecorator } = props.form;
  const [loading, setLoading] = useState(false);
  const [valuesList, setValuesList] = useState([]);

  // 通过dva存当前需要传递的数据
  const saveParamsObj = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveParamsObj`,
      payload: params,
    });
  };

  useEffect(() => {
    setLoading(true);
    request('portal/DomainDataController/getValuesList.do', {
      data: { busiNbr: 'BaseEntity', propertyName: 'appPage' },
    }).then(res => {
      setLoading(false);
      res ? setValuesList(res) : null;
    });
  }, []);

  const submit = () => {
    props.form.validateFields((err, fieldsValue) => {
      if (err) return;
      const obj = {
        startDate: fieldsValue.feedbackType[0].format('YYYY-MM-DD'),
        endDate: fieldsValue.feedbackType[1].format('YYYY-MM-DD'),
        regionId: fieldsValue.regionId ? fieldsValue.regionId.commonRegionId : undefined,
        sysUserId: fieldsValue.sysUserId ? fieldsValue.sysUserId[0].sysUserId : undefined,
        visitPage: fieldsValue.visitPage,
        timeGroup: 'dd',
      };
      // 将查询的form 通过dva进行传递
      saveParamsObj(obj);
    });
  };

  const handleReset = () => {
    props.form.resetFields();
  };

  const disabledRangeTime = current => {
    return current > moment();
  };

  return (
    <Card
      title="日志分析报告"
      // style={{ minHeight: props.size.height }}
      bordered
      className="cute"
      extra={<span className="text-danger">注：默认展示近一个月统计结果</span>}
    >
      <Skeleton active loading={loading}>
        <Form className="flow fix-label">
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label="日期">
                {getFieldDecorator('feedbackType', {
                  rules: [
                    {
                      required: true,
                      message: '时间范围不能为空',
                    },
                  ],
                  initialValue: [moment().subtract(1, 'months'), moment()],
                })(<RangePicker style={{ width: '100%' }} disabledDate={disabledRangeTime} />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="归属区域">
                {getFieldDecorator('regionId')(<AreaSelect placeholder="请选择" />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="用户">
                {getFieldDecorator('sysUserId')(
                  <ComboGrid
                    url="orgauth/SystemUserController/selectUserGridData.do"
                    popupStyle={{ width: 560 }}
                    placeholder="请选择系统用户"
                    destroyPopupOnHide
                    searchPlaceholder="请输入名称进行搜索"
                    label="userName"
                    rowKey="sysUserId"
                    pick="radio"
                    params={{ filterCol: 'userName', stfExpStatus: '1100' }}
                    columns={[
                      {
                        title: '账号',
                        dataIndex: 'sysUserCode',
                      },
                      {
                        title: '名称',
                        dataIndex: 'userName',
                        ellipsis: true,
                      },
                      {
                        title: '所属区域等级',
                        dataIndex: 'userOrgLevel',
                        ellipsis: true,
                      },
                    ]}
                  />
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="访问页面">
                {getFieldDecorator('visitPage')(
                  <Select
                    showSearch
                    placeholder="请选择"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {/* <Option value="jack">Jack</Option> */}
                    {valuesList.length > 0
                      ? valuesList.map(item => {
                          return (
                            <Option value={item.value} key={item.value}>
                              {item.name}
                            </Option>
                          );
                        })
                      : null}
                  </Select>
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24} className="text-right">
              <Button type="primary" onClick={submit}>
                查询
              </Button>
              <Button className="margin-left" onClick={handleReset}>
                重置
              </Button>
            </Col>
          </Row>
        </Form>
      </Skeleton>
      <OperTab />
    </Card>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(appOperLog));
