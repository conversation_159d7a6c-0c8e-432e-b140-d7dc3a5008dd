import React from 'react';
import { Avatar } from 'antd';
import cls from 'classnames';
import styles from '../../index.less';

const MessageItem = ({ isSender, curUser, msgInfo = {} }) => {
  // 样式
  const textStyles = cls(styles['text-message-view'], {
    [styles.sender]: isSender,
    [styles.receiver]: !isSender,
  });

  const bubbleStyles = cls(styles.bubble, {
    [styles.sender]: isSender,
    [styles.receiver]: !isSender,
  });

  const messageTimeStyle = cls(styles['message-time'], {
    [styles.sender]: isSender,
    [styles.receiver]: !isSender,
  });

  const handleName = str => {
    if (str?.length > 2) {
      return str.substring(str.length - 2);
    }
      return str;
  };

  return (
    <div className={isSender ? styles.senderItem : styles.receiverItem}>
      {/* 流布局，发送者头像在文本右边代码在文本下面 */}
      {
        !isSender && (
          <div className={styles.avatarWrap}>
            <Avatar style={{ backgroundColor: '#2a97f7', verticalAlign: 'middle' }}>
              {handleName(curUser.objName)}
            </Avatar>
          </div>
        )
      }
      <div className={textStyles}>
        <div className={styles['text-message-wrapper']}>
          <div className={messageTimeStyle}>{msgInfo.createDate}</div>
          <div className={bubbleStyles}>
            <div className={styles['text-item']}>{msgInfo.messageText}</div>
          </div>
        </div>
      </div>
      {
        isSender && (
          <div className={styles.avatarWrap}>
            <Avatar style={{ backgroundColor: '#2a97f7', verticalAlign: 'middle' }}>
              {handleName(msgInfo.sendName)}
            </Avatar>
          </div>
        )
      }
    </div>
  );
};
export default MessageItem;
