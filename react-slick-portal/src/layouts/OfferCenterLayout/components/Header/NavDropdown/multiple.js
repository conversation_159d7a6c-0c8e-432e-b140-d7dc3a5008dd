import React, { useEffect, useState } from 'react';
import { Icon } from 'antd';
import { Link } from 'umi';
import style from './NavDropdown.module.less';

/**
 * 多级菜单组件
 * @param {props} props
 * @returns
 */
const MulitpleMenu = props => {
  const { list } = props;
  const [current, setCurrent] = useState(0);
  const [currentList, setCurrentList] = useState([]);

  useEffect(() => {
    setCurrentList(list[current] && (list[current].catalogItemList || []));
  }, [current, list]);

  const renderLinkList = offerList => {
    if (offerList.length > 0) {
      return offerList.map(offer => (
        <Link
          className={style.link}
          key={offer.offerId}
          replace
          to={`/offerCenter/detail/${offer.offerId}`}
        >
          {offer.offerName}
        </Link>
      ));
    }
    return null;
  };

  const renderList = () => currentList && currentList.map(menuList => {
    const {
      offerInfoMaps, catalogItemName, catalogItemId, catalogItemType,
    } = menuList;
    return (
      catalogItemType === '1000' ? (
        <li key={catalogItemId}>
          <div className={style.titleAContainer}>
            <span className={style.titleA}>{catalogItemName}</span>
          </div>
          <div className={style.subMenuContainer}>
            {
              offerInfoMaps && offerInfoMaps.length > 0 ? (
                <div className={style.linkList}>
                  {renderLinkList(offerInfoMaps || [])}
                </div>
              ) : null
            }
          </div>
        </li>
      ) : (
        offerInfoMaps && offerInfoMaps.length > 0 && (
          <li key={catalogItemId}>
            <div className={style.offersContainer}>
              <div className={style.linkList}>
                {renderLinkList(offerInfoMaps || [])}
              </div>
            </div>
          </li>
        )
      )
    );
  });

  return (
    <div className={style.mulitWrap}>
      <div className={style.left}>
        <ul>
          {
            list.map((item, key) => (
              <li
                className={current === key ? style.check : null}
                onMouseEnter={() => { setCurrent(key); }}
                key={item.id}
              >
                <span>{item.catalogName}</span>
                <Icon type="right" className={style.icon} />
              </li>
            ))
          }
        </ul>
      </div>
      <div className={style.right}>
        <div className={style.navContainer}>
          <span className={style.navTitle}>产品列表</span>
          <ul className={style.container}>
            {renderList()}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default MulitpleMenu;
