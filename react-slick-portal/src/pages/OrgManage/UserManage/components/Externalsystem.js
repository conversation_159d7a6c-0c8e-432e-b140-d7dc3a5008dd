/* eslint-disable */
import React, { useState, useEffect, createContext, useRef } from 'react';
import { connect } from 'dva';
import { Row, Col, Card, Form, Input, Icon, Button, Table, Modal, message, Select, Popover, Divider } from 'antd';
import SlickTable from '@/components/SlickTable';
import { useAntdTable } from '@umijs/hooks';
import styles from '../styles.less';
import PubSub from 'pubsub-js';
import request from '@/utils/request';
import pick from 'lodash/pick';
import omit from 'lodash/omit';
import classNames from 'classnames';
import AddExternalsystem from './AddExternalsystem';
import SlickUpload from '@/components/SlickUpload';
const { Option } = Select;
const Search = Input.Search;
const InputGroup = Input.Group;

// 空对象校验
const judgeObj = obj => {
  if (Object.keys(obj).length == 0) return true;
  return false;
};

function Externalsystem(props) {
  const systemEl = useRef();
  const { form } = props;
  const [visible, setVisible] = useState(false);
  const [paramsObj, setParamsObj] = useState({
    sysUserId: props.tableData.sysUserId,
    queryInfo: '',
  });
  const [sysCode, setSysCode] = useState([]);
  // 初始化变量
  const [variable, setVariable] = useState(false);

  const [passObj, setPassObj] = useState({}); // add 新增  edit 编辑

  const getTableData = ({ current, pageSize, paramsObj }) => {
    return request('orgauth/ExternalUserController/selectExtUserGridData.do', {
      data: {
        pageNum: current,
        pageSize: pageSize,
        sortName: '',
        sortOrder: 'asc',
        ...paramsObj,
      },
    }).then(res => {
      if (Array.isArray(res.resultObject.list)) {
        return {
          total: res.resultObject.total,
          data: res.resultObject.list.map(val => {
            const { children, ...rest } = val;
            return rest;
          }),
        };
      }
      return {
        total: 0,
        data: [],
      };
    });
  };

  const {
    tableProps,
    search: { submit },
  } = useAntdTable(
    params => {
      return getTableData({ ...params, paramsObj });
    },
    [paramsObj],
    {
      form,
      defaultPageSize: 5,
    }
  );
  const { pagination, ...restTableProps } = tableProps;

  useEffect(() => {
    if (!judgeObj(props.tableData) && variable) {
      setParamsObj({
        ...paramsObj,
        sysUserId: props.tableData.sysUserId,
        queryInfo: '',
      });
    }
  }, [props.tableData]);

  useEffect(() => {
    setVariable(true);
  }, []);

  const onSearch = value => {
    setParamsObj({
      ...paramsObj,
      queryInfo: value,
    });
  };

  // 列表删除
  const handleDelete = record => {
    Modal.confirm({
      content: '确认进行删除？',
      onOk() {
        request(`orgauth/ExternalUserController/delete.do?id=${record.externalUserId}`, {
          method: 'DELETE',
          data: {
            id: record.externalUserId,
          },
        }).then(res => {
          if (res) {
            submit();
            message.success('删除成功！');
          } else {
            message.error(res.resultMsg);
          }
        });
      },
    });
  };

  const handleEdit = record => {
    setPassObj(record);
    setVisible(true);
  };

  const deleteSelectRows = arr => {
    // request('orgauth/SystemUserRoleController/deleteUserRolesByIds.do', {
    //   data: arr,
    //   method: 'DELETE',
    // }).then(res => {
    //   if (res) {
    //     message.success('删除成功！');
    //     setParamsObj({
    //       ...paramsObj,
    //     });
    //     setSelectRows([]);
    //   } else {
    //     message.error(`删除失败！`);
    //   }
    // });
  };

  const handleAdd = () => {
    if (props.tableData.statusCd === '1100') {
      message.error('用户已失效不能进行相关操作！');
    } else {
      setPassObj({});
      setVisible(true);
    }
  };

  const handleSync = async () => {
    message.info('账号同步中,请稍等');
    const syncResult = await request('portal/CrmSyncController/syncUser.do', {
      method: 'post',
      data: {
        sysUserCode: props.tableData.sysUserCode,
        sysUserId: props.tableData.sysUserId,
      },
    });
    if (syncResult && syncResult.resultCode === 'TRUE') {
      message.success('同步成功');
      onSearch();
    } else {
      message.error('同步失败');
    }
  };

  const handleOk = () => {
    systemEl.current.handleAdd().then(res => {
      if (res === true) {
        setVisible(false);
        submit();
      }
    });
  };
  const handleCancel = () => {
    setVisible(false);
  };

  const columns = [
    {
      title: '编号',
      dataIndex: 'externalUserId',
    },
    {
      title: '系统名称',
      dataIndex: 'systemName',
    },
    {
      title: '系统编码',
      dataIndex: 'systemNbr',
    },
    {
      title: '登录账号',
      dataIndex: 'externalUserCode',
    },
    {
      title: '是否有效',
      dataIndex: 'statusCd',
      render: (text, record) => {
        return text === '1000' ? '有效' : '失效';
      },
    },
    {
      title: '是否默认账号',
      dataIndex: 'defaultFlag',
      render: text => {
        if (text === '1000') {
          return '是';
        }
        return '否';
      },
    },
    {
      title: '外部组织ID',
      dataIndex: 'outOrgId',
    },
    {
      title: '外部区域ID',
      dataIndex: 'outRegionId',
    },
    {
      title: '操作',
      render: (text, record) => {
        return (
          <span>
            <a
              onClick={() => {
                handleEdit(record);
              }}
            >
              编辑
            </a>
            <Divider type="vertical" />
            <a
              onClick={() => {
                handleDelete(record);
              }}
            >
              删除
            </a>
          </span>
        );
      },
    },
  ];
  return (
    <div>
      <div className={classNames('text-right', 'margin-bottom')}>
        <Search placeholder="系统编码/登录账号" onSearch={value => onSearch(value)} style={{ width: 200 }} />
        <Button type="primary" className="margin-left" onClick={handleAdd}>
          新增
        </Button>
        <Button type="primary" className="margin-left" onClick={handleSync}>
          账号同步
        </Button>
      </div>
      <SlickTable
        rowKey={record => record.id}
        columns={columns}
        {...restTableProps}
        data={{
          pagination: {
            ...pagination,
          },
        }}
      />
      <Modal
        title={judgeObj(passObj) ? `新增外部系统账号` : '编辑外部系统账号'}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width="500px"
        destroyOnClose
      >
        <AddExternalsystem
          ref={systemEl}
          cRef={systemEl}
          sysUserId={props.tableData.sysUserId}
          passObj={passObj}
          tableData={restTableProps.dataSource}
        />
      </Modal>
    </div>
  );
}

export default connect(({ userManage }) => ({
  tableData: userManage.tableData,
}))(Form.create()(Externalsystem));
