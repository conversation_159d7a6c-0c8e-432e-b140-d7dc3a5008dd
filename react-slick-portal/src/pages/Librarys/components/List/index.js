import React, { useMemo, useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, message, Modal, Spin, Table } from 'antd';
import { connect } from 'dva';
import { Portal } from '@/utils/utils';
import request from '@/utils/request';
import QueryForm from '../QueryForm';
import useLibraryReq from '../../useLibraryReq';
import style from './Librarys.module.less';
import { changeLibStatus, delLib, downloadFiles, checkAction } from '../../services';
import DetailInfo from '../DetailInfo';
import { INFO_PROGRESS, KNOWLEDGE_STATUS, KL_FLOW_CODE, LIBRARYS_TYPE } from '@/pages/Librarys/const';

const LibrarysType = props => {
  const { type, userInfo, isAdmin, attrs } = props;
  const librarysReq = useLibraryReq({ type });
  const [detailInfo, setDetailInfo] = useState({});
  const [syncVisible, setSyncVisible] = useState(false);
  const [currentInfoId, setCurrentInfoId] = useState('');
  const [currentIsPut, setCurrentIsPut] = useState('');
  const [spinning, setSpinning] = useState(false);
  // const identification = useRef('');
  const { loading, dataSource, columns, title, pagination, reloadList } = librarysReq;

  // 上下架操作
  const handlePutOrOff = async (knowledgeInfoId, isPut, isSync) => {
    setSpinning(true);
    const changeRes = await changeLibStatus({ knowledgeInfoId, isSync }, isPut);
    if (changeRes && changeRes.resultCode === 'TRUE') {
      message.success('操作成功', 1, () => {
        reloadList();
      });
      setSyncVisible(false);
    }
    setSpinning(false);
  };

  // 上下架按钮
  const putAndOffBtn = (isPut, actionType, record) => (
    <Button
      type="link"
      onClick={async () => {
        const result = await checkAction({ knowledgeInfoId: record.knowledgeInfoId, actionType });
        if (result === 'notFlow') {
          if ([LIBRARYS_TYPE.PROD, LIBRARYS_TYPE.COOP, LIBRARYS_TYPE.HARDWARE].includes(type) || (isPut === false && !record.caseId)) {
            // 如果是产品库\合作伙伴\硬件终端，不需要同步集团，isSync设为false
            // 如果是下架操作，只有caseId有值时才弹窗确认是否同步集团（因为上架有同步，下架、更新才可能有同步）
            Modal.confirm({
              title: `是否确认进行资料${isPut ? '上架' : '下架'}操作？`,
              onOk: async () => {
                handlePutOrOff(record.knowledgeInfoId, isPut, false);
              },
            });
          } else {
            // 弹窗确认是否同步集团
            setCurrentInfoId(record.knowledgeInfoId);
            setCurrentIsPut(isPut);
            setSyncVisible(true);
          }
        } else if (result === 'goFlow') {
          const sourceFrom = encodeURIComponent(`/librarys/${type}`);
          Portal.open(
            `/iframe/TYMH_MENU_KNOWLEDGE_AUDIT?type=edit&knowledgeInfoId=${record.knowledgeInfoId}&flowCode=${actionType}&sourceFrom=${sourceFrom}`
          );
        }
      }}
    >
      {isPut ? '上架' : '下架'}
    </Button>
  );

  // 控制各按钮是否显示
  const setBtnShow = r => {
    /**
     * 删除: 状态待发布、已下架, 资料进度不等于 审批中 -- 逻辑删除（）
      上架：状态待发布、已下架并且caseId不为空，资料进度不等于 审批中和草稿
      编辑：草稿编辑（待发布 1000，资料进度草稿，直接进入编辑页面）
      普通编辑（状态待发布、已下架，资料进度不等于 审批中和草稿）
      流程编辑（状态已发布，资料进度不等于 审批中）
      下架：状态已发布，资料进度不等于 审批中
      下载：有附件才允许下载（1. 逻辑保持不变  2. 自身草稿才能允许下载）
    */
    const showDelete =
      (r.statusCd === KNOWLEDGE_STATUS.waitRelease || r.statusCd === KNOWLEDGE_STATUS.offShelf) && r.knowledgeProgress !== INFO_PROGRESS.approveing;

    const showPutShelf =
      (r.statusCd === KNOWLEDGE_STATUS.waitRelease || (!r.caseId && r.statusCd === KNOWLEDGE_STATUS.offShelf)) &&
      r.knowledgeProgress !== INFO_PROGRESS.approveing &&
      r.knowledgeProgress !== INFO_PROGRESS.draft;

    let showEdit = false;
    if (r.statusCd === KNOWLEDGE_STATUS.waitRelease && r.knowledgeProgress === INFO_PROGRESS.draft) {
      showEdit = 'draft';
    } else if (
      (r.statusCd === KNOWLEDGE_STATUS.waitRelease) &&
      r.knowledgeProgress !== INFO_PROGRESS.approveing &&
      r.knowledgeProgress !== INFO_PROGRESS.draft
    ) {
      showEdit = 'update';
    } else if (r.statusCd === KNOWLEDGE_STATUS.released && r.knowledgeProgress !== INFO_PROGRESS.approveing) {
      showEdit = 'process';
    }

    const showOffShelf = r.statusCd === KNOWLEDGE_STATUS.released && r.knowledgeProgress !== INFO_PROGRESS.approveing;

    const showDownload = r.files?.length > 0;

    return { showDelete, showPutShelf, showEdit, showOffShelf, showDownload };
  };

  const _columns = useMemo(
    () => [
      ...columns,
      {
        title: '资料进度',
        dataIndex: 'knowledgeProgressName',
        align: 'center',
      },
      {
        title: '发布状态',
        dataIndex: 'statusCd',
        render: v => attrs?.statusCd?.find(item => item.value === v)?.name,
        align: 'center',
      },
      {
        title: '操作',
        width: '200px',
        align: 'center',
        fixed: 'right',
        render: (v, r) => {
          const res = [];
          res.push(
            <Button
              type="link"
              onClick={() => {
                setDetailInfo({
                  visible: true,
                  knowledgeInfoId: r.knowledgeInfoId,
                  type,
                });
              }}
            >
              详情
            </Button>
          );
          const { showEdit, showDelete, showPutShelf, showOffShelf, showDownload } = setBtnShow(r);
          if (type !== 'experience' || (type === 'experience' && showDownload)) {
            res.push(
              <Button
                type="link"
                onClick={() => {
                  downloadFiles(r.knowledgeInfoId, null, false, null, type);
                }}
              >
                下载
              </Button>
            );
          }
          if (isAdmin || r.createStaff === userInfo.userId) {
            if (showEdit !== false) {
              res.push(
                <Button
                  type="link"
                  onClick={async () => {
                    const result = await checkAction({ knowledgeInfoId: r.knowledgeInfoId, actionType: KL_FLOW_CODE.KNOWLEDGE_UPDATE_FLOW });
                    if (result === 'notFlow') {
                      props.goToStep(2, {
                        type,
                        title,
                        mode: 'update',
                        knowledgeInfoId: r.knowledgeInfoId,
                        caseId: r.caseId,
                      });
                    } else if (result === 'goFlow') {
                      const sourceFrom = encodeURIComponent(`/librarys/${type}`);
                      Portal.open(
                        `/iframe/TYMH_MENU_KL_EDIT?type=edit&knowledgeInfoId=${r.knowledgeInfoId}&flowCode=${KL_FLOW_CODE.KNOWLEDGE_UPDATE_FLOW}&sourceFrom=${sourceFrom}`
                      );
                    }
                  }}
                >
                  更新
                </Button>
              );
            }
            if (showDelete) {
              res.push(
                <Button
                  type="link"
                  onClick={() => {
                    Modal.confirm({
                      title: '是否确认删除',
                      onOk: async () => {
                        const delRes = await delLib(r.knowledgeInfoId);
                        if (delRes && delRes.resultCode === 'TRUE') {
                          message.success('操作成功', 1, () => {
                            reloadList();
                          });
                        } else {
                          // message.warning(delRes?.resultMsg);
                        }
                      },
                    });
                  }}
                >
                  删除
                </Button>
              );
            }
            if (showPutShelf) {
              res.push(putAndOffBtn(true, KL_FLOW_CODE.KNOWLEDGE_EFF_FLOW, r));
            }
            if (showOffShelf) {
              res.push(putAndOffBtn(false, KL_FLOW_CODE.KNOWLEDGE_EXP_FLOW, r));
            }
          }
          return <div className={style.btnCon}>{res}</div>;
        },
      },
    ],
    [type, dataSource, attrs]
  );

  // 监听来自低代码平台的消息
  useEffect(() => {
    function hanlder(event) {
      if (!event.data || !event.data.u__$Data) {
        return;
      }
      const data = event.data.u__$Data;
      const { to, action } = data;

      if (to === 'library' && action === 'refresh') {
        reloadList();
      }
    }
    window.addEventListener('message', hanlder);

    return () => {
      window.removeEventListener('message', hanlder);
    };
  }, []);

  useEffect(() => {
    const schemeSharing = window.sessionStorage.getItem('schemeSharing');
    if (schemeSharing) {
      const content = JSON.parse(schemeSharing);
      const { files } = content?.newBackfilling;
      const { attachment } = files[0];
      request('orgauth/DocumentController/saveDocumentInfo.do', {
        data: {
          docName: attachment.fileName,
          docLink: attachment.filePath,
          docSize: attachment.length,
        },
      }).then(res => {
        if (res?.resultObject) {
          const data = {
            ...content.newBackfilling,
          };
          data.files[0].attachId = res.resultObject.id;
          data.files[0].attachment.attachId = res.resultObject.id;
          data.files[0].attachment.id = res.resultObject.id;
          data.files[0].attachment.docNbr = res.resultObject.docNbr;
          props.goToStep(2, { type: content.type, title: content.title, newBackfilling: data });
        } else {
          message.error('上传附件失败');
        }
      });
    }
  }, [props.location]);

  return (
    <div className={style.wrap}>
      <Card
        title={title}
        extra={(
          <Button
            type="primary"
            onClick={() => {
                props.goToStep(2, { type, title });
              }}
          >
            新增
          </Button>
        )}
      >
        <div className={style.container}>
          <div className={style.queryForm}>
            <QueryForm {...librarysReq} type={type} />
          </div>
          <div>
            <Table
              bordered
              dataSource={dataSource}
              loading={loading}
              columns={_columns}
              rowKey={(r, idx) => `${idx}`}
              pagination={pagination}
              onRow={record => ({
                onDoubleClick: () => {
                  setDetailInfo({
                    visible: true,
                    knowledgeInfoId: record.knowledgeInfoId,
                    type,
                  });
                },
              })}
              scroll={{ x: 'max-content' }}
            />
          </div>
        </div>
      </Card>
      <DetailInfo
        {...detailInfo}
        onCancel={() => {
          setDetailInfo({
            visible: false,
          });
        }}
      />

      <Modal
        loading
        title="提示"
        visible={syncVisible}
        className={style.putModal}
        onCancel={() => {
          setSyncVisible(false);
        }}
        footer={(
          <Spin spinning={spinning}>
            <Button
              size="default"
              type="primary"
              onClick={() => {
                handlePutOrOff(currentInfoId, currentIsPut, true);
              }}
            >
              提交，同步集团
            </Button>
            <Button
              size="default"
              type="primary"
              onClick={() => {
                handlePutOrOff(currentInfoId, currentIsPut, false);
              }}
            >
              提交，不同步集团
            </Button>
            <Button
              size="default"
              onClick={() => {
                setSyncVisible(false);
              }}
            >
              取消
            </Button>
          </Spin>
        )}
      >
        <font style={{ fontSize: '15px' }}>本次资料提交是否触发集团同步，请确认！</font>
      </Modal>
    </div>
  );
};

export default connect(({ login, librarys }) => ({
  userInfo: login.user.userInfo,
  isAdmin: login.user.isAdmin,
  attrs: librarys.attrs,
}))(LibrarysType);
