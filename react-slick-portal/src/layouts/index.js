import React, { useEffect } from 'react';
import { connect } from 'dva';
import { ConfigProvider, Button, message } from 'antd';
import find from 'lodash/find';
import router from 'umi/router';
import { getLocale } from 'umi-plugin-react/locale';
import memoizeOne from 'memoize-one';
import isEqual from 'lodash/isEqual';
import isPlainObject from 'lodash/isPlainObject';
import dynamic from 'umi/dynamic';
import enUS from 'antd/es/locale/en_US';
import zhCN from 'antd/es/locale/zh_CN';
import moment from 'moment';
import { Redirect } from 'umi';
import pathToRegexp from 'path-to-regexp';
import { stringify } from 'qs';
import 'moment/locale/zh-cn';
import traceLogAction from '@/services/traceLogAction';
import mapping, { checkStatus } from '@/services/login';
import { getItem, parseQuery, IEVersion, setItem } from '@/utils/utils';
import LoadingComponent from '@/components/PageLoading/index';
import Locked from '@/pages/User/Locked';
import AccountInit from '@/pages/FourAAccountManage/AccountInit';
import { themeConfig, loginRedirect } from '@/defaultSettings';
import watermark from '@/utils/waterMark';
import PagePointUtil from '@/utils/pagePointUtil';
import { getDictData, newGetDictData } from '@/services/common';
import request from '@/utils/request';

let locale = zhCN;

if (getLocale() === 'zh-CN') {
  locale = zhCN;
  moment.locale('zh-cn');
}

if (getLocale() === 'en-US') {
  locale = enUS;
  moment.locale('en-US');
}

const ThemeLayout = dynamic({
  loader: () => import('./ThemeLayout'),
  loading: LoadingComponent,
});

const UserLayout = dynamic({
  loader: () => import('./UserLayout'),
  loading: LoadingComponent,
});

let statusIntervalId = null; // checkStatus轮询

/**
 * 以pathname从后端返回的菜单数组中提取当前页面的菜单信息,情况分3种
 * 1、pathname以iframe开头
 * 2、普通路由
 * 3、嵌套路由 如：/user/login; /iframe/2769963
 * @param {string} pathname 当前页面的pathname,
 * @param {object[]} all 后端返回的菜单数组
 * @param {object[]} route
 * @returns {Object|undefined}  如果是对象，则必须包含3个字段：{menuId,urlAddr,menuName,menuCode}
 */
function _findMenu(pathname, all, route) {
  let result;
  // 1、形式如/iframe/2769963的表示都是第三方提供的内嵌页面，用menuId查找
  if (/^\/iframe\/\w+/i.test(pathname)) {
    const menuCode = pathname.match(/^\/iframe\/(\w+)/)[1];
    result = find(all, { menuCode });
    return result;
  }

  // 带有micro，说明是micro微前端嵌套页面，形如 /lcdpMicro/xxx
  if (pathname.toLocaleLowerCase().indexOf('micro/') !== -1) {
    result = find(all, item => {
      if (!item?.urlAddr) {
        return false;
      }

      const paramsIndex = String(item?.urlAddr).indexOf('?');
      // 去除url中?及其后面的参数
      const urlAddr = paramsIndex === -1 ? item?.urlAddr : item?.urlAddr.slice(0, paramsIndex);

      return urlAddr.endsWith(pathname);
    });

    return result;
  }

  const menu = find(all, { urlAddr: pathname });
  if (isPlainObject(menu)) {
    // 2、普通路由
    result = menu;
    return result;
  }

  if (menu === undefined) {
    // 3、嵌套路由
    const parent = find(all, item => {
      const { urlAddr } = item;
      // 有的菜单没有配置urlAddr
      if (!urlAddr) {
        return false;
      }
      const rule = urlAddr.endsWith('/') ? `${urlAddr}:id?` : `${urlAddr}/:id?`;
      // 不区分大小写  pathToRegexp('/notice/:id?').test('/notice/123')  => true
      return pathToRegexp(rule).test(pathname);
    });

    if (parent === undefined) {
      // 嵌套路由 不匹配
      result = undefined;
      return result;
    }

    if (isPlainObject(parent)) {
      const component = find(route, item => {
        // 匹配 /notice/123 这样的动态路由
        const { path } = item;

        // 有的菜单没有配置urlAddr
        if (path === undefined) {
          return false;
        }
        const rule = path.endsWith('/') ? `${path}:id?` : `${path}/:id?`;
        //
        return pathToRegexp(rule).test(pathname);
      });

      // 嵌套路由 匹配
      if (isPlainObject(component)) {
        const { menuId, urlAddr, menuName, menuCode } = parent;
        result = { menuId, urlAddr, menuName, menuCode };
      } else {
        result = undefined;
      }
    }
  }

  return result;
}

export const findMenu = memoizeOne(_findMenu, isEqual);

function FrameLayout(props) {
  const {
    status,
    children,
    dispatch,
    initDataConfirmed,
    all,
    success,
    user,
    viewMode,
    showSubSystemAccountSelect,
    loginoutResSerList,
    watermarkConf,
    ...rest
  } = props;
  const {
    location: { pathname },
    route,
  } = rest;

  // const getCatchPlugin = async () => {
  //   // 异常插件引入
  //   const result = await request('portal/NmgDataDictController/getDataDictByCode.do', {
  //     method: 'GET',
  //     data: {
  //       groupCode: 'SYSTEM_VAR',
  //       paramCode: 'CatchPluginInfo',
  //     },
  //   });

  //   if (result?.paramValue && window.CatchPlugin) {
  //     const [projectId, groupId, url] = result.paramValue.split(',');
  //     const catcherIns = await window.CatchPlugin.init({ projectId, groupId, url });
  //     catcherIns.manualTrigger();
  //   }
  // };

  /**
   * 这块的处理逻辑 需要兼顾noraml和inner两种视图模式
   *
   * ## normal模式
   * 1、未登录，登陆状态超时 或 访问/user/login
   *  - 使用 UserLayout模板
   * 2、已登录
   *  - 访问异常页面 则使用 Exception模板
   *  - 初始化失败：获取BasicLayout页面初始化数据失败，跳转到500页面
   *  - 初始化成功
   *    - 访问页面不存在 则使用 Exception模板(因为判断页面是否存在依赖于异步数据all，所以在异步结束前返回null，即让页面空白)
   *    - 其余使用 BasicLayout (进入basicLayout页面之前，需要保证初始化data异步获取成功)
   *
   * ## inner模式
   * 1. 未登录
   *  - 始总停留在loading界面，等待sessionId校验。
   * 2. 状态过期或直接访问登录页
   *  - redirect到403页面
   * 2. 已登录
   *  - 处理逻辑同normal模式下的已登录
   */
  function renderLayout() {
    // 未登录 - viewMode=inner
    if (viewMode === 'inner' && status === mapping.LOG_OUT) {
      return <LoadingComponent />;
    }
    if (viewMode === 'inner' && (status === mapping.SESSION_TIME_OUT || pathname.toLocaleLowerCase() === '/nmg')) {
      return <Redirect to="/403" />;
    }

    // 未登录
    if (
      viewMode === 'normal' &&
      (status === mapping.LOG_OUT || status === mapping.SESSION_TIME_OUT || pathname.toLocaleLowerCase() === '/nmg')
    ) {
      if (window.location.href.includes('netvan.cn')) {
        window.location.href = 'https://agent2.netvan.cn/ngboss';
        return null;
      }
      return <UserLayout {...rest}>{children}</UserLayout>;
    }

    if (viewMode === 'normal' && status === mapping.LOCKED) {
      return <Locked />;
    }

    // 已登录，直接访问异常页面
    if (/^\/((404)|(500)|(403)\/?)$/.test(pathname)) {
      const Exception = dynamic({
        loader: () => import(`@/pages/Exception/${pathname.split('/')[1]}`),
        loading: LoadingComponent,
      });
      return <Exception />;
    }

    // 已登录，异步未发起或异步进行中
    if (initDataConfirmed === false) {
      return <LoadingComponent />;
    }

    // 水印初始化未完成
    if (watermarkConf.initCompleted === false) {
      return <LoadingComponent />;
    }

    // 初始化失败：获取basicLayout初始化数据异常
    if (all.length === 0) {
      return <Redirect to="/500" />;
    }

    // 黑龙江产品大厅路由处理逻辑
    if (pathname.startsWith('/offerCenter')) {
      return <div>{children}</div>;
    }

    // 初始化成功：没找到当前页数据（首页是个特例 需要剔除）
    const currMenu = findMenu(pathname, all, route);
    if (pathname !== '/' && currMenu === undefined) {
      message.error('无权限，请联系管理员授权');
      router.goBack();
      return null;
      // return <Redirect to="/403" />;
    }

    // 添加水印
    // TODO: 需要在退出时清掉水印
    if (!document.getElementById('wm_div_id') && user && watermarkConf.enableWatermark) {
      const {
        userInfo: { userName },
      } = user;

      let watermarkText = `${getItem('systemTitle', 'localStorage') || '火麒麟 政企智慧中台'} ${userName}`;
      if (watermarkConf.config.watermarkText) {
        watermarkText = watermarkConf.config.watermarkText.replace(/\n/g, ' ');
      }
      watermark.init({
        watermark_id: 'wm_div_id', // 水印总体的id
        watermark_prefix: 'mask_div_id', // 小水印的id前缀
        watermark_txt: watermarkText, // 水印的内容
        watermark_x: watermarkConf.config.coordinateX ? Number(watermarkConf.config.coordinateX) : -100, // 水印起始位置x轴坐标
        watermark_y: watermarkConf.config.coordinateY ? Number(watermarkConf.config.coordinateY) : 30, // 水印起始位置Y轴坐标
        watermark_rows: 0, // 水印行数
        watermark_cols: 0, // 水印列数
        watermark_x_space: 300, // 水印x轴间隔
        watermark_y_space: 100, // 水印y轴间隔
        watermark_font: '微软雅黑', // 水印字体
        watermark_color: watermarkConf.config.fontColor ? watermarkConf.config.fontColor : 'red', // 水印字体颜色
        watermark_fontsize: watermarkConf.config.fontSize ? `${watermarkConf.config.fontSize}px` : '13px', // 水印字体大小
        watermark_alpha: watermarkConf.config.transparency ? watermarkConf.config.transparency : 0.06, // 水印透明度，要求设置在大于等于0.005
        watermark_width: 150, // 水印宽度
        watermark_height: 100, // 水印长度
        watermark_angle: watermarkConf.config.inclination ? watermarkConf.config.inclination : 15, // 水印倾斜度数
        watermark_parent_width: 0, // 水印的总体宽度（默认值：body的scrollWidth和clientWidth的较大值）
        watermark_parent_height: 0, // 水印的总体高度（默认值：body的scrollHeight和clientHeight的较大值）
        watermark_parent_node: null, // 水印插件挂载的父元素element,不输入则默认挂在body上
      });
    }

    if (showSubSystemAccountSelect === 'init') {
      return <AccountInit />;
    }

    // currMenu {object} 当前菜单信息 必须包含3个字段：{menuId,urlAddr,menuName}

    // 外系统登录触发埋点内容
    setTimeout(() => {
      const regButton = document.getElementById('login_extra');
      if (document.getElementById('login_extra')?.getAttribute('data-reg-idflag') === '0') {
        regButton.click();
      }
    }, 1000);

    return (
      <ThemeLayout {...rest} currMenu={pathname === '/' ? { menuName: '首页', urlAddr: '/', menuId: undefined } : currMenu}>
        {children}
      </ThemeLayout>
    );
  }

  useEffect(() => {
    dispatch({
      type: 'login/checkLogin',
    });
  }, [dispatch]);

  const getSystemTitle = async () => {
    const result = await request('portal/NmgDataDictController/getDataDictByCode.do', {
      method: 'get',
      data: {
        groupCode: 'SYSTEM_VAR',
        paramCode: 'SYSTEM_NAME',
      },
    });
    if (result?.paramValue) {
      setItem('systemTitle', result.paramValue, 'localStorage');
      document.title = result.paramValue; // 手动更新浏览器标签内容
    }
  };

  useEffect(() => {
    // 只有在非ie浏览器时，才引入microApp
    if (IEVersion() === -1) {
      // fn?.default 就是 import microApp from '@micro-zoe/micro-app' 中的microApp
      import('@micro-zoe/micro-app').then(({ default: microApp }) => {
        microApp.start();
      });
    }
    // 前端异常监控初始化
    // getCatchPlugin();

    getSystemTitle();

    // 启动埋点
    request('portal/NmgDataDictController/getDataDictByCode.do', {
      method: 'get',
      data: { groupCode: 'SYSTEM_PARAMS_ISCONSOLE', paramCode: 'isConsole' },
    }).then(res => {
      // eslint-disable-next-line no-new
      new PagePointUtil({
        pointKeyArr: ['data-reg-id'],
        requestUrl: '/effectiveness-view/businessTraceInfo/acceptPageLog',
        isConsole: res?.paramValue === 'true',
        isDefaultRegister: false, // 手动注册
      });
    });
  }, []);

  /**
   * 监听登录状态
   * 1、登录成功后： a、请求菜单数据；b、监听并记录用户最后操作时间；c、开始轮询，登录状态是否超时
   * 2、其他状态，停止以上a\b\c
   */
  useEffect(() => {
    if (status === mapping.LOGINED) {
      dispatch({
        type: 'menu/getInitData',
      });
      dispatch({
        type: 'watermarkConf/init',
      });
      // 开始监听用户动作，并记录最后操作时间
      traceLogAction.start();

      if (statusIntervalId === null) {
        // 每隔10秒状态检查一下，看看是否被踢下线了
        // 心跳时间修改为60秒
        getDictData({ groupCode: 'SYSTEM_LOGIN_OUT' }).then(async _loginoutResSerList => {
          const result = await newGetDictData({ groupCode: 'SYSTEM_VAR', paramCode: 'HEARTBEAT_INTERVAL' });
          const POLLING_INTERVAL = result || mapping.POLLING_INTERVAL;
          statusIntervalId = window.setInterval(() => {
            checkStatus(dispatch, getItem('lastOpDate'), _loginoutResSerList, router);
          }, 1000 * POLLING_INTERVAL);
        });
      }
      checkStatus(dispatch, moment().format('YYYY-MM-DD HH:mm:ss'), loginoutResSerList, router);
    } else {
      traceLogAction.stop();
      clearInterval(statusIntervalId);
      statusIntervalId = null;
    }
  }, [dispatch, status, loginoutResSerList]);

  // useEffect(() => {
  //   renderLayout(props);
  // });

  const getFourALoginUrl = () => {
    const result = request('portal/DataDictController/getValidValueByCode.do', {
      data: { groupCode: 'LOGIN_GROUP', paramCode: 'REDIRECT_4A_URL', defValue: '' },
      method: 'GET',
    });
    return result;
  };

  // 防止打开控制台
  useEffect(() => {
    const blockDevTools = () => {};

    // 监听F12和右键菜单
    const handleKeyDown = e => {
      if (e.keyCode === 123 || (e.ctrlKey && e.shiftKey && e.keyCode === 73)) {
        e.preventDefault();
        blockDevTools();
      }
    };

    const handleContextMenu = e => {
      e.preventDefault();
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('contextmenu', handleContextMenu);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('contextmenu', handleContextMenu);
    };
  }, []);

  /**
   * 负责**未登录**时，页面的跳转。不论访问什么页面统一跳转到/user/login。
   * 并且需要带上'?redirtct=xxx'。
   * 比如:访问http://localhost:8000/#/bulletin
   * 需要变成'http://localhost:/user/login?redirtct=http://localhost:8000/#/bulletin'
   * 用于登录成功后跳转至指定页面。
   * 同时需要判断是否已带有'?redirtct=xxx' 因为登出时会自动在url上添加。避免重复
   */
  useEffect(() => {
    const init = async () => {
      if (viewMode === 'normal' && status === mapping.LOG_OUT && pathname !== '/nmg') {
        if (pathname !== '/') {
          router.push({
            pathname: '/loading',
          });
          const fourALoginUrl = await getFourALoginUrl();
          if (fourALoginUrl) {
            // 配置4a登录地址则直接跳转4a登录地址
            window.location.assign(fourALoginUrl);
          }
        }
        if ((/^\/user*/.test(pathname) || pathname.indexOf('/500') !== -1) && window.location.href.indexOf('?redirect=http') === -1) {
          // 登录页相关，且不带'?redirtct=xxx'。
          router.push({
            pathname: '/nmg',
          });
        } else if (!/^\/user*/.test(pathname) && window.location.href.indexOf('user/login?redirect=http') === -1) {
          // 内页且不包含'?redirtct=xxx'
          let obj = { pathname: '/nmg' };
          // 重定向开关
          if (loginRedirect === true) {
            obj = {
              ...obj,
              search: stringify({
                redirect: window.location.href,
              }),
            };
          }
          router.push(obj);
        }
      }
    };
    init();
  }, [pathname, status, viewMode]);

  useEffect(() => {
    const { bss3SessionId } = parseQuery(window.location.href);
    if (viewMode === 'inner' && bss3SessionId && status === mapping.LOG_OUT) {
      // TODO: 需要更换单点登录接口服务的地址，目前的服务是Mock的,所以需要先用user/login正常登录一边，再切换到inner模式时 否则在获取菜单数据时可能会出现500错误
      dispatch({
        type: 'login/ssoLogin',
        payload: { bss3SessionId },
      });
    }
  }, [viewMode, dispatch, status]);

  return (
    <ConfigProvider locale={locale} prefixCls={themeConfig['@ant-prefix']}>
      {renderLayout()}
      {/* 右下角报账图标 */}
      {/* {props.user == null ? null : (<BugReport pathname={pathname} />)} */}

      {/* 指定登录情况下，触发埋点按钮 */}
      <Button
        id="login_extra"
        data-reg-id="login_extra"
        data-reg-idflag="0"
        hidden
        onClick={() => {
          document.getElementById('login_extra').setAttribute('data-reg-idflag', '1');
        }}
      >
        登录首页触发埋点
      </Button>
    </ConfigProvider>
  );
}

export default connect(({ login, menu, setting, fourAAccount, watermarkConf }) => ({
  status: login.status,
  loginoutResSerList: login.loginoutResSerList,
  viewMode: setting.viewMode,
  user: login.user,
  all: menu.all,
  success: menu.success,
  initDataConfirmed: menu.initDataConfirmed,
  showSubSystemAccountSelect: fourAAccount.showSubSystemAccountSelect,
  watermarkConf,
}))(FrameLayout);
