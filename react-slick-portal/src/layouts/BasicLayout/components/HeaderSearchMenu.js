/* eslint-disable react/no-array-index-key */
/* eslint-disable react/jsx-indent */
/* eslint-disable camelcase */
import React, { useState, useMemo, useRef } from 'react';
import { useDebounceFn } from '@umijs/hooks';
import { Input, Icon, Empty, Modal, Popover } from 'antd';
import memoizeOne from 'memoize-one';
import isEqual from 'lodash/isEqual';
import classNames from 'classnames';
import { connect } from 'dva';
import Link from 'umi/link';
import { quickSearch } from './utils';
import { getItem, isEmptyStr, getFinalUrl, openWindowTab } from '@/utils/utils';
import request from '@/utils/request';
import styles from '../less/header.less';
import { uploadSearchContent } from '@/services/menu';
import historySearch from '../img/historySearch.png';
import userSearch from '../img/userSearch.png';

const _quickSearch = memoizeOne(quickSearch, isEqual);

function HeaderSearchMenu({
  menu: { all, appSysCode, taskSysCode },
  showDropDown,
  setShowSearchMenuPanel: setShowDropDown,
  heightSearchList,
  searchHistoryList,
  getRecommendList,
}) {
  // 弹出框打开
  const [visible, setVisible] = useState(false);

  const [modalUrl, setModalUrl] = useState('');
  const [inputValue, setInputValue] = useState();
  const inputRef = useRef();
  const user = getItem('user');
  const { sessionId, userInfo } = user;

  // 按类别排序的所有菜单
  const [allByCategory, setAllByCategory] = useState(_quickSearch(all, '', appSysCode, taskSysCode)); // 搜索结果
  const { run } = useDebounceFn((_all, value, _appSysCode, _taskSysCode) => {
    setAllByCategory(_quickSearch(_all, value, _appSysCode, _taskSysCode));
  }, 300);

  // 菜单打开方式
  async function openMenu(menu) {
    // var MENU_OPEN_MODE_WORK_BENCH_LABEL = "1"; //工作台标签页打开
    // var MENU_OPEN_MODE_POPUP = "2"; //弹出框打开
    // var MENU_OPEN_MODE_NEW_BROWSER = "3"; //新开浏览器打开
    // var MENU_OPEN_MODE_BROWSER_LABEL = "4"; //浏览器新标签打开
    let menuUrl = menu.urlAddr.replace('[iframe]', '');
    if (menuUrl.indexOf('?') === -1) {
      menuUrl += `?bss3SessionId=${sessionId}`;
    } else {
      menuUrl += `&bss3SessionId=${sessionId}`;
    }

    if (menuUrl.indexOf('{SYS_USER_ID}') !== -1) {
      menuUrl = menuUrl.replace('{SYS_USER_ID}', userInfo.userCode);
    }
    if (menuUrl.indexOf('{SYS_ORG_ID}') !== -1) {
      menuUrl = menuUrl.replace('{SYS_ORG_ID}', userInfo.userOrgId);
    }

    // 采用SSO单点校验方式进行菜单打开
    if (menuUrl.indexOf('ssoType=1') !== -1) {
      // let urlParams = parseUrlParams(menuUrl);
      const urlParams = {
        bssSessionId: sessionId,
      };

      // 菜单地址SSO加密，拼接返回的signString和singTimestamp
      const response = await request(
        `orgauth/SystemInfoController/calcSign.do?systemInfoId=${menu.systemCode}`,
        {
          method: 'POST',
          data: urlParams,
        }
      );

      if (response && response.resultCode === '0') {
        menuUrl = `${menuUrl}&signString=${response.signString}&signTimestamp=${response.signTimestamp}`;
      }
    }

    if (menu.menuOpenMode === '4') {
      openWindowTab(menuUrl);
    } else if (menu.menuOpenMode === '3') {
      window.open(menuUrl, menu.menuName, 'width=800,height=600,location=yew,toolbar=yes');
    } else {
      setVisible(true);
      setModalUrl(menuUrl);
      setShowDropDown(false);
    }
  }

  // 负责渲染级联菜单
  function renderMenu(menu) {
    // 1100 表示叶子节点
    if (parseInt(menu.menuType, 10) === 1100) {
      if (menu.menuOpenMode !== '1') {
        return (
          <a
            onClick={() => openMenu(menu)}
            className={classNames('text-ellipsis')}
            key={menu.menuId}
          >
            {menu.menuName}
          </a>
        );
      }
      return (
        <Link
          to={getFinalUrl(menu, undefined, user)}
          onClick={() => setShowDropDown(false)}
          title={menu.menuName}
          className="text-ellipsis"
        >
          {menu.menuName}
        </Link>
      );
    }
    return (
      <a title={menu.menuName} href="#" className="text-ellipsis">
        {menu.menuName}
        <Icon type="right" />
      </a>
    );
  }

  /**
   *
   * @param {object[]} result 格式如下
   * [
   *   {
   *     title: '组织管理',
   *     menus: [
   *       {
   *         firstLetter: 'YGGL',
   *         iconUrl: 'icon-gene-perm-identity',
   *         menuCode: 'TYMH_MENU_019',
   *         menuDesc: '员工管理',
   *         menuId: 276111,
   *         menuIndex: 7,
   *         menuLevel: 2,
   *         menuName: '员工管理',
   *         menuOpenMode: '3',
   *         menuType: '1100',
   *         menuTypeName: '叶子菜单',
   *         parMenuId: 276110,
   *         paramEncryptType: '1000',
   *         privId: 88352,
   *         statusCd: '1000',
   *         systemCode: '727001',
   *         urlAddr: 'orgauth/modules/staff/views/StaffManageView',
   *       },
   *     ],
   *   },
   * ];
   */
  function renderAllByCategory(_all) {
    return _all.length === 0 ? (
      <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ margin: '150px 0 32px' }} />
    ) : (
      <div style={{ columnGap: 24, columnCount: 3 }}>
        {
          _all.map((group, n) => (
            <div key={`${group.title}_${n}`} style={{ breakInside: 'avoid' }}>
              {/**
           * TODO: 测试环境配置了一样的一级菜单导致key重复，暂时使用索引区分
           */}
              <dl>
                <dt className={styles['letter-title']}>
                  <span>{group.title}</span>
                </dt>
                <dd>
                  <ul>
                    {group.menus.map((menu, i) => (
                      <li className={styles['ng-item']} key={`${menu.menuId}_${i}`}>
                        {renderMenu(menu)}
                      </li>
                    ))}
                  </ul>
                </dd>
              </dl>
            </div>
          ))
        }
      </div>
    );
  }

  const menuList = useMemo(() => renderAllByCategory(allByCategory, setShowDropDown), [allByCategory]);

  const onSearch = async text => {
    if (!showDropDown) {
      setShowDropDown(true);
    }
    run(all, text, appSysCode, taskSysCode);
    if (!isEmptyStr(text)) {
      await uploadSearchContent(text);
    }
    getRecommendList();
  };

  const setAndSearch = text => {
    setInputValue(text);
    onSearch(text);
  };

  const content = (
    <div
      className={styles.searchDropdownBox}
    >
      <div className={styles.row}>
        <div className={styles.searchRecommend}>
          <div className={styles.unit}>
            <img alt="" src={historySearch} width={16} height={16} className="margin-right" />
            <span className={styles.title}>历史搜索推荐</span>
            <div>
              {
                searchHistoryList.map(item => (
                  <span
                    className={styles.label}
                    onClick={() => {
                      setAndSearch(item?.searchContent);
                    }}
                  >
                    {item.searchContent}
                  </span>
                ))
              }
            </div>
          </div>

          <div className={styles.unit}>
            <img alt="" src={userSearch} width={16} height={16} className="margin-right" />
            <span className={styles.title}>角色搜索高频关键字</span>
            <div>
              {
                heightSearchList.map(item => (
                  <span
                    className={styles.label}
                    onClick={() => {
                      setAndSearch(item?.searchContent);
                    }}
                  >
                    {item.searchContent}
                  </span>
                ))
              }
            </div>
          </div>

        </div>
        <div className={styles['letter-panel']}>
          <div className={classNames(styles['letter-category'], 'clearfix')}>
            {menuList}
          </div>
        </div>
      </div>
      <Modal visible={visible} onCancel={() => setVisible(false)} footer={null} width="800px">
        <iframe title="title" src={modalUrl} height="600px" width="752px" />
      </Modal>
    </div>
  );

  return (
    <div
      className={styles.headerSearchMenuWrap}
      onMouseLeave={() => {
        inputRef.current.blur();
        setShowDropDown(false);
      }}
    >
      <Popover
        content={content}
        placement="bottom"
        trigger="hover"
        overlayStyle={{ top: '50px', paddingTop: 0 }}
        getPopupContainer={trigger => trigger.parentNode}
        visible={showDropDown}
      >
        <div
          className={styles.search}
        >
          <Input.Search
            className={styles.searchInput}
            allowClear
            value={inputValue}
            ref={inputRef}
            placeholder="搜索关键字"
            onClick={() => { setShowDropDown(true); }}
            onSearch={onSearch}
            onChange={e => { setInputValue(e.target.value); }}
          />
        </div>
      </Popover>
    </div>
  );
}

export default connect(({ menu, loading }) => ({
  menu,
  heightSearchList: menu.heightSearchList,
  searchHistoryList: menu.searchHistoryList,
  refreshLoading: loading.effects['menu/refreshMenu'],
}))(HeaderSearchMenu);
