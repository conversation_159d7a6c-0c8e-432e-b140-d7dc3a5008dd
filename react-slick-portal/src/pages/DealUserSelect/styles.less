.bg {
  background-color: #fff;
}

.main {
  display: flex;
  flex: 1 0 auto;
  gap: 16px;
  align-items: stretch;
}

.left {
  flex: 0 0 30%;
  padding-right: 8px;
  border-right: 1px solid #e6e6e6;
}

.search {
  margin-bottom: 8px;
  background: #fff;
  border: 0;
  input {
    background: #fff;
  }
  :global(.anticon) {
    color: #4e5969;
  }
}
.right {
  position: relative;
  flex: 1 1 auto;
  margin: 0 auto;
}

.title {
  padding: 6px 0;
  color: black;
  font-weight: bold;
  font-size: 14px;
}

.bottom-divider {
  margin: 12px 0;
}

.bottom-area {
  display: flex;
  justify-content: flex-end;
}

.close-icon {
  cursor: pointer;
}

.left-tree {
  // max-height: 400px;
  overflow: auto;
}

.deal-user-card {
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: stretch;
  min-width: 100%;
  min-height: 100%;
  :global(.ant-card-body) {
    display: flex;
    flex: 1 0 auto;
    flex-direction: column;
    justify-content: stretch;
    padding-bottom: 10px;
  }
}
