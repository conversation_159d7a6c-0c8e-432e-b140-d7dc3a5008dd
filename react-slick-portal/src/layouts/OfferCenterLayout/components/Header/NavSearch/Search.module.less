@import '../../../less/variable.less';

@navHeight: 64px;
.wrap {
  position: relative;

  .search {
    :global(.ant-input-search) {
      height: @navHeight;
      border: none;
    }

    :global(.ant-input) {
      padding-left: @padding-xl;
      color: @bg-default;
      background: #0d2039;
      border: none;
    }

    :global(.ant-input-suffix) {
      margin-right: @padding-s;
      font-size: 24px;
      svg {
        color: @bg-default;
      }
    }
  }
}
