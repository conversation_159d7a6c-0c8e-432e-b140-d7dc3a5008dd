import { message } from 'antd';
import request from '@/utils/request';

/**
 * 查询公共静态数据枚举列表
 * @param {string} codeType - 枚举类型代码
 * @returns {Promise<Array>} - 返回一个包含枚举数据的数组
 */
export async function queryCommonStaticDataEnumList(codeType) {
  try {
    const res = await request('portal/CommonStaticDataController/queryCommonStaticData.do', {
      data: { codeType },
    });

    const { resultCode, resultObject } = res;

    if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
      return resultObject.rspParam.busiInfo.outData; // 确保返回的是数组
    }

    // 处理错误消息
    const resultMsg = resultObject?.rspParam?.busiInfo?.resultMsg || '未知错误';
    message.error(resultMsg);
    return [];
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('查询公共静态数据枚举列表失败:', error);
    message.error('查询公共静态数据枚举列表失败，请稍后再试。');
    return [];
  }
}

/**
 * 查询公共静态数据枚举映射
 * @param {string} codeType - 枚举类型代码
 * @returns {Promise<Object>} - 返回一个以 STATIC_KEY 为键，STATIC_VALUE 为值的对象
 */
export async function queryCommonStaticDataEnumMap(codeType) {
  try {
    const enums = await queryCommonStaticDataEnumList(codeType);

    if (!Array.isArray(enums)) {
      // eslint-disable-next-line no-console
      console.warn('queryCommonStaticDataEnumList 返回的数据不是数组:', enums);
      return {};
    }

    // 使用 reduce 构建键值映射对象
    const result = enums.reduce((acc, item) => {
      if (item.STATIC_KEY && item.STATIC_VALUE) {
        acc[item.STATIC_KEY] = item.STATIC_VALUE;
      } else {
        // eslint-disable-next-line no-console
        console.warn('无效的枚举项:', item);
      }
      return acc;
    }, {});

    // eslint-disable-next-line no-console
    console.log('queryCommonStaticDataEnumMap result:', result);
    return result;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('查询公共静态数据枚举映射失败:', error);
    message.error('查询公共静态数据枚举映射失败，请稍后再试。');
    return {};
  }
}
