import React, { useMemo, useState } from 'react';
import { Button } from 'antd';
import ComboGrid from './ComboGrid';

const Index = props => {
  const { value, onChange, colFieldsChange } = props;
  const [outsideOpen, setOutsideOpen] = useState(true);

  const renderComboGrid = useMemo(
    () => (
      <ComboGrid
        url="portal/KlKnowledgeInfoController/getKlKnowledgePmsPartnetInfo.do"
        popupStyle={{ width: 800 }}
        placeholder="请选择"
        searchPlaceholder="请输入单位名称或编码进行搜索"
        label="partnerName"
        destroyPopupOnHide
        rowKey="partnerName"
        pick="radio"
        outsideOpen={outsideOpen}
        setOutsideOpen={setOutsideOpen}
        value={value}
        onChange={onChange}
        columns={[
          {
            title: '合作伙伴',
            dataIndex: 'partnerName',
            ellipsis: true,
          },
          {
            title: '联系人',
            dataIndex: 'contactName',
            ellipsis: true,
          },
          {
            title: '联系人电话',
            ellipsis: true,
            dataIndex: 'contactPhone',
          },
        ]}
        extra={(
          <Button
            type="primary"
            onClick={() => {
              setOutsideOpen(false);
            }}
          >
            确定
          </Button>
        )}
      />
    ),
    [outsideOpen, colFieldsChange]
  );

  return renderComboGrid;
};

export default Index;
