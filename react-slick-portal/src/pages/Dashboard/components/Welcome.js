import React from 'react';
import { connect } from 'dva';
import classNames from 'classnames';
import TweenOne from 'rc-tween-one';
import Children from 'rc-tween-one/lib/plugin/ChildrenPlugin';
import { Avatar, Card } from 'antd';
import styles from './welcome.less';
import avatar from '@/layouts/BasicLayout/img/user-head.png';

TweenOne.plugins.push(Children);

function Whecome({ user: { userInfo, orgInfo, loginIp } }) {
  return (
    <Card bordered>
      <div className="clearfix">
        <div className="pull-left">
          <Avatar
            icon="user"
            src={avatar}
            size={60}
            className={classNames('inline-block', styles.avatar)}
          />
          <div className={classNames('inline-block', styles.user)}>
            <div className={styles.slogn}>欢迎{userInfo.userName} ，祝您开心每一天！</div>
            <div className={styles.info}>
              {userInfo.userCode} <span className="margin-h-sm">/</span>
              {orgInfo.orgName}({orgInfo.orgCode}) <span className="margin-h-sm">/</span> IP：
              {loginIp}
            </div>
          </div>
        </div>
        <div className={classNames(styles.right)}>
          <div className={classNames(styles.item)}>
            <div>客户建档率</div>
            <div className={styles.suffix}>
              <TweenOne
                animation={{
                  Children: {
                    value: 28,
                    floatLength: 0,
                  },
                  duration: 1000,
                }}
              >
                0
              </TweenOne>
            </div>
          </div>

          <div className={classNames(styles.item)}>
            <div>线索转化率</div>
            <div className={styles.suffix}>
              <TweenOne
                animation={{
                  Children: {
                    value: 81,
                    floatLength: 0,
                  },
                  duration: 1000,
                }}
              >
                0
              </TweenOne>
            </div>
          </div>
          <div className={classNames(styles.item)}>
            <div>商机转化率</div>
            <div className={styles.suffix}>
              <TweenOne
                animation={{
                  Children: {
                    value: 25,
                    floatLength: 0,
                  },
                  duration: 1000,
                }}
              >
                0
              </TweenOne>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}

export default connect(({ login }) => ({
  user: login.user,
}))(Whecome);
