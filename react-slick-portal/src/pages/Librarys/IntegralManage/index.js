import React, { useEffect, useState } from 'react';
import { Card, Modal, Table, DatePicker, Form, Input, Button, Row, Col, Select } from 'antd';
import { connect } from 'dva';
import { useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import { queryCommonRegion } from '@/pages/Librarys/services';
import { qryKlKnowledgeActionGrid } from '@/pages/Librarys/services-use';
import { TYPE_VALUE } from '@/pages/Librarys/const';
import DetailInfo from '../components/DetailInfo';
import styles from './IntegralManage.module.less';

const data = [
  {
    actionTypeName: '资料上架',
    ruleDesc: '库资料信息上架发布成功可获得10积分，即10积分/份。 \n 若同份资料存在多次上架，积分仅计算1次。',
    integralNum: '10',
    ownName: '资料上传人员',
  },
  {
    actionTypeName: '资料下载',
    ruleDesc: '库资料信息每被人下载一次可获得1积分，即1积分/次。\n同一用户多次下载同一资料，积分仅计算1次。',
    integralNum: '1',
    ownName: '资料上传人员',
  },
  {
    actionTypeName: '资料浏览',
    ruleDesc: '库资料信息每被人浏览一次可获得0.5积分，即0.5积分/次。\n同一用户多次浏览同一资料，积分仅计算1次。',
    integralNum: '0.5',
    ownName: '资料上传人员',
  },
  {
    actionTypeName: '资料学习',
    ruleDesc: '每浏览（下载）一份资料可获得0.5积分，即0.5积分/次。\n同一用户多次浏览或下载同一资料，积分分别仅计算1次。',
    integralNum: '0.5',
    ownName: '资料使用人员',
  },
];

const IntegralManage = ({ form, attrs, dispatch }) => {
  const { getFieldDecorator, setFieldsValue } = form;
  const { knowledgeClassType = [], knowledgeType = [] } = attrs;
  const [currentKnowledgeType, setCurrentKnowledgeType] = useState(knowledgeType);
  const [regionList, setRegionList] = useState([]);
  const [areaList, setAreaList] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailInfo, setDetailInfo] = useState(false);

  const { tableProps, search } = useAntdTable(qryKlKnowledgeActionGrid, [], { form });
  const { pagination, ...restTableProps } = tableProps;
  const { submit, reset } = search;

  const getAreaList = async regionId => {
    setFieldsValue({
      areaId: undefined,
    });
    const res = await queryCommonRegion({
      parRegionId: regionId,
      regionLevel: '3',
    });
    setAreaList(res);
  };

  useEffect(() => {
    const initData = async () => {
      // 获取主数据
      dispatch({
        type: 'librarys/initData',
      });

      const regionRes = await queryCommonRegion({ regionLevel: '2' });
      setRegionList(regionRes);
    };

    initData();
  }, []);

  const columns = [
    {
      title: '归属地市',
      dataIndex: 'regionName',
      ellipsis: true,
      align: 'center',
      render: text => text ?? '-',
    },
    {
      title: '归属县市',
      dataIndex: 'areaName',
      align: 'center',
      render: text => text ?? '-',
    },
    {
      title: '用户名称',
      dataIndex: 'userName',
      align: 'center',
      render: text => text ?? '-',
    },
    {
      title: '用户工号',
      dataIndex: 'userCode',
      align: 'center',
      render: text => text ?? '-',
      ellipsis: true,
    },
    {
      title: '库类型',
      dataIndex: 'knowledgeClassTypeName',
      align: 'center',
      render: text => text ?? '-',
    },
    {
      title: '资料类型',
      dataIndex: 'knowledgeTypeName',
      align: 'center',
      render: text => text ?? '-',
    },
    {
      title: '资料编号',
      dataIndex: 'knowledgeNbr',
      align: 'center',
      render: text => text ?? '-',
    },
    {
      title: '资料名称',
      dataIndex: 'knowledgeName',
      align: 'center',
      render: text => text ?? '-',
      onCell: () => ({
        style: {
          textAlign: 'left',
        },
      }),
    },
    {
      title: '资料上架时间',
      dataIndex: 'shelfTime',
      align: 'center',
      render: text => text ?? '-',
    },
    {
      title: '积分类型',
      dataIndex: 'actionTypeName',
      align: 'center',
      render: text => text ?? '-',
    },
    {
      title: '积分数',
      dataIndex: 'integralNum',
      align: 'center',
      render: text => text ?? '-',
    },
    {
      title: '积分获取时间',
      dataIndex: 'createDate',
      align: 'center',
      render: text => text ?? '-',
    },
  ];

  // 积分规则信息
  const ruleColumns = [
    {
      title: '积分类型',
      dataIndex: 'actionTypeName',
      render: text => text ?? '-',
      width: '20%',
      align: 'center',
    },
    {
      title: '积分规则描述',
      dataIndex: 'ruleDesc',
      render: text => <div style={{ whiteSpace: 'pre' }}>{text}</div>,
      width: '40%',
      align: 'center',
    },
    {
      title: '积分数',
      dataIndex: 'integralNum',
      width: '20%',
      align: 'center',
    },
    {
      title: '积分获得者',
      dataIndex: 'ownName',
      render: text => text ?? '-',
      width: '20%',
      align: 'center',
    },
  ];

  return (
    <div>
      <Card
        title="积分管理"
        className="cute"
        extra={(
          <Button
            type="primary"
            onClick={() => {
              setModalVisible(true);
            }}
          >
            积分获取规则
          </Button>
        )}
      >
        <Form labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
          <Row>
            <Col span={6}>
              <Form.Item label="归属地市">
                {getFieldDecorator('regionId')(
                  <Select allowClear onChange={getAreaList}>
                    {Array.isArray(regionList) &&
                      regionList.map(item => (
                        <Select.Option key={item.value} value={item.value}>
                          {item.label}
                        </Select.Option>
                      ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="归属县市">
                {getFieldDecorator('areaId')(
                  <Select allowClear>
                    {Array.isArray(areaList) &&
                      areaList.map(item => (
                        <Select.Option key={item.value} value={item.value}>
                          {item.label}
                        </Select.Option>
                      ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="库类型">
                {getFieldDecorator('knowledgeClassType')(
                  <Select
                    allowClear
                    onChange={value => {
                      setFieldsValue({
                        knowledgeType: undefined,
                      });
                      if (value === undefined) {
                        setCurrentKnowledgeType(knowledgeType);
                      } else {
                        setCurrentKnowledgeType(knowledgeType.filter(item => item.desc === value));
                      }
                    }}
                  >
                    {knowledgeClassType.map(item => (
                      <Select.Option key={item.value} value={item.value}>
                        {item.name}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="资料类型">
                {getFieldDecorator('knowledgeType')(
                  <Select allowClear>
                    {currentKnowledgeType.map(item => (
                      <Select.Option key={item.value} value={item.value}>
                        {item.name}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={6}>
              <Form.Item label="用户名称">{getFieldDecorator('userName')(<Input />)}</Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="用户工号">{getFieldDecorator('userCode')(<Input />)}</Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="资料编码">{getFieldDecorator('knowledgeNbr')(<Input />)}</Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="资料名称">{getFieldDecorator('knowledgeName')(<Input />)}</Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={6}>
              <Form.Item label="积分类型">
                {getFieldDecorator('actionType')(
                  <Select>
                    <Select.Option key="1000" value="1000">
                      资料下载
                    </Select.Option>
                    <Select.Option key="1100" value="1100">
                      资料浏览
                    </Select.Option>
                    <Select.Option key="1200" value="1200">
                      资料上架
                    </Select.Option>
                    <Select.Option key="1300" value="1300">
                      资料学习
                    </Select.Option>
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="资料上架时间" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                {getFieldDecorator('shelfTime')(<DatePicker.RangePicker showTime style={{ width: '100%' }} />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="积分获取时间" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                {getFieldDecorator('createTime')(<DatePicker.RangePicker showTime style={{ width: '100%' }} />)}
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <div className={styles.buttonRow}>
          <Button type="primary" onClick={submit}>
            查询
          </Button>
          <Button
            onClick={() => {
              reset();
              setCurrentKnowledgeType(knowledgeType);
            }}
          >
            重置
          </Button>
        </div>
        <SlickTable
          {...restTableProps}
          data={{
            pagination,
          }}
          columns={columns}
          onRow={record => ({
            onDoubleClick: () => {
              const typeNameList = Object.keys(TYPE_VALUE);
              setDetailInfo({
                visible: true,
                knowledgeInfoId: record.knowledgeInfoId,
                type: typeNameList.find(item => TYPE_VALUE[item] === record.knowledgeClassType),
              });
            },
          })}
          scroll={{ x: 'max-content' }}
        />
        <Modal
          title="积分规则信息"
          visible={modalVisible}
          onCancel={() => {
            setModalVisible(false);
          }}
          footer={null}
          width="1000px"
        >
          <Table rowKey="actionTypeName" bordered pagination={false} columns={ruleColumns} dataSource={data} />
        </Modal>

        <DetailInfo
          {...detailInfo}
          onCancel={() => {
            setDetailInfo({
              visible: false,
            });
          }}
        />
      </Card>
    </div>
  );
};

export default connect(({ librarys }) => ({
  attrs: librarys.attrs,
}))(Form.create()(IntegralManage));
