import React from 'react';
import { Card, Row, Col, Space, Typography } from 'antd';
import { FrameIcon } from './menuIcons';

const { Title } = Typography;

/**
 * 图标使用示例
 * 展示如何使用 FrameIcon 组件
 */
const IconExample = () => {
  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>FrameIcon 使用示例</Title>
      
      {/* 基础使用 */}
      <Card title="1. 基础使用" style={{ marginBottom: 16 }}>
        <Space size="large">
          <FrameIcon index={0} />
          <FrameIcon index={1} />
          <FrameIcon index={2} />
          <FrameIcon index={3} />
        </Space>
      </Card>

      {/* 不同尺寸 */}
      <Card title="2. 不同尺寸" style={{ marginBottom: 16 }}>
        <Space size="large" align="center">
          <FrameIcon index={0} style={{ width: 12, height: 12 }} />
          <FrameIcon index={0} style={{ width: 16, height: 16 }} />
          <FrameIcon index={0} style={{ width: 20, height: 20 }} />
          <FrameIcon index={0} style={{ width: 24, height: 24 }} />
          <FrameIcon index={0} style={{ width: 32, height: 32 }} />
        </Space>
        <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
          12px / 16px / 20px / 24px / 32px
        </div>
      </Card>

      {/* 所有图标展示 */}
      <Card title="3. 所有图标 (index 0-17)" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          {Array.from({ length: 18 }, (_, index) => (
            <Col key={index} span={4} style={{ textAlign: 'center' }}>
              <div style={{ padding: 16, border: '1px solid #f0f0f0', borderRadius: 4 }}>
                <FrameIcon index={index} style={{ width: 24, height: 24 }} />
                <div style={{ marginTop: 8, fontSize: 12 }}>index: {index}</div>
              </div>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 在菜单中使用 */}
      <Card title="4. 在菜单中使用" style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', padding: 8, border: '1px solid #d9d9d9', borderRadius: 4 }}>
          <FrameIcon index={0} style={{ marginRight: 8 }} />
          <span>菜单项示例</span>
        </div>
      </Card>

      {/* 使用代码示例 */}
      <Card title="5. 使用代码示例">
        <pre style={{ background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
{`import { FrameIcon } from './menuIcons';

// 基础使用
<FrameIcon index={0} />

// 自定义样式
<FrameIcon index={1} style={{ width: 24, height: 24, marginRight: 8 }} />

// 在菜单中使用
<Menu.SubMenu
  title={(
    <span>
      <FrameIcon index={0} style={{ marginRight: 8 }} />
      <span>菜单项</span>
    </span>
  )}
>

// 遍历显示所有图标
{Array.from({ length: 18 }, (_, index) => (
  <FrameIcon key={index} index={index} />
))}`}
        </pre>
      </Card>
    </div>
  );
};

export default IconExample;
