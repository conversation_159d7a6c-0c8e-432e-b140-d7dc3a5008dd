import React, { useEffect } from 'react';
import { connect } from 'dva';
import { Spin } from 'antd';
import { Element } from 'react-scroll';
import { scrollToBottom } from '@/utils/scroller';
import MessageItem from './MessageItem';
import styles from '../../index.less';

const ChatMessage = ({ message, dispatch }) => {
  const { messageList = [], loginId, curUser, hasMoreMessage, pageNum, messageLoading } = message;

  useEffect(() => {
    scrollToBottom('bottomElement', 'chatItems');
  }, []);

  const onChatItemScroll = e => {
    if (!hasMoreMessage) return;

    if (e.wheelDelta > 0) {
      const chatItems = document.getElementById('chatItems');
      if (chatItems && chatItems.scrollTop === 0) {
        // 获取当前聊天对象聊天记录
        dispatch({ type: 'message/getMessageList', payload: { pageNum: pageNum + 1 } });
      }
      console.log('onChatItemScroll chat :: ', pageNum, hasMoreMessage);
    }
  };

  const addChatItemScrollListener = () => {
    const chatItems = document.getElementById('chatItems');
    chatItems && chatItems.addEventListener('mousewheel', onChatItemScroll, false);
  };

  const removeChatItemScrollListener = () => {
    const chatItems = document.getElementById('chatItems');
    chatItems && chatItems.removeEventListener('mousewheel', onChatItemScroll, false);
  };

  useEffect(() => {
    addChatItemScrollListener();
    return () => {
      removeChatItemScrollListener();
    };
  }, [pageNum, hasMoreMessage]);

  return (
    <div className={styles.chatContainer}>
      <div id="chatItems" className={styles.chatItems}>
        <Spin spinning={messageLoading}>
          {/* {
              !hasMoreMessage && (
                <div className={styles.noMore}>无更多消息</div>
              )
            } */}
          {messageList.length > 0 &&
              messageList.map(item => (
                // 判断发送者不是当前登录用户Id为接收者
                // 显示接受者视图
                // item.sendId !== userId
                <div className={styles.chatItem} key={item.instantMessageId || item.uuid}>
                  <MessageItem isSender={item.sendId === loginId} curUser={curUser} msgInfo={item} />
                </div>
              ))
            }
          <Element name="bottomElement" />
        </Spin>
      </div>
    </div>
  );
};
export default connect(({ message }) => ({
  message,
}))(ChatMessage);
