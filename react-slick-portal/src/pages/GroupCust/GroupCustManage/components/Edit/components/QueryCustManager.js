/* 客户经理查询 */
import React, { useEffect, useState } from 'react';
import { Button, Col, Descriptions, Form, Icon, Input, message, Modal, Row, Select } from 'antd';
import { useFormTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import { queryDisticts, queryQxArea, queryCustomerManager } from '@/pages/GroupCust/GroupCustManage/services';
import withPopover from '@/components/HOC/withPopover';

// 获取客户经理列表
// eslint-disable-next-line no-unused-vars
const getTableData = ({ current, pageSize }, formData) =>
  queryCustomerManager({
    ...formData,
  }).then(res => {
    const { resultCode, resultObject, resultMsg } = res;
    if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
      return {
        total: parseInt(resultObject?.rspParam?.pageInfo?.recordCount, 10),
        list: resultObject?.rspParam?.busiInfo?.outData,
      };
    }
    message.error(resultMsg);
    return {
      total: 0,
      list: [],
    };
  });
const { Option } = Select;
const QueryCustManager = props => {
  const { form, value, handleSelectRow, customerInfo } = props;

  const [visible, setVisible] = useState(false);
  const { getFieldDecorator } = form;
  const [selectRows, setSelectRows] = useState([]);
  const [inputValue, setInputValue] = useState('');

  const [disInfos, setDisInfos] = useState([]);
  const [countyInfos, setCountyInfos] = useState(null);
  // const [regionDetails, setRegionDetails] = useState([]);
  // const [orgs, setOrgs] = useState([]);
  const {
    tableProps,
    search: { submit, reset },
  } = useFormTable(getTableData, {
    defaultPageSize: 10,
    form,
    manual: true,
  });
  const { pagination, ...restTableProps } = tableProps;


  // 关闭弹窗
  const handleCancel = () => {
    setVisible(false);
  };

  // 打开弹窗
  const showModal = () => {
    setVisible(true);
  };

  // 处理选择确认
  const handleSelect = () => {
    if (selectRows.length > 0) {
      const selectedData = selectRows[0];
      setInputValue(selectedData.STAFF_NAME);
      setVisible(false);
      handleSelectRow(selectedData);
    } else {
      message.warning('请选择一条数据');
    }
  };


  // 获取地市
  const getDisInfo = async () => {
    const resp = await queryDisticts();
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.resultInfo) {
      setDisInfos(resp.resultObject.rspParam.busiInfo.resultInfo);
    }
  };
  // 获取区县
  const queryCounty = val => {
    queryQxArea(val).then(resp => {
      if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.resultInfo) {
        setCountyInfos(resp.resultObject.rspParam.busiInfo.resultInfo);
      }
    });
  };
  // 获取片区
  // const queryRegionDetails = val => {
  //   getRegionDetail(val).then(() => {
  //     setRegionDetails([]);
  //   });
  // };
  // 获取部门
  // const queryOrgs = () => {
  //   setOrgs([]);
  // };
  useEffect(() => {
    getDisInfo();
    // queryOrgs();
  }, []);

  // 更新输入值
  useEffect(() => {
    if (value?.STAFF_NAME) {
      setInputValue(value.STAFF_NAME);
    }
  }, [value]);

  // 自定义渲染Popover内容的函数
  const renderCustomContent = renderProps => {
    // 使用传入的value或组件内部的value
    const displayValue = renderProps.customerInfo || value;

    // console.log('props', renderProps, value);
    return (
      <div style={{ width: 300 }}>
        <Descriptions column={1}>
          <Descriptions.Item label="姓名">{displayValue?.STAFF_NAME}</Descriptions.Item>
          <Descriptions.Item label="工号">{displayValue?.CUST_MGR_ID}</Descriptions.Item>
          <Descriptions.Item label="手机号码">{displayValue?.BILL_ID}</Descriptions.Item>
          <Descriptions.Item label="电子邮件">{displayValue?.EMAIL}</Descriptions.Item>
          <Descriptions.Item label="办公电话">{displayValue?.OFFICE_TEL}</Descriptions.Item>
        </Descriptions>
      </div>
    );
  };

  const InputWithCustomPopover = withPopover(Input, {
    renderContent: renderCustomContent,
    popoverProps: {
      placement: 'top',
      title: '客户经理',
    },
  });

  return (
    <>
      <InputWithCustomPopover
        value={inputValue}
        onClick={showModal}
        readOnly
        className={props.className}
        placeholder="请选择客户经理"
        addonAfter={<Icon type="search" onClick={showModal} />}
        customerInfo={customerInfo}
      />
      <Modal title="客户经理查询" destroyOnClose width="60%" visible={visible} onCancel={handleCancel} onOk={() => handleSelect()}>
        <div>
          <Form className="flow fix-label" onSubmit={submit}>
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item label="经理编号">{getFieldDecorator('custMgrId', {})(<Input placeholder="请输入经理编号" />)}</Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="经理姓名">{getFieldDecorator('staffName', {})(<Input placeholder="请输入经理姓名" />)}</Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="手机号码">
                  {getFieldDecorator('billId', {
                    rules: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }],
                  })(<Input placeholder="请输入手机号码" maxLength={11} />)}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="所属地市">
                  {getFieldDecorator('mgmtDistrict', {
                    rules: [{ required: false }],
                  })(
                    <Select placeholder="请选择所属地市" onChange={val => queryCounty(val, 'custCityPart')}>
                      {disInfos &&
                        disInfos.map(item => (
                          <Option key={item.REGION_ID} value={item.REGION_ID}>
                            {item.DISTRICT_NAME}
                          </Option>
                        ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="所属区县">
                  {getFieldDecorator('mgmtCounty', {
                    rules: [{ required: false }],
                  })(
                    <Select placeholder="请选择所属区县">
                      {countyInfos &&
                        countyInfos.map(item => (
                          <Option key={item.REGION_ID} value={item.REGION_ID}>
                            {item.DISTRICT_NAME}
                          </Option>
                        ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              {/* <Col span={8}>
                <Form.Item label="所属片区">
                  {getFieldDecorator('CUSTMGR_REGION_DETAIL', {
                    rules: [{ required: false }],
                  })(
                    <Select placeholder="请选择所属片区">
                      {regionDetails &&
                        regionDetails.map(item => (
                          <Option key={item.REGION_ID} value={item.REGION_ID}>
                            {item.DISTRICT_NAME}
                          </Option>
                        ))}
                    </Select>
                  )}
                </Form.Item>
              </Col> */}

              {/* <Col span={8}>
                <Form.Item label="所属部门">
                  {getFieldDecorator(
                    'deptId',
                    {}
                  )(
                    <Select placeholder="请选择所属部门">
                      {orgs &&
                        orgs.map(item => (
                          <Option key={item.REGION_ID} value={item.REGION_ID}>
                            {item.DISTRICT_NAME}
                          </Option>
                        ))}
                    </Select>
                  )}
                </Form.Item>
              </Col> */}
              <Col span={8} offset={16} className="text-right">
                <Button type="primary" htmlType="submit" loading={tableProps.loading}>
                  查询
                </Button>
                <Button className="margin-left" onClick={reset}>
                  重置
                </Button>
              </Col>
            </Row>
          </Form>
          <SlickTable
            style={{ marginTop: 20 }}
            pick="radio"
            onSelectRow={row => setSelectRows(row)}
            selectedRows={selectRows}
            rowKey={record => record.CUST_MGR_ID}
            {...restTableProps}
            data={{
              pagination: {
                ...pagination,
                pageSize: 10,
              },
            }}
            columns={[
              {
                title: '经理编号',
                dataIndex: 'CUST_MGR_ID',
                ellipsis: true,
              },
              {
                title: '经理姓名',
                dataIndex: 'STAFF_NAME',
                ellipsis: true,
              },
              {
                title: '手机号码',
                ellipsis: true,
                dataIndex: 'BILL_ID',
              },
              // {
              //   title: '所属部门',
              //   ellipsis: true,
              //   dataIndex: 'DEPT_ID',
              // },
              // {
              //   title: '地市',
              //   ellipsis: true,
              //   dataIndex: 'MGMT_DISTRICT',
              //   render: (text, record) => (),
              // },
              // {
              //   title: '区县',
              //   ellipsis: true,
              //   dataIndex: 'MGMT_COUNTY',
              // },
              // {
              //   title: '片区',
              //   ellipsis: true,
              //   dataIndex: 'REGION_DETAIL',
              // },
              // {
              //   title: '办公电话',
              //   ellipsis: true,
              //   dataIndex: 'OFFICE_TEL',
              // },
              {
                title: '邮件地址',
                ellipsis: true,
                dataIndex: 'EMAIL',
              },
            ]}
          />
        </div>
      </Modal>
    </>
  );
};
export default Form.create()(QueryCustManager);
