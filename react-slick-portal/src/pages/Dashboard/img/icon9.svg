<?xml version="1.0" encoding="UTF-8"?>
<svg width="30px" height="30px" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/添加快捷</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="30" height="30"></rect>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="政企智慧中台-工作台-线性图标" transform="translate(-1065.000000, -584.000000)">
            <g id="编组-15" transform="translate(962.000000, 502.000000)">
                <g id="编组-19备份-4" transform="translate(90.000000, 82.000000)">
                    <g id="icon/添加快捷" transform="translate(13.000000, 0.000000)">
                        <mask id="mask-2" fill="white">
                            <use xlink:href="#path-1"></use>
                        </mask>
                        <g id="矩形"></g>
                        <path d="M15.75,7.5 C15.8880712,7.5 16,7.61192881 16,7.75 L16,14 L22.25,14 C22.3880712,14 22.5,14.1119288 22.5,14.25 L22.5,15.75 C22.5,15.8880712 22.3880712,16 22.25,16 L15.999875,16 L16,22.25 C16,22.3880712 15.8880712,22.5 15.75,22.5 L14.25,22.5 C14.1119288,22.5 14,22.3880712 14,22.25 L13.999875,16 L7.75,16 C7.61192881,16 7.5,15.8880712 7.5,15.75 L7.5,14.25 C7.5,14.1119288 7.61192881,14 7.75,14 L14,14 L14,7.75 C14,7.61192881 14.1119288,7.5 14.25,7.5 L15.75,7.5 Z" id="路径" fill="#696B76" fill-rule="nonzero" mask="url(#mask-2)"></path>
                        <path d="M15,1 C22.7319865,1 29,7.2680135 29,15 C29,22.7319865 22.7319865,29 15,29 C7.2680135,29 1,22.7319865 1,15 C1,7.2680135 7.2680135,1 15,1 Z M15,3 C8.372583,3 3,8.372583 3,15 C3,21.627417 8.372583,27 15,27 C21.627417,27 27,21.627417 27,15 C27,8.372583 21.627417,3 15,3 Z" id="椭圆形" fill="#696B76" fill-rule="nonzero" mask="url(#mask-2)"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>