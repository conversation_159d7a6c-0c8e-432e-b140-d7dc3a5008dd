import React, { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Card, Col, Divider, Form, Input, message, Row, Select } from 'antd';
import { connect } from 'dva';
import { useFormTable } from '@umijs/hooks';
import moment from 'moment';
import SlickTable from '@/components/SlickTable';
import { exportExcelFile, getItem, getPageSizeByCardHeight } from '@/utils/utils';
import CustomSpace from '@/components/CustomSpace';
import { queryFields } from '@/pages/GroupCust/GroupCustRedListManage/const';
import { deleteRedListInfo, queryRedListInfo } from '@/pages/GroupCust/GroupCustRedListManage/services';
import { queryCommonStaticDataEnumMap } from '@/services/commonStaticData';
import VaultApprovalPop from '@/components/VaultApprovalPop';
import { checkVaultValidation } from '@/services/vaultValidation';

const cellStyle = {
  maxWidth: 200,
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  cursor: 'pointer',
};

// 数据查询函数
const fetchRedListData = async params => {
  const { key, value, groupRedListQueryType, current, pageSize } = params;
  return queryRedListInfo({
    [key]: value,
    groupRedListQueryType,
    pageFlag: 1,
    pageInfo: {
      currentPage: current,
      pageSize,
    },
  }).then(res => {
    const { resultCode, resultObject, resultMsg } = res;
    if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
      return {
        total: resultObject?.rspParam?.busiInfo?.outData.length,
        list: resultObject?.rspParam?.busiInfo?.outData,
      };
    }
    message.error(resultMsg);
    return {
      total: 0,
      list: [],
    };
  });
};

const getTableData = ({ current, pageSize }, formData) => {
  const { groupRedListQueryType, groupId, groupName } = formData;
  let value = '';
  let key = '';
  switch (groupRedListQueryType) {
    case 'groupName':
      value = groupName;
      key = 'groupName';
      break;
    case 'groupId':
      value = groupId;
      key = 'groupId';
      if (!value) {
        return {
          total: 0,
          list: [],
        };
      }
      break;
    case 'currentRegion':
      key = 'regionId';
      value = getItem('user')?.staffInfo?.regionId;
      break;
    case 'allRegion':
      key = 'regionId';
      value = '';
      break;
    default:
      return {
        total: 0,
        list: [],
      };
  }
  return fetchRedListData({
    key,
    value,
    groupRedListQueryType,
    current,
    pageSize,
  });
};

const Index = ({ size: { height }, form, dispatch, vaultApproval, ...otherProps }) => {
  const { goToStep } = otherProps;
  const [exportLoading, setExportLoading] = useState(false);
  const tableRef = useRef(null);
  // card-body除rowHeight
  const [size, setSize] = useState(getPageSizeByCardHeight(height));
  const { getFieldDecorator } = form;

  const [groupHalfLevel, setGroupHalfLevel] = useState([]);
  const [creditLevel, setCreditLevel] = useState([]);
  const [redFlag, setRedFlag] = useState([]);
  // const [applyStatus, setApplyStatus] = useState([]);

  useEffect(() => {
    const fetchEnumGroupHalfLevelMap = async () => {
      const result = await queryCommonStaticDataEnumMap('GROUP_LEVEL');
      setGroupHalfLevel(result);
    };
    fetchEnumGroupHalfLevelMap();
    const fetchEnumCreditLevelMap = async () => {
      const result = await queryCommonStaticDataEnumMap('CREDIT_LEVEL');
      setCreditLevel(result);
    };
    fetchEnumCreditLevelMap();
    const fetchEnumRedFlagMap = async () => {
      const result = await queryCommonStaticDataEnumMap('RED_FLAG');
      setRedFlag(result);
    };
    fetchEnumRedFlagMap();
    // const fetchEnumApplyStatusMap = async () => {
    //   const result = await queryCommonStaticDataEnumMap('APPLY_STATE');
    //   setApplyStatus(result);
    // };
    // fetchEnumApplyStatusMap();
  }, []);


  /**
   * 编辑 新增 详情
   * @param targetRow
   * @param viewMode view 详情 edit 编辑 add 新增
   */
  const openDrawer = (targetRow, viewMode) => {
    goToStep(2, {
      groupCode: targetRow.groupCode,
      viewMode,
      groupName: targetRow.GROUP_NAME,
    });
  };

  const handleRemove = record => {
    deleteRedListInfo({ groupId: record.GROUP_ID }).then(res => {
      const { resultCode, resultMsg } = res;
      if (resultCode === 'TRUE') {
        message.success('删除成功');
        // eslint-disable-next-line
        refresh();
      } else {
        message.error(resultMsg);
      }
    });
  };

  const handleSubmit = () => {
    // eslint-disable-next-line consistent-return
    form.validateFields(async (err, values) => {
      if (!err) {
        const { groupRedListQueryType } = values;
        if (groupRedListQueryType === 'currentRegion' || groupRedListQueryType === 'allRegion') {
          let params = {};
          if (groupRedListQueryType === 'currentRegion') {
            params = {
              ACT_URL: 'GroupRedListCity?action=query',
            };
          } else if (groupRedListQueryType === 'allRegion') {
            params = {
              ACT_URL: 'GroupRedListGlobal?action=query',
            };
          }
          const isValidated = await checkVaultValidation(dispatch, 'queryRedList', params);
          if (isValidated) {
            // eslint-disable-next-line
            submit();
          }
        } else {
          // eslint-disable-next-line
          submit();
        }
      }
    });
  };

  /**
   * 导出
   */
  const handleExport = () => {
    form.validateFields((err, values) => {
      if (!err) {
        const { groupRedListQueryType, groupName, groupId } = values;
        let value = '';
        let key = '';
        switch (groupRedListQueryType) {
          case 'groupName':
            value = groupName;
            key = 'groupName';
            break;
          case 'groupId':
            value = groupId;
            key = 'groupId';
            break;
          case 'currentRegion':
            key = 'regionId';
            value = getItem('user')?.staffInfo?.regionId;
            break;
          case 'allRegion':
            key = 'regionId';
            value = '';
            break;
          default:
            return {
              total: 0,
              list: [],
            };
        }
        exportExcelFile({
          url: 'portal/GroupRedListController/exportRedlistExcelFile.do',
          title: `${moment().format('YYYY-MM-DD HH-mm-ss')}`,
          sendParams: {
            [key]: value,
            groupRedListQueryType,
            pageFlag: 1,
            pageInfo: {
              currentPage: 1,
              pageSize: 10000,
            },
          },
          setLoading: setExportLoading,
        });
      }
      return null;
    });
  };

  const renderMoreConditions2 = () => {
    const groupRedListQueryType = form.getFieldValue('groupRedListQueryType');
    switch (groupRedListQueryType) {
      case 'groupName':
        /* 查询条件-集团名称 */
        return (
          <>
            <Col span={6}>
              <Form.Item label="集团名称">
                {getFieldDecorator('groupName', {
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
          </>
        );
      case 'groupId':
        /* 查询条件-集团编码 */
        return (
          <>
            <Col span={6}>
              <Form.Item label="集团编码">
                {getFieldDecorator('groupId', {
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
          </>
        );
      default:
        return (
          <>
            <Col span={6} />
          </>
        );
    }
  };

  useEffect(() => {
    renderMoreConditions2();
  }, [form.getFieldValue('groupRedListQueryType')]);

  // 根据size.height 变化即时更新pageSize
  useEffect(() => {
    setSize(getPageSizeByCardHeight(height));
  }, [height]);

  const {
    tableProps,
    refresh,
    search: { submit, reset },
  } = useFormTable(getTableData, {
    defaultPageSize: size,
    form,
    manual: true,
  });

  const handleVaultSuccess = (action, vaultIsPass) => {
    if (action === 'queryRedList') {
      if (vaultIsPass) {
        // 金库验证通过后，获取当前表单值
        form.validateFields(err => {
          if (!err) {
            submit();
          }
        });
      }
    }
  };

  return (
    <>
      <Card title="集团红名单管理" className="cute" style={{ minHeight: height }} bordered>
        <Form className="flow fix-label">
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item label="查询类型">
                {getFieldDecorator('groupRedListQueryType', {
                  initialValue: 'groupId', // 默认查询集团编码
                })(
                  <Select placeholder="请选择">
                    {queryFields.map(({ disable, key, label }) => (
                      <Select.Option disabled={disable} key={key} value={key}>
                        {label}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            {renderMoreConditions2()}
            <Col span={12} className="text-right">
              <Button type="primary" onClick={handleSubmit} loading={tableProps.loading}>
                查询
              </Button>
              <Button className="margin-left" onClick={reset}>
                重置
              </Button>
              <Button disabled={exportLoading} loading={exportLoading} type="default" className="inline-block margin-left" onClick={handleExport}>
                导出
              </Button>
              <Button type="primary" className="margin-left" onClick={() => openDrawer({}, 'add')}>
                新增
              </Button>
            </Col>
          </Row>
        </Form>
        <SlickTable
          style={{ marginTop: 20 }}
          ref={tableRef}
          scroll={{ x: 1300 }}
          rowKey={record => record.GROUP_ID}
          {...tableProps}
          data={{
            pagination: {
              ...tableProps.pagination,
              pageSize: size,
            },
          }}
          columns={[
            {
              title: '集团编码',
              dataIndex: 'GROUP_ID',
              key: 'groupId',
              width: 150,
            },
            {
              title: '集团名称',
              dataIndex: 'GROUP_NAME',
              key: 'groupName',
              width: 200,
              onCell: () => ({
                style: cellStyle,
              }),
            },
            {
              title: '集团级别',
              dataIndex: 'GROUP_LEVEL_NEW',
              key: 'groupLevelNew',
              width: 120,
              render: (_, record) => (
                <span>{groupHalfLevel[record.GROUP_LEVEL_NEW] || `${record.GROUP_LEVEL_NEW}级`}</span>
              ),
            },
            {
              title: '客户信用等级',
              dataIndex: 'CREDIT_LEVEL',
              key: 'creditLevel',
              width: 150,
              render: (_, record) => <span>{creditLevel[record.CREDIT_LEVEL] || `${record.CREDIT_LEVEL}`}</span>,
            },
            {
              title: '客户类型',
              dataIndex: 'RED_FLAG',
              key: 'groupLevel',
              width: 120,
              render: (_, record) => <span>{redFlag[record.RED_FLAG] || `${record.RED_FLAG}`}</span>,
            },
            {
              title: '申请时间',
              dataIndex: 'CREATE_DATE',
              key: 'create_date',
              width: 120,
            },
            {
              title: '申请人工号',
              dataIndex: 'APPLY_OP_ID',
              key: 'applyOpId',
              width: 120,
            },
            {
              title: '申请人名称',
              dataIndex: 'APPLY_OP_NAME',
              key: 'applyOpName',
              width: 120,
            },
            {
              title: '红名单生效时间',
              dataIndex: 'CREATE_DATE',
              key: 'effective_date',
              width: 120,
            },
            {
              title: '红名单失效时间',
              dataIndex: 'EXPIRE_DATE',
              key: 'expire_date',
              width: 120,
            },
            {
              title: '操作',
              align: 'center',
              key: 'action',
              width: 120,
              fixed: 'right',
              render: (_, record) => {
                const buttons = [<a onClick={() => handleRemove(record)}>删除红名单</a>];
                return (
                  <CustomSpace align="center" wrap split={<Divider type="vertical" />}>
                    {buttons.map(item => item)}
                  </CustomSpace>
                );
              },
            },
          ]}
        />
      </Card>
      <VaultApprovalPop onVaultSuccess={(actionName, vaultIsPass) => handleVaultSuccess(actionName, vaultIsPass)} />
    </>
  );
};

export default connect(({ setting, vaultApproval }) => ({
  size: setting.size,
  vaultApproval,
}))(Form.create()(Index));
