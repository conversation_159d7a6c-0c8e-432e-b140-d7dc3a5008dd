import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, message, Form, Row, Col, Cascader, Tag, Icon } from 'antd';
import { connect } from 'dva';
import debounce from 'lodash/debounce';
import { useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import request from '@/utils/request';
import { openWebSocket } from '@/utils/websocket';
import AreaSelect from '@/components/AreaSelect';
import ComboGrid from '@/components/ComboGrid';

const initial = {
  visible: true,
  dataSource: [],
  loading: false,
};

function reducer(state, action) {
  const { type, payload } = action;

  if (type === 'toggleModal') {
    const next = !state.visible;

    if (next === false) {
      return false;
    }
    return {
      ...state,
      visible: !state.visible,
    };
  }

  if (type === 'updateTable') {
    return {
      ...state,
      loading: false,
      dataSource: payload?.dataSource,
    };
  }

  if (type === 'toggleLoading') {
    return {
      ...state,
      loading: !state.loading,
    };
  }

  if (type === 'reset') {
    return initial;
  }

  throw Error();
}

function AddressBook({ children, form }) {
  const { getFieldDecorator } = form;
  const [state, dispatch] = React.useReducer(reducer, initial);
  const [paramsObj, setParamsObj] = useState({});
  const [selectLabelItems, setSelectLabelItems] = useState([]);
  const [labelOptions, setLabelOptions] = useState([]);

  const ref = React.useRef('');
  const cloned = React.cloneElement(children, { onClick: () => dispatch({ type: 'toggleModal' }) });

  const clickMessage = record => {
    const userObj = {
      userId: record.sysUserId,
      userCode: record.sysUserCode,
      userName: record.userName,
      type: 'privateChat',
    };
    // 打开聊天窗口
    openWebSocket(userObj);
  };

  // 获取标签选择数据
  const getLabelData = async () => {
    const result = await request('orgauth/LabelsController/getLabelTree.do', {});
    if (result.success) {
      if (result.resultObject) {
        const labelData = Object.keys(result.resultObject).map(labelName => ({
          label: labelName,
          value: labelName,
          children: result.resultObject[labelName] || [],
        }));
        setLabelOptions(labelData);
      }
    } else {
      message.error(result.resultMsg);
    }
  };

  const getTableData = async ({ paramsObj }) => {
    if (paramsObj && (paramsObj.roleId || paramsObj.regionId || paramsObj.labelSubcategoryCodes || paramsObj.params)) {
      dispatch({ type: 'toggleLoading' });
      const res = await request('orgauth/SystemUserController/getUserInfoList.do', {
        data: {
          ...paramsObj,
        },
      });
      if (res) {
        if (Array.isArray(res?.resultObject)) {
          dispatch({ type: 'updateTable', payload: { dataSource: res.resultObject } });
        } else {
          dispatch({ type: 'updateTable', payload: { dataSource: [] } });
        }
      }
    } else {
      // message.info('请选择条件后查询');
    }
  };

  const { tableProps } = useAntdTable(params => getTableData({ ...params, paramsObj }), [paramsObj], {
    form,
  });

  const submit = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      const newFormValues = fieldsValue;
      newFormValues.roleId = newFormValues.roleId?.length > 0 ? newFormValues.roleId[0].id : undefined;
      newFormValues.regionId = newFormValues.regionId?.id || undefined;
      newFormValues.labelSubcategoryCodes = selectLabelItems.length > 0 ? selectLabelItems.map(item => item.value) : undefined;
      setParamsObj(newFormValues);
    });
  };

  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 8 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 16 },
    },
  };

  // const onChange = record => {
  //   form.setFieldsValue({ regionId: record.commonRegionId });
  // };

  // 标签改变
  const changeLabel = (value, selectedOptions) => {
    const curValue = selectedOptions[1];
    if (!selectLabelItems.includes(curValue)) {
      setSelectLabelItems([...selectLabelItems, curValue]);
    }
  };
  // 删除标签
  const deleteLabel = index => {
    const labels = [...selectLabelItems];
    labels.splice(index, 1);
    setSelectLabelItems(labels);
  };

  const handleReset = () => {
    form.resetFields(['roleId', 'regionId', 'params', 'subcategoryCode', 'filterVal']);
    setSelectLabelItems([]);
    dispatch({ type: 'updateTable', payload: { dataSource: [] } });
  };

  const update = debounce(
    value => {
      ref.current = value;
    },
    300,
    {}
  );

  useEffect(() => {
    getLabelData();
  }, []);

  return (
    <>
      {cloned}
      <Modal
        onCancel={() => {
          setSelectLabelItems([]);
          dispatch({ type: 'toggleModal' });
        }}
        title={<span className="bold">通讯录</span>}
        visible={state.visible}
        width={900}
        destroyOnClose
        footer={null}
      >
        <Form>
          <Row>
            <Col span={4}>
              <Form.Item label="区域" {...formItemLayout}>
                {getFieldDecorator('regionId')(<AreaSelect placeholder="请选择" style={{ width: 300 }} allowClear />)}
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item label="角色" {...formItemLayout}>
                {getFieldDecorator('roleId')(
                  <ComboGrid
                    allowClear
                    url="orgauth/SystemRolesController/selectSystemRolesGridDataAll.do"
                    popupStyle={{ width: 560 }}
                    placeholder="选择角色"
                    searchPlaceholder="请输入角色名称进行搜索"
                    label="sysRoleName"
                    rowKey="id"
                    pick="radio"
                    params={{ filterCol: 'sysRoleName,sysRoleCode' }}
                    columns={[
                      {
                        title: '角色名称',
                        dataIndex: 'sysRoleName',
                        ellipsis: true,
                      },
                      {
                        title: '角色编码',
                        dataIndex: 'sysRoleCode',
                        ellipsis: true,
                      },
                      {
                        title: '角色类型',
                        ellipsis: true,
                        dataIndex: 'sysRoleTypeName',
                      },
                      {
                        title: '归属系统',
                        ellipsis: true,
                        dataIndex: 'sysCodeName',
                      },
                    ]}
                  />
                )}
              </Form.Item>
            </Col>

            <Col span={4}>
              <Form.Item label="标签" {...formItemLayout}>
                <Cascader onChange={changeLabel} options={labelOptions}>
                  <div
                    style={{
                      padding: '0 6px 0 2px',
                      minHeight: '22px',
                      maxHeight: '50px',
                      overflowY: 'auto',
                      overflowX: 'hidden',
                      border: '1px solid #d9d9d9',
                      borderRadius: '2px',
                    }}
                  >
                    {selectLabelItems.length ? (
                      selectLabelItems.map((item, index) => (
                        <Tag
                          closable
                          key={item.value}
                          // eslint-disable-next-line no-unused-vars
                          onClose={e => {
                            // 删除对应索引元素
                            deleteLabel(index);
                          }}
                        >
                          {item.label}
                        </Tag>
                      ))
                    ) : (
                      <span
                        style={{
                          color: '#bfbfbf',
                          fontSize: '13px',
                          marginLeft: '11px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          height: '22px',
                        }}
                      >
                        选择标签
                      </span>
                    )}
                  </div>
                </Cascader>
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item label="关键字：" {...formItemLayout}>
                {getFieldDecorator('params')(
                  <Input
                    placeholder="姓名/组织名称/手机号/邮箱"
                    onChange={e => update(e.target.value)}
                    style={{ width: 185 }}
                    allowClear
                    // onPressEnter={e => getTableData(e.target.value)}
                  />
                )}
              </Form.Item>
            </Col>
            <Col span={6} className="text-right">
              <Button type="primary" className="margin-left" onClick={submit}>
                查询
              </Button>
              <Button type="default" className="margin-left" onClick={handleReset}>
                重置
              </Button>
            </Col>
            {/* </div> */}
          </Row>
        </Form>
        <SlickTable
          key="sysUserId"
          bordered={false}
          columns={[
            { title: '姓名', dataIndex: 'userName' },
            { title: '组织', dataIndex: 'orgName' },
            { title: '区域', dataIndex: 'regionName' },
            { title: '手机号', dataIndex: 'mobilePhone' },
            { title: '邮箱', dataIndex: 'email' },
            {
              title: '',
              render: record => (
                <Icon
                  type="message"
                  style={{ fontSize: '16px', color: '#08c' }}
                  onClick={() => {
                    clickMessage(record);
                  }}
                />
              ),
            },
          ]}
          loading={tableProps.loading}
          dataSource={state.dataSource}
          pagination={{ pageSize: 5 }}
        />
      </Modal>
    </>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(AddressBook));
