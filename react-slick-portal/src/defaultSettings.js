module.exports = {
  themeConfig: {
    '@ant-prefix': 'ant', // theme主题配置项
  },
  language: 'zh-CN', // 表示默认的语言，暂且支持 'zh-CN' 'en-US'。在baseNavigator=true时失效
  baseNavigator: true, // true 表示用navigator.language的值作为默认语言。优先级比language高，比localStorage内的umi_locale低
  timeout: 1000 * 600, // 1分钟超时
  delay: 300, // mock接口延迟返回的时长，单位毫秒
  theme: 'blue', // 可选值 "default | blue"
  SESSION_PREFIX: 'SLICK_',

  /**
   * 页面风格设置
   */
  gutter: '8', // 容器边距
  loginRedirect: false, // 是否根据?redirect=xxx重定向
  showLeftMenu: false, // 默认开启左侧菜单
  menuDrawerActive: true, // 是否默认展开左侧菜单
  showLeftMenuText: true, // 是否显示左侧边栏文字
  enableWatermark: false, // 开启水印
  enableLanguage: false, // 开启国际化选择器
};
