import React, { useState, useEffect } from 'react';
import { Card } from 'antd';
import dynamic from 'umi/dynamic';
import LoadingComponent from '@/components/PageLoading/index';

// 涉及到的标签组件按需加载
const DesensitizationSceneConf = dynamic({
  loader: () => import('./DesensitizationSceneConf'),
  loading: LoadingComponent,
});
const DesensitizationRuleConf = dynamic({
  loader: () => import('./DesensitizationRuleConf'),
  loading: LoadingComponent,
});

const Tab = props => {
  const [menuKey, setMenuKey] = useState('DesensitizationSceneConf');
  const [contentList, setContentList] = useState({});

  const onTabChange = key => {
    const contentMap = {
      DesensitizationSceneConf: <DesensitizationSceneConf />,
      DesensitizationRuleConf: <DesensitizationRuleConf />,
    };
    if (!contentList[key]) {
      contentList[key] = contentMap[key];
    }
    setContentList(contentList);
    setMenuKey(key);
  };

  useEffect(() => {
    onTabChange('DesensitizationSceneConf');
  }, []);

  useEffect(() => {
    if (props.behavior !== 'disabled') {
      onTabChange('DesensitizationSceneConf');
    }
  }, [props.behavior]);

  const getTabList = () => {
    const res = [
      {
        key: 'DesensitizationSceneConf',
        tab: '脱敏场景信息',
      },
      {
        key: 'DesensitizationRuleConf',
        tab: '关联脱敏规则',
      },
    ];

    return res;
  };

  return (
    <div>
      <Card
        className="gb_tabs_samll"
        style={{ width: '100%' }}
        tabList={getTabList()}
        activeTabKey={menuKey}
        onTabChange={key => {
          onTabChange(key);
        }}
      >
        {Object.keys(contentList).map(key => (
          <div key={key} style={{ display: menuKey === key ? 'block' : 'none' }}>
            {contentList[key]}
          </div>
        ))}
      </Card>
    </div>
  );
};

export default Tab;
