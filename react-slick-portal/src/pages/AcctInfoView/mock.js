import mockjs from 'mockjs';
import { delay } from 'roadhog-api-doc';
import defaultSettings from '@/defaultSettings';

const proxy = {
  'POST /portal/GroupAccountController/qryAccountByQueryType.do': (req, res) => {
    const {
      body: { pageSize, pageNum },
    } = req;
    res.send(
      { resultCode: 'TRUE', resultMsg: '操作成功', resultObject: { rspParam: { busiInfo: { code: '200', result: { acct_list: [{ statusDate: '2020-11-04 09:39:04', acctStatus: '1', mgmtCounty: '7112', GS_FLUX: 0, acctClass: '1', validDate: '2020-11-04 09:39:04', FLUX: 0, orgId: '********', creditLevel: '6', payType: '1', custId: '***************', expireDate: '2099-12-31 23:59:59', creditProfileType: '2', createDate: '2020-11-04 09:39:04', creditScore: '0', doneCode: '0', createOpId: '********', creditProfileId: '***********', LIMIT_FEE: '0', opId: '********', dataStatus: '1', acctId: '***********', acctName: '测*', mgmtDistrict: '471', creditProfileCreDate: '2020-11-04 09:39:04', BALANCE: 0, createOrgId: '********', REAL_FEE: 0, regionId: '471', acctType: '2', doneDate: '2020-11-04 09:39:04' }] } }, pubInfo: { message: 'success', code: '0', reqSerialNo: '**************42135319' } } }, success: true }
    );
  },
};

export default delay(proxy, defaultSettings.delay);
