import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, Input, message } from 'antd';
import { connect } from 'dva';
import PropTypes from 'prop-types';
import findIndex from 'lodash/findIndex';
import SlickTable from '@/components/SlickTable';

/**
 * 从后台获取所有可配置的portlets，同时剔除已配置过的(addedPortlets)
 */
function AddReleMenu({ visible, close, allFunc, onOk }) {
  const [dataSource, setDataSource] = useState(allFunc);
  const modalEl = useRef(null);

  function handleSubmit(e) {
    e.preventDefault();
    if (modalEl.current!=null&&modalEl.current.length === 0) {
      message.warn('别着急，先选中一个模块');
    } else {
      onOk(modalEl.current);
      close();
    }
  }

  function filterDataSource(value) {
    if (value === '') {
      setDataSource(allFunc);
    } else {
      setDataSource(
        dataSource.filter(item =>
          JSON.stringify(item)
            .toLowerCase()
            .includes(value.toLowerCase())
        )
      );
    }
  }

  return (
    <Modal
      title="新增关联模块"
      width={800}
      visible={visible}
      onOk={handleSubmit}
      onCancel={close}
      destroyOnClose
      centered
      okButtonProps={{ htmlType: 'submit' }}
    >
      <div className="margin-bottom ">
        <Input.Search
          allowClear
          placeholder="请输入关键字"
          style={{ width: 260 }}
          onSearch={val => {
            filterDataSource(val);
          }}
        />
      </div>
      <SlickTable
        rowKey={record => record.menuId}
        pick="checkbox"
        onSelectRow={list => {
          modalEl.current = list;
        }}
        dataSource={dataSource}
        columns={[
          { dataIndex: 'menuName', title: '模块名称', width: 200 },
          { dataIndex: 'menuCode', title: '模块编码', width: 200 },
          { dataIndex: 'menuDesc', title: '模块描述' },
        ]}
      />
    </Modal>
  );
}

AddReleMenu.defaultProps = {};

AddReleMenu.propTypes = {
  visible: PropTypes.bool.isRequired,
  allFunc: PropTypes.arrayOf(PropTypes.object).isRequired,
  close: PropTypes.func.isRequired,
  onOk: PropTypes.func.isRequired,
};

export default connect(({ login }) => ({
  login,
}))(Form.create()(AddReleMenu));
