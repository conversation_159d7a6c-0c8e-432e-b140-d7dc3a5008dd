import { But<PERSON>, Card, Col, Divider, Form, Input, message, Row, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { useFormTable } from '@umijs/hooks';
import { connect } from 'dva';
import { GROUP_CARD_TYPE } from '@/utils/consts';
import SlickTable from '@/components/SlickTable';
import { delIdenInfo, queryIdentInfos } from '@/pages/GroupCertManage/services';
import CustomSpace from '@/components/CustomSpace';
import AuditInfo from '@/pages/GroupCust/GroupCustManage/components/Edit/components/AuditInfo';
import { downloadFile, getPageSizeByCardHeight } from '@/utils/utils.js';

const cellStyle = {
  maxWidth: 200,
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
};
// todo: 补充接口
const getTableData = ({ current, pageSize }, formData) =>
  queryIdentInfos({
    ...formData,
    pageFlag: 1,
    pageInfo: {
      currentPage: current,
      pageSize,
    },
  }).then(res => {
    const { resultCode, resultObject, resultMsg } = res;
    if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
      return {
        total: parseInt(resultObject?.rspParam?.pageInfo?.recordCount, 10),
        list: resultObject?.rspParam?.busiInfo?.outData,
      };
    }
    message.error(resultMsg);
    return {
      total: 0,
      list: [],
    };
  });
const List = props => {
  const {
    goToStep,
    form,
    size: { height },
  } = props;
  const { getFieldDecorator } = form;
  const [selectRow, setSelectRow] = useState([]);
  const [auditVis, setAuditVis] = useState(false);

  const [size, setSize] = useState(getPageSizeByCardHeight(height));
  const {
    tableProps,
    search: { submit, reset },
  } = useFormTable(getTableData, {
    defaultPageSize: size,
    form,
    manual: true,
  });
  const { pagination, ...restTableProps } = tableProps;
  const handleDownload = (fileId = '') => {
    const fileIds = fileId.split(',');
    // 去重
    downloadFile(fileId);
  };
  const handleSearch = () => {
    form.validateFields(err => {
      if (!err) {
        submit();
      }
    });
  };

  const handleDelete = audit => {
    delIdenInfo({
      IDEN_ID: selectRow.IDEN_ID,
      AUDIT_STAFF_NAME: audit.AUDIT_STAFF_NAME,
      AUDIT_STAFF_ID: audit.AUDIT_STAFF_ID,
      IDEN_NR: selectRow.IDEN_NR,
      IDEN_TYPE_CODE: selectRow.IDEN_TYPE_CODE,
    }).then(() => {
      setAuditVis(false);
    });
  };
  const handleAddOper = () => {
    goToStep(2, {
      record: {},
      mode: 'add',
    });
  };
  const handleViewOper = row => {
    goToStep(2, {
      record: row,
      mode: 'view',
    });
  };
  const handleModifyOper = row => {
    goToStep(2, {
      record: row,
      mode: 'edit',
    });
  };
  const handleDeleteOper = row => {
    // todo ： 金库
    setSelectRow(row);
    setAuditVis(true);
  };
  // 根据size.height 变化即时更新pageSize
  useEffect(() => {
    setSize(getPageSizeByCardHeight(height));
  }, [height]);
  return (
    <Card className="cust" title="集团证件管理" style={{ minHeight: height }}>
      <Form className="flow fix-label">
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item label="证件类型">
              {getFieldDecorator('idenTypeCode', {
                initialValue: '200002',
                rules: [{ required: true, message: '证件类型不能为空' }],
              })(
                <Select
                  showSearch
                  filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                  placeholder="请选择"
                >
                  {Object.keys(GROUP_CARD_TYPE).map(key => (
                    <Select.Option key={key} value={key}>
                      {GROUP_CARD_TYPE[key]}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="证件号码">
              {getFieldDecorator('idenNr', {
                rules: [{ required: true, message: '证件号码不能为空' }],
              })(<Input allowClear placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="单位名称">{getFieldDecorator('idenName')(<Input allowClear placeholder="请输入" />)}</Form.Item>
          </Col>
          <Col span={6} className="text-right">
            <Button type="primary" onClick={handleSearch} loading={tableProps.loading}>
              查询
            </Button>
            {/* <Button className="margin-left" type="primary" onClick={handleAddOper}>
              新增
            </Button> */}
            <Button className="margin-left" onClick={reset}>
              重置
            </Button>
          </Col>
        </Row>
      </Form>
      <SlickTable
        style={{ marginTop: 20 }}
        rowKey={record => record.IDEN_ID}
        {...restTableProps}
        data={{
          pagination: {
            ...pagination,
            pageSize: 10,
          },
        }}
        columns={[
          {
            title: '证件类型',
            dataIndex: 'IDEN_TYPE_CODE',
            key: 'IDEN_TYPE_CODE',
            render: _ => GROUP_CARD_TYPE?.[_],
          },
          {
            title: '证件号码',
            dataIndex: 'IDEN_NR',
            key: 'idenNr',
          },
          {
            title: '单位名称',
            dataIndex: 'IDEN_NAME',
            key: 'idenName',
            onCell: () => ({
              style: cellStyle,
            }),
          },
          {
            title: '单位地址',
            dataIndex: 'IDEN_ADDRESS',
            key: 'idenAddress',
            onCell: () => ({
              style: cellStyle,
            }),
          },
          {
            title: '在线比对核验状态',
            dataIndex: 'COMPARE_STATUS_STR',
            key: 'compareStatusStr',
            render: (text, record) => {
              if (['200009', '200010'].includes(record.IDEN_TYPE_CODE)) {
                return text;
              }
              return null;
            },
          },
          {
            title: '是否使用',
            dataIndex: 'IS_USED',
            key: 'isUsed',
            render: text => (text === '1' ? '是' : '否'),
          },

          {
            title: '附件',
            dataIndex: 'SCAN',
            key: 'scan',
            render: text => (
              <Button type="link" onClick={() => handleDownload}>
                {text}
              </Button>
            ),
          },
          {
            title: '操作',
            align: 'center',
            key: 'action',
            width: 180,
            fixed: 'right',
            render: (_, record) => {
              const buttons = [
                <a onClick={() => handleViewOper(record)}>查看</a>,
                <a onClick={() => handleModifyOper(record)}>修改</a>,
                <a onClick={() => handleDeleteOper(record)}>删除</a>,
              ];
              return (
                <CustomSpace align="center" wrap split={<Divider type="vertical" />}>
                  {buttons.map(item => item)}
                </CustomSpace>
              );
            },
          },
        ]}
      />
      {/* 删除审核弹窗 */}
      {auditVis && <AuditInfo visible={auditVis} onOK={handleDelete} />}
    </Card>
  );
};

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(List));
