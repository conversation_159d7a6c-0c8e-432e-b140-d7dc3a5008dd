.todoTabs{
  display: flex;
  align-items: center;
  .todoTabItem{
    font-weight: 400;
    font-size: 14px;
    color: #666;
    line-height: 22px;
    cursor: pointer;

    &:last-child{
      margin-right: 0;
    }
  }
  .tabActive{
    font-weight: 550;
    font-size: 16px;
    color: #000;
    line-height: 24px;
  }

  :global{
    .ant-badge-count{
      min-width: 14px;
      height: 14px;
      line-height: 14px;
    }
    .ant-badge-multiple-words{
      padding: 0 4px;
    }
  }
}

.unifiedTodo{
  :global{
    .ant-tabs-bar{
      display: none;
    }
  }
}

.typeContainer {
  position: relative;
  overflow: auto;
  display: flex;
  align-items: center;

  .scrollImg{
    // display: none;
    width: 24px;
    font-size: 18px;
  }

  .todoTypeList{
    position: relative;
    display: flex;
    flex: 1;
    align-items: center;
    overflow: auto;

    .sliderTab {
      position: absolute;
      height: 100%;
      background: linear-gradient(180deg, #00D2F8 0%, #0D94D7 100%);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 4px;
    }

    .todoTypeItem{
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-width: 140px;
      padding: 11px 16px;
      margin-right: 12px;
      background: #E6F3FF;
      border-radius: 4px;
      font-weight: 400;
      font-size: 14px;
      color: #333;
      cursor: pointer;
      transition: all 0.4s ease; // 只保留颜色过渡
      flex-shrink: 0;

      &:last-child{
        margin-right: 0;
      }

      .todoTypeName{
        display: flex;
        align-items: center;
        flex-grow: 1;
        .name{
          line-height: 22px;
        }
        img{
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }
    }

    .todoTypeItemActive{
      color: #FFFFFF;
      background: transparent;
    }
  }
}

.todoTasks{
  padding: 12px 20px;

  .subType{
    margin-top: 12px;
    display: flex;
    align-items: center;
  }
  :global{
    .slick-table .ant-table{
      .ant-table-thead > tr > th{
        padding: 5px 12px !important;
        background-color: #F5F8FA;
      }

      .ant-table-tbody > tr > td{
        padding: 5px 12px !important;
      }

      /* 隐藏横向滚动条 */
      .ant-table-body {
        overflow-x: hidden !important;
      }

      /* 隐藏滚动条（跨浏览器方案） */
      .ant-table-body::-webkit-scrollbar {
        display: none; /* Chrome/Safari */
        width: 0 !important; /* Firefox */
      }

      .ant-table-body {
        -ms-overflow-style: none;  /* IE/Edge */
        scrollbar-width: none;     /* Firefox */
      }
    }
    .ant-pagination-item-ellipsis {
      font-size: 10px;
    }
  }
}

.pendingMessage, .unreadBulletin{
  padding: 12px 20px;
}

.todoList{
  margin-top: 8px;
  cursor: default;
  .todoItem{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    border-bottom: 1px solid #F0F0F0;
    font-weight: 400;
    font-size: 12px;
    color: #999;
    cursor: pointer;

    &:hover{
      background: #e6faff;
    }

    .todoContent{
      display: flex;
      align-items: center;
      // width: calc(100% - 120px);
      width: 85%;

      .point{
        width: 6px;
        height: 6px;
        background: #F02688;
        border-radius: 50%;
        margin-right: 8px;
      }
      .content{
        width: 90%;
        .itemTitle{
          width: 90%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 14px;
          color: #333;
          line-height: 22px;
        }

        .itemContent{
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
.todoModal{
  :global{
    .ant-modal-content{
      background-color: rgba(255, 255, 255, 0.5);
      backdrop-filter: blur(5px);
    }
    .ant-modal-header{
      background: transparent;
    }
  }
}
