import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Col, DatePicker, Drawer, Form, Input, message, Row } from 'antd';
import { connect } from 'dva';
import { useFormTable } from '@umijs/hooks';
import moment from 'moment';
import SlickTable from '@/components/SlickTable';
import { exportExcelFile, getPageSizeByCardHeight } from '@/utils/utils';
import styles from './index.less';
import { getList } from '@/pages/GroupCust/GroupCustManage/services';
import Details from './details';

const { RangePicker } = DatePicker;

// 表格单元格样式
const cellStyle = {
  maxWidth: 200,
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  cursor: 'pointer',
};

// 获取表格数据
const getTableData = ({ current, pageSize }, formData) =>
  getList({
    type: 'cust_IQueryHighLevel_queryHighLevel',
    data: {
      ...formData,
    },
    pageFlag: 1,
    pageInfo: {
      currentPage: current,
      pageSize,
    },
  }).then(res => {
    const { resultCode, resultObject, resultMsg } = res;
    if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
      return {
        total: parseInt(resultObject?.rspParam?.pageInfo?.recordCount, 10),
        list: resultObject?.rspParam?.busiInfo?.outData,
      };
    }
    message.error(resultMsg);

    return {
      total: 0,
      list: [],
    };
  });

const Index = ({ size: { height }, form, groupCustView }) => {
  const [exportLoading, setExportLoading] = useState(false);
  const tableRef = useRef(null);

  const [drawerParams, setDrawerParams] = useState({
    visible: false,
    title: '',
    selectRow: {},
    action: '',
  });
  // 设置表格分页大小
  const [size, setSize] = useState(getPageSizeByCardHeight(height));
  const { getFieldDecorator } = form;

  // 使用hooks获取表格数据
  const {
    tableProps,
    search: { submit, reset },
  } = useFormTable(getTableData, {
    defaultPageSize: size,
    form,
    manual: true,
  });

  const { pagination, ...restTableProps } = tableProps;

  // 业务办理处理函数
  const handleBusinessProcess = record => {
    message.info(`执行业务办理: ${record.MEMBER_ID}`);
    // todo: 跳转页面
  };

  // 指令查询
  const handleViewInstructionQuery = record => {
    setDrawerParams({
      visible: true,
      title: '指令查询',
      selectRow: record,
      action: 'instructionQuery',
    });
  };
  // 查看详情
  const handleViewDetail = record => {
    setDrawerParams({
      visible: true,
      title: '成员订购信息',
      selectRow: record,
      action: 'memQuery',
    });
  };

  const handleClose = () => {
    setDrawerParams({
      visible: false,
      title: '',
      selectRow: {},
      action: '',
    });
  };
  // 导出数据 todo:
  const handleExport = () => {
    const formParams = form.getFieldsValue();
    exportExcelFile({
      url: '/project/LocalHighQueryController/exportHighQuery',
      title: `高级查询-${moment().format('YYYY-MM-DD HH-mm-ss')}`,
      sendParams: {
        pageNum: 1,
        pageSize: 10,
        sortName: '',
        sortOrder: 'asc',
        ...formParams,
      },
      setLoading: setExportLoading,
    });
  };

  useEffect(() => {
    setSize(getPageSizeByCardHeight(height));
  }, [height]);

  return (
    <>
      <Card title="高级查询" className="cute" style={{ minHeight: height }} bordered>
        <Form className="flow fix-label" onSubmit={submit}>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label="成员接入号">{getFieldDecorator('memberAccessNum')(<Input placeholder="成员接入号" allowClear />)}</Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="成员短号">{getFieldDecorator('memberShortNum')(<Input placeholder="成员短号" allowClear />)}</Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="操作时间">
                {getFieldDecorator('operateTime')(
                  <RangePicker placeholder={['开始日期', '结束日期']} style={{ width: '100%' }} format="YYYY-MM-DD" />
                )}
              </Form.Item>
            </Col>
            <Col span={4} className="text-right">
              <Button type="primary" htmlType="submit" loading={tableProps.loading}>
                查询
              </Button>
              <Button className="margin-left" onClick={reset}>
                重置
              </Button>
              <Button type="primary" className="margin-left" onClick={handleExport} loading={exportLoading}>
                导出
              </Button>
            </Col>
          </Row>
        </Form>

        <SlickTable
          style={{ marginTop: 20 }}
          ref={tableRef}
          rowKey={record => record.GROUP_SUBSCRIBER_INS_ID}
          {...restTableProps}
          data={{
            pagination: {
              ...pagination,
              pageSize: size,
            },
          }}
          columns={[
            {
              title: '集团用户标识',
              dataIndex: 'GROUP_SUBSCRIBER_INS_ID',
              key: 'groupSubscriberInsId',
            },
            {
              title: '集团用户标识',
              dataIndex: 'MEM_SUBSCRIBER_INS_ID',
              key: 'memSubscriberInsId',
            },
            {
              title: '成员接入号',
              dataIndex: 'ACCESS_NUM',
              key: 'accessNum',
              onCell: () => ({
                style: cellStyle,
              }),
            },
            {
              title: '服务短号',
              dataIndex: 'SHORT_NUM',
              key: 'shortNum',
            },
            {
              title: '用户状态',
              dataIndex: 'STATUS_NAME',
              key: 'statusName',
              render: text => <span className={text === '正常' ? styles.statusNormal : styles.statusAbnormal}>{text}</span>,
            },
            {
              title: '成员关系状态',
              dataIndex: 'DATA_STATUS',
              key: 'dataStatus',
              render: text => <span className={text === '正常' ? styles.statusNormal : styles.statusAbnormal}>{text}</span>,
            },
            {
              title: '生效日期',
              dataIndex: 'VALID_DATE',
              key: 'validDate',
            },
            {
              title: '失效日期',
              dataIndex: 'EXPIRY_DATE',
              key: 'expiryDate',
            },
            {
              title: '所属区域',
              dataIndex: 'REGION_NAME',
              key: 'regionName',
            },
            {
              title: '操作',
              key: 'action',
              width: 180,
              render: (_, record) => (
                <div className={styles.actionButtons}>
                  <Button type="link" className={styles.businessButton} onClick={() => handleBusinessProcess(record)}>
                    业务办理
                  </Button>
                  {['100001340005'].includes(record.MAIN_OFFER_ID) ? (
                    <Button type="link" className={styles.businessButton} onClick={() => handleViewInstructionQuery(record)}>
                      指令查询
                    </Button>
                  ) : (
                    <Button type="link" className={styles.viewButton} onClick={() => handleViewDetail(record)}>
                      详情
                    </Button>
                  )}
                </div>
              ),
            },
          ]}
        />
      </Card>
      <Drawer
        title={drawerParams.title}
        destroyOnClose
        width={620}
        onClose={handleClose}
        visible={drawerParams.visible}
        bodyStyle={{ paddingBottom: 80 }}
      >
        <Details onClose={handleClose} {...drawerParams} />
      </Drawer>
    </>
  );
};

export default connect(({ setting, groupCustView }) => ({
  size: setting.size,
  currentCust: groupCustView.currentCust,
}))(Form.create()(Index));
