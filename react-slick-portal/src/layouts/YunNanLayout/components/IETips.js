import React, { useState } from 'react';
import { Button, Modal } from 'antd';
import DownCenter from './DownCenter';
import styles from '../less/ieTips.less';

const IETips = () => {
  const [downVisible, setDownVisible] = useState(false);

  return (
    <div className={styles.ieTips}>
      <div className={styles.tipsText}>温馨提示：为了更好的用户使用体验，请使用谷歌或火狐浏览器进行操作。</div>
      <Button type="primary" size="large" onClick={() => setDownVisible(true)}>打开下载中心</Button>
      {/* <Icon type="close" onClick={closeTips} className={styles.closeIcon} /> */}

      <Modal
        title="下载中心"
        visible={downVisible}
        footer={null}
        onCancel={() => setDownVisible(false)}
        destroyOnClose
        width={800}
        className={styles.downCenter}
      >
        <DownCenter />
      </Modal>
    </div>
  );
};
export default IETips;
