.queryTable{
  display: flex;
  flex-direction: column;
  height: 100%;
  .formCard{
    margin-bottom: 8px;
    padding: 16px 16px 8px;
    background: #FFF;
    border-radius: 4px;
  }
  .tableCard{
    flex-grow: 1;
    padding: 16px;
    background: #FFF;
    border-radius: 4px;
    overflow-x: auto;
    width: 100%;
  }
  :global{
    .slick-table .ant-table{
      .ant-table-thead > tr > th{
        background-color: #F5F8FA;
      }
    }
  }
}
