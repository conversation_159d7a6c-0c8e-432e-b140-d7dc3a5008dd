@import '~antd/lib/style/themes/default.less';
@import '~@/less/utils.less';

.listContent {
  .description {
    max-width: 720px;
    line-height: 22px;
  }
  .extra {
    margin-top: 16px;
    color: @text-color-secondary;
    line-height: 22px;
    & > :global(.ant-avatar) {
      position: relative;
      top: 1px;
      width: 20px;
      height: 20px;
      margin-right: 8px;
      vertical-align: top;
    }
    & > em {
      margin-left: 16px;
      color: @disabled-color;
      font-style: normal;
    }
  }
}
a.listItemMetaTitle {
  color: @heading-color;
}
.listItemExtra {
  width: 272px;
  height: 1px;
}
.selfTrigger {
  margin-left: 12px;
}

@media screen and (max-width: @screen-xs) {
  .selfTrigger {
    display: block;
    margin-left: 0;
  }
  .listContent {
    .extra {
      & > em {
        display: block;
        margin-top: 8px;
        margin-left: 0;
      }
    }
  }
}
@media screen and (max-width: @screen-md) {
  .selfTrigger {
    display: block;
    margin-left: 0;
  }
}
@media screen and (max-width: @screen-lg) {
  .listItemExtra {
    width: 0;
    height: 1px;
  }
}
