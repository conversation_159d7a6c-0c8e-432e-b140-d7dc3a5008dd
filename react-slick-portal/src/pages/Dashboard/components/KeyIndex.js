import React, { useState, useEffect } from 'react';
import { Card, Row, Col } from 'antd';
import { Pie } from '@/components/Charts';
import request from '@/utils/request';
import { getItem } from '@/utils/utils';

function KeyIndex() {
  const [loading, setLoading] = useState(true);
  const [cust, setCust] = useState(0);
  const [pie1, setPie1] = useState([]);
  const [pie2, setPie2] = useState([]);
  const [pie3, setPie3] = useState([]);
  const [opp, setOpp] = useState(0);
  const [lead, setLead] = useState(0);

  useEffect(() => {
    // 因为async函数肯定会返回一个Promise，会和useEffect返回的cleanup函数混淆所以不要直接将async function传给useEffect，最简单的解决方法是IIFE
    (async () => {
      /**
       * 客户中心数据统计
       * statisticType 1：正式建档率；2：客户分布；3：状态数量统计；4：排行榜；5：个人排位
       */
      const response1 = await request('orgauth/WorkbenchController/qryStatisticCustIndicators.do', {
        method: 'get',
        data: { statisticType: '1' },
      });

      // 线索转化率
      const response2 = await request('orgauth/WorkbenchController/qryStatisticsOfBest.do', {
        method: 'get',
        data: { objType: 'LEAD' },
      });

      // 商机转化率
      const response3 = await request('orgauth/WorkbenchController/qryStatisticsOfBest.do', {
        method: 'get',
        data: { objType: 'OPP' },
      });

      if (response1.resultCode === '0' && response1.resultObject.data.resultCode === '0') {
        const rstData = response1.resultObject.data.resultObject.monthNewNormalPercentStatistic;
        if (rstData) {
          const data = [];
          setCust((rstData.month_normal_add_percent * 100).toFixed(2));
          data.push({ y: rstData.month_normal_add, x: '已建档' });
          data.push({ y: rstData.month_un_normal_add, x: '未建档' });
          setPie1(data);
        }
      }

      /**
       * 获取当前登录用户的sysRoleType 是否为 ‘1100’ 如果是为管理者 线索转换与商机转换取 regionMap 的值 反之取 ownMap 的值
       *
       */

      const { portalRoles, userInfo } = getItem('user');
      let flag = true;
      /* eslint-disable */
      for (const item of portalRoles) {
        if (item.sysRoleId === userInfo.roleId) {
          if (item.sysRoleType === '1100') {
            flag = true;
          } else {
            flag = false;
          }
          break;
        }
      }
      /* eslint-enable */

      if (response2.resultCode === '0' && response2.resultObject.data.resultCode === '0') {
        if (flag) {
          const { regionMap } = response2.resultObject.data.resultObject;
          if (regionMap !== undefined) {
            const data = [];
            setLead(parseFloat(regionMap.percentage.split('%')[0]));
            data.push({ y: regionMap.num, x: '已转化' });
            data.push({ y: regionMap.totalNum - regionMap.num, x: '未转化' });
            setPie2(data);
          }
        } else {
          const { ownMap } = response2.resultObject.data.resultObject;
          if (ownMap !== undefined) {
            const data = [];
            setLead(parseFloat(ownMap.percentage.split('%')[0]));
            data.push({ y: ownMap.num, x: '已转化' });
            data.push({ y: ownMap.totalNum - ownMap.num, x: '未转化' });
            setPie2(data);
          }
        }
      }

      if (response3.resultCode === '0' && response3.resultObject.data.resultCode === '0') {
        if (flag) {
          const { regionMap } = response3.resultObject.data.resultObject;
          if (regionMap !== undefined) {
            const data = [];
            setOpp(parseFloat(regionMap.percentage.split('%')[0]));
            data.push({ y: regionMap.num, x: '已转化' });
            data.push({ y: regionMap.totalNum - regionMap.num, x: '未转化' });
            setPie3(data);
          }
        } else {
          const { ownMap } = response3.resultObject.data.resultObject;
          if (ownMap !== undefined) {
            const data = [];
            setOpp(parseFloat(ownMap.percentage.split('%')[0]));
            data.push({ y: ownMap.num, x: '已转化' });
            data.push({ y: ownMap.totalNum - ownMap.num, x: '未转化' });
            setPie3(data);
          }
        }
      }

      // 取消loading
      setLoading(false);
    })();
  }, []);

  return (
    <Card
      bordered={false}
      title="关键指标（本月）"
      loading={loading}
      bodyStyle={{ height: '180px' }}
      // className="margin-bottom"
    >
      <Row gutter={16}>
        <Col span={8}>
          <Pie
            percent={cust}
            tooltipFormat={[
              'x*y*percent',
              (x, y, p) => ({
                name: `${x} ${y}个`,
                value: `${(p * 100).toFixed(2)}%`,
              }),
            ]}
            mainKey="已建档"
            data={pie1}
            subTitle="客户建档率"
            total={`${cust}%`}
            height={140}
          />
        </Col>
        <Col span={8}>
          <Pie
            percent={lead}
            tooltipFormat={[
              'x*y*percent',
              (x, y, p) => ({
                name: `${x} ${y}个`,
                value: `${(p * 100).toFixed(2)}%`,
              }),
            ]}
            subTitle="线索转化率"
            total={`${lead}%`}
            mainKey="已转化"
            data={pie2}
            height={140}
            color="#5DDECF"
          />
        </Col>
        <Col span={8}>
          <Pie
            percent={opp}
            tooltipFormat={[
              'x*y*percent',
              (x, y, p) => ({
                name: `${x} ${y}个`,
                value: `${(p * 100).toFixed(2)}%`,
              }),
            ]}
            subTitle="商机转化率"
            total={`${opp}%`}
            mainKey="已转化"
            data={pie3}
            height={140}
            color="#2FC25B"
          />
        </Col>
      </Row>
    </Card>
  );
}

export default KeyIndex;
