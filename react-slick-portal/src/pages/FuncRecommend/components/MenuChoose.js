import React, { useState } from 'react';
import { Modal, Input, message } from 'antd';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
import useGetMenu from '@/hooks/useGetMenu';
import { isEmptyArray, isEmptyStr } from '@/utils/utils';


const MenuChoose = props => {
  const { visible, close, onOk, MODAL_TYPE, targetMenuId, sourceMenuId, menu } = props;
  const { search, subMenuList } = useGetMenu([], menu);
  const [selectedRows, setSelectedRows] = useState([]);

  const columns = [
    {
      dataIndex: 'menuName',
      title: '菜单名称',
      ellipsis: true,
      width: 150,
    },
    {
      dataIndex: 'parMenuName',
      title: '上级菜单',
      ellipsis: true,
      width: 150,
    },
    {
      dataIndex: 'menuDesc',
      title: '菜单描述',
      ellipsis: true,
    },
  ];

  const submit = () => {
    if (isEmptyArray(selectedRows)) {
      message.warning('请选择数据');
      return;
    }

    if (visible === MODAL_TYPE.CHOOSE && !isEmptyStr(targetMenuId) && selectedRows[0]?.menuId === targetMenuId) {
      message.warning('选定功能与推荐功能不能是相同的菜单，请重新选择');
      return;
    }

    if (visible === MODAL_TYPE.RECOMMEND && !isEmptyStr(sourceMenuId) && selectedRows[0]?.menuId === sourceMenuId) {
      message.warning('选定功能与推荐功能不能是相同的菜单，请重新选择');
      return;
    }
    onOk(selectedRows);
  };

  return (
    <Modal
      title="选择菜单"
      width={800}
      visible={visible !== false}
      onCancel={close}
      onOk={submit}
    >
      <div style={{ width: 250, marginBottom: 8 }}>
        <Input.Search
          allowClear
          placeholder="搜索关键字"
          onSearch={search}
        />
      </div>
      <SlickTable
        rowKey={record => record.menuId}
        pick={visible === MODAL_TYPE.FUNC ? 'checkbox' : 'radio'}
        onSelectRow={_selectedRows => {
          setSelectedRows(_selectedRows);
        }}
        columns={columns}
        data={{
          list: subMenuList,
        }}
      />
    </Modal>
  );
};

export default connect(({ menu }) => ({
  menu,
}))(MenuChoose);
