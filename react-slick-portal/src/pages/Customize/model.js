import { message } from 'antd';
import pick from 'lodash/pick';
import {
  getUserConfigMenu,
  saveTemplet,
  getUserTempletId,
  getTempletById,
  getRoleConfigMenu,
  saveUserMobileTemplet,
  getTemplet,
} from './services/customize';

const defaultState = {
  templetId: '-1',
  templetName: '',
  templetDesc: '',
  current: [],
  all: [],
};

export default {
  namespace: 'customize',

  state: {
    pcUser: defaultState,
    pcRole: defaultState,
    mobileUser: defaultState,
    mobileRole: defaultState,
  },

  effects: {
    // *getUserAllByTerminalType({ payload }, { call, put, select }) {
    //   const terminalType = parseInt(payload.terminalType, 10);
    //   /**
    //    * 1. 获取all,
    //    * all 表示当前用户允许添加的所有portlet数据。pc端和手机端过滤规则不一致       *
    //    */
    //   const res1 = yield call(getUserConfigMenu);
    //   const appSysCode = yield select(state => state.menu.appSysCode);
    //   // 当前有权限配置的portlets
    //   const all = [];
    //   if (!Array.isArray(res1)) {
    //     message.error('获取当前用户有权限添加的portlet数据失败');
    //     return;
    //   }
    //   if (terminalType === 1000) {
    //     // 筛选出pc端的工作台菜单（即portlet） menuLevel=-1 表示工作台菜单；systemCode !== appSysCode表示非app端；urlAddr以[portlet]开头表示是新版portlet模块
    //     res1.forEach(menu => {
    //       if (
    //         menu.menuLevel === -1 &&
    //         parseInt(menu.systemCode, 10) !== appSysCode &&
    //         menu.menuCode.indexOf('TYMH_MENU_CARD') !== 0 &&
    //         /\[portlet\]/i.test(menu.urlAddr)
    //       ) {
    //         all.push(
    //           pick(
    //             menu,
    //             'menuId',
    //             'parMenuId',
    //             'parMenuName',
    //             'urlAddr',
    //             'regionId',
    //             'systemCode',
    //             'menuName',
    //             'menuDesc',
    //             'menuLevel',
    //             'iconUrl',
    //             'menuCode'
    //           )
    //         );
    //       }
    //       return null;
    //     });
    //   }
    //   if (terminalType === 1200) {
    //     // 筛选出手机端的菜单
    //     res1.forEach(menu => {
    //       if (menu.menuCode.indexOf('TYMH_MENU_CARD') === 0) {
    //         all.push(
    //           pick(
    //             menu,
    //             'menuId',
    //             'parMenuId',
    //             'parMenuName',
    //             'urlAddr',
    //             'regionId',
    //             'systemCode',
    //             'menuName',
    //             'menuDesc',
    //             'menuLevel',
    //             'iconUrl',
    //             'menuCode'
    //           )
    //         );
    //       }
    //       return null;
    //     });
    //   }
    //   yield put({
    //     type: 'save',
    //     payload: {
    //       terminalType,
    //       type: 'user',
    //       all,
    //     },
    //   });
    // },
    // *getUserTemplet({ payload }, { call, put }) {
    //   let { terminalType } = payload;
    //   terminalType = parseInt(terminalType, 10);

    //   const { templetId } = payload;
    //   let current = [];
    //   /**
    //    * 2. 根据模板id 获取当前模板需展示的portlet
    //    * 1000: PC端;
    //    */
    //   if (templetId) {
    //     const res2 = yield call(getTempletById, {
    //       templetId,
    //       terminalType,
    //     });

    //     //  templetModuleRels表示当前模板包含的portles
    //     const {
    //       resultCode,
    //       resultObject: { portlets, ...rest },
    //     } = res2;
    //     if (resultCode !== '0' || !Array.isArray(portlets)) {
    //       message.error('获取当前模板包含的portlet数据失败');
    //     } else {
    //       current = portlets;
    //       /**
    //        * 3.  1000: PC端; 1200: 手机端
    //        */
    //       yield put({
    //         type: 'save',
    //         payload: {
    //           terminalType,
    //           type: 'user',
    //           templetId,
    //           current,
    //           ...rest,
    //         },
    //       });
    //     }
    //   } else {
    //     const { all, ...rest } = defaultState;
    //     yield put({
    //       type: 'save',
    //       payload: {
    //         terminalType,
    //         type: 'user',
    //         ...rest,
    //       },
    //     });
    //   }
    // },
    // *getRoleAllByTerminalType({ payload }, { call, put, select }) {
    //   const terminalType = parseInt(payload.terminalType, 10);
    //   /**
    //    * 1. 获取all,
    //    * all 表示当前用户允许添加的所有portlet数据。pc端和手机端过滤规则不一致       *
    //    */
    //   const res1 = yield call(getRoleConfigMenu, { postId: payload.postId });
    //   const appSysCode = yield select(state => state.menu.appSysCode);
    //   // 当前有权限配置的portlets
    //   const all = [];
    //   if (!Array.isArray(res1)) {
    //     message.error('获取当前用户有权限添加的portlet数据失败');
    //     return;
    //   }
    //   if (terminalType === 1000) {
    //     // 筛选出pc端的工作台菜单（即portlet） menuLevel=-1 表示工作台菜单；systemCode !== appSysCode表示非app端；urlAddr以[portlet]开头表示是新版portlet模块
    //     res1.forEach(menu => {
    //       if (
    //         menu.menuLevel === -1 &&
    //         parseInt(menu.systemCode, 10) !== appSysCode &&
    //         menu.menuCode.indexOf('TYMH_MENU_CARD') !== 0 &&
    //         /\[portlet\]/i.test(menu.urlAddr)
    //       ) {
    //         all.push(
    //           pick(
    //             menu,
    //             'menuId',
    //             'parMenuId',
    //             'parMenuName',
    //             'urlAddr',
    //             'regionId',
    //             'systemCode',
    //             'menuName',
    //             'menuDesc',
    //             'menuLevel',
    //             'iconUrl',
    //             'menuCode'
    //           )
    //         );
    //       }
    //       return null;
    //     });
    //   }
    //   if (terminalType === 1200) {
    //     // 筛选出手机端的菜单
    //     res1.forEach(menu => {
    //       if (menu.menuCode.indexOf('TYMH_MENU_CARD') === 0) {
    //         all.push(
    //           pick(
    //             menu,
    //             'menuId',
    //             'parMenuId',
    //             'parMenuName',
    //             'urlAddr',
    //             'regionId',
    //             'systemCode',
    //             'menuName',
    //             'menuDesc',
    //             'menuLevel',
    //             'iconUrl',
    //             'menuCode'
    //           )
    //         );
    //       }
    //       return null;
    //     });
    //   }
    //   yield put({
    //     type: 'save',
    //     payload: {
    //       terminalType,
    //       type: 'role',
    //       all,
    //     },
    //   });
    // },
    // *getRoleTemplet({ payload }, { call, put }) {
    //   let { terminalType } = payload;
    //   const { templetId } = payload;
    //   terminalType = parseInt(terminalType, 10);
    //   let current = [];
    //   const res2 = yield call(getTempletById, {
    //     templetId,
    //     terminalType,
    //   });

    //   //  templetModuleRels表示当前模板包含的portles
    //   const {
    //     resultCode,
    //     resultObject: { portlets, ...rest },
    //   } = res2;
    //   if (resultCode !== '0' || !Array.isArray(portlets)) {
    //     message.error('获取当前模板包含的portlet数据失败');
    //   } else {
    //     current = portlets;
    //     /**
    //      * 3.  1000: PC端; 1200: 手机端
    //      */
    //     yield put({
    //       type: 'save',
    //       payload: {
    //         terminalType,
    //         type: 'role',
    //         templetId,
    //         current,
    //         ...rest,
    //       },
    //     });
    //   }
    // },
    *saveTemplet({ payload }, { call, put }) {
      const { type } = payload;
      const terminalType = parseInt(payload.terminalType, 10);
      const res = yield call(saveTemplet, payload);
      const { resultCode, resultData } = res;

      if (resultCode === '0') {
        message.success('保存模板成功');
        yield put({
          type: 'save',
          payload: {
            terminalType,
            type,
            templetId: resultData.templetId,
            templetDesc: payload.templetDesc,
            templetName: payload.templetName,
            current: payload.portlets,
          },
        });

        /**
         * 2. 如果保存的是PC端的用户模板，需要额外更新dashboard
         */
        if (type === 'user' && terminalType === 1000) {
          yield put({
            type: 'dashboard/save',
            payload: {
              templetId: resultData.templetId,
              templetDesc: payload.templetDesc,
              templetName: payload.templetName,
              current: payload.portlets,
            },
          });
        }
      } else {
        message.error('保存模板失败');
      }
    },
  },

  reducers: {
    save(state, { payload: { terminalType, type, ...rest } }) {
      const mapping = {
        1000: {
          user: 'pcUser',
          role: 'pcRole',
        },
        1200: {
          user: 'mobileUser',
          role: 'mobileRole',
        },
      };
      const key = mapping[terminalType][type];
      return {
        ...state,
        [`${key}`]: { ...state[key], ...rest },
      };
    },
  },
};
