import React, { useState, useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import { Tabs, Icon, Tag, Input, Pagination, Spin, Empty } from 'antd';
import { useAntdTable } from '@umijs/hooks';
import router from 'umi/router';
import styles from './businessExamine.less';
import icon1 from '../img/icon1101.png';
import icon2 from '../img/icon1102.png';
import icon3 from '../img/icon1103.png';
import icon4 from '../img/icon1104.png';
import { getSceneTotal, getSceneQuery } from '../service';

const Panel = (props, ref) => {
  const { type: cardType, searchValue } = props;
  const [cardData, setCardData] = useState({});
  const [status, setStatus] = useState(); // 0：待审批，1：审批中，3：驳回，4：通过

  const { tableProps, refresh } = useAntdTable(
    params =>
      getSceneQuery({
        sceneId: '1',
        systemInfoId: cardType,
        pageNum: params.current,
        status,
        title: searchValue,
        ...params,
      }),
    [status],
    {
      defaultPageSize: 3,
    }
  );

  useImperativeHandle(
    ref,
    () => ({
      refresh: () => {
        refresh();
      },
    }),
    [refresh]
  );

  // 获取总数数据
  const getSceneTotalData = async (type = cardType) => {
    const params = { type };
    const result = await getSceneTotal(params);
    if (result && result.success) {
      const cardObj = {};
      result.resultObject.scenario.attrs.forEach(item => {
        cardObj[item.attrCode] = item.attrValue;
      });
      setCardData(cardObj);
    }
  };

  useEffect(() => {
    getSceneTotalData();
  }, [cardType]);

  // 选择审批状态
  const changeStatus = value => {
    setStatus(status === value ? undefined : value);
  };

  // 处理任务单详情、处理URL
  const sceneDetail = url => {
    const urlArr = url.split('?');
    const query = {};
    if (urlArr[1]) {
      const paramsArr = urlArr[1].split('&');
      paramsArr.forEach(param => {
        const [key, value] = param.split('=');
        query[key] = value;
      });
    }
    router.push({
      pathname: urlArr[0],
      query,
    });
  };

  const rowUnit = data => (
    <div className={styles.unit}>
      <div className={styles.title} title={data.title}>
        <span>{data.title}</span>
      </div>
      <div className={styles.content}>
        {data.itemArr.map((item, index) => {
          if (index < 5) {
            return (
              <div className={styles.desc}>
                <span>{item.attrName}：</span>
                <span title={item.attrValue}>{item.attrValue}</span>
              </div>
            );
          }
          return '';
        })}
        <div className={styles.bottom}>
          <Tag style={{ float: 'left', marginRight: 0, display: data.tips ? 'block' : 'none' }}>{data.tips}</Tag>
          <span style={{ marginRight: 4 }}>
            <Icon type="eye" theme="filled" style={{ marginRight: 2 }} />
            <a
              onClick={() => {
                sceneDetail(data.TASK_DETAIL_URL);
              }}
            >
              详情
            </a>
          </span>
          <span style={{ display: data.IS_EDIT === 'TRUE' ? 'inline' : 'none' }}>
            <Icon type="edit" style={{ marginRight: 2 }} />
            <a
              onClick={() => {
                sceneDetail(data.TASK_MODIFY_URL);
              }}
            >
              处理
            </a>
          </span>
        </div>
      </div>
    </div>
  );

  return (
    <div className={styles.panel}>
      <div className={styles.row1}>
        <div
          className={`${styles.unit} ${status === '0' ? styles.active : ''}`}
          onClick={() => {
            changeStatus('0');
          }}
        >
          <img src={icon1} alt="" width={40} height={40} />
          <div>待审批</div>
          <div className={styles.num}>{cardData.PENDING_APPROVAL}</div>
        </div>
        <div
          className={`${styles.unit} ${status === '1' ? styles.active : ''}`}
          onClick={() => {
            changeStatus('1');
          }}
        >
          <img src={icon2} alt="" width={40} height={40} />
          <div>审批中</div>
          <div className={styles.num}>{cardData.UNDER_APPROVAL}</div>
        </div>
        <div
          className={`${styles.unit} ${status === '2' ? styles.active : ''}`}
          style={{ display: cardType === '727028' ? '' : 'none' }}
          onClick={() => {
            changeStatus('2');
          }}
        >
          <img src={icon3} alt="" width={40} height={40} />
          <div>归档</div>
          <div className={styles.num}>{cardData.ARCHIVING}</div>
        </div>
        <div
          className={`${styles.unit} ${status === '3' ? styles.active : ''}`}
          style={{ display: cardType === '727030' ? '' : 'none' }}
          onClick={() => {
            changeStatus('3');
          }}
        >
          <img src={icon3} alt="" width={40} height={40} />
          <div>驳回</div>
          <div className={styles.num}>{cardData.REJECT_APPROVAL}</div>
        </div>
        <div
          className={`${styles.unit} ${status === '4' ? styles.active : ''}`}
          style={{ display: cardType === '727030' ? '' : 'none' }}
          onClick={() => {
            changeStatus('4');
          }}
        >
          <img src={icon4} alt="" width={40} height={40} />
          <div>通过</div>
          <div className={styles.num}>{cardData.PASS_APPROVAL}</div>
        </div>
      </div>
      <div className={styles.row2}>
        <span>
          我的审批数量：
          <span style={{ color: '#0085D0' }}>{cardData.MY_APPROVAL || 0}</span>
        </span>
        <span style={{ display: cardType === '727030' ? 'inline' : 'none' }}>
          我的审批通过率：
          <span style={{ color: '#0085D0' }}>{cardData.PASS_PERCENTAGE || 0}</span>
        </span>
      </div>
      <Spin spinning={tableProps.loading}>
        <div className={styles.row3}>
          {tableProps.dataSource.length > 0 && tableProps.dataSource.map(item => rowUnit(item))}
          {tableProps.dataSource.length === 0 && <Empty style={{ width: '100%' }} />}
        </div>
      </Spin>
      {tableProps.dataSource.length > 0 && (
        <Pagination
          {...tableProps.pagination}
          onChange={(pageNum, pageSize) => {
            tableProps.onChange({
              current: pageNum,
              pageSize,
            });
          }}
          style={{ textAlign: 'right' }}
        />
      )}
    </div>
  );
};

const PanelWrap = forwardRef(Panel);

const BusinessExamine = () => {
  const [activeKey, setActiveKey] = useState('727030');
  const [searchValue, setSearchValue] = useState();
  const panelRef1 = useRef(null);
  const panelRef2 = useRef(null);

  const extra = (
    <Input.Search
      placeholder="标题、单号查询"
      value={searchValue}
      onChange={e => {
        setSearchValue(e.target.value);
      }}
      onSearch={() => {
        activeKey === '727030' && panelRef1.current && panelRef1.current.refresh();
        activeKey === '727028' && panelRef2.current && panelRef2.current.refresh();
      }}
      // onSearch={value => {
      //   if (value === searchValue || (!value && !searchValue)) {
      //     panelRef1.current && panelRef1.current.refresh();
      //     panelRef2.current && panelRef2.current.refresh();
      //   } else {
      //     setSearchValue(value || undefined);
      //   }
      // }}
      style={{ width: 200 }}
    />
  );

  return (
    <div className={styles.wrap}>
      <Tabs
        defaultActiveKey="727030"
        tabBarExtraContent={extra}
        onChange={key => {
          setActiveKey(key);
          setSearchValue('');
        }}
      >
        <Tabs.TabPane tab="业务审批" key="727030" forceRender>
          <PanelWrap type="727030" searchValue={searchValue} ref={panelRef1} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="风控审批" key="727028" forceRender>
          <PanelWrap type="727028" searchValue={searchValue} ref={panelRef2} />
        </Tabs.TabPane>
      </Tabs>
    </div>
  );
};

export default BusinessExamine;
