import React from 'react';
import { connect } from 'dva';
import { Menu, Icon, Empty, Popover } from 'antd';
import Link from 'umi/link';
import classNames from 'classnames';
import styles from '../less/header.less';
import { getFinalUrl } from '@/utils/utils';

function CollectionMenuDropdown({ collection }) {
  const content =
    collection.length === 0 ? (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="您未收藏菜单"
        style={{ padding: '16px', margin: 0, textAlign: 'center' }}
      />
    ) : (
      <Menu className={styles.menu}>
        {/**
         * collection中urlAddr值为[iframe]开头的表示第三方集成的子系统
         * 取menuCode值，menuCode是全局唯一的
         * 第三方集成的页面统一指向@/pages/iframe。会根据传进去的menuCode从all中遍历出对应的菜单地址
         */}
        {collection.map(menu => (
          <Menu.Item key={menu.menuCode}>
            <Link
              className="text-ellipsis"
              title={menu.menuName}
              to={getFinalUrl(menu)}
            >
              {menu.menuName}
            </Link>
          </Menu.Item>
        ))}
      </Menu>
    );

  return (
    <Popover
      placement="bottom"
      content={content}
      trigger="hover"
      getPopupContainer={trigger => trigger.parentNode}
    >
      <span className={classNames(styles.action)}>
        <Icon type="star" />
      </span>
    </Popover>
  );
}

export default connect(({ menu }) => ({
  recent: menu.recent,
  collection: menu.collection,
}))(CollectionMenuDropdown);
