import React, { useEffect, useState, useImperativeHandle, useRef } from 'react';
import { connect } from 'dva';
import { Radio, Divider } from 'antd';
import { useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import { openMenuByValue, getIframeFinalUrl } from '@/utils/utils';
import { queryTaskList } from '@/services/unifiedTodo';
import commonNav1 from '@/pages/Dashboard/img/nmgIcon/commonNav1.png';
import TypeListView from './TypeListView';
import styles from '../unifiedTodo.less';

const defaultPageSize = 5;

const TodoTasks = props => {
  const {
    searchValue,
    tableRef,
    unifiedTodo: { taskMenuList },
    allMenu,
    dispatch,
  } = props;

  const [activeMenu, setActiveMenu] = useState('');
  const [activeType, setActiveType] = useState('');
  const [subTypeList, setSubTypeList] = useState([]);
  const [columns, setColumns] = useState([]);

  const scrollRef = useRef(null);
  const isInTable = useRef(false); // 鼠标是否在表格内

  useEffect(() => {
    if (taskMenuList?.length) {
      if (activeMenu) {
        const typeItem = taskMenuList.find(item => item.menuCode === activeMenu);
        setActiveMenu(typeItem?.menuCode);
        setSubTypeList(typeItem?.subTypeList);
      } else {
        setActiveMenu(taskMenuList[0]?.menuCode);
        setSubTypeList(taskMenuList[0]?.subTypeList);
      }
    }
  }, [JSON.stringify(taskMenuList)]);

  const getSubType = () => {
    dispatch({
      type: 'unifiedTodo/getTaskSubType',
      payload: {
        menuCode: activeMenu,
      },
    });
  };

  useEffect(() => {
    const typeItem = taskMenuList.find(item => item.menuCode === activeMenu);
    if (typeItem) {
      if (typeItem.subTypeList?.length) {
        setSubTypeList(typeItem.subTypeList);
      } else {
        setSubTypeList([]);
        getSubType();
      }
    }
    const tableBody = scrollRef.current?.querySelector('.ant-table-body');
    if (tableBody) {
      tableBody.scrollLeft = 0;
    }
  }, [activeMenu]);

  const operateClick = async operate => {
    const { buttonValue } = operate;

    // 获取通过公共方法处理过的iframe链接
    const url = await getIframeFinalUrl(buttonValue);

    // 使用通用导航打开页面
    openMenuByValue('TYMH_MENU_XTGL_IFRAMEBOX', allMenu, url);
  };

  const getTaskListData = ({ current }) => queryTaskList({
    pageNum: current,
    pageSize: defaultPageSize,
    menuCode: activeMenu,
    taskTypeCode: activeType,
    query: searchValue,
  }).then(result => {
    if (result?.resultObject) {
      if (result.resultObject.taskList?.length > 0) {
        // 获取表格列
        const columnData = result.resultObject.taskList[0].map((item, index) => {
          const columnObj = {
            title: item.keyName,
            dataIndex: item.keyCode,
            ellipsis: true,
          };
          if (index === 0) {
            columnObj.render = text => (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {text ? <img src={commonNav1} alt="" width={24} height={18} style={{ marginRight: 4 }} /> : null}
                <span>{text}</span>
              </div>
            );
          }
          if (item.keyCode === 'operate') {
            columnObj.fixed = 'right';
            // columnObj.width = 108;
            columnObj.onCell = record => {
              // 根据操作按钮数量设置操作列宽度
              const length = record.operate?.length || 0;
              if (length === 1) {
                return { width: 62 };
              }
              if (length === 2) {
                return { width: 108 };
              }
              if (length === 3) {
                return { width: 150 };
              }
              return { width: 150 };
            };
            columnObj.render = text => (
              Array.isArray(text) && text.length ? (
                <span>
                  {text.map((operate, optIndex) => (
                    <>
                      {optIndex > 0 && <Divider type="vertical" />}
                      <a onClick={() => operateClick(operate)} style={{ color: '#0085D0' }}>{operate.buttonName}</a>
                    </>
                  ))}
                </span>
              ) : null
            );
          }
          return columnObj;
        });
        setColumns(columnData);

        // 获取列表数据
        const listData = result.resultObject.taskList.map((data, index) => data.reduce((dataObj, curItem) => {
          dataObj[curItem.keyCode] = curItem.value;
          return dataObj;
        }, { id: index }));

        return {
          total: result.resultObject.todoCount,
          data: listData,
        };
      }
      setColumns([]);
    } else {
      setColumns([]);
    }

    return {
      total: 0,
      data: [],
    };
  }).catch(() => {
    setColumns([]);
  });

  const { tableProps, refresh } = useAntdTable(
    query => getTaskListData(query),
    // 依赖变化会触发请求
    [activeMenu, activeType, searchValue],
    {
      defaultPageSize,
    }
  );

  const { pagination, ...restTableProps } = tableProps;

  useImperativeHandle(tableRef, () => ({
    refresh: () => {
      refresh();
    },
    loading: tableProps?.loading,
  }));

  // 滚轮触发列表横向滚动
  const handleWheel = e => {
    e.stopPropagation();
    if (Math.abs(e.deltaY) > 0) {
      e.preventDefault();
      const tableBody = scrollRef.current?.querySelector('.ant-table-body');
      if (tableBody) {
        tableBody.scrollLeft += e.deltaY;
      }
    }
  };

  // 鼠标进入表格时标记状态
  const handleMouseEnter = () => {
    isInTable.current = true;
    document.body.style.overflow = 'hidden'; // 禁用全局滚动
  };

  // 鼠标离开表格时恢复
  const handleMouseLeave = () => {
    isInTable.current = false;
    document.body.style.overflow = 'auto'; // 恢复全局滚动
  };

  return (
    <div className={styles.todoTasks}>
      {
        taskMenuList.length ? (
          <TypeListView
            typeList={taskMenuList}
            activeType={activeMenu}
            changeType={type => setActiveMenu(type)}
          />
        ) : null
      }
      {
        subTypeList?.length ? (
          <div className={styles.subType}>
            <div>子类型：</div>
            <Radio.Group onChange={e => setActiveType(e.target.value)} value={activeType}>
              {
                subTypeList.map(item => (
                  <Radio key={item.taskType} value={item.taskType}>{item.taskTypeName}</Radio>
                ))
              }
            </Radio.Group>
          </div>
        ) : null
      }
      <div
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onWheel={handleWheel}
        ref={scrollRef}
        style={{ width: '100%' }}
      >
        <SlickTable
          rowKey={record => record.id}
          columns={columns}
          {...restTableProps}
          bordered={false}
          size={null}
          data={{
            pagination: {
              ...pagination,
              pageSize: defaultPageSize,
              showQuickJumper: false,
              showSizeChanger: false,
              showTotal: false,
              hideOnSinglePage: true,
            },
          }}
          loading={tableProps?.loading}
          scroll={columns.length > 5 && tableProps?.dataSource?.length ? { x: columns.length * 200 } : {}}
          // scroll="max-content"
          style={{ marginTop: 12 }}
        />
      </div>
    </div>
  );
};
export default connect(({ unifiedTodo, menu }) => ({
  unifiedTodo,
  allMenu: menu.all,
}))(TodoTasks);
