.contractContainer {
  padding: 16px;
}

.infoCard {
  margin-bottom: 16px;
  background-color: #f9ffff;
}

.contractHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.contractTitle {
  display: flex;
  align-items: center;
}

.contractId {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.modifyButton {
  margin-left: 16px;
  color: #1890ff;
}

.contractDescriptions {
  :global {
    .ant-descriptions-item-label {
      color: #666;
      font-weight: normal;
      min-width: 90px;
    }
    
    .ant-descriptions-item-content {
      color: #333;
    }
  }
}

.contractSource {
  :global {
    .ant-descriptions-item-label {
      color: #666;
      font-weight: normal;
    }
    
    .ant-descriptions-item-content {
      color: #333;
    }
  }
}

.attachmentCard {
  margin-bottom: 16px;
  background-color: #f9ffff;
}

.downloadAll {
  margin-bottom: 16px;
  text-align: right;
}

.fileDescriptions {
  :global {
    .ant-descriptions-item-label {
      color: #666;
      font-weight: normal;
      min-width: 120px;
      text-align: right;
    }
    
    .ant-descriptions-item-content {
      color: #333;
    }
  }
}

.fileLink {
  color: #1890ff;
  padding: 0;
  height: auto;
}

.nineFilesTitle {
  font-size: 14px;
  color: #333;
  margin: 16px 0;
}