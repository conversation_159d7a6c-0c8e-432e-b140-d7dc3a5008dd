// 主题色
@primary-color: #3181f0; // 5g
@success-color: #059f00;
@danger-color: #fc5a5a;
@warning-color: #ff974a;
@info-color: #7788aa;

// 背景
@bg-default: #fff;
@bg-gray: #f0f3f7;
@bg-img: #f1f1f5;
@bg-disabled: #f5f5f5;
@bg-nav: #071527;
@bg-hover: mix(#fff, @primary-color, 90%);
@box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
@box-hover-shadow: 0 12px 12px 0 rgba(0, 0, 0, 0.08);

@table-header-bg: #f0f3f7;

// 间距
@padding: 4px;
@padding-xs: @padding; // 4px
@padding-s: @padding*2; // 8px
@padding-m: @padding*3; // 12px
@padding-l: @padding*4; // 16px
@padding-xl: @padding*6; // 24px
@padding-xxl: @padding-xl*2; // 48px

// 文字大小
@font-size-sm: 12px;
@font-size-base: 14px;
@font-size-md: 16px;
@font-size-lg: 18px;
@font-size-xl: 20px;
@font-size-heading: 22px;
@font-size-strong: 32px;
@font-weight: 500;
@font-light: 300;

// 文字颜色
@color-text-title: #333333; // 5g
@color-text-base: #333333; // 5g
@color-text-light: #fff; // 5g
@color-text-paragraph: rgba(28, 36, 46, 0.85); // 段落
@color-text-secondary: #999999; // 5g
@color-text-disable: #999999;
@color-text-base-inverse: #fff; // 基本 - 反色
@color-text-strong: @danger-color; //价格、强调的数值，包含单位
@color-link: @primary-color; // 链接
@color-link-inverse: #f3af61; // 链接

// 线条
@border-color: #eeeeee;
@dividing-color: rgba(28, 36, 46, 0.1);

// 圆角
@border-radius-base: 2px;
@border-radius-sm: 0;

// 宽度
@min-width: 1200px;
@default-width: 1200px;

// 头部高度
@header-height: 64px;

// 清除浮动
.clearfix {
  &::before,
  &::after {
    display: table;
    content: ' ';
  }
  &::after {
    clear: both;
  }
}

.containerWidth {
  width: @default-width;
  min-width: @min-width;
  margin: 0 auto;
}

.containerFull {
  width: 100%;
  height: 100%;
}

// // 自助服务门户普通面板统一样式
.selfPanel {
  padding: @padding-l;
  background: @bg-default;
  border-radius: @border-radius-base;
}

.panel {
  padding: @padding-l;
  background: @bg-default;
  border-radius: @border-radius-base;
}

.textEllipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.textOverflow(@line) {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: @line; //将元素设为盒子伸缩模型显示//伸缩方向设为垂直方向//超出
}

// flex 居中
.flexCenter(@direction) {
  display: flex;
  flex-direction: @direction;
  align-items: center;
  justify-content: center;
}
// flex 靠起始位置
.flexStart(@direction) {
  display: flex;
  flex-direction: @direction;
  align-items: flex-start;
  justify-content: flex-start;
}
// flex 靠结束位置
.flexEnd(@direction) {
  display: flex;
  flex-direction: @direction;
  align-items: flex-end;
  justify-content: flex-end;
}
// 主轴居中 副轴起始位置
.flexCenterStart(@direction) {
  display: flex;
  flex-direction: @direction;
  align-items: flex-start;
  justify-content: center;
}

// 主轴居中 副轴结束位置
.flexCenterEnd(@direction) {
  display: flex;
  flex-direction: @direction;
  align-items: flex-end;
  justify-content: center;
}
// 主轴起始位置 副轴居中
.flexStartCenter(@direction) {
  display: flex;
  flex-direction: @direction;
  align-items: center;
  justify-content: flex-start;
}
// 主轴结束位置 副轴居中
.flexEndCenter(@direction) {
  display: flex;
  flex-direction: @direction;
  align-items: center;
  justify-content: flex-end;
}

.cleanStyle {
  margin: 0;
  padding: 0;
}
