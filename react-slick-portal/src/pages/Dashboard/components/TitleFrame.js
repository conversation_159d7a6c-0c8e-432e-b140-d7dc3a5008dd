import React, { useEffect, useMemo, useState } from 'react';
import { connect } from 'dva';
import dynamic from 'umi/dynamic';
import { serialize } from '@/utils/utils';

const TitleFrame = React.memo(({ params, allMenu }) => {
  const [titleValue, setTitleValue] = useState('');
  const [menu, setMenu] = useState({});

  let isIframe = false;

  useEffect(() => {
    if (params) {
      const query = serialize(params.startsWith('?') ? params.slice(1) : params);
      const { title, menuCode } = query;
      setTitleValue(title);
      if (menuCode) {
        const menuItem = allMenu.find(item => item.menuCode === menuCode);
        if (menuItem) {
          setMenu(menuItem);
        }
      }
      isIframe = false;
    }
  }, [params]);

  const renderFrame = useMemo(() => {
    if (menu) {
      const { urlAddr = '' } = menu;
      if (!/\[portlet\]+/.test(urlAddr)) {
        return <p>菜单编码{menu.menuCode}地址不符合portlet格式</p>;
      }
      // 地址适配浏览器地址
      let address = urlAddr.split('[portlet]')[1];
      // 如果配置的url不带域名，则直接取当前域名
      if (address.startsWith('/')) {
        // 去掉address头部的/
        address = address.startsWith('/') ? address.slice(1) : address;

        const { origin } = window.location;
        address = `${origin}/${address}`;
      }

      const urlArr = address.split('?');
      let componentName = urlArr[0];
      const componentParams = urlArr[1] || '';
      let iframeUrl = '';
      if (address.indexOf('http') === 0) {
        iframeUrl = address;
        componentName = 'Iframe';
        isIframe = true;
      }
      const C = dynamic({
        loader: () => import(`@/pages/Dashboard/components/${componentName}`),
      });
      return (
        <C iframeUrl={iframeUrl} params={componentParams} />
      );
    }
    return null;
  }, [JSON.stringify(menu)]);

  return (
    <div style={{ overflowY: isIframe ? 'hidden' : 'auto', overflowX: 'auto', height: '100%' }}>
      <div style={{ padding: '16px 0', display: 'flex', alignItems: 'center' }}>
        <div
          style={{ width: 4, height: 16, marginRight: 12, background: '#0085D0', borderRadius: '1px' }}
        />
        <div
          style={{ fontSize: '16px', fontWeight: '550', color: '#000', lineHeight: '24px' }}
        >
          {titleValue}
        </div>
      </div>
      <div
        style={{ height: 'calc(100% - 56px)', overscrollBehavior: 'contain' }}
      >
        {renderFrame}
      </div>

    </div>
  );
});
export default connect(({ menu }) => ({
  allMenu: menu.all,
}))(TitleFrame);
