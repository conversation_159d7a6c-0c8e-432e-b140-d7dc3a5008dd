import React, { useState, useEffect, useRef, useImperativeHandle } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Card,
  Form,
  Input,
  Icon,
  Button,
  Select,
  Modal,
  message,
  Spin,
  InputNumber,
  DatePicker,
} from 'antd';
import request from '@/utils/request';
import omit from 'lodash/omit';
import styles from '../styles.less';
import ComboGrid from '@/components/ComboGrid';
const InputGroup = Input.Group;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Option } = Select;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const textAreaFormItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

function AddUserList(props) {
  const { getFieldDecorator } = props.form;
  const [spinning, setSpinning] = useState(true);

  useEffect(() => {
    setSpinning(false);
  }, []);

  // 新增
  useImperativeHandle(props.cRef, () => ({
    handleAdd: () => {
      return new Promise(function(resolve) {
        props.form.validateFields((err, fieldsValue) => {
          if (err) return;
          if (fieldsValue.staffName[0].orgName) {
            Modal.confirm({
              content: `员工关联的原组织为：${fieldsValue.staffName[0].orgName}，是否确认调整到当前组织？`,
              onOk() {
                request('orgauth/StaffOrgRelController/updateStaffOrgRel.do', {
                  data: {
                    orgId: props.orgId,
                    staffId: fieldsValue.staffName[0].staffId,
                  },
                }).then(res => {
                  setSpinning(false);
                  if (res) {
                    resolve(true);
                    message.success('新增成功！');
                  } else {
                    message.error('新增失败！');
                  }
                });
              },
            });
          }
        });
      });
    },
  }));

  const onConfirm = value => {
    if (value.length > 0) {
      props.form.setFieldsValue({
        staffTypeName: value[0].staffTypeName,
        staffCode: value[0].staffCode,
        staffAccount: value[0].staffAccount,
      });
    }
  };

  return (
    <Spin spinning={spinning}>
      <Form>
        <Row>
          <Col span={24}>
            <Form.Item label="员工名称" {...formItemLayout}>
              {getFieldDecorator('staffName', {
                rules: [
                  {
                    required: true,
                    message: '员工名称不能为空',
                  },
                ],
              })(
                <ComboGrid
                  url="orgauth/StaffController/qryManageStaffGridData.do"
                  popupStyle={{ width: 560 }}
                  placeholder="请选择员工"
                  searchPlaceholder="请输入员工名称进行搜索"
                  label="staffName"
                  rowKey="staffId"
                  pick="radio"
                  params={{ createType: '1100' }}
                  onConfirm={onConfirm}
                  columns={[
                    {
                      title: '员工名称',
                      dataIndex: 'staffName',
                    },
                    {
                      title: '员工编码',
                      dataIndex: 'staffCode',
                      ellipsis: true,
                    },
                    {
                      title: '员工类型',
                      dataIndex: 'staffTypeName',
                      ellipsis: true,
                    },
                    {
                      title: '账号',
                      dataIndex: 'staffAccount',
                    },
                  ]}
                />
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="员工类型" {...formItemLayout}>
              {getFieldDecorator('staffTypeName')(<Input disabled />)}
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Form.Item label="员工编码" {...formItemLayout}>
              {getFieldDecorator('staffCode')(<Input disabled />)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="账号" {...formItemLayout}>
              {getFieldDecorator('staffAccount')(<Input disabled />)}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Spin>
  );
}

export default connect(({}) => ({}))(Form.create()(AddUserList));
