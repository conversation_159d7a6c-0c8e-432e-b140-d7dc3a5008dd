import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, Input, message, Icon, Button } from 'antd';
import { connect } from 'dva';
import { sendForgetPwdSmsCode, resetUserPwd } from '@/services/login';
import { encryptUtf8 } from '@/utils/utils';
import styles from '../less/login.less';


const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

function checkPwd(rule, value, callback) {
  if (value === undefined) {
    callback();
    return;
  }
  let hasNumber = false;
  let hasLowcase = false;
  let hasSpecLetter = false;
  let hasUppercase = false;
  const missInfo = [];
  const specialReg = new RegExp(
    '[`\\~\\!@#\\$%^&\\*\\(\\)\\-_+=\\[\\{\\]\\}\\|\\\\:;\'"/\\?,\\.<>]'
  );
  if (/[0-9]/.test(value)) {
    hasNumber = true;
  } else {
    missInfo.push('数字');
  }
  if (/[a-z]/.test(value)) {
    hasLowcase = true;
  } else {
    missInfo.push('小写字母');
  }
  if (/[A-Z]/.test(value)) {
    hasUppercase = true;
  } else {
    missInfo.push('大写字母');
  }
  if (specialReg.test(value)) {
    hasSpecLetter = true;
  } else {
    missInfo.push('特殊字符');
  }
  if (value.length < 6 || value.length > 20) {
    missInfo.push('长度必须是6-20位');
  }

  if (!hasNumber || !hasLowcase || !hasSpecLetter || !hasUppercase) {
    callback(`缺少：${missInfo.join(',')}`);
  } else {
    callback();
  }
}

const ResetPassword = props => {
  const { verifyCode, visible, close, form, dispatch, needVerifyCode } = props;
  const [loading, setLoading] = useState(false);
  // init 初始化获取验证码  loading 为验证码发送倒计时状态 reload 重获验证码
  const [codeStatus, setCodeStatus] = useState('init');
  const [validateText, setValidateText] = useState('获取验证码');
  const timer = useRef(null);
  const { getFieldDecorator, resetFields } = form;

  function compareToFirstPassword(rule, value, callback) {
    if (value && value !== form.getFieldValue('newPwd')) {
      callback('两次输入的密码不一致');
    } else {
      callback();
    }
  }

  const getVerifyCode = () => {
    dispatch({
      type: 'login/setVerifyCode',
    });
  };

  const reset = () => {
    form.validateFields((err, values) => {
      if (err) {
        return;
      }
      setLoading(true);
      resetUserPwd({
        userCode: values.userCode,
        newPwd: encryptUtf8(values.newPwd),
        smsCode: values.smsCode,
      }).then(res => {
        if (res.resultCode === '0') {
          message.success('用户密码已修改，请重新进行登录');
          close();
        } else {
          message.error(res.resultMsg);
          getVerifyCode();
        }
        setLoading(false);
      });
    });
  };

  useEffect(() => {
    // 重置验证码
    getVerifyCode();
    if (!needVerifyCode) {
      dispatch({
        type: 'login/needVerifyCode',
        payload: true,
      });
    }
  }, []);

  const getCode = () => {
    if (codeStatus === 'loading') {
      return;
    }

    const userCode = props.form.getFieldValue('userCode');
    if (!userCode) {
      message.warn('请输入账号');
      return;
    }

    const verifyCodeInput = props.form.getFieldValue('verifyCode');
    if (!verifyCodeInput) {
      message.warn('请输入验证码');
      return;
    }


    // 发送验证码
    sendForgetPwdSmsCode({
      userCode,
      verifyCode: verifyCodeInput,
    }).then(result => {
      if (result.resultCode === '0') {
        resetFields(['smsCode']);
        setCodeStatus('loading');
        message.info('短信验证码已发送，请查收！');

        let time = 60;
        setValidateText(`${time}s后重新获取`);
        timer.current = setInterval(() => {
          time -= 1;
          if (time >= 0) {
            setValidateText(`${time}s后重新获取`);
          } else {
            setValidateText('重新获取验证码');
            clearInterval(timer.current);
            setCodeStatus('reload');
            // 倒计时加载完成，重置验证码
            getVerifyCode();
          }
        }, 1000);
      } else {
        // 重置验证码
        getVerifyCode();

        dispatch({
          type: 'login/needVerifyCode',
          payload: true,
        });
        message.error(result.resultMsg);
      }
    });
  };

  return (
    <Modal
      title="重置密码"
      visible={visible}
      onOk={reset}
      onCancel={close}
      confirmLoading={loading}
      destroyOnClose
      centered
      maskClosable={false}
    >
      <Form layout="horizontal" className={styles.resetForm} onSubmit={() => {}}>
        <Form.Item style={{ marginBottom: 16 }} label="账号" {...formItemLayout}>
          {getFieldDecorator('userCode', {
            rules: [
              {
                required: true,
                whitespace: true,
                message: '请输入账号',
              },
            ],
          })(<Input className={styles.resetInput} allowClear placeholder="请输入账号" />)}
        </Form.Item>
        <Form.Item style={{ marginBottom: 16 }} label="验证码" {...formItemLayout}>
          {getFieldDecorator('verifyCode', {
            rules: [
              {
                required: true,
                message: '验证码错误',
              },
            ],
          })(
            <Input
              prefix={<Icon type="check-circle" style={{ color: 'rgba(0,0,0,.25)' }} />}
              addonAfter={(
                <img
                  src={verifyCode}
                  onClick={() => {
                    getVerifyCode();
                    resetFields(['verifyCode']);
                  }}
                  style={{ cursor: 'pointer' }}
                  width={64}
                  height={28}
                  alt="code"
                />
              )}
              placeholder="验证码"
              className={styles.resetInput}
            />
          )}
        </Form.Item>
        <Form.Item style={{ marginBottom: 16 }} label="短信验证码" {...formItemLayout}>
          {getFieldDecorator('smsCode', {
            rules: [
              {
                required: true,
                message: '输入的验证码有误',
              },
            ],
          })(
            <Input
              className={styles.resetGetCodeBtn}
              prefix={<Icon type="lock" style={{ color: 'rgba(0,0,0,.25)' }} />}
              placeholder="请输入验证码"
              maxLength={6}
              addonAfter={(
                <div>
                  <Button type="link" onClick={getCode}>{validateText}</Button>
                </div>
              )}
            />
          )}
        </Form.Item>
        <Form.Item style={{ marginBottom: 16 }} label="新密码" {...formItemLayout}>
          {form.getFieldDecorator('newPwd', {
            rules: [
              {
                required: true,
                message: '请输入新密码',
              },
              {
                validator: checkPwd,
              },
            ],
          })(
            <Input
              type="password"
              allowClear
              placeholder="6-20位，包含数字、大小写字母和特殊字符"
              className={styles.resetInput}
              autocomplete="new-password"
            />
          )}
        </Form.Item>
        <Form.Item style={{ marginBottom: 16 }} label="确认密码" {...formItemLayout}>
          {form.getFieldDecorator('cfmPwd', {
            rules: [
              {
                required: true,
                message: '请确认你的新密码',
              },
              {
                validator: compareToFirstPassword,
              },
            ],
          })(
            <Input type="password" allowClear placeholder="确认密码" className={styles.resetInput} />
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default connect(({ login }) => ({
  login,
  verifyCode: login.verifyCode,
  needVerifyCode: login.needVerifyCode,
}))(Form.create()(ResetPassword));
