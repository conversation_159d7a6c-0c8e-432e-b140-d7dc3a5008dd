/* eslint-disable global-require */
import React, { useState, useEffect } from 'react';
import { Typo<PERSON>, Tooltip, Spin, Tabs, Button } from 'antd';
import { connect } from 'dva';
import { qryKlKnowledgeInfoGrid } from '@/pages/Librarys/services-use';
import { TYPE_VALUE, LIBRARYS_TYPE } from '@/pages/Librarys/const';
import styles from './ProdLibPage.module.less';
import commonStyles from '../common.module.less';

const ProdLibPage = ({ attrs = {}, goToStep, currentKey, setCurrentKey }) => {
  const { industryType = [] } = attrs;
  const [allTypeList, setAllTypeList] = useState({});
  const [hotProdList, setHotProdList] = useState([]);
  const [spinning, setSpinning] = useState(false);
  const [activeKey, setActiveKey] = useState(industryType[0]?.value);

  const getTabPaneInfo = async key => {
    const res = await qryKlKnowledgeInfoGrid({
      knowledgeClassType: TYPE_VALUE.prod,
      industryType: key,
      pageSize: 8,
    });

    setAllTypeList({
      ...allTypeList,
      [key]: res,
    });
  };

  const initData = async () => {
    setSpinning(true);
    const res = await qryKlKnowledgeInfoGrid({
      knowledgeClassType: TYPE_VALUE.prod,
      pageSize: 4,
    });
    setActiveKey(industryType[0]?.value);
    getTabPaneInfo(industryType[0]?.value);
    setHotProdList(res);
    setSpinning(false);
    setCurrentKey('');
  };

  useEffect(() => {
    if (currentKey === TYPE_VALUE.prod) {
      initData();
    }
  }, [currentKey]);

  const handleShowMore = () => {
    goToStep('2', {
      searchParams: {
        knowledgeClassType: TYPE_VALUE.prod,
        sort: 4,
      },
    });
  };

  return (
    <div>
      <div className={commonStyles.topRowPhoto} style={{ backgroundImage: `url(${require('@/pages/Librarys/img/prod-head.png')})` }} />
      <Spin spinning={spinning}>
        <div className={commonStyles.module} style={{ backgroundColor: 'white' }}>
          <div className={commonStyles.moduleTitle}>
            <span>热门产品资料</span>
            <Button type="link" className={commonStyles.showMoreBtn} onClick={handleShowMore}>
              更多 {'>>'}
            </Button>
          </div>
          <div className={commonStyles.moduleContent}>
            {Array.isArray(hotProdList) && hotProdList.length > 0 ? (
              hotProdList.map(item => (
                <div
                  key={item.id}
                  className={commonStyles.moduleBlock}
                  onClick={() => {
                    goToStep('3', {
                      type: LIBRARYS_TYPE.PROD,
                      fromHome: true,
                      knowledgeInfoId: item.knowledgeInfoId,
                    });
                  }}
                >
                  <div className={commonStyles.left}>
                    <div className={commonStyles.titleWrap}>
                      <Typography.Paragraph ellipsis={{ rows: 2 }}>
                        <font className={commonStyles.title}>{item.knowledgeName}</font>
                      </Typography.Paragraph>
                    </div>
                    <div className={commonStyles.detail}>
                      <Tooltip title={item.knowledgeDesc} placement="bottomLeft" mouseEnterDelay={1}>
                        <Typography.Paragraph ellipsis={{ rows: 2 }}>{item.knowledgeDesc}</Typography.Paragraph>
                      </Tooltip>
                    </div>
                  </div>
                  <div className={commonStyles.right}>
                    <div className={commonStyles.assist} />
                    <img
                      className={commonStyles.photo}
                      alt="avatar"
                      src={`portal/FileStoreController/download.do?docNbr=${item.iconNbr}`}
                      onError={e => {
                        e.target.src = require('@/pages/Librarys/img/default-icon.png');
                      }}
                    />
                  </div>
                </div>
              ))
            ) : (
              <span style={{ fontSize: '16px' }}>暂无数据</span>
            )}
          </div>
        </div>
        <div className={commonStyles.end} />
        <div className={commonStyles.module}>
          <Tabs
            activeKey={activeKey}
            renderTabBar={(props, DefaultTabBar) => <DefaultTabBar {...props} className={styles.prodTabs} />}
            onTabClick={key => {
              getTabPaneInfo(key);
              setActiveKey(key);
            }}
          >
            {Array.isArray(industryType) &&
              industryType.map(industry => (
                <Tabs.TabPane tab={industry.label} key={industry.value}>
                  <div className={commonStyles.moduleContent}>
                    {Array.isArray(allTypeList?.[industry.value]) &&
                      allTypeList[industry.value].map(item => (
                        <div
                          key={item.knowledgeInfoId}
                          className={commonStyles.moduleBlock}
                          onClick={() => {
                            goToStep('3', {
                              type: LIBRARYS_TYPE.PROD,
                              fromHome: true,
                              knowledgeInfoId: item.knowledgeInfoId,
                            });
                          }}
                        >
                          <div className={commonStyles.left}>
                            <div className={commonStyles.titleWrap}>
                              <Typography.Paragraph ellipsis={{ rows: 2 }}>
                                <font className={commonStyles.title}>{item.knowledgeName}</font>
                              </Typography.Paragraph>
                            </div>
                            <div className={commonStyles.detail}>
                              <Tooltip title={item.knowledgeDesc} placement="bottomLeft" mouseEnterDelay={1}>
                                <Typography.Paragraph ellipsis={{ rows: 2 }}>{item.knowledgeDesc}</Typography.Paragraph>
                              </Tooltip>
                            </div>
                          </div>
                          <div className={commonStyles.right}>
                            <div className={commonStyles.assist} />
                            <img
                              className={commonStyles.photo}
                              alt="avatar"
                              src={`portal/FileStoreController/download.do?docNbr=${item.iconNbr}`}
                              onError={e => {
                                e.target.src = require('@/pages/Librarys/img/default-icon.png');
                              }}
                            />
                          </div>
                        </div>
                      ))}
                    {(!Array.isArray(allTypeList?.[industry.value]) || allTypeList?.[industry.value].length === 0) && (
                      <div style={{ marginTop: '15px', fontSize: '16px' }}>暂无数据</div>
                    )}
                  </div>
                </Tabs.TabPane>
              ))}
          </Tabs>
        </div>
      </Spin>
      <div className={commonStyles.end} />
    </div>
  );
};

export default connect(({ librarys }) => ({
  attrs: librarys.attrs,
}))(ProdLibPage);
