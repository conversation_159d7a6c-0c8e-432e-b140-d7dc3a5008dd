import request from '@/utils/request';
import { setItem, getItem ,operActionLog} from '@/utils/utils';
import mapping from '@/services/login';
import debounce from 'lodash/debounce';
import moment from 'moment';

/**
 * 记录操作轨迹日志.
 * create by zhouwenqing 2017/7/26 .
 */

// 填写各个中心自己的操作轨迹接受controller地址
const baseUrl = 'portal/TraceInfoController';
const traceLogAction = {
  baseUrl,
  /**
   * 开启一个新的计时器.
   * 返回一个用于记录操作时长的对象，调用对象上的duration方法，返回时间差.
   */
  newStopwatch() {
    return new (function() {
      const self = this;
      this.beginTime = new Date().getTime();

      /**
       * 返回时长，从调用newStopwatch到调用duration方法之间的时长.
       * @return {number}
       */
      this.duration = function() {
        return new Date().getTime() - self.beginTime;
      };
    })();
  },
  /**
   * 开始记录用户的最新的操作时间
   * 以lastOpDate保存在session storage中
   */
  start() {
    setItem('lastOpDate', moment().format('YYYY-MM-DD HH:mm:ss'));

    // 防止重复绑定
    traceLogAction.stop();
    // 开始监听
    window.addEventListener('click', traceLogAction.updateLastOpDate);
    window.addEventListener('keydown', traceLogAction.updateLastOpDate);
    window.addEventListener('mousemove', traceLogAction.updateLastOpDate);
  },
  stop() {
    window.removeEventListener('click', traceLogAction.updateLastOpDate);
    window.removeEventListener('keydown', traceLogAction.updateLastOpDate);
    window.removeEventListener('mousemove', traceLogAction.updateLastOpDate);
  },
  updateLastOpDate: debounce(() => {
    if (getItem('status') === mapping.LOGINED) {
      setItem('lastOpDate', moment().format('YYYY-MM-DD HH:mm:ss'));
    }
  }, 500),
  /**
   * 调用controller总入口方法.
   * @param {object} params
   *  {
   *    "accNum": "18050417049",
   *    "centerCode": "13",
   *    "menuId": 1,
   *    "menuName": "权限管理",
   *    "serviceCode": "************",
   *    "serviceName": "新增权限",
   *    "operDuration": 2000,
   *    "operStatus": "成功",
   *    "comment": "",
   *    "serviceContent": {
   *       "privId": 1,
   *       "privName": "查询权限"
   *     }
   * }
   * @param {function} success
   */
  traceLog(params, success) {
    return request(`${baseUrl}/traceLog.do`, {
      method: 'post',
      data: { ...params, ignoreSession: true },
    }).then(res => typeof success === 'function' && success(res));
  },
  /**
   * 记录菜单点击操作.
   * 打开菜单的时候调用这个方法，传入menuId和menuName，然后在调用traceLog总入口方法。
   * @param params{
   * "menuId":
   * "menuName":
   * }
   * @param success
   */
  traceOpenMenuLog(params, success) {

    // 调用新的日志记录方法
    operActionLog( {actionType: '1000', actionModule: params.menuId,action: '1000', handResult:'',  actionMsg:'' })
    return traceLogAction.traceLog(
      {
        serviceCode: '************',
        serviceName: '打开菜单页面',
        operStatus: '成功',
        ...params,
      },
      success
    );
  },
  /**
   * 记录菜单点击操作.
   * 关闭菜单的时候调用这个方法，传入menuId和menuName，然后在调用traceLog总入口方法。
   * @param params{
   * "menuId":
   * "menuName":
   * }
   * @param success
   */
  traceCloseMenuLog(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '关闭菜单页面',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 记录修改员工信息操作轨迹.
   * @param params{
   * "menuId":
   * "menuName":
   * "serviceContent": {"staffId":,"staffName":...  }
   * "operDuration":
   * }
   * @param success
   */
  traceUpdateStaffInfo(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '修改员工信息',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 记录操作员登录操作轨迹..
   * @param params{
   * "operDuration":
   * }
   * @param success
   */
  traceLoginInfo(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '登录系统',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 记录操作员登出操作轨迹..
   * @param params{
   * "operDuration":
   * }
   * @param success
   */
  traceLogoutInfo(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '登出系统',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增权限.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName":
   * ...
   * }
   * }
   * @param success
   */
  tracePrivAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增权限',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除权限.
   * @param params{
   * "operDuration":,
   * menuid,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName":
   * ...
   * }
   * }
   * @param success
   */
  tracePrivDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除权限',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 添加权限包含功能.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":
   * privRefType,privRefName,privRefId
   *
   * }
   * }
   * @param success
   */
  tracePrivFuncAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '添加权限包含功能项',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除权限包含功能.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName":
   * delInfos:[
   * {privRefId,privRefType,funcName,privFuncRelId}
   * ]
   * }
   * }
   * @param success
   */
  tracePrivFuncDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除权限包含功能项',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 添加权限授予角色.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName":
   * addInfos:[
   * {sysRoleCode,sysRoleName,sysRoleId}
   * ]
   * }
   * }
   * @param success
   */
  tracePrivRoleAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '添加权限授予角色',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除权限授予角色.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName":
   * delInfos:[
   * {privGrantId,sysRoleCode,sysRoleName,sysRoleId}
   * ]
   * }
   * }
   * @param success
   */
  tracePrivRoleDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除权限授予角色',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 添加权限授予岗位.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName":
   * addInfos:[
   * {sysPostId,sysPostName}
   * ]
   * }
   * }
   * @param success
   */
  tracePrivPostAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '添加权限授予岗位',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除权限授予岗位.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName":
   * delInfos:[
   * {privGrantId,sysPostId,sysPostName}
   * ]
   * }
   * }
   * @param success
   */
  tracePrivPostDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除权限授予岗位',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 添加权限授予工号.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName":
   * delInfos:[
   * {privGrantId,sysPostId,sysPostName}
   * ]
   * }
   * }
   * @param success
   */
  tracePrivUserAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '添加权限授予用户',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除权限授予工号.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName":
   * delInfos:[
   * {privGrantId,sysPostId,sysPostName}
   * ]
   * }
   * }
   * @param success
   */
  tracePrivUserDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除权限授予用户',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除权限授予工号.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName":
   * delInfos:[
   * {privGrantId,sysPostId,sysPostName}
   * ]
   * }
   * }
   * @param success
   */
  tracePrivUserDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除权限授予用户',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增权限包含数据.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName"
   * ...
   * }
   * }
   * @param success
   */
  tracePrivDataAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增权限包含数据',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除权限包含数据.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName"
   * ...
   * }
   * }
   * @param success
   */
  tracePrivDataDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除权限包含数据',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增权限包含数据规则.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName"
   * ...
   * }
   * }
   * @param success
   */
  tracePrivDataRuleAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增权限包含数据规则',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除权限包含数据规则.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName"
   * ...
   * }
   * }
   * @param success
   */
  tracePrivDataRuleDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除权限包含数据规则',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 复制新增权限.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * "privId":,
   * "privName"
   * ...
   * }
   * }
   * @param success
   */
  tracePrivCopy(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '复制新增权限',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增系统用户.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,sysUserCode,staffId
   * }
   * }
   * @param success
   */
  traceUserAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增系统用户',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除系统用户.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * delInfos:[
   * {sysUserId,sysUserCode}
   * ]
   * }
   * }
   * @param success
   */
  traceUserDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '失效系统用户',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增工号关联角色.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,sysRoleId,regionId,sysUserRoleId
   * }
   * }
   * @param success
   */
  traceUserRoleAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增用户关联角色',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除工号关联角色.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceUserRoleDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除用户关联角色',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增工号关联岗位.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceUserPostAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增用户关联岗位',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除工号关联岗位.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceUserPostDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除用户关联岗位',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增工号关联权限.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceUserPrivAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增用户关联权限',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除工号关联权限.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceUserPrivDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除用户关联权限',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增登录设置信息.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceUserLoginLimitAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增登录设置信息',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除登录设置信息.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceUserLoginLimitDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除登录设置信息',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 生效登录设置信息.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceUserLoginLimitEnable(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '生效登录设置信息',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 失效登录设置信息.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceUserLoginLimitDisable(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '失效登录设置信息',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 复制新增工号.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceUserCopy(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '复制新增用户',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增岗位.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  tracePostAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增岗位',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除岗位.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  tracePostDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除岗位',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增岗位关联角色.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  tracePostRoleAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增岗位关联角色',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除岗位关联角色.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  tracePostRoleDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除岗位关联角色',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增岗位关联工号.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  tracePostUserAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增岗位关联用户',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除岗位关联工号.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  tracePostUserDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除岗位关联用户',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增岗位关联权限.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  tracePostPrivAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增岗位关联权限',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除岗位关联权限.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  tracePostPrivDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除岗位关联权限',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增岗位关联组织.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  tracePostOrgAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增岗位关联组织',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除岗位关联组织.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  tracePostOrgDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除岗位关联组织',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 复制新增岗位.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  tracePostCopy(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '复制新增岗位',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增角色.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceRoleAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增角色',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除角色.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceRoleDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除角色',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增角色关联权限.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceRolePrivAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增角色关联权限',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除角色关联权限.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceRolePrivDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除角色关联权限',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增角色关联工号.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceRoleUserAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增角色关联用户',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除角色关联工号.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceRoleUserDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除角色关联用户',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 新增角色关联岗位.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceRolePostAdd(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '新增角色关联岗位',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 删除角色关联岗位.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceRolePostDel(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '删除角色关联岗位',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
  /**
   * 复制新增角色.
   * @param params{
   * "operDuration":,
   * menuId,
   * menuName,
   * serviceContent:{
   * sysUserId,
   * delInfos:[
   * {sysUserRoleId,regionId, regionName,sysRoleId,sysRoleName}
   * ]
   * }
   * }
   * @param success
   */
  traceRoleCopy(params, success) {
    params = $.extend(
      {
        serviceCode: '************',
        serviceName: '复制新增角色',
        operStatus: '成功',
      },
      params
    );
    return traceLogAction.traceLog(params, success);
  },
};

export default traceLogAction;
