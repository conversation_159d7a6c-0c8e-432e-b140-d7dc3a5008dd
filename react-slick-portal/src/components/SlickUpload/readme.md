
对Upload的二次封装。

新增`length`和`size`，分别用于附件个数和大小的校验
增强`accept`，根据配置值，内部自动校验附件格式
当`listType="picture-card"`时，自动开启查看大图的功能
其余[props](https://ant.design/components/upload-cn/#API)与 antd upload一致

注：回参格式约定，必须包含`url`，如果是图片建议返回`thumbUrl`

内置上传地址 'portal/FileStoreController/upload.do'，POST 类型
内置删除地址 'portal/FileStoreController/delete.do'，GET 类型 入参 {docNbr}

上传成功，预期回参

```json
[{
  "docId": 128805, 
  "docNbr":"",
  "fileName": "logo-new.png",
  "fileGetUrl": "", // 下载链接
  "photoUrl":"",// 缩略图展示
}]
```

## API

| 参数      | 说明                                      | 类型         | 默认值 |
|----------|------------------------------------------|-------------|-------|
| fileList | 已经上传的文件列表。uid,name, status: 'done',url是upload组件需要的字段必须包含 | object[{uid,name, status: 'done',url, docNbr,fileGetUrl,fileGetUrl,docId}] | [] |
| length | 允许上传的附件个数 | number | 999(表示不限制) |
| size | 允许单个附件的最大size，单位字节。1 * 1014 * 1024 = 1MB | number | 1024*1024*1024(1GB) |
| accept | 允许的附件个数,默认不限制格式。可选值：accept = '.doc,.docx,.xls,.xlsx,.pdf' | string | - |
| disabled | 禁用上传，删除动作 | boolean | false |


更多[props](https://ant.design/components/upload-cn/#API)

## demo

普通用法

```jsx
import SlickUpload from '@/components/SlickUpload';

<SlickUpload />
```

与form结合使用

```jsx
import SlickUpload from '@/components/SlickUpload';

<Form.Item label="附件">
  {getFieldDecorator('files', {
    initialValue: fileList,
    valuePropName: 'fileList',
  })(<SlickUpload length={2} accept=".png,.jpg" />)}
</Form.Item>
```

```jsx
<Form.Item label="附件">
  {getFieldDecorator('files', {
    initialValue: fileList,
    valuePropName: 'fileList',    
    getValueFromEvent: e => {
      console.log('Upload event:', e);
      if (Array.isArray(e)) {
        return e;
      }
      return e && e.fileList;
    },
  })(<SlickUpload length={2} accept=".png,.jpg" />)}
</Form.Item>
```
