const nameList = [
  'registerProvinceId',
  'registerCityId',
  'registerAreaId',
  'supportAreaId',
  'terminalCapacity',
  'companyType',
  'sceneSub',
  'dictLevel',
  'releaseChannel',
];

const caseAttrHandler = (row, value) => {
  const { key, detail, attrs } = row;
  if (['generalizedLabel'].includes(key)) {
    return attrs?.caseGeneralizedLabel?.getLabel(value);
  }
  return detail[key];
};

const certHandler = row => {
  const { key, detail, attrs } = row;
  if (key === 'downloadPrivType') {
    return attrs?.certDownloadPrivType?.getLabel(detail[key]);
  }
  return detail[key];
};

/**
 * 根据类型渲染对应详情
 * @param {row} row
 * @returns
 */
export const renderDescItem = row => {
  const { detail, key, colFields, attrs } = row;
  if (colFields?.includes(key)) {
    const info = detail?.knowledgeAttrs?.find(item => item.attrNbr === key);
    const _attrVal = info?.attrValue;

    // 1-方案库 2-案例库 3-投标库 4-资质库
    // 案例库 attr 额外处理逻辑
    const caseSpecialKey = ['generalizedLabel'].includes(key);
    if (detail.knowledgeClassType === '2' && caseSpecialKey) {
      return caseAttrHandler(row, _attrVal);
    }

    // 纵表字段如果返回的是value 不是label  进行二次处理获取label
    if (['isinvite', 'defandStatus', 'is5G', 'workerDepart', 'generalizedLabel'].includes(key)) {
      return attrs?.[key]?.getLabel(_attrVal);
    }

    if (key === 'businessStartTime') {
      const businessEndTime = detail?.knowledgeAttrs?.find(item => item.attrNbr === key)?.attrValue;
      return `${_attrVal}-${businessEndTime}`;
    }

    if (key === 'companyQualify') {
      return _attrVal?.replaceAll(',', '、');
    }

    if (nameList.includes(key)) {
      return info?.attrName;
    }

    return _attrVal;
  }

  // 1-方案库 2-案例库 3-投标库 4-资质库
  // 资质库额外处理逻辑
  if (detail.knowledgeClassType === '4') {
    return certHandler(row);
  }
  if (key === 'downloadPrivType') {
    return attrs?.downloadPrivType?.getLabel(detail.downloadPrivType);
  }
  return detail[key];
};
