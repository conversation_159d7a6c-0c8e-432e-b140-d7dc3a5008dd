import React, { useEffect, useState } from 'react';
import { Button, Card, Col, Form, Input, message, Row, Select, Spin } from 'antd';
import { connect } from 'dva';
import style from './index.less';
import { getList } from '@/pages/GroupCust/GroupCustManage/services';
import { queryGroupExt } from '@/pages/GroupCust/GroupCustRedListManage/services';
import { queryGroupFields } from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/const';
import RedList from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/RedList';
import BaseInfo from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/BaseInfo';
import IdenInfo from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/iden';
import OrderOperatorInfo from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/OrderOperatorInfo';
import UserOwner from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/UserOwner';
import NetworkCoverInfo from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/NetworkCoverInfo';
import ResourceCoverageInfo from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/ResourceCoverageInfo';
import MailingAddressInfo from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/MailingAddressInfo';
import RegionInfo from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/RegionInfo';
import EnterpriseManagerInfo from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/EnterpriseManagerInfo';
import ClassifiedInfo from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/ClassifiedInfo';
import ExtendedInfo from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/ExtendedInfo';
import OtherInfo from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/components/OtherInfo';

const Edit = props => {
  const {
    size: { height },
    form,
  } = props;
  const [loading, setLoading] = useState(false);
  const [cmiotInfo, setCmiotInfo] = useState({});
  const [baseInfo, setBaseInfo] = useState({});
  const { getFieldDecorator } = form;

  const queryBaseInfo = () => {
    form.validateFields((err, values) => {
      const { groupQueryType, groupName, groupId } = values;
      if (!err) {
        setLoading(true);
        let type = '';
        let keyword = '';
        let value = '';
        switch (groupQueryType) {
          case 'groupName':
            type = 'cust_IQueryEnterpriseCSV_queryEnterpriseByGroupName';
            keyword = 'groupName';
            value = groupName;
            break;
          case 'groupId':
            type = 'cust_IQueryEnterpriseCSV_queryEnterpriseByGroupId';
            keyword = 'groupId';
            value = groupId;
            break;
          default:
            type = '';
        }

        getList({
          type,
          data: {
            [keyword]: value,
          },
          pageFlag: 0,
          pageInfo: {
            currentPage: 1,
            pageSize: 5,
          },
        })
          .then(res => {
            const { resultCode, resultObject, resultMsg } = res;
            if (resultCode === 'TRUE') {
              if (!Array.isArray(resultObject?.rspParam?.busiInfo?.outData) || resultObject?.rspParam?.busiInfo?.outData.length === 0) {
                message.error('没有查询到该集团信息！');
                setLoading(false);
                return;
              }
              const data = resultObject.rspParam.busiInfo.outData[0];
              setBaseInfo(data);
              setLoading(false);
            } else {
              message.error(resultMsg);
              setLoading(false);
            }
          })
          .catch(() => {
            setLoading(false);
            message.error('查询失败');
          })
          .always(() => {
            setLoading(false);
          });
      }
    });
  };

  useEffect(() => {
    if (!baseInfo.ORGA_ENTERPRISE_ID) {
      // eslint-disable-next-line no-console
      console.log('partyId is null!');
      return;
    }
    queryGroupExt({ partyId: baseInfo.ORGA_ENTERPRISE_ID }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        setCmiotInfo(resultObject?.rspParam?.busiInfo?.outData);
      } else {
        message.error(resultMsg);
      }
    });
  }, [baseInfo.ORGA_ENTERPRISE_ID]);

  /**
   * 返回
   */
  const goBack = () => {
    props.goToStep(1);
  };
  const renderMoreConditions1 = () => {
    const groupQueryType = form.getFieldValue('groupQueryType');
    switch (groupQueryType) {
      case 'groupName':
        /* 查询条件-集团名称 */
        return (
          <>
            <Col span={6}>
              <Form.Item label="集团名称">
                {getFieldDecorator('groupName', {
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
          </>
        );

      case 'groupId':
        /* 查询条件-集团编码 */
        return (
          <Col span={6}>
            <Form.Item label="集团编码">
              {getFieldDecorator('groupId', {
                rules: [{ required: true, message: '请输入' }],
              })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
        );
      default:
        return null;
    }
  };

  const reset = () => {
    form.resetFields();
  };

  return (
    <Spin spinning={false}>
      <Card title="新增集团红名单" className="cute" style={{ minHeight: height }} extra={<Button onClick={goBack}>返回</Button>}>
        <div className={style.content}>
          <Form className="flow fix-label">
            <Row gutter={24}>
              <Col span={6}>
                <Form.Item label="查询类型">
                  {getFieldDecorator('groupQueryType', {
                    initialValue: 'groupId', // 默认查询集团编码
                  })(
                    <Select placeholder="请选择">
                      {queryGroupFields.map(({ disable, key, label }) => (
                        <Select.Option disabled={disable} key={key} value={key}>
                          {label}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              {renderMoreConditions1()}
              <Col span={12} className="text-right">
                <Button type="primary" className={style.buttonStyle1} onClick={queryBaseInfo} loading={loading}>
                  选择集团
                </Button>
                <Button className={`margin-left ${style.buttonStyle}`} onClick={reset}>
                  重置
                </Button>
              </Col>
            </Row>
          </Form>
          <BaseInfo baseInfo={baseInfo} cmiotInfo={cmiotInfo} />
          <IdenInfo baseInfo={baseInfo} />
          <OrderOperatorInfo baseInfo={baseInfo} />
          <UserOwner baseInfo={baseInfo} />
          <NetworkCoverInfo baseInfo={baseInfo} />
          <ResourceCoverageInfo baseInfo={baseInfo} />
          <RegionInfo baseInfo={baseInfo} />
          <MailingAddressInfo baseInfo={baseInfo} />
          <EnterpriseManagerInfo baseInfo={baseInfo} />
          <ClassifiedInfo baseInfo={baseInfo} />
          <ExtendedInfo baseInfo={baseInfo} />
          <OtherInfo baseInfo={baseInfo} />
          <RedList baseInfo={baseInfo} FatherProps={props} />
        </div>
      </Card>
    </Spin>
  );
};

export default connect(({ setting, groupCustView }) => ({
  size: setting.size,
  ...groupCustView,
}))(Form.create()(Edit));
