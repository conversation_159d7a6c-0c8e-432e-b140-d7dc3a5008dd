/* 黑名单信息填写 */
import React, { useEffect, useState } from 'react';
import { Button, Card, Col, Form, Input, message, Row, Select, Spin } from 'antd';
import style from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/index.less';
import { addBlackListInfo } from '@/pages/GroupCust/GroupCustBlackListManage/services';
import { PA_IDEN_TYPE } from '@/pages/GroupCust/GroupCustBlackListManage/const';
import { queryCommonStaticDataEnumMap } from '@/services/commonStaticData';
import { queryIdentificationsByPartyId } from '@/pages/GroupCust/GroupCustRedListManage/services';

// 简单的样式对象
const styles = {
  container: {
    maxWidth: '400px',
    margin: '50px auto',
    padding: '20px',
    border: '1px solid #ccc',
    borderRadius: '8px',
    textAlign: 'center',
  },
  formGroup: {
    marginBottom: '20px',
  },
  submitButton: {
    padding: '10px 20px',
    fontSize: '16px',
    cursor: 'pointer',
    backgroundColor: '#4CAF50',
    color: '#fff',
    border: 'none',
    borderRadius: '4px',
  },
  overlay: {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },
  popup: {
    backgroundColor: '#fff',
    padding: '20px',
    borderRadius: '8px',
    textAlign: 'center',
    width: '300px',
  },
  popupMessage: {
    marginBottom: '20px',
    fontSize: '16px',
    color: '#333',
  },
};

const RedList = props => {
  const { form, baseInfo, FatherProps } = props;
  const [isPopupVisible, setPopupVisible] = useState(false);
  const [popupMessage, setPopupMessage] = useState('');
  const { getFieldDecorator } = form;

  const [idenInfo, setIdenInfo] = useState({});

  useEffect(() => {
    if (!baseInfo.ORGA_ENTERPRISE_ID) {
      // eslint-disable-next-line no-console
      console.log('partyId is null!');
      return;
    }
    queryIdentificationsByPartyId({ partyId: baseInfo.ORGA_ENTERPRISE_ID, maskFlag: '0' }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        const data = Array.isArray(resultObject?.rspParam?.busiInfo?.outData) ? resultObject.rspParam.busiInfo.outData[0] : {};
        setIdenInfo(data);
      } else {
        message.error(resultMsg);
      }
    });
  }, [baseInfo.ORGA_ENTERPRISE_ID]);

  const submit = () => {
    form.validateFields((err, values) => {
      const { remarks, supplementaryInstruction } = values;
      if (!err) {
        if (!baseInfo.GROUP_ID) {
          setPopupMessage('请先选择需要添加黑名单的集团，再进行提交！');
          setPopupVisible(true);
          return; // 阻止后续提交逻辑
        }
        addBlackListInfo({
          idenCode: idenInfo.IDEN_NR,
          idenType: idenInfo.IDEN_TYPE_ID,
          remarks,
          // 1 - 省侧黑名单  2 - 集团黑名单
          blackSource: '1',
          supplementaryInstruction,
          oaIdenType: PA_IDEN_TYPE[idenInfo.IDEN_TYPE_ID] || '',
          groupId: baseInfo.GROUP_ID,
          groupName: baseInfo.GROUP_NAME,
          mgmtDistrict: baseInfo.MGMT_DISTRICT,
          opId: baseInfo.OP_ID,
          createOpId: baseInfo.CREATE_OP_ID,
          orgId: baseInfo.ORG_ID,
          createOrgId: baseInfo.CREATE_ORG_ID,
          dataStatus: '1',
          acctId: '0',
        }).then(res => {
          const { resultCode, resultMsg } = res;
          if (resultCode === 'TRUE') {
            message.success('提交成功');
            FatherProps.goToStep(1);
          } else {
            message.error(resultMsg);
          }
        });
      }
    });
  };

  /**
   * 关闭弹窗的方法
   */
  const closePopup = () => {
    setPopupVisible(false);
    setPopupMessage('');
  };

  /**
   * 返回
   */
  const goBack = () => {
    FatherProps.goToStep(1);
  };

  // 证件类型
  // const [idenTypeMap, setIdenTypeMap] = useState([]);
  // const [idenTypeLoading, setIdenTypeLoading] = useState([]);
  // // 黑名单来源映射
  // const [blackSourceMap, setBlackSourceMap] = useState([]);
  // const [blackSourceMapLoading, setBlackSourceMapLoading] = useState([]);
  // 加黑原因
  const [remarksMap, setRemarksMap] = useState([]);
  const [remarksMapLoading, setRemarksMapLoading] = useState([]);

  useEffect(() => {
    // setIdenTypeLoading(true);
    // setBlackSourceMapLoading(true);
    setRemarksMapLoading(true);
    // const fetchEnumGroupIdenTypeMap = async () => {
    //   const result = await queryCommonStaticDataEnumMap('GROUP_BUSI_LICENCE_TYPE');
    //   setIdenTypeMap(result);
    //   setIdenTypeLoading(false);
    // };
    // fetchEnumGroupIdenTypeMap();
    // const fetchEnumBlackSourceMap = async () => {
    //   const result = await queryCommonStaticDataEnumMap('BLACK_SOURCE');
    //   setBlackSourceMap(result);
    //   setBlackSourceMapLoading(false);
    // };
    // fetchEnumBlackSourceMap();
    const fetchEnumRemarksMap = async () => {
      const result = await queryCommonStaticDataEnumMap('BLACK_REASON');
      setRemarksMap(result);
      setRemarksMapLoading(false);
    };
    fetchEnumRemarksMap();
  }, []);

  return (
    <Spin spinning={false}>
      <Card title="黑名单审核信息" className="cute">
        <div className={style.content}>
          <Form className="flow fix-label">
            <Row gutter={24} type="flex" justify="start">
              <Col span={8}>
                <Form.Item label="加黑原因">
                  {getFieldDecorator('remarks', {
                    rules: [{ required: true, message: '请选择加黑原因!' }],
                    initialValue: '',
                  })(
                    <Select placeholder="请选择" allowClear loading={remarksMapLoading}>
                      {Object.keys(remarksMap).map(key => (
                        <Select.Option value={key} key={key}>
                          {remarksMap[key]}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="加黑补充说明">
                  {getFieldDecorator('supplementaryInstruction', {
                    initialValue: '',
                    rules: [
                      {
                        required: form.getFieldValue('remarks') === '4' || form.getFieldValue('remarks') === '6',
                        message: '请填写加黑补充说明!',
                      },
                    ],
                  })(<Input allowClear placeholder="请输入" />)}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12} className="text-right">
                <Button type="primary" className={style.buttonStyle1} onClick={submit}>
                  提交
                </Button>
                {/* 弹窗 */}
                {isPopupVisible && (
                  <div style={styles.overlay}>
                    <div style={styles.popup}>
                      <p style={styles.popupMessage}>{popupMessage}</p>
                      {/* eslint-disable-next-line react/button-has-type */}
                      <Button type="primary" onClick={closePopup}>
                        确定
                      </Button>
                    </div>
                  </div>
                )}
              </Col>
              <Col span={12}>
                <Button onClick={goBack}>取消</Button>
              </Col>
            </Row>
          </Form>
        </div>
      </Card>
    </Spin>
  );
};
export default Form.create()(RedList);
