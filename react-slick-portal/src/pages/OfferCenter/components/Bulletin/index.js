/* eslint-disable global-require */
import React, { useEffect, useState } from 'react';
import { Row, Col } from 'antd';
import { router } from 'umi';
import { qryConMessageUsersGridData } from '@/services/bulletin';
import style from './Bulletin.module.less';
// import ComTitle from '../ComTitle';
import MessageDetail from '../../message/components/MessageDetail';

const Bulletin = props => {
  const [conMessageList, setConMessageList] = useState([]);
  // const [maxWidth, setMaxWidth] = useState(299);
  const [visible, setVisible] = useState(false);
  const [currentDetail, setCurrentDetail] = useState({});
  const [currentIndex, setCurrentIndex] = useState(0);
  const [detailLoading, setDetailLoading] = useState(false);

  // 公告栏信息
  const loadData = async () => {
    const res = await qryConMessageUsersGridData({ pageNum: 0, pageSize: 5 });
    let tmpList = []
    if (Array.isArray(res.resultObject.list)) {
      tmpList = res.resultObject.list.filter((item, index) => (item.conMessage.topFlag === '1' && index <= 2))
    }
    setConMessageList(tmpList);
    // if (tmpList.length === 1) {
    //   setMaxWidth(1200);
    // }
    // if (tmpList.length === 2) {
    //   setMaxWidth(500);
    // }
  };

  useEffect(() => {
    loadData();
  }, [])

  useEffect(() => {
    if (visible) {
      document.body.style.overflowY = 'hidden';
    } else {
      document.body.style.overflowY = 'auto';
    }
  }, [visible])

  return (
    <div className={style.bannerList} id={props.id}>
      <Row className={style.bulletin} style={{ display: conMessageList.length > 0 ? '' : 'none' }}>
        <Col span={2} className={style.bulletinTitle}>
          <img
            src={require("./img/bulletin.png")}
            alt=""
          />
          <span>公告:</span>
        </Col>
        <Col span={19} className={style.bulletinContent}>
          {conMessageList.length > 0 ?
            <span
              onClick={() => {
                setCurrentDetail(conMessageList[0]);
                setCurrentIndex(0);
                setVisible(true);
              }}
              dangerouslySetInnerHTML={{ __html: conMessageList[0].conMessage.messageTitle }}
            />
            : <span style={{ fontSize: '14px' }}>暂无公告待展示</span>
          }
        </Col>
        <Col span={2} className={style.bulletinMore}>
          <span
            onClick={() => router.push('/offerCenter/message')}
            style={{cursor: 'pointer'}}
          >
            更多
            <img
              src={require(`./img/more.png`)}
              alt=""
            />
          </span>
        </Col>
      </Row>
      <MessageDetail
        visible={visible}
        detail={currentDetail}
        onClose={() => {
          setVisible(false);
        }}
        onPrev={() => {
          if (currentIndex !== 0) {
            setDetailLoading(true);
            setCurrentIndex(currentIndex - 1);
            setCurrentDetail(conMessageList[currentIndex - 1]);
          }
        }}
        onNext={() => {
          if (currentIndex !== conMessageList.length - 1) {
            setDetailLoading(true);
            setCurrentIndex(currentIndex + 1);
            setCurrentDetail(conMessageList[currentIndex + 1]);
          }
        }}
        length={conMessageList.length}
        currentIndex={currentIndex}
        setDetailLoading={setDetailLoading}
        detailLoading={detailLoading}
      />
    </div>
  );
};

export default Bulletin;
