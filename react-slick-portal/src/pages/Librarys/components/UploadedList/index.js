import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Card, Form, Input, Button, Tooltip } from 'antd';
import SlickTable from '@/components/SlickTable';
import { TYPE_VALUE } from '@/pages/Librarys/const';
import { queryPageUserUploadInfo } from '../../services-use';
import DetailInfo from '../DetailInfo';

const FormItem = Form.Item;
const Index = props => {
  const { form, attrs, goToStep, activeKey } = props;
  const [detailInfoProps, setDetailInfoProps] = useState({});
  const { getFieldDecorator, getFieldValue } = form;

  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const queryTableData = async _current => {
    const { pageSize } = pagination;
    const current = _current ?? pagination.current;
    const knowledgeInfo = getFieldValue('knowledgeInfo');

    setLoading(true);
    try {
      const res = await queryPageUserUploadInfo({ current, pageSize, knowledgeInfo });
      setList(res.data);
      setPagination({
        ...pagination,
        current,
        total: res.total,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    queryTableData();
  }, []);

  const getKnowledgeClassTypeByValue = value => {
    const entries = Object.entries(TYPE_VALUE);
    return entries.find(item => item[1] === value)[0];
  };

  const cellStyle = {
    maxWidth: 200,
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
  };

  const descCellStyle = {
    maxWidth: 120,
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
  };

  const columns = [
    {
      title: '归属地市',
      dataIndex: 'regionName',
      align: 'center',
      onCell: () => ({
        style: cellStyle,
      }),
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '归属区县',
      dataIndex: 'areaName',
      align: 'center',
      onCell: () => ({
        style: cellStyle,
      }),
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '9大行业',
      dataIndex: 'industryTypeName',
      align: 'center',
      onCell: () => ({
        style: cellStyle,
      }),
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '库类型',
      dataIndex: 'knowledgeClassType',
      align: 'center',
      onCell: () => ({
        style: cellStyle,
      }),
      render: text => {
        let value;
        switch (text) {
          case TYPE_VALUE.plan:
            value = '方案库';
            break;
          case TYPE_VALUE.case:
            value = '案例库';
            break;
          case TYPE_VALUE.bid:
            value = '投标库';
            break;
          case TYPE_VALUE.cert:
            value = '资质库';
            break;
          case TYPE_VALUE.prod:
            value = '产品库';
            break;
          case TYPE_VALUE.coop:
            value = '合作伙伴库';
            break;
          case TYPE_VALUE.hardware:
            value = '硬件终端库';
            break;
          default:
            value = '-';
        }
        return (
          <Tooltip placement="topLeft" title={value}>
            {value}
          </Tooltip>
        );
      },
    },
    {
      title: '资料类型',
      dataIndex: 'knowledgeTypeName',
      align: 'center',
      onCell: () => ({
        style: cellStyle,
      }),
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '资料编码',
      dataIndex: 'knowledgeNbr',
      align: 'center',
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '资料名称',
      dataIndex: 'knowledgeName',
      align: 'center',
      onCell: () => ({
        style: {
          textAlign: 'left',
        },
      }),
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '关键词',
      dataIndex: 'knowledgeKeyword',
      align: 'center',
      onCell: () => ({
        style: {
          ...cellStyle,
          textAlign: 'left',
        },
      }),
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '简介',
      dataIndex: 'knowledgeDesc',
      onCell: () => ({
        style: {
          ...descCellStyle,
          textAlign: 'left',
        },
      }),
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '浏览量',
      dataIndex: 'browseCount',
      align: 'center',
      sorter: (a, b) => {
        const _a = JSON.parse(JSON.stringify(a));
        const _b = JSON.parse(JSON.stringify(b));
        if (!_a.browseCount) {
          _a.browseCount = 0;
        }
        if (!_b.browseCount) {
          _b.browseCount = 0;
        }
        return _a.browseCount - _b.browseCount;
      },
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '下载量',
      dataIndex: 'downloadCount',
      align: 'center',
      sorter: (a, b) => {
        const _a = JSON.parse(JSON.stringify(a));
        const _b = JSON.parse(JSON.stringify(b));
        if (!_a.downloadCount) {
          _a.downloadCount = 0;
        }
        if (!_b.downloadCount) {
          _b.downloadCount = 0;
        }
        return _a.downloadCount - _b.downloadCount;
      },
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '评分',
      dataIndex: 'knowledgeSorce',
      align: 'center',
      sorter: (a, b) => {
        const _a = JSON.parse(JSON.stringify(a));
        const _b = JSON.parse(JSON.stringify(b));
        if (!_a.knowledgeSorce) {
          _a.knowledgeSorce = 0;
        }
        if (!_b.knowledgeSorce) {
          _b.knowledgeSorce = 0;
        }
        return _a.knowledgeSorce - _b.knowledgeSorce;
      },
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '上传者',
      align: 'center',
      onCell: () => ({
        style: cellStyle,
      }),
      dataIndex: 'createStaffName',
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '上传时间',
      dataIndex: 'createDate',
      align: 'center',
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updateDate',
      align: 'center',
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '资料状态',
      dataIndex: 'statusCd',
      align: 'center',
      render: val => {
        const text = attrs?.statusCd?.find(item => item.value === val)?.name;
        return (
          <Tooltip placement="topLeft" title={text}>
            {text}
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      width: '90px',
      align: 'center',
      render: record => (
        <Button
          type="link"
          onClick={() => {
            setDetailInfoProps({
              visible: true,
              knowledgeInfoId: record.knowledgeInfoId,
              type: getKnowledgeClassTypeByValue(record.knowledgeClassType),
            });
          }}
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <Card
      title="资料列表"
      className="cute"
      extra={(
        <Button
          type="primary"
          onClick={() => {
            goToStep('1', { originalKey: activeKey });
          }}
        >
          返回
        </Button>
      )}
    >
      <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} layout="inline" style={{ marginBottom: '12px' }}>
        <FormItem label="资料信息">
          {getFieldDecorator('knowledgeInfo')(<Input allowClear placeholder="请输入资料名称/简介/关键词" style={{ width: 250 }} />)}
        </FormItem>
        <Button
          type="primary"
          onClick={() => {
            queryTableData(1);
          }}
          className="margin-left"
          style={{ float: 'right' }}
        >
          查询
        </Button>
      </Form>

      <SlickTable
        rowKey={record => record.id}
        columns={columns}
        loading={loading}
        data={{
          list,
          pagination,
        }}
        onChange={page => {
          queryTableData(page.current);
        }}
        onRow={record => ({
          onDoubleClick: () => {
            setDetailInfoProps({
              visible: true,
              knowledgeInfoId: record.knowledgeInfoId,
              type: getKnowledgeClassTypeByValue(record.knowledgeClassType),
            });
          },
        })}
        scroll={{ x: 'max-content' }}
      />
      {detailInfoProps && detailInfoProps.visible && (
        <DetailInfo
          {...detailInfoProps}
          onCancel={() => {
            setDetailInfoProps({
              visible: false,
            });
          }}
        />
      )}
    </Card>
  );
};

export default connect(({ librarys }) => ({
  attrs: librarys.attrs,
}))(Form.create()(Index));
