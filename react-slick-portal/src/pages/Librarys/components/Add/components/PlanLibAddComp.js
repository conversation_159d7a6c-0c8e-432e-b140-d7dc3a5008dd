import React, { useState, useEffect } from 'react';
import { Col, Form } from 'antd';
import { connect } from 'dva';
import { renderFormItem, renderRows, validatePhone } from '../const';
import ProdMultiModal from './ProdMultiModal';
import { TYPE_VALUE, MODE } from '@/pages/Librarys/const';
import SignDepartComp from './SignDepartComp';
import PartnerSelect from './PartnerSelectComp';

// 判断空对象
const judgeObj = obj => {
  if (Object.keys(obj).length === 0) return true;
  return false;
};

const fields = [
  {
    label: '方案名称',
    type: 'input',
    key: 'knowledgeName',
    span: 2,
    rules: [{ required: true, message: '请输入' }],
  },
  {
    label: '方案编号',
    type: 'input',
    key: 'knowledgeNbr',
    span: 1,
    rules: [
      // { required: true, message: '请输入' },
    ],
    optionKey: '',
    disabled: true,
  },
  {
    label: '归属地市',
    type: 'select',
    key: 'regionId',
    placeholder: '请选择',
    span: 1,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '归属区县',
    type: 'select',
    key: 'areaId',
    placeholder: '请先选择归属地市',
    allowClear: true,
    span: 1,
    rules: [
      // { required: true, message: '请选择' },
    ],
  },
  {
    label: '方案分类',
    type: 'select',
    key: 'knowledgeType',
    placeholder: '请选择',
    span: 1,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '9大行业',
    type: 'select',
    key: 'industryType',
    placeholder: '请选择',
    span: 1,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '一级行业',
    type: 'select',
    key: 'industryFirstType',
    placeholder: '请先选择9大行业',
    span: 1,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '二级行业',
    type: 'select',
    key: 'industrySubType',
    placeholder: '请先选择一级行业',
    span: 1,
    allowClear: true,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '所属产品',
    type: 'select',
    key: 'prodType',
    span: 1,
    custom: true,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '合作方名称',
    type: 'select',
    key: 'partnerName',
    span: 1,
    custom: true,
  },
  {
    label: '合作方联系人',
    type: 'input',
    key: 'workerName',
    span: 1,
    disabled: true,
  },
  {
    label: '合作方联系人电话',
    type: 'input',
    key: 'workerTel',
    span: 1,
    disabled: true,
  },
  {
    label: '合作方类型',
    type: 'select',
    key: 'workerDepart',
    optionKey: 'workerDepart',
    span: 1,
    disabled: true,
  },
  {
    label: '方案联系人姓名',
    type: 'input',
    key: 'contactName',
    span: 1,
    rules: [{ required: true, message: '请输入' }],
  },
  {
    label: '方案联系人电话',
    type: 'input',
    key: 'contactPhone',
    span: 1,
    rules: [{ required: true, message: '请输入' }, { validator: validatePhone }],
  },
  {
    label: '方案联系人邮箱',
    type: 'input',
    key: 'contactEmail',
    span: 1,
    rules: [
      // { required: true, message: '请输入' },
    ],
  },
  {
    label: '方案维护人姓名',
    type: 'input',
    key: 'createStaffName',
    span: 1,
    rules: [{ required: true, message: '请输入' }],
    disabled: true,
  },
  {
    label: '方案维护人电话',
    type: 'input',
    key: 'defandTel',
    span: 1,
    rules: [
      // { required: true, message: '请输入' },
    ],
    disabled: true,
  },
  {
    label: '方案维护人邮箱',
    type: 'input',
    key: 'defandMail',
    span: 1,
    rules: [
      // { required: true, message: '请输入' },
    ],
    disabled: true,
  },
  {
    label: '生效时间(上架)',
    type: 'input',
    key: 'shelfTime',
    span: 1,
    disabled: true,
  },
  {
    label: '失效时间(下架)',
    type: 'input',
    key: 'shelvesTime',
    span: 1,
    disabled: true,
  },
  {
    label: '归属人单位',
    type: 'select',
    key: 'userDepart',
    custom: true,
    span: 1,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '是否包含5G',
    type: 'select',
    key: 'is5G',
    optionKey: 'is5G',
    span: 1,
  },
  {
    label: '方案关键词(标签)',
    type: 'input',
    key: 'knowledgeKeyword',
    span: 2,
    // offset: 1,
    placeholder: '请用半角逗号，如：电力,5G,智慧工厂,尊享',
    rules: [{ required: true, message: '请输入' }],
  },
  {
    label: '通用标签',
    type: 'select',
    key: 'generalizedLabel',
    optionKey: 'generalizedLabel',
    span: 1,
  },
  {
    label: '方案描述',
    type: 'textarea',
    key: 'knowledgeDesc',
    span: 3,
    // offset: 1,
    placeholder: '请输入',
    rules: [{ required: true, message: '请输入' }],
  },
];

/**
 * 方案库基本信息组件
 * @param {props}} props
 * @returns
 */
const PlanLibAddComp = props => {
  const { form, useLibAdd, type, mode, colFields, knowledgeDetailInfo, user } = props;
  const {
    cityList,
    regionList,
    industryList,
    subIndustryList,
    firstIndustryList,
    onChangeCity,
    onChangeIndustry,
    onChangeIndustryFirst,
    attrs,
  } = useLibAdd;
  const { getFieldDecorator, setFieldsValue } = form;
  const [colFieldsChange, setColFieldsChange] = useState(false);
  const [partnerInitMark, setPartnerInitMark] = useState(true);

  // 监听colFields并以此驱动详情页内ComboGrid更新，防止详情页已渲染而colFields值尚为空的情况
  useEffect(() => {
    setColFieldsChange(!colFieldsChange);
  }, [colFields]);

  const getInitValue = key => {
    // 编辑模式
    if (mode === MODE.UPDATE) {
      if (colFields.includes(key)) {
        // 纵表字段
        return knowledgeDetailInfo?.knowledgeAttrs?.find(el => el.attrNbr === key)?.attrValue;
      }
      // 特殊纵表字段：归属人单位默认值
      if (key === 'userDepart') {
        const _idVal = knowledgeDetailInfo?.knowledgeAttrs?.find(el => el.attrNbr === 'userDepartId')?.attrValue;
        const _nameVal = knowledgeDetailInfo?.knowledgeAttrs?.find(el => el.attrNbr === 'userDepartName')?.attrValue;
        return _idVal && [{ org_code: _idVal, org_name: _nameVal }];
      }
      // 合作方名称默认值
      if (key === 'partnerName') {
        return knowledgeDetailInfo?.partnerName && [{ partnerName: knowledgeDetailInfo?.partnerName }];
      }
      return knowledgeDetailInfo[key];
    }
    // 维护人信息默认回显当前用户，新增时不传值
    if (key === 'createStaffName') {
      return user?.userInfo?.userName;
    }
    if (key === 'defandTel') {
      return user?.userInfo?.mobilePhone;
    }
    if (key === 'defandMail') {
      return user?.staffInfo?.email;
    }
    if (mode === MODE.ADD && !judgeObj(knowledgeDetailInfo)) {
      if (colFields.includes(key)) {
        // 纵表字段
        return knowledgeDetailInfo?.knowledgeAttrs?.find(el => el.attrNbr === key)?.attrValue;
      }
      return knowledgeDetailInfo[key];
    }
    return undefined;
  };

  const renderFields = row => {
    const onChange = e => {
      const val = e?.target?.value || e;
      if (row.key === 'regionId') {
        onChangeCity(val);
        setFieldsValue({
          areaId: undefined,
        });
      } else if (row.key === 'industryType') {
        const pcode = industryList?.filter(item => item.value === val)?.[0].PCODE;
        onChangeIndustry(pcode);
        setFieldsValue({
          industryFirstType: undefined,
          industrySubType: undefined,
        });
      } else if (row.key === 'industryFirstType') {
        const pcode = firstIndustryList?.filter(item => item.value === val)?.[0].pcode;
        onChangeIndustryFirst(pcode);
        setFieldsValue({
          industrySubType: undefined,
        });
      }
    };
    let comp = null;
    let options = [];
    switch (row.key) {
      case 'regionId':
        options = cityList?.map(item => ({ ...item, value: item.id, label: item.name }));
        break;
      case 'areaId':
        options = regionList?.map(item => ({ ...item, value: item.id, label: item.name }));
        break;
      case 'industryType':
        options = industryList;
        break;
      case 'industryFirstType':
        options = firstIndustryList;
        break;
      case 'industrySubType':
        options = subIndustryList;
        break;
      case 'knowledgeType':
        options = attrs.knowledgeType?.filter(item => item.desc === TYPE_VALUE[type]);
        break;
      default:
        // 从主数据获取
        options = attrs[row?.optionKey] || [];
        break;
    }
    if (row.custom) {
      // 产品
      if (row.key === 'prodType') {
        comp = <ProdMultiModal />;
      }
      if (['userDepart'].includes(row.key)) {
        comp = <SignDepartComp colFieldsChange={colFieldsChange} />;
      }
      if (['partnerName'].includes(row.key)) {
        comp = (
          <PartnerSelect
            colFieldsChange={colFieldsChange}
            onChange={val => {
              // 编辑模式下，赋初始时跳过，因为该组件还未发送请求，无该对象数据
              if (mode === MODE.UPDATE && partnerInitMark) {
                setPartnerInitMark(false);
                return;
              }
              setFieldsValue({
                workerName: val?.[0]?.contactName,
                workerTel: val?.[0]?.contactPhone,
                workerDepart: val?.[0]?.workerDepart ? val[0].workerDepart : '9',
              });
            }}
          />
        );
      }
    } else {
      comp = renderFormItem({ ...row, onChange, options });
    }

    // 编辑模式下默认值获取
    const initialValue = getInitValue(row.key);
    return (
      <Col span={(row.span || 1) * 8} offset={(row?.offset || 0) * 8} key={row.key}>
        <Form.Item label={row.label}>
          {getFieldDecorator(row.key, {
            rules: row?.rules || [],
            initialValue,
          })(comp)}
        </Form.Item>
      </Col>
    );
  };

  return (
    <div>
      {/* <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}> */}
      <Form>{renderRows(fields, renderFields)}</Form>
    </div>
  );
};

export default connect(({ login }) => ({
  user: login.user,
}))(PlanLibAddComp);
