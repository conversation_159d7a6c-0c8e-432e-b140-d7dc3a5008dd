import React, { useState, useEffect } from 'react';
import { Tooltip, message, But<PERSON>, List, Divider } from 'antd';
import request from '@/utils/request';
import { checkFreshCachePriv } from '@/services/common';
import styles from '../less/settingDrawer.less';

const Body = ({ children, title, style }) => (
  <div
    style={{
      ...style,
      marginBottom: 24,
    }}
  >
    <h3 className={styles.title}>{title}</h3>
    {children}
  </div>
);

const renderLayoutSettingItem = item => {
  const action = React.cloneElement(item.action, {
    disabled: item.disabled,
  });
  return (
    <Tooltip title={item.disabled ? item.disabledReason : ''} placement="left">
      <List.Item actions={[action]}>
        <span style={{ opacity: item.disabled ? '0.5' : '' }}>{item.title}</span>
      </List.Item>
    </Tooltip>
  );
};

const CacheRefresh = () => {
  const [cachePriv, setCachePriv] = useState(false);
  const [loading1, setLoading1] = useState(false);
  const [loading2, setLoading2] = useState(false);
  const [loading3, setLoading3] = useState(false);
  const [loading4, setLoading4] = useState(false);
  const [loading5, setLoading5] = useState(false);
  const [loading6, setLoading6] = useState(false);

  async function getFreshCachePriv() {
    const flag = await checkFreshCachePriv();
    setCachePriv(flag);
  }

  useEffect(() => {
    getFreshCachePriv();
  }, []);

  function refresh1() {
    setLoading1(true);
    request('portal/DomainDataController/reloadDomainData2Cache.do', { method: 'get' })
      .then(res => {
        if (res === true) {
          message.success('刷新成功');
        } else {
          message.error('刷新失败');
        }
      })
      .always(() => setLoading1(false));
  }

  function refresh2() {
    setLoading2(true);
    request('portal/DataDictController/reload.do', { method: 'get' })
      .then(res => {
        if (res === true) {
          message.success('刷新成功');
        } else {
          message.error('刷新失败');
        }
      })
      .always(() => setLoading2(false));
  }

  function refresh3() {
    setLoading3(true);
    request('orgauth/PrivilegeCacheController/loadPrivilege2Cache.do', { method: 'get' })
      .then(res => {
        if (res === true) {
          message.success('刷新成功');
        } else {
          message.error('刷新失败');
        }
      })
      .always(() => setLoading3(false));
  }

  function refresh4() {
    setLoading4(true);
    request('orgauth/PrivilegeCacheController/loadPrivilege2Cache.do', {
      method: 'get',
      data: { isIncr: true },
    })
      .then(res => {
        if (res === true) {
          message.success('刷新成功');
        } else {
          message.error('刷新失败');
        }
      })
      .always(() => setLoading4(false));
  }

  function refresh5() {
    setLoading5(true);
    request('orgauth/PrivilegeCacheController/reloadPrivilege2Cache.do', { method: 'get' })
      .then(res => {
        if (res === true) {
          message.success('刷新成功');
        } else {
          message.error('刷新失败');
        }
      })
      .always(() => setLoading5(false));
  }

  function refresh6() {
    setLoading6(true);
    request('orgauth/WorkbenchController/refreshRecentHourCache.do', { method: 'get' })
      .then(res => {
        if (res.resultCode === '0') {
          message.success('刷新成功');
        } else {
          message.error('刷新失败');
        }
      })
      .always(() => setLoading6(false));
  }

  return (
    cachePriv ? (
      <>
        <Body title="门户缓存刷新">
          <List
            split={false}
            renderItem={renderLayoutSettingItem}
            dataSource={[
              {
                title: '公共属性模块缓存',
                action: (
                  <Button
                    size="small"
                    icon="sync"
                    className={styles.circle}
                    loading={loading1}
                    onClick={refresh1}
                  />
                ),
              },
              {
                title: '系统参数模块缓存',
                action: (
                  <Button
                    size="small"
                    icon="sync"
                    className={styles.circle}
                    loading={loading2}
                    onClick={refresh2}
                  />
                ),
              },
              {
                title: '权限模块缓存（全量）',
                action: (
                  <Button
                    size="small"
                    icon="sync"
                    className={styles.circle}
                    loading={loading3}
                    onClick={refresh3}
                  />
                ),
              },
              {
                title: '权限模块缓存（增量）',
                action: (
                  <Button
                    size="small"
                    icon="sync"
                    className={styles.circle}
                    loading={loading4}
                    onClick={refresh4}
                  />
                ),
              },
              {
                title: '权限模块缓存（两分钟内）',
                action: (
                  <Button
                    size="small"
                    icon="sync"
                    className={styles.circle}
                    loading={loading5}
                    onClick={refresh5}
                  />
                ),
              },
              // {
              //   title: '客户中心缓存',
              //   action: (
              //     <Button
              //       size="small"
              //       icon="sync"
              //       className={styles.circle}
              //       loading={loading6}
              //       onClick={refresh6}
              //     />
              //   ),
              // },
            ]}
          />
        </Body>
        <Divider />
      </>
    ) : null
  );
};

export default CacheRefresh;
