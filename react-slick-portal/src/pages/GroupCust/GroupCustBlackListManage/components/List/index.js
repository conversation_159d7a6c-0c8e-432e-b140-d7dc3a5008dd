import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Card, Col, Divider, Form, Input, message, Modal, Row, Select } from 'antd';
import { connect } from 'dva';
import { useFormTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import { getPageSizeByCardHeight, Portal } from '@/utils/utils';
import CustomSpace from '@/components/CustomSpace';
import style from './index.less';
import { queryCommonStaticDataEnumMap } from '@/services/commonStaticData';
import { deleteBlackListInfo, queryBalckListInfo, querySettingUpRiskInfo } from '@/pages/GroupCust/GroupCustBlackListManage/services';
import { BLACK_DATA_STATUS } from '@/utils/consts';
import { queryBlackFields } from '@/pages/GroupCust/GroupCustBlackListManage/const';
import AuditModal from '@/pages/Approval/components/AuditModal';
import RiskComponent from '@/pages/GroupCust/GroupCustBlackListManage/components/List/UpRiskInfo';

const cellStyle = {
  maxWidth: 200,
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  cursor: 'pointer',
};

const getTableData = ({ current, pageSize }, formData) => {
  const { groupBlackListQueryType, groupId, groupName, idenCode } = formData;
  let value = '';
  let key = '';
  switch (groupBlackListQueryType) {
    case 'groupName':
      value = groupName;
      key = 'groupName';
      break;
    case 'groupId':
      value = groupId;
      key = 'groupId';
      break;
    case 'idenCode':
      value = idenCode;
      key = 'idenCode';
      break;
    default:
      return {
        total: 0,
        list: [],
      };
  }
  if (!value) {
    return {
      total: 0,
      list: [],
    };
  }
  return queryBalckListInfo({
    [key]: value,
    pageFlag: 1,
    pageInfo: {
      currentPage: current,
      pageSize,
    },
  }).then(res => {
    const { resultCode, resultObject, resultMsg } = res;
    if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
      return {
        total: resultObject?.rspParam?.busiInfo?.outData.length,
        list: resultObject?.rspParam?.busiInfo?.outData,
      };
    }
    message.error(resultMsg);
    return {
      total: 0,
      list: [],
    };
  });
};

const Index = ({ size: { height }, form, ...otherProps }) => {
  const { goToStep } = otherProps;
  const tableRef = useRef(null);
  // card-body除rowHeight
  const [size, setSize] = useState(getPageSizeByCardHeight(height));
  const { getFieldDecorator } = form;
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [auditRecord, setAuditRecord] = useState('');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [upRiskInfo, setUpRiskInfo] = useState({});

  const {
    tableProps,
    refresh,
    search: { submit, reset },
  } = useFormTable(getTableData, {
    defaultPageSize: size,
    form,
    manual: true,
  });
  const { pagination, ...restTableProps } = tableProps;

  // useEffect(() => {
  //   console.log('tableProps', tableProps);
  // }, [tableProps]);

  // 证件类型
  const [idenType, setIdenType] = useState([]);
  // 集团所在地市
  const [mgmtDistrict, setMgmtDistrict] = useState([]);
  // 黑名单来源映射
  const [blackSource, setBlackSource] = useState([]);
  // 加黑原因
  const [remarks, setRemarks] = useState([]);

  useEffect(() => {
    const fetchEnumGroupIdenTypeMap = async () => {
      const result = await queryCommonStaticDataEnumMap('GROUP_BUSI_LICENCE_TYPE');
      setIdenType(result);
    };
    fetchEnumGroupIdenTypeMap();
    const fetchEnumMgmtDistrictMap = async () => {
      const result = await queryCommonStaticDataEnumMap('TAG_AREA_CODE');
      setMgmtDistrict(result);
    };
    fetchEnumMgmtDistrictMap();
    const fetchEnumBlackSourceMap = async () => {
      const result = await queryCommonStaticDataEnumMap('BLACK_SOURCE');
      setBlackSource(result);
    };
    fetchEnumBlackSourceMap();
    const fetchEnumRemarksMap = async () => {
      const result = await queryCommonStaticDataEnumMap('BLACK_REASON');
      setRemarks(result);
    };
    fetchEnumRemarksMap();
  }, []);

  /**
   * 编辑 新增 详情
   * @param targetRow
   * @param viewMode view 详情 edit 编辑 add 新增
   */
  const openDrawer = (targetRow, viewMode) => {
    goToStep(2, {
      viewMode,
    });
  };

  const handleRemove = record => {
    deleteBlackListInfo({
      blacklistId: record.BLACKLIST_ID,
      groupId: record.GROUP_ID,
      groupName: record.GROUP_NAME,
    }).then(res => {
      const { resultCode, resultMsg, resultObject } = res;
      if (resultCode === 'TRUE') {
        message.success('删除成功');
        refresh();
        // eslint-disable-next-line no-empty
      } else if (resultCode === '110' || resultCode === '180') {
        setApprovalModalVisible(true); // 显示弹窗
        setAuditRecord(resultObject);
        refresh();
      } else {
        message.error(resultMsg);
      }
    });
  };

  const jumpToApprovalCenter = record => {
    const orderNbr = record.BUP_AUDIT_RECORD?.approvalOrderNbr || '';
    Portal.open(`/iframe/APPROVE_MENU_ORDERDETAIL?approvalOrderNbr=${orderNbr}&origin=portal`);
  };

  const approvalModalVisibleChange = value => {
    setApprovalModalVisible(value);
  };

  const handleSubmit = () => {
    form.validateFields(err => {
      if (!err) {
        submit();
      }
    });
  };

  const renderMoreConditions2 = () => {
    const groupBlackListQueryType = form.getFieldValue('groupBlackListQueryType');
    switch (groupBlackListQueryType) {
      case 'groupName':
        /* 查询条件-集团名称 */
        return (
          <>
            <Col span={6}>
              <Form.Item label="集团名称">
                {getFieldDecorator('groupName', {
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
          </>
        );
      case 'groupId':
        /* 查询条件-集团编码 */
        return (
          <>
            <Col span={6}>
              <Form.Item label="集团编码">
                {getFieldDecorator('groupId', {
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
          </>
        );
      case 'idenCode':
        /* 查询条件-注册编号 */
        return (
          <>
            <Col span={6}>
              <Form.Item label="注册编号">
                {getFieldDecorator('idenCode', {
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
          </>
        );
      default:
        return (
          <>
            <Col span={6} />
          </>
        );
    }
  };

  useEffect(() => {
    renderMoreConditions2();
  }, [form.getFieldValue('groupBlackListQueryType')]);

  // 根据size.height 变化即时更新pageSize
  useEffect(() => {
    setSize(getPageSizeByCardHeight(height));
  }, [height]);

  const showRiskComponentDrawer = record => {
    querySettingUpRiskInfo({
      idenCode: record.IDEN_CODE,
      groupName: record.GROUP_NAME,
    }).then(res => {
      const { resultCode, resultMsg, resultObject } = res;
      if (resultCode === 'TRUE') {
        setUpRiskInfo(resultObject);
      } else {
        message.error(resultMsg);
      }
    });
    if (record.BLACK_SOURCE === '立户风险') {
      setIsDrawerOpen(true);
    }
  };

  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };

  return (
    <>
      <Card title="集团黑名单管理" className="cute" style={{ minHeight: height }} bordered>
        <Form className="flow fix-label">
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item label="查询类型">
                {getFieldDecorator('groupBlackListQueryType', {
                  initialValue: 'groupId', // 默认查询集团编码
                })(
                  <Select placeholder="请选择">
                    {queryBlackFields.map(({ disable, key, label }) => (
                      <Select.Option disabled={disable} key={key} value={key}>
                        {label}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            {renderMoreConditions2()}
            <Col span={12} className="text-right">
              <Button type="primary" onClick={handleSubmit} loading={tableProps.loading}>
                查询
              </Button>
              <Button className="margin-left" onClick={reset}>
                重置
              </Button>
              <Button type="primary" className="margin-left" onClick={() => openDrawer({}, 'add')}>
                新增
              </Button>
            </Col>
          </Row>
        </Form>
        <SlickTable
          style={{ marginTop: 20 }}
          ref={tableRef}
          rowKey={record => record.GROUP_ID}
          {...restTableProps}
          scroll={{ x: 1300 }}
          data={{
            pagination: {
              ...pagination,
              pageSize: size,
            },
          }}
          columns={[
            {
              title: '集团编码',
              dataIndex: 'GROUP_ID',
              key: 'groupId',
              width: 150,
            },
            {
              title: '集团名称',
              dataIndex: 'GROUP_NAME',
              key: 'groupName',
              width: 200,
              onCell: () => ({
                style: cellStyle,
              }),
            },
            {
              title: '证件编码',
              dataIndex: 'IDEN_CODE',
              key: 'idenCode',
              width: 200,
            },
            {
              title: '证件类型',
              dataIndex: 'IDEN_TYPE',
              key: 'idenType',
              width: 150,
              render: (_, record) => <span className={style.groupLevel}>{idenType[record.IDEN_TYPE] || `${record.IDEN_TYPE}`}</span>,
            },
            {
              title: '创建时间',
              dataIndex: 'CREATE_DATE',
              key: 'createDate',
              width: 120,
            },
            {
              title: '生效时间',
              dataIndex: 'DONE_DATE',
              key: 'doneDate',
              width: 120,
            },
            {
              title: '操作员编码',
              dataIndex: 'OP_ID',
              key: 'opId',
              width: 120,
            },
            {
              title: '集团所在地市',
              dataIndex: 'MGMT_DISTRICT',
              key: 'mgmtDistrict',
              width: 120,
              render: (_, record) => <span className={style.groupLevel}>{mgmtDistrict[record.MGMT_DISTRICT] || `${record.MGMT_DISTRICT}`}</span>,
            },
            {
              title: '数据状态',
              dataIndex: 'DATA_STATUS',
              key: 'dataStatus',
              width: 120,
              render: (_, record) => <span className={style.groupLevel}>{BLACK_DATA_STATUS[record.DATA_STATUS] || `${record.DATA_STATUS}`}</span>,
            },
            {
              title: '加解黑原因',
              dataIndex: 'REMARKS',
              key: 'remarks',
              width: 120,
              render: (_, record) => <span className={style.groupLevel}>{remarks[record.REMARKS] || `${record.REMARKS}`}</span>,
            },
            {
              title: '黑名单来源',
              dataIndex: 'BLACK_SOURCE',
              key: 'blackSource',
              width: 120,
              render: (_, record) => <span className={style.groupLevel}>{blackSource[record.BLACK_SOURCE] || `${record.BLACK_SOURCE}`}</span>,
            },
            {
              title: '是否可以解黑',
              dataIndex: 'IS_REMOVE_BLACK_LIST',
              key: 'isRemoveBlackList',
              width: 120,
            },
            {
              title: '是否可以通过OA流程办理业务',
              dataIndex: 'IS_OA_PROCESS',
              key: 'isOaProcess',
              width: 120,
            },
            {
              title: '操作',
              align: 'center',
              key: 'action',
              width: 150,
              fixed: 'right',
              render: (_, record) => {
                const buttons = [
                  record.AUDIT_STATUS === '1' ? (
                    <a onClick={() => jumpToApprovalCenter(record)}>跳转到审批详情页面 </a>
                  ) : (
                    <a onClick={() => handleRemove(record)}>解除黑名单</a>
                  ),
                  record.BLACK_SOURCE === '立户风险' && <a onClick={() => showRiskComponentDrawer(record)}>立户风险查询</a>,
                ];
                return (
                  <CustomSpace align="center" wrap split={<Divider type="vertical" />}>
                    {buttons.map(item => item)}
                  </CustomSpace>
                );
              },
            },
          ]}
        />
      </Card>
      <AuditModal approvalModalVisible={approvalModalVisible} approvalModalVisibleChange={approvalModalVisibleChange} auditRecord={auditRecord} />
      <Modal
        footer={<Button onClick={closeDrawer}>关闭</Button>}
        title="立户风险"
        width={1200}
        onCancel={closeDrawer}
        visible={isDrawerOpen}
        cancelText="关闭"
      >
        <RiskComponent riskData={upRiskInfo} />
      </Modal>
    </>
  );
};

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(Index));
