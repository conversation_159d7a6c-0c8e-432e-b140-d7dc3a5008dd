import React, { useState, useEffect } from 'react';
import { Card, Icon, Button, Tooltip } from 'antd';
import { connect } from 'dva';
import imgs from '@/components/NavigateImgs';
import styles from './styles.less';
import { getNavigateNodes } from './services';
import { getNavigateDetail } from '../BusinessNavigateManage/service';
import { transformToArray, openMenu } from '@/utils/utils';
import IframeComponents from '@/pages/Iframe/$menuCode';

const CARD_TOP_HEIGHT = 188;

const Index = props => {
  const { dispatch, size, match: { params: { funcNavigationId } }, all } = props;
  const [loading, setLoading] = useState(false);
  const [nodeList, setNodeList] = useState([]);
  const [shrinkTop, setShrinkTop] = useState(false);
  const [currentNode, setCurrentNode] = useState();
  const [heightLightId, setHeightLightId] = useState();
  const [navigateInfo, setNavigateInfo] = useState();
  const [cardTopHeight, setCardTopHeight] = useState(CARD_TOP_HEIGHT); // 第一张卡片的高度


  const setLeftNavigateMenus = funcNavigationAttrId => {
    dispatch({
      type: 'menu/saveFunNavigationIdMap',
      payload: { id: String(funcNavigationId), attrId: funcNavigationAttrId },
    });
  };

  const initData = async () => {
    setLoading(true);
    const query1 = getNavigateNodes(funcNavigationId);
    const query2 = getNavigateDetail(funcNavigationId);
    const list = await query1;
    const info = await query2;

    setNodeList(list);
    setNavigateInfo(info);
    setCurrentNode(list?.[0]);
    setHeightLightId(list?.[0]?.funcNavigationAttrId);
    setLeftNavigateMenus(String(list?.[0]?.funcNavigationAttrId));
    setLoading(false);
  };

  useEffect(() => {
    initData();

    return () => {
      setLeftNavigateMenus(undefined);
    };
  }, []);

  const getIcon = node => {
    const _imgName = node?.iconUrl ?? 'icon1';
    return heightLightId === node.funcNavigationAttrId
      ? imgs[_imgName]
      : imgs[`${_imgName}T`];
  };

  return (
    <div>
      <div className={`${styles.cardTop} ${shrinkTop ? styles.shrink : ''}`}>
        <Card
          title={`业务导航 ${navigateInfo?.navigationName ? `- ${navigateInfo?.navigationName}` : ''}`}
          className="cute"
          style={{ height: 180 }}
          loading={loading}
          extra={(
            <div className={styles.navBtn} onClick={() => { setShrinkTop(true); setCardTopHeight(0); }}>
              <Button type="link">收起导航</Button>
              <Icon type="vertical-left" />
            </div>
          )}
        >
          <div className={styles.navigateRow}>
            {
              transformToArray(nodeList).map((node, index) => {
                const res = [
                  (
                    <Tooltip
                      title={node?.remark}
                      mouseEnterDelay={0.3}
                      className={styles.content}
                      onClick={() => {
                        if (node.menuOpenMode === '1') {
                          setHeightLightId(node.funcNavigationAttrId);
                          setLeftNavigateMenus(node.funcNavigationAttrId);
                          setCurrentNode(node);
                        } else {
                          openMenu(node, all, dispatch);
                        }
                      }}
                    >
                      <img
                        alt=""
                        src={getIcon(node)}
                        width={38}
                        height={38}
                      />
                      <div className={styles.text} style={{ color: heightLightId === node.funcNavigationAttrId ? '#1890ff' : '#696B76' }}>
                        <Icon type={`${heightLightId === node.funcNavigationAttrId ? 'check-circle' : 'clock-circle'}`} />
                        <span className="margin-left">{node.menuName}</span>
                      </div>
                    </Tooltip>
                  ),
                ];

                if (index !== nodeList.length - 1) {
                  res.push(<div className={styles.line} />);
                }
                return res;
              })
            }
          </div>
        </Card>
      </div>
      <div className={`${styles.btnWrap} ${shrinkTop ? '' : styles.hideBtnLeft}`}>
        <div
          className={styles.btnContent}
          onClick={() => { setShrinkTop(false); setCardTopHeight(CARD_TOP_HEIGHT); }}
        >
          <Icon type="vertical-right" />
          <span className={styles.text}>展开导航</span>
        </div>
      </div>
      {
        currentNode && (
          <div style={{ marginTop: shrinkTop ? 0 : 8 }}>
            <Card
              title={currentNode.menuName}
              className="cute"
            >
              <IframeComponents
                {...props}
                menu={currentNode}
                key={currentNode?.urlAddr}
                height={size.height - 4 - cardTopHeight - 72} // 72是这张卡片的头部高度
              />
            </Card>
          </div>
        )
      }
    </div>
  );
};

export default connect(({ setting, menu }) => ({
  size: setting.size,
  all: menu.all,
}))(Index);
