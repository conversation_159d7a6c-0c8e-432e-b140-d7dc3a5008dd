import React, { useReducer, useEffect, useCallback } from 'react';
import { Modal } from 'antd';
import isPlainObject from 'lodash/isPlainObject';
import { isJSON, ErrorListener, isObject } from './utils';
import Parallel from './components/Parallel';
import FormLayout from './components/FormLayout';
import styles from './styles.less';

const initialState = {
  current: null, // 当前申报的故障

  parallelData: [],
  serialData: null, //
  showModal: false, // 仅用于串行模式的弹窗
};

function reducer(state, action) {
  const { type, payload } = action;

  if (type === 'updateParallelData') {
    const { errors } = payload;

    return {
      ...state,
      parallelData: errors, // [{parallel,reportTo,type,code,name,msg}]
    };
  }

  if (type === 'updateSerialData') {
    const { error } = payload;
    // 表示弹窗激活方式，'auto'自动打开，'manual'手动

    return {
      ...state,
      serialData: error,
      showModal: true,
    };
  }

  // 显示串行模式的弹窗
  if (type === 'showModal') {
    return {
      ...state,
      showModal: true,
    };
  }
  // 关闭串行模式的弹窗
  if (type === 'hideModal') {
    return {
      ...state,
      serialData: null,
      showModal: false,
    };
  }

  if (type === 'clear') {
    return initialState;
  }

  throw Error();
}

let listener;

function BugReport({ pathname }) {
  const [state, dispatch] = useReducer(reducer, initialState);

  useEffect(() => {
    listener = new ErrorListener({
      topic: 'quick_report',
      onChange: data => {
        if (Array.isArray(data)) {
          dispatch({ type: 'updateParallelData', payload: { errors: data } });
        } else if (isPlainObject(data)) {
          dispatch({ type: 'updateSerialData', payload: { error: data } });
        }
      },
    });

    return () => {
      listener.unsubscribe();
    };
  }, []);

  const clear = useCallback(() => {
    dispatch({ type: 'clear' });
    listener.clear();
  }, []);

  // 切换页面时，主动清空
  useEffect(() => {
    clear();
  }, [pathname, clear]);

  // 监听JS报错
  useEffect(() => {
    window.onerror = (errorMessage, scriptURI, lineNo, columnNo) => {
      listener.report({
        reportTo: 'bomc',
        ejectMode: 'parallel',
        type: 'system',
        code: 'system',
        msg: `${scriptURI} ${scriptURI} 行：${lineNo}、列：${columnNo}`,
        name: errorMessage,
      });
    };
  }, []);

  // 监听异步请求错误
  useEffect(() => {
    $(document).ajaxError((jqXHR, textStatus, errorThrown) => {
      listener.report({
        reportTo: 'bomc',
        ejectMode: 'parallel',
        type: 'system',
        name: textStatus.statusText,
        code: `XHR ${textStatus.status}`,
        msg: `[${errorThrown.method}] ${errorThrown.url}`,
      });
    });
  }, []);

  // 通过postMessage监听第三方上报的信息
  useEffect(() => {
    function handleActions(event) {
      if (!isJSON(event.data)) {
        return;
      }
      const data = JSON.parse(event.data);
      if (!isObject(data) || data.topic !== 'quick_report') {
        return;
      }

      listener.report(data);
    }
    window.addEventListener('message', handleActions);
    return () => {
      window.removeEventListener('message', handleActions);
    };
  }, []);

  const hasParallelData = Array.isArray(state.parallelData) && state.parallelData.length > 0;

  return (
    <>
      {hasParallelData ? <Parallel list={state.parallelData} /> : null}

      {state.showModal === true ? (
        <Modal
          title={null}
          width={840}
          footer={null}
          className={styles.modal}
          centered
          visible={state.showModal}
          onCancel={() => dispatch({ type: 'hideModal' })}
        >
          <div className={styles.header}>
            <div className={styles.tool}>快捷报障</div>
          </div>

          <FormLayout current={state.serialData} />
        </Modal>
      ) : null}
    </>
  );
}

export default BugReport;
