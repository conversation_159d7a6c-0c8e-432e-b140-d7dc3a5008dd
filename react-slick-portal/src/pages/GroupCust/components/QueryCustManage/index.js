import React, { useState, useRef, useEffect } from 'react';
import { Form, Input, Icon, Select, Button, Row, Col, message } from 'antd';
import Trigger from 'rc-trigger';
import SlickTable from '@/components/SlickTable';
import request from '@/utils/request';
import { queryDepIdApi, queryDisticts, queryQxArea } from './services/index';
import styles from './index.less';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const QueryCustManager = ({
  form,
  onChange,
  value,
  action,
  placeholder,
  searchPlaceholder,
  columns,
  rowKey,
  label,
  tokenSeparators,
  extra,
  pick,
  disabled,
  params,
  onConfirm,
  outsideOpen,
  setOutsideOpen,
  multipleFlag,
  suffix,
  currentCust,
  mode,
  custName,
  ...restProps
}) => {
  const { getFieldDecorator } = form;
  const [selectedRows, setSelectedRows] = useState(value);
  const [open, setOpen] = useState(false);
  const tableEl = useRef(null);
  const [listData, setListData] = useState([]);
  const [dsArea, setDsArea] = useState([]); // 地市映射
  const [qxArea, setQxArea] = useState([]); //  区县映射
  const [depId, setDepId] = useState([]); //  部门映射
  const [isLoading, setIsLoading] = useState(false);

  const [formValues, setFormValues] = useState({
    custMgrId: '',
    staffName: '',
    billId: '',
    mgmtDistrict: '',
    mgmtCounty: '',
    // CUSTMGR_REGION_DETAIL: '',
    deptId: '',
  });
  // 格式化值
// eslint-disable-next-line no-shadow
const getValue = (value, tokenSeparators, label) => {
  // 如果是数组，尝试获取第一个元素的 label 属性
  if (Array.isArray(value) && value.length > 0) {
    const firstItem = value[0];
    // 确保第一个元素有 label 属性
    if (firstItem && firstItem[label]) {
      return firstItem[label]; // 返回label的值
    }
  }
  return custName;
};

// 获取表格数据
const getTableData = ({ staffName = '', fieldsValue = {} }) =>
  request('portal/GroupEnterpriseController/queryAllCustManager.do', {
    method: 'post',
    data: {
      // page: current,
      // pageNum: current,
      // pageSize,
      // rowNum: pageSize,
      // ...filterVal,
      // ...rest,
      staffName,
      ...fieldsValue,
    },
  }).then(res => {
    setIsLoading(false);
    if (Array.isArray(res?.resultObject?.rspParam?.busiInfo?.outData)) {
      setListData(res?.resultObject?.rspParam?.busiInfo?.outData || []);
    }
  });

  const handleSearch = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      setIsLoading(true);
      getTableData({ fieldsValue });
      setFormValues({ ...fieldsValue });
    });
  };

  const handleReset = () => {
    form.resetFields();
    setFormValues({
      custMgrId: '',
      staffName: '',
      billId: '',
      mgmtDistrict: '',
      mgmtCounty: '',
      // CUSTMGR_REGION_DETAIL: '',
      deptId: '',
    });
  };
    // 获取地市
    const queryCity = async () => {
      const resp = await queryDisticts();
      const { resultCode, resultObject } = resp;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.resultInfo.length !== 0) {
        setDsArea(resultObject?.rspParam?.busiInfo?.resultInfo);
      } else {
        message.warning('获取地市失败');
      }
    };

    // 获取区县
    const handleQxArea = async e => {
      const resp = await queryQxArea(e);
      const { resultCode, resultObject } = resp;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.resultInfo.length !== 0) {
        setQxArea(resultObject?.rspParam?.busiInfo?.resultInfo);
      }
    };

    // 获取地市
    const handleSelectDs = () => {
      if (qxArea.length === 0) {
        message.warning('请选择地市');
      }
    };

    // 获取部门
    const queryDepId = async () => {
      const params2 = { groupId: currentCust.GROUP_ID };
      const resp = await queryDepIdApi(params2);
      const { resultCode, resultObject } = resp;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData.length !== 0) {
        setDepId(resultObject?.rspParam?.busiInfo?.outData || []);
      } else {
        message.warning('获取部门失败');
      }
    };

    useEffect(() => {
      queryCity();
      queryDepId();
    }, []);

  return (
    <div>
      <Trigger
        className={styles.queryCustManager}
        action={[action]}
        popupAlign={{ overflow: { adjustX: true, adjustY: true }, offset: [0, 4] }}
        getPopupContainer={trigger => trigger.parentNode}
        onPopupVisibleChange={isVisible => {
          if (!isVisible) {
            onConfirm(selectedRows);
          }
          if (typeof setOutsideOpen === 'function' && isVisible) {
            setOutsideOpen(true);
          }
          setOpen(isVisible);
        }}
        mouseEnterDelay={0.3}
        builtinPlacements={{
          left: {
            points: ['cr', 'cl'],
          },
          right: {
            points: ['cl', 'cr'],
          },
          top: {
            points: ['bc', 'tc'],
          },
          bottom: {
            points: ['tc', 'bc'],
          },
          topLeft: {
            points: ['bl', 'tl'],
          },
          topRight: {
            points: ['br', 'tr'],
          },
          bottomRight: {
            points: ['tr', 'br'],
          },
          bottomLeft: {
            points: ['tl', 'bl'],
          },
        }}
        popupClassName={styles.queryCustManagerDropdown}
        popupVisible={open && outsideOpen}
        popup={(
          <div>
            <Form layout="vertical">
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item label="经理编号" {...formItemLayout}>
                    {getFieldDecorator('custMgrId')(<Input />)}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="姓名" {...formItemLayout}>
                    {getFieldDecorator('staffName',
                    )(<Input />)}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="手机号码" {...formItemLayout}>
                    {getFieldDecorator('billId')(<Input />)}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item label="所属地市" {...formItemLayout}>
                    {getFieldDecorator('mgmtDistrict')(
                      <Select onSelect={e => handleQxArea(e)} allowClear>
                        {dsArea.map(e => (
                          <Select.Option key={e.DISTRICT_ID}>{e.DISTRICT_NAME}</Select.Option>
                     ))}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="所属区县" {...formItemLayout}>
                    {getFieldDecorator('mgmtCounty')(
                      <Select onFocus={() => handleSelectDs()} disabled={qxArea.length === 0} allowClear>
                        {
                         qxArea.map(e => (
                           <Select.Option key={e.DISTRICT_ID}>{e.DISTRICT_NAME}</Select.Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="所属部门" {...formItemLayout}>
                    {getFieldDecorator('deptId')(
                      <Select allowClear>
                        {
                       depId.map(e => (
                         <Select.Option key={e.DIVISION_ID}>{e.DIVISION_NAME}</Select.Option>
                      ))}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
              </Row>
              {/* <Col span={8}>
                  <Form.Item label="所属片区" {...formItemLayout}>
                    {getFieldDecorator('regionDetail')(
                      <Select>
                        <Option value="">--所有--</Option>
                        Add more options as needed
                      </Select>,
                    )}
                  </Form.Item>
                </Col> */}
              <Row>
                <Col className="text-right">
                  <Button type="primary" onClick={handleSearch}>
                    查询
                  </Button>
                  <Button onClick={handleReset}>重置</Button>
                </Col>
              </Row>
            </Form>
            <SlickTable
              loading={isLoading}
              ref={tableEl}
              extra={extra}
              rowKey={rowKey}
              pick={pick}
              style={{ marginTop: 8 }}
              data={{ list: listData }}
              selectedRowKeys={Array.isArray(selectedRows) && selectedRows?.map(row => row[rowKey])}
              onSelectRow={rows => {
                setSelectedRows(rows);
                if (!multipleFlag) {
                  onConfirm(rows);
                  setOpen(false);
                }
              }}
              columns={columns}
            />
          </div>
        )}
        {...restProps}
      >
        <Input
          placeholder={placeholder}
          disabled={disabled}
          suffix={suffix || <Icon type="table" />}
          value={getValue(selectedRows, tokenSeparators, label)}
        />
      </Trigger>
    </div>
  );
};

QueryCustManager.defaultProps = {
  value: [],
  method: 'post',
  placeholder: '',
  searchPlaceholder: '',
  action: 'click',
  stretch: 'width',
  popupStyle: {},
  popupPlacement: 'bottomLeft',
  tokenSeparators: ',',
  destroyPopupOnHide: false,
  disabled: false,
  extra: null,
  pick: 'checkbox',
  params: {},
  outsideOpen: true,
  onConfirm: () => {},
};

  export default Form.create()(QueryCustManager);
