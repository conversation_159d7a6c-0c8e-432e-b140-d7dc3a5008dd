/* 全国宽带订购明细 */
import React, { useEffect, useRef } from 'react';
import { useAntdTable } from '@umijs/hooks';
import { Form, message } from 'antd';
import { connect } from 'dva';
import { queryBroadDetail } from '@/pages/GroupCust/GroupCustView/components/OrderInfo/components/GroupAllNetLinesDetailView/services';
import SlickTable from '@/components/SlickTable';

const cellStyle = {
  maxWidth: 200,
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  cursor: 'pointer',
};

const getTableData = ({ current, pageSize, ...rest }) => {
  const { groupId } = rest;
  const params = {
    groupId,
    pageSize,
    pageNo: current,
  };

  return queryBroadDetail(params).then(res => {
    const { resultCode, resultObject, resultMsg } = res;
    if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.respData) {
      return {
        total: parseInt(resultObject?.rspParam?.busiInfo?.respData?.IDNum, 10),
        data: resultObject?.rspParam?.busiInfo?.respData?.BroadbandList,
      };
    }
    message.error(resultMsg);

    return {
      total: 0,
      data: [],
    };
  });
};

const Index = ({ form, ...otherProps }) => {
  const { refreshFlag, currentCust } = otherProps;
  const tableRef = useRef(null);
  const { tableProps, refresh } = useAntdTable(params => getTableData({ ...params, groupId: currentCust.GROUP_ID }), [], {
    defaultPageSize: 10,
    form,
  });

  const { pagination, ...restTableProps } = tableProps;

  useEffect(() => {
    if (refreshFlag) {
      refresh();
    }
  }, [refreshFlag]);

  useEffect(() => {
    console.log('tableProps', tableProps);
  }, [tableProps]);

  return (
    <>
      <div>
        <SlickTable
          bordered={false}
          style={{ marginTop: 10 }}
          ref={tableRef}
          rowKey={record => record.RESTRICTE_ID}
          {...restTableProps}
          data={{
            pagination: {
              ...pagination,
              pageSize: 10,
            },
          }}
          columns={[
            {
              title: '发起机构',
              dataIndex: 'HomeProv',
              key: 'HomeProv',
              onCell: () => ({
                style: cellStyle,
              }),
            },
            {
              title: '宽带帐号',
              dataIndex: 'BroadbandAccount',
              key: 'BroadbandAccount',
            },
            {
              title: '订购实例ID',
              dataIndex: 'InstancesID',
              key: 'InstancesID',
            },
            {
              title: '宽带类型',
              dataIndex: 'BroadbandType',
              key: 'BroadbandType',
            },
            {
              title: '入网时间',
              dataIndex: 'EffetiTime',
              key: 'EffetiTime',
            },
          ]}
        />
      </div>
    </>
  );
};
export default connect(({ setting, groupCustView }) => ({
  size: setting.size,
  currentCust: groupCustView.currentCust,
}))(Form.create()(Index));
