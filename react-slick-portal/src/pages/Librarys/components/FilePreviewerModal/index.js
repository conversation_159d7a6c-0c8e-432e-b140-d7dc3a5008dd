import React, { useEffect, useState } from 'react';
import { Modal } from 'antd';
import FileViewer from 'react-file-viewer';
import classNames from 'classnames';
import ExcelPreview from './ExcelPreview';
import style from './FilePreviewerModal.module.less';

const FilePreviewerModal = props => {
  const { visible, onCancel, file, browseWmkType } = props;

  const [height, setHeight] = useState(400);

  const handleFileType = type => {
    switch (type) {
      case 'xls':
        return 'xlsx';
      case 'doc':
      case 'docx':
      case 'wps':
      case 'ppt':
      case 'pptx':
        return 'pdf';
      default:
        return type;
    }
  };

  useEffect(() => {
    const rootStyle = document.getElementById('root').style;
    if (visible) {
      rootStyle.overflow = 'hidden';
      rootStyle.position = 'fixed';
    } else {
      rootStyle.overflowY = 'auto';
      rootStyle.position = '';
    }
  }, [visible]);

  useEffect(() => {
    setHeight(window.innerHeight);
  }, []);

  return (
    <Modal
      visible={visible}
      title="文件预览"
      onCancel={onCancel}
      onOk={onCancel}
      width="60%"
      footer={null}
      destroyOnClose
      wrapClassName={classNames({ [style.modalWrap]: browseWmkType && browseWmkType === '1' })}
    >
      {file?.type === 'png' || file?.type === 'jpg' ? (
        <div className={style.contentWrap}>
          <img
            alt="avatar"
            src={file?.url}
            style={{
              maxWidth: '100%',
            }}
          />
        </div>
      ) : (
        <div className={style.wrap} style={{ height: `${height - 300}px` }}>
          {visible && file?.type !== 'xls' && file?.type !== 'xlsx' && (
            <FileViewer
              fileType={handleFileType(file?.type)}
              filePath={file?.url}
              // errorComponent={CustomErrorComponent}
              onError={() => {}}
              style={{ margin: '0 auto' }}
            />
          )}
          {visible && (file?.type === 'xls' || file?.type === 'xlsx') && <ExcelPreview url={file?.url} watermark={browseWmkType === '1'} />}
        </div>
      )}
    </Modal>
  );
};

export default FilePreviewerModal;
