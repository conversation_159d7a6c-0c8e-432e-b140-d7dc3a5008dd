// 历史订单列表
import React, { useState, useEffect, useImperativeHandle } from 'react';
import { Form, message, Table } from 'antd';
import { connect } from 'dva';
import { getOrderHistoryList } from '../services';

const Index = ({ form, ...otherProps }) => {
  const { refreshFlag, cRef } = otherProps;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);

  const getTableData = params => {
    setLoading(true);
    getOrderHistoryList({
      data: {
        ...params,
      },
    })
      .then(res => {
        const { resultCode, resultObject, resultMsg } = res;
        if (resultCode === 'TRUE') {
          setDataSource(resultObject?.rspParam?.busiInfo?.result || []);
        } else {
          message.error(resultMsg);
        }
      })
      .always(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    if (refreshFlag) {
      getTableData();
    }
  }, [refreshFlag]);

  useImperativeHandle(
    cRef,
    () => ({
      query: getTableData,
    }),
    []
  );

  const tableColumns = [
    {
      title: '受理时间',
      dataIndex: 'createDate',
      render: text => <span>{text}</span>,
    },
    {
      title: '受理业务',
      dataIndex: 'offerName',
      render: text => <span>{text}</span>,
    },
    {
      title: '计费号码',
      dataIndex: 'accessNum',
      render: text => <span>{text}</span>,
    },
    {
      title: '操作员',
      dataIndex: 'createOpId',
      render: text => <span>{text}</span>,
    },
    {
      title: '业务类型',
      dataIndex: 'busiCode',
      render: text => <span>{text}</span>,
    },
  ];

  return (
    <>
      <Table
        rowKey={r => r.orderId}
        columns={tableColumns}
        loading={loading}
        dataSource={dataSource}
        pagination={{
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: total => `共 ${total} 条记录`,
        }}
      />
    </>
  );
};

export default connect(({ setting, groupCustView }) => ({
  size: setting.size,
  currentCust: groupCustView.currentCust,
}))(Form.create()(Index));
