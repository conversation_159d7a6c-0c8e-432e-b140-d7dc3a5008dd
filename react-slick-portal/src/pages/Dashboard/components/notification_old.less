.wrap {
  ul {
    margin: 0;
    padding: 0;
    list-style: none;

    li {
      position: relative;
      margin-bottom: 12px;

      .title {
        color: rgba(102, 102, 102, 1);
        font-size: 14px;
        cursor: pointer;

        &:hover {
          color: #1890ff;
        }
      }

      .date {
        position: absolute;
        right: 0;
        color: rgba(153, 153, 153, 1);
        font-size: 14px;
      }
    }
  }

  // :global(.ant-card-head-wrapper) {
  //   // height: 46px;
  // }
  :global(.ant-card-head-title) {
    font-size: 14px;
  }

  :global(.ant-card-body) {
    padding-top: 18px !important;
  }
}

.tag {
  font-size: 12px !important;
  border-radius: 2px !important;
  background: rgba(245, 248, 250, 1) !important;
  border: none !important;
}
