import request from '@/utils/request';

// 获取产品质料表格数据
export async function getProductList(params) {
  return request('api/messageNotify/queryMessagePage', {
    data: params,
  })
};

// 获取产品详情基本信息
export async function getBasicDetails(offerId) {
  return request(`portal/OfferHomePageController/detailByIdByHome/${offerId}`, {
    method: 'get',
  })
}

// 收藏
export async function getCollection(params) {
  return request('portal/OfferController/collectOfferByIds.do', {
    data: params,
  })
};

// 取消收藏Cancel
export async function getCancelCollection(params) {
  return request('portal/OfferController/cancelCollectOfferByIds.do', {
    data: params,
  })
};

// 商品质料列表
export async function getMaterialList(params) {
  return request('portal/OfferController/qryKnowledgeListByOfferCode.do', {
    data: params,
  })
};

// 附件查询
export async function getAttachmentList(params) {
  return request('portal/AttachmentRelController/getAttachmentList.do', {
    data: params,
  })
};

// 调用下载量+1
export async function addKnowledgeBaseDownloadCount(params) {
  return request('portal/KnowledgeBaseController/addKnowledgeBaseDownloadCount.do', {
    data: params,
  })
};
