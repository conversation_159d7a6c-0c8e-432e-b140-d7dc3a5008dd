@import '~antd/lib/style/themes/default.less';
@import '../../../../../config/theme/variables.less';

.queryCustManager {
  :global(.ant-input-suffix) {
    color: fade(@black, 20%) !important;
  }
}
.queryCustManagerDropdown {
  padding: 16px 16px 8px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.search {
  margin-bottom: @slick-space-base;
}

:global(.has-error) {
  .queryCustManagerDropdown {
    :global(.ant-input) {
      border-color: @border-color-base !important;
      &:hover {
        border-color: @border-color-base !important;
      }
      &:focus {
        box-shadow: none;
      }
    }
  }
}
