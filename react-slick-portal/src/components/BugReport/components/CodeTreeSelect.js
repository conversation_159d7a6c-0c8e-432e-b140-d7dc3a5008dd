import React, { useReducer, useEffect, useCallback } from 'react';
import { Select, Row, Col, message } from 'antd';
import { useAsync } from '@umijs/hooks';
import get from 'lodash/get';
import { find } from 'lodash';
import request from '@/utils/request';
import useDeepCompareEffect from './useDeepCompareEffect';

function queryCode(typeValue = [], size = 1) {
  return request('portal/error/complain', {
    data: {
      typeValue,
      size,
    },
  });
}

const initialState = {
  trees: [[], [], [], [], [], [], [], []],
  value: [],
};

function reducer(state, action) {
  const { type, payload } = action;

  if (type === 'setOptions') {
    const { trees } = state;
    const { data, index } = payload;
    return {
      ...state,
      trees: trees.map((k, i) => {
        if (i < index) {
          return k;
        }
        if (i === index) {
          return data;
        }
        return [];
      }),
    };
  }

  if (type === 'setValue') {
    const { newValue, index } = payload;

    // 当前值 之后的选项全部清空
    return {
      ...state,
      value: newValue,
      trees: state.trees.map((k, i) => {
        if (i > index) {
          return [];
        }
        return k;
      }),
    };
  }

  throw Error();
}

function format(oldValue, current, index) {
  const result = [...oldValue];
  result[index] = current;
  return result.slice(0, index + 1);
}

function CodeTreeSelect({ onChange, value }) {
  const [state, dispatch] = useReducer(reducer, initialState);

  const { loading: loading1, run: run1 } = useAsync(queryCode, {
    manual: true,
    onSuccess: (res, params) => {
      const { resultCode, resultObject, resultMsg } = res;
      const data = get(resultObject, 'data');
      if (resultCode === '0' && Array.isArray(data)) {
        dispatch({
          type: 'setOptions',
          payload: { data: get(resultObject, 'data'), index: params[1] - 1 },
        });
      } else {
        message.warning(resultMsg || '客户不存在');
      }
    },
  });

  // 获取省市数据
  useEffect(() => {
    run1([], 1);
  }, [run1]);

  return (
    <Row gutter={8}>
      {state.trees.map((k, index) => (
        // eslint-disable-next-line react/no-array-index-key
        <Col span={6} key={index} className="margin-bottom">
          <Select
            loading={loading1 && state.value.length === index}
            placeholder="请选择"
            value={state.value[index]}
            onChange={current => {
              const newValue = format(state.value, current, index);
              dispatch({
                type: 'setValue',
                payload: {
                  newValue,
                  index,
                },
              });

              // 最后一位不触发
              if (current && index !== state.trees.length - 1) {
                run1(newValue, newValue.length + 1);
              }

              if (onChange) {
                onChange(newValue.length === 8 ? newValue : undefined);
              }
            }}
          >
            {k.map(item => {
              return (
                <Select.Option value={item} key={item}>
                  {item}
                </Select.Option>
              );
            })}
          </Select>
        </Col>
      ))}
    </Row>
  );
}

export default React.forwardRef((props, ref) => <CodeTreeSelect {...props} />);
