import React, { useState, useEffect } from 'react';
import {
  Button, Carousel, Icon, message,
} from 'antd';
import { connect } from 'dva';
import btnMap from './const';
import { getCollection, getCancelCollection } from '@/services/detail';
import { getOfferOrderUrlById } from '@/services/offer';
import style from './Banner.module.less';

const Banner = props => {
  const { offerInfo, offerId } = props;
  const {
    files, offerName, offerDesc, collect,
  } = offerInfo;
  const [icoStatus, setIcoStatus] = useState(false);
  const { docNbr } = (files || []).find(item => item.docSubType === '2' && item.statusCd === '1000') || {};

  const handleStatus = () => {
    if (icoStatus) {
      // 如果是true,说明目前是收藏的状态点击就是为了取消收藏的
      getCancelCollection([offerId]).then(res => {
        if (res) {
          message.success('取消收藏成功');
          setIcoStatus(false);
        }
      });
    } else {
      // 收藏
      getCollection([offerId]).then(res => {
        if (res) {
          message.success('收藏成功');
          setIcoStatus(true);
        }
      });
    }
  };

  /**
 * 初始化收藏的状态
 */
  useEffect(() => {
    setIcoStatus(collect === '1');
  }, [collect]);
  return (
    <div className={style.wrap}>
      <div className={style.banners}>
        <Carousel>
          <div
            className={style.bg}
          >
            <div
              style={
                docNbr
                  ? { backgroundImage: `url(portal/FileStoreController/download.do?docNbr=${docNbr})` }
                  : null}
              className={style.bg}
            >
              <div
                className={style.baseInfo}
              >
                <span className={style.title}>
                  {offerName}
                </span>
                <span className={style.desc}>
                  {offerDesc}
                </span>
                <div className={style.btns}>
                  <Button
                    type="primary"
                    size="large"
                    onClick={() => {
                      getOfferOrderUrlById(offerId);
                    }}
                  >
                    {btnMap['1']}
                  </Button>
                  <div className={style.collection}>
                    <Icon
                      type="heart"
                      theme="filled"
                      className={icoStatus ? style.avtiveClorl : ''}
                      onClick={() => handleStatus()}
                    />
                    {/* <span className={style.collectionHandle}>{icoStatus ? "取消收藏" : "收藏"}</span> */}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Carousel>
      </div>
    </div>
  );
};
export default connect(state => ({
    ...state.offerDetail,
  }))(Banner);
