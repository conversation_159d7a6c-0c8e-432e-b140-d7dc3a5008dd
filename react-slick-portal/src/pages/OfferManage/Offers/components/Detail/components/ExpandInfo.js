import React, { useState } from 'react'
import { Table, Modal } from 'antd';
import BraftEditor from 'braft-editor';
import style from './style.less';
import stringToHtml from '@/utils/string';

const Detail = props => {
  const { data } = props;
  const [imgData, setImgData] = useState({
    url: '',
    visible: false,
  });
  // let advantages = [];
  // let funcDescs = [];
  // let applications = [];
  // try {
  //   data.foreach(item => {
  //     if (item.groupId === '2') {
  //       advantages = item.advantages || [];
  //     } else if (item.groupId === '4') {
  //       funcDescs = item.funcDescs || [];
  //     } else if (item.groupId === '5') {
  //       applications = item.applications || []
  //     }
  //   })
  // } catch (e) {
  //   advantages = [];
  //   funcDescs = [];
  //   applications = [];
  // }

  return (
    <>
      <div className={style.item}>
        <div className={style.title}>{`${OFFER_FLAG}优势：`}</div>
        <div className={style.table}>
          <Table
            bordered
            rowKey='title'
            columns={[
              {
                title: '优势标题',
                dataIndex: 'title',
                ellipsis: true,
              }, {
                title: '优势图标',
                dataIndex: 'url',
                ellipsis: true,
                render: (v) => (
                  <a onClick={() => {
                    setImgData({
                      ...imgData,
                      visible: true,
                      url: v
                    })
                  }}
                  >
                    {v}
                  </a>
                ),
              }, {
                title: '优势描述',
                dataIndex: 'desc',
                ellipsis: true,
              },
            ]}
            dataSource={(data.find(item => item.groupId === '2') || {}).advantages || []}
            pagination={false}
          />
        </div>
      </div>
      <div className={style.item}>
        <div className={style.title}>功能描述：</div>
        <div className={style.table}>
          <Table
            bordered
            rowKey="title"
            columns={[
              {
                title: '标题',
                dataIndex: 'title',
                ellipsis: true,
              }, {
                title: '描述',
                dataIndex: 'desc',
                ellipsis: true,
              },
            ]}
            dataSource={(data.find(item => item.groupId === '4') || {}).funcDescs || []}
            pagination={false}
          />
        </div>
      </div>
      <div className={style.item}>
        <div className={style.title}>应用场景：</div>
        <div className={style.table}>
          <Table
            bordered
            rowKey="title"
            columns={[
              {
                title: '场景标题',
                dataIndex: 'title',
                ellipsis: true,
              }, {
                title: '场景图片',
                dataIndex: 'url',
                ellipsis: true,
                render: (v) => (
                  <a onClick={() => {
                    setImgData({
                      ...imgData,
                      visible: true,
                      url: v
                    })
                  }}
                  >
                    {v}
                  </a>
                ),
              }, {
                title: '描述',
                dataIndex: 'desc',
                ellipsis: true,
                render: v => (
                  BraftEditor.createEditorState(stringToHtml(v)).toText()
                )
              },
            ]}
            dataSource={(data.find(item => item.groupId === '5') || {}).applications || []}
            pagination={false}
          />
        </div>
      </div>
      {
        imgData.visible &&
        <Modal
          visible={imgData.visible}
          footer={null}
          onCancel={() => setImgData({ url: '', visible: false })}
        >
          <img
            alt=""
            style={{ width: '100%', padding: '12px' }}
            src={imgData.url}
          />
        </Modal>
      }
    </>
  )
}

export default Detail
