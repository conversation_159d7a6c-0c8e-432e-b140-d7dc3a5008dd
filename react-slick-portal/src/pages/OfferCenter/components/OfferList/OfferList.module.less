.container {
  width: 1200px;
  margin: 0 auto;

  .offerList {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    margin: 0 3.5px;
    padding-left: 0;

    .offerItem {
      position: relative;
      width: 273px;
      height: 314px;
      margin: 0 12.5px 16px 12.5px;
      .itemImg{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 273px;
        height: 170px;
        overflow: hidden;
        border-radius: 5px;
      }

      .itemImage {
        // position: relative;
        // top: -50%;
        // right: 50%;
        border: 1px solid #eee;
        cursor: pointer;
      }

      .content {
        padding-top: 12px;
      }

      .title {
        position: relative;
        display: -webkit-box;
        display: -ms-flexbox;
        display: -moz-box;
        display: -moz-flex;
        overflow: hidden;
        color: #333;
        font-weight: 600;
        font-size: 14px;
        line-height: 22px;
        text-overflow: ellipsis;

        /* ! autoprefixer: off */
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;

        .itemTitle {
          display: block;
          width: 254px;
          cursor: pointer;
        }

        .collect {
          position: absolute;
          top: 1px;
          right: 10px;
          width: 20px;
          height: 20px;
          cursor: pointer;
        }
      }

      .desc {
        display: -webkit-box;
        display: -ms-flexbox;
        display: -moz-box;
        display: -moz-flex;
        max-height: 54px;
        padding-top: 4px;
        overflow: hidden;
        color: #999;
        font-size: 12px;
        text-align: justify;
        text-overflow: ellipsis;
        cursor: pointer;

        /* ! autoprefixer: off */
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
      }

      .descInfo {
        padding: 16px 0;
      }

      .icon {
        &::before {
          display: inline-block;
          padding-right: 8px;
          color: #999;
          content: '收藏';
        }
      }

      .count {
        color: #333;
        &::before {
          color: #999;
          content: '成交量：';
        }
      }

      .amount {
        display: inline-block;
        color: #ff4d4f;
        font-weight: bold;
        font-size: 28px;
        &::before {
          font-size: 16px;
          content: '￥';
        }
        &::after {
          color: #999;
          font-size: 14px;
          content: '/月';
        }
      }

      .orderBtn {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 40px;
        overflow: hidden;

        button {
          width: 100%;
          height: 40px;
        }
      }
    }

    .offerItem:nth-child(4n) {
      margin-right: 0;
    }
  }

  .showMore {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    text-align: center;
  }

  :global(.ant-btn-primary) {
    background-color: #0382c1;
  }
  :global(.ant-btn-primary:hover) {
    background-color: #5ba4fc;
  }
}
