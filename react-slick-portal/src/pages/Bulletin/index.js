import React, { useState, useEffect, useRef } from 'react';
import { Card, Form, Row, Col, Select, Icon, Input, Button, Tag, Modal, Divider } from 'antd';
import classNames from 'classnames';
import { connect } from 'dva';
import { useBoolean, useAntdTable } from '@umijs/hooks';
import map from 'lodash/map';
import findIndex from 'lodash/findIndex';
import SlickTable from '@/components/SlickTable';
import SlickUpload from '@/components/SlickUpload';
import { getPageSizeByCardHeight } from '@/utils/utils';
import {
  makeAsRead as makeAsReadService,
  getValuesList,
  selectBulletinGridData,
  queryByObjId,
} from '@/services/bulletin';
import style from './bulletin.less';

function getTableData({ current, pageSize, ...rest }) {
  return selectBulletinGridData({
    page: current,
    pageNum: current,
    pageSize,
    rowNum: pageSize,
    sortName: '',
    sortOrder: 'asc',
    ...rest,
  }).then(res => {
    if (Array.isArray(res.list)) {
      return {
        total: res.total,
        data: res.list,
      };
    }
  });
}

function Bulletin({ size: { height }, form }) {
  const { state: expand, toggle } = useBoolean(false);
  const [detail, setDetail] = useState({});
  const [preDetail, setPreDetail] = useState({});
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('');
  const tableRef = useRef(null);
  const [selectedRows, setSelectedRows] = useState([]);
  const [bulletinTypeMap, setBulletinTypeMap] = useState([]);
  const [bulletinlevelMap, setBulletinLevelMap] = useState([]);
  const [showPreFlag, setShowPreFlag] = useState('none');
  // card-body除rowHeight
  const [size, setSize] = useState(getPageSizeByCardHeight(height));
  const { getFieldDecorator } = form;
  // tableProps格式{dataSource,loading,onChange,pagination:{current,pageSize,total}}
  const {
    tableProps,
    search: { submit, reset },
  } = useAntdTable(
    params => {
      /**
       * params包含 分页和表单值，格式如下：
       * {current: 1,filters: {},pageSize: 2,sorter:{},bulletinTitle:''}
       * expand=true 表示高级搜索；高级搜索无视快捷搜索上的bulletinTitle值；反之一样
       */

      if (expand) {
        return getTableData({ ...params, pageSize: size });
      }
      return getTableData({ ...params, pageSize: size, bulletinTitle: title });
    },
    // 依赖变化会触发请求
    [title, height],
    {
      defaultPageSize: size,
      form,
    }
  );
  const { pagination, ...restTableProps } = tableProps;

  /**
   * 设置已读
   * @param {object[]} target 被标记行数据
   * @param {object[]} tableData 当前table数据（dataSource）
   * @param {object[]} selectedRows 当前选中的所有的行数据
   */
  function makeAsRead(target, tableData, selectedRows) {
    const params = [];

    // 提取id，并序列化
    target.forEach(item => {
      const { bulletin, statusCdName, rcvTypeName, ...rest } = item;
      const { createStaff } = bulletin;
      // 只要这些字段
      // {
      //   bulletinId: 344512,
      //   bulletinRcvObjRelId: 5188744,
      //   createDate: "2020-01-08 10:59:29",
      //   createStaff: 1,
      //   id: 5188744,
      //   rcvId: 1,
      //   rcvType: "1200",
      //   statusCd: "1000",
      //   updateDate: "2020-01-08 11:00:00",
      // }
      params.push({ ...rest, createStaff });
    });

    makeAsReadService(params).then(response => {
      // 回参：1 标记成功
      // IMPROVE: /notice 回参0 表示成功 不统一
      if (response >= 0) {
        // 更新table视图
        // IMPORVE: table视图之所以会更新是因为数组是引用类型。数组被修改后，在执行setSelectedRows时视图刷新自动同步
        target.forEach(item => {
          const i = findIndex(tableData, { id: item.id });
          if (i !== -1) {
            const newItem = tableData[i];
            newItem.statusCd = '1100';
            newItem.statusCdName = '已读';
            tableData[i] = newItem;
          }
        });
        // 剔除selectedRows中已记录的。因为存在批量标记和点击单个详情标记两种情况
        // 当checkbox选中若干项时，又点击其中的某个详情。这个时候就需要从选中项剔除
        selectedRows.forEach(item => {
          const i = findIndex(target, { id: item.id });
          if (i !== -1) {
            selectedRows.splice(i, 1);
          }
        });
        tableRef.current.handleRowSelectChange(map(selectedRows, 'id'), selectedRows);
        setSelectedRows([]);
      }
    });
  }

  function actionHandler(e, mode, record) {
    e.preventDefault();
    // 详情
    if (mode === 'view') {
      // 标记已读; 1100表示已读
      // IMPORVE: statusCd 含义与消息中心里的回参相反，那边'1000'表示已读
      if (record.statusCd !== '1100') {
        makeAsRead([record], tableProps.dataSource, selectedRows);
      }
      // 查询附件信息
      queryByObjId({ objId: record.bulletinId, objType: '1000' }).then(res => {
        // 存在附件
        if (Array.isArray(res)) {
          setVisible(true);
          setDetail({
            ...record.bulletin,
            fileList: res.map(item => {
              const { docId, docNbr, fileName, fileGetUrl } = item;
              return {
                uid: docId,
                name: fileName,
                status: 'done',
                url: fileGetUrl,
                docId,
                fileName,
                fileGetUrl,
                docNbr,
              };
            }),
          });
        } else {
          setVisible(true);
          setDetail(record.bulletin);
        }
      });

      // 旧公告内容
      if (record.bulletin && record.bulletin.prevBulletinId > 0) {
        // 查询附件信息
        queryByObjId({ objId: record.bulletin.prevBulletinId, objType: '1000' }).then(res => {
          // 存在附件
          if (Array.isArray(res)) {
            setPreDetail({
              fileList: res.map(item => {
                const { docId, docNbr, fileName, fileGetUrl } = item;
                return {
                  uid: docId,
                  name: fileName,
                  status: 'done',
                  url: fileGetUrl,
                  docId,
                  fileName,
                  fileGetUrl,
                  docNbr,
                };
              }),
            });
          }
        });
      }
    }
  }

  // 获取码表数据
  useEffect(() => {
    getValuesList({ busiNbr: 'Bulletin', propertyName: 'bulletinType' }).then(res => {
      if (Array.isArray(res)) {
        setBulletinTypeMap(res);
      }
    });
  }, []);

  // 获取码表数据
  useEffect(() => {
    getValuesList({ busiNbr: 'Bulletin', propertyName: 'bulletinLevel' }).then(res => {
      if (Array.isArray(res)) {
        setBulletinLevelMap(res);
      }
    });
  }, []);

  // 根据size.height 变化即时更新pageSize
  useEffect(() => {
    setSize(getPageSizeByCardHeight(height));
  }, [height]);

  // 点击事件控制旧公告内容是否展示
  function showPreInfo() {
    if (showPreFlag === 'none') {
      setShowPreFlag('');
    } else {
      setShowPreFlag('none');
    }
  }

  return (
    <>
      <Card
        title="系统公告"
        extra={(
          <>
            {expand ? null : <Input.Search placeholder="公告标题搜索" allowClear style={{ width: 'auto' }} onSearch={val => setTitle(val)} />}
            <Button type="default" className="inline-block margin-left" onClick={() => toggle()}>
              高级查询
              <Icon className={classNames('animated', { rotate180: expand })} type="down" />
            </Button>
          </>
        )}
        style={{ minHeight: height }}
        bordered
      >
        {expand ? (
          <Form className="flow fix-label" onSubmit={submit}>
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item label="公告标题">{getFieldDecorator('bulletinTitle')(<Input allowClear placeholder="请输入" />)}</Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="发布人">{getFieldDecorator('launchStaffName')(<Input allowClear placeholder="请输入" />)}</Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="公告等级">
                  {getFieldDecorator('bulletinLevel')(
                    <Select placeholder="请选择">
                      {bulletinlevelMap.map(item => (
                        <Select.Option key={item.value}>{item.name}</Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="公告类型">
                  {getFieldDecorator('bulletinType')(
                    <Select placeholder="请选择" allowClear>
                      {bulletinTypeMap.map(item => (
                        <Select.Option key={item.value}>{item.name}</Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="公告内容">{getFieldDecorator('bulletinContent')(<Input allowClear placeholder="请输入" />)}</Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="阅读状态">
                  {getFieldDecorator('statusCd')(
                    <Select placeholder="请选择" allowClear>
                      <Select.Option value="1000">未读</Select.Option>
                      <Select.Option value="1100">已读</Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="备注">{getFieldDecorator('remark')(<Input allowClear placeholder="请输入" />)}</Form.Item>
              </Col>
              <Col span={6} className="text-right">
                <Button type="primary" htmlType="submit">
                  查询
                </Button>
                <Button className="margin-left" onClick={reset}>
                  重置
                </Button>
              </Col>
            </Row>
          </Form>
        ) : null}
        <SlickTable
          ref={tableRef}
          extra={allChecked =>
            allChecked.length > 0 ? (
              <Button type="primary" ghost onClick={() => makeAsRead(selectedRows, tableProps.dataSource, selectedRows)}>
                标记已读
              </Button>
            ) : null
          }
          rowKey={record => record.id}
          rowSelection={{
            getCheckboxProps: record => ({
              disabled: record.statusCd === '1100',
            }),
            selectedRowKeys: selectedRows.map(item => item.id),
          }}
          pick="checkbox"
          rowClassName={record => {
            if (record.bulletin.bulletinLevel === '1000') {
              return `${style.highLevel}`;
            }
            return null;
          }}
          {...restTableProps}
          data={{
            pagination: {
              ...pagination,
              pageSize: size,
            },
          }}
          columns={[
            {
              title: '阅读状态',
              align: 'center',
              dataIndex: 'statusCdName',
              width: 80,
              render: (text, record) => {
                let color = 'cyan';
                if (record.statusCd === '1100') {
                  color = ''; // 已读
                }
                // '1000未读'
                // IMPROVE: 该字段含义与消息中心 /notice相反
                return <Tag color={color}>{text}</Tag>;
              },
            },
            {
              title: '公告标题',
              dataIndex: 'bulletin.bulletinTitle',
              ellipsis: true,
              width: 200,
              render: (text, record) => {
                const newFlag = `【新】${text}`;
                const editFlag = `【变更】${text}`;
                record.prevBulletinContent = record.bulletin.preBulletinContent;
                return (
                  <a href="#" onClick={e => actionHandler(e, 'view', record)} style={{ color: record.bulletin.bulletinLevel === '1000' && 'red' }}>
                    {record.bulletin.prevBulletinId ? editFlag : newFlag}
                  </a>
                );
              },
            },
            {
              title: '公告内容',
              dataIndex: 'bulletin.bulletinContent',
              ellipsis: true,
            },
            {
              title: '时间',
              align: 'center',
              width: 160,
              dataIndex: 'bulletin.effDate',
            },
            {
              title: '公告发布人',
              align: 'center',
              ellipsis: true,
              width: 110,
              dataIndex: 'bulletin.launchStaffName',
            },
            {
              title: '公告等级',
              align: 'center',
              width: 80,
              dataIndex: 'bulletin.bulletinLevelName',
            },
            {
              title: '公告类型',
              align: 'center',
              width: 160,
              dataIndex: 'bulletin.bulletinTypeName',
            },
          ]}
          onSelectRow={rows => {
            setSelectedRows(rows);
          }}
        />
      </Card>
      <Modal
        width={600}
        title={detail.bulletinTitle}
        visible={visible}
        footer={null}
        onCancel={() => {
          setVisible(false);
          setDetail({});
          setShowPreFlag('none');
        }}
        destroyOnClose
        centered
      >
        <p>{detail.bulletinContent}</p>
        <div>
          <SlickUpload multiple disabled fileList={detail.fileList} />
        </div>
        {detail.prevBulletinId && detail.preBulletinTitle ? (
          <div>
            <Divider />
            <p>
              关联公告：{detail.preBulletinTitle}
              <a onClick={showPreInfo}>(点击后查看内容)</a>
            </p>

            <div style={{ display: showPreFlag }}>
              <p className="text-gray" style={{ textDecoration: 'line-through' }}>
                {detail.preBulletinContent}
              </p>
              <div>
                <SlickUpload multiple disabled fileList={preDetail.fileList} />
              </div>
            </div>
          </div>
        ) : null}
        <div className="text-right text-gray">{detail.effDate}</div>
      </Modal>
    </>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(Bulletin));
