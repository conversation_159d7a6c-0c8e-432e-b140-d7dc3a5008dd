/* eslint-disable quote-props */
import React, { useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { Button, Card, Modal, Icon, Select, Table, Upload, Form, message, Tooltip, DatePicker } from 'antd';
import moment from 'moment';
import { connect } from 'dva';
import { TYPE_VALUE, TYPE_NAME, LIBRARYS_TYPE, MODE } from '@/pages/Librarys/const';
import FilePreviewerModal from '../../../FilePreviewerModal';
import styles from './styles.less';

/**
 * 知识类型对应必填附件类型映射（key 知识分类ID， value附件类型ID）
 */
const KnowledgeFileType = {
  '1001': ['1'],
  '1002': ['1'],
  '2001': ['5'],
  '2002': ['5'],
  '2003': ['5'],
  '4': ['7'],
  '5': ['7'],
  '001': ['9'],
  '002001': ['9'],
  '002002': ['9'],
  '002003': ['9'],
  '002004': ['9'],
  '002005': ['9'],
  '002006': ['9'],
  '002007': ['9'],
  '003001': ['9'],
  '003002': ['9'],
  '003003': ['9'],
  '003004': ['9'],
  '003005': ['9'],
  '003006': ['9'],
  '003007': ['9'],
  '004': ['9'],
  '005': ['9'],
  '006': ['9'],
  '5001': ['50001'],
  '5002': ['50002'],
  '6001': ['60001', '60002', '60003', '60004', '60005'],
  '6002': ['60001', '60002', '60003', '60004', '60005'],
  '7001': ['70001'],
  '7002': ['70001'],
};

/**
 * 附件组件
 * @param {props} props
 */
const FileUploadComp = props => {
  const { type, mode, form, useLibAdd, user, refInstance, knowledgeDetailInfo } = props;
  const { userInfo } = user;
  const { attrs } = useLibAdd;
  const { getFieldValue, getFieldDecorator, setFieldsValue } = form;
  const [showModal, setShowModal] = useState(false);
  const [currentUploadFile, setFile] = useState([]);

  // 知识分类
  const knowledgeType = getFieldValue('knowledgeType');

  const [fileList, setFileList] = useState([]);
  const [selectType, setSelectType] = useState('');
  const [signDate, setSignDate] = useState('');
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const rootStyle = document.getElementById('root').style;
    if (visible || showModal) {
      rootStyle.overflow = 'hidden';
      rootStyle.position = 'fixed';
    } else {
      rootStyle.overflowY = 'auto';
      rootStyle.position = '';
    }
  }, [visible, showModal]);

  useEffect(() => {
    if (!showModal) {
      setFile([]);
    }
  }, [showModal]);

  const columns = [
    {
      title: '附件',
      dataIndex: 'fileName',
    },
    {
      title: '附件类型',
      dataIndex: 'fileType',
    },
    {
      title: '签约时间',
      dataIndex: 'signDate',
    },
    {
      title: '上传人',
      dataIndex: 'createStaffName',
    },
    {
      title: '上传时间',
      dataIndex: 'createDate',
      render(v) {
        return typeof v === 'string' ? v : moment(v).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      render(v, r) {
        const arr = [];
        // arr.push(<Button type="link" onClick={() => { window.open(`portal/FileStoreController/download.do?docNbr=${r.docNbr}`); }}>预览</Button>);
        arr.push(
          <Button
            type="link"
            onClick={() => {
              const { fileName, docNbr } = r;
              const fileType = fileName.split('.')[fileName.split('.')?.length - 1];
              setFile({
                url: `portal/FileStoreController/preview.do?docNbr=${docNbr}`,
                type: fileType,
              });
              setVisible(true);
            }}
            style={{ paddingLeft: 0 }}
          >
            预览
          </Button>
        );
        arr.push(
          <a href={`portal/FileStoreController/download.do?docNbr=${r.docNbr}`} download={r.fileName}>
            下载
          </a>
        );
        arr.push(
          <Button
            type="link"
            onClick={() => {
              const _fileList = fileList.filter(item => item.docId !== r.docId);
              setFileList([..._fileList]);
            }}
          >
            删除
          </Button>
        );
        return arr;
      },
    },
  ];

  const allUpload = useMemo(() => !!knowledgeType, [knowledgeType]);

  const fileTypeList = useMemo(() => {
    if (knowledgeType && attrs?.knowledgeFileType?.length > 0) {
      return attrs?.knowledgeFileType?.filter(el => el.desc === TYPE_VALUE[type]);
    }
    return [];
  }, [knowledgeType]);

  // 必传文件类型数组中的每一项，都能在fileList中找到对应的type
  const checkFileType = () => KnowledgeFileType[knowledgeType]?.every(item => fileList.find(file => file.type === item));

  useImperativeHandle(refInstance, () => ({
    // 校验必传文件类型是否已经添加
    onValidate: () => {
      if (!KnowledgeFileType[knowledgeType]) {
        return true;
      }
      if (fileList?.length > 0 && checkFileType()) {
        return true;
      }
      message.warning('必传文件未上传');
      return false;
    },
  }));

  useEffect(() => {
    getFieldDecorator('files', {
      preserve: true,
      initialValue: [],
    });
  }, []);

  useEffect(() => {
    if (Array.isArray(getFieldValue('files'))) {
      setFieldsValue({
        files: fileList?.map(item => ({
          attachId: item.docId,
          objType: item.type,
          keyValue1: item.signDate,
        })),
      });
    }
  }, [fileList]);

  useEffect(() => {
    // 编辑模式初始化数据
    if (mode === MODE.UPDATE) {
      const files = knowledgeDetailInfo?.files || [];
      setFileList(
        files.map(item => {
          const { attachment, keyValue1, objType } = item;
          return {
            ...attachment,
            docId: attachment.attachId,
            type: objType,
            fileType: fileTypeList?.find(el => el.value === objType)?.label,
            signDate: keyValue1,
          };
        })
      );
    } else if (mode === MODE.ADD && knowledgeDetailInfo?.files?.length > 0) {
      const files = knowledgeDetailInfo?.files || [];
      setFileList(
        files.map(item => {
          const { attachment, objType } = item;
          return {
            ...attachment,
            docId: attachment.attachId,
            type: objType,
            fileType: type === LIBRARYS_TYPE.PLAN ? '方案资料附件' : '投标资料附件',
            signDate: '',
          };
        })
      );
    }
  }, [knowledgeDetailInfo]);

  return (
    <>
      <Card
        title={(
          <div>
            资料附件列表
            {type === LIBRARYS_TYPE.COOP && (
              <span className={styles.tip}>
                友情提示：请上传必传附件：营业执照、企业资质、一般纳税人证明、中国企业信用查询记录、法人授权函，单个文件不能超过50M，请注意。
              </span>
            )}
            {type === LIBRARYS_TYPE.HARDWARE && (
              <span className={styles.tip}>友情提示：请上传必传附件：硬件终端资料，单个文件不能超过50M，请注意。</span>
            )}
          </div>
        )}
        extra={(
          <Tooltip title={!allUpload ? `请先选择${TYPE_NAME[type]}` : null}>
            <Button
              type="primary"
              onClick={() => {
                setShowModal(true);
              }}
              disabled={!allUpload}
            >
              上传
            </Button>
          </Tooltip>
        )}
      >
        <Table
          bordered
          dataSource={fileList}
          rowKey={(r, idx) => `${idx}`}
          columns={type === LIBRARYS_TYPE.CASE ? columns : columns.filter(item => item.dataIndex !== 'signDate')}
        />
      </Card>
      <Modal
        title="附件上传"
        visible={showModal}
        destroyOnClose
        onCancel={() => {
          setShowModal(false);
        }}
        onOk={() => {
          setFileList([
            ...fileList,
            {
              ...currentUploadFile[0],
            },
          ]);
          setSignDate('');
          setShowModal(false);
        }}
        okButtonProps={{ disabled: currentUploadFile?.length === 0 }}
      >
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <Form.Item label="附件类型" required>
            <Select
              value={selectType}
              onChange={v => {
                setSelectType(v);
              }}
            >
              {fileTypeList?.map(item => {
                const isNeed = KnowledgeFileType[knowledgeType]?.includes(item.value);
                return (
                  <Select.Option value={item.value} key={item.value}>
                    <span style={isNeed ? { color: 'red' } : {}}>
                      {item.label}
                      {isNeed ? '（该类型文件必传）' : ''}
                    </span>
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          {selectType === '2' && (
            <Form.Item label="签约时间" required>
              <DatePicker onChange={v => setSignDate(moment(v).format('YYYY-MM-DD'))} />
            </Form.Item>
          )}
          <Form.Item label="附件" required>
            <Upload
              action="portal/FileStoreController/uploadFile.do "
              beforeUpload={file => {
                // 50MB = 50 * 1MB = 50 * 1024KB = 50 * 1024 * 1024
                if (file.size >= 50 * 1024 * 1024) {
                  message.error('请上传小于50MB的文件');
                  return Promise.reject();
                }
                if (file.size === 0) {
                  message.error('请勿上传空文件!');
                  return Promise.reject();
                }
                return Promise.resolve();
              }}
              onChange={file => {
                if (file.file.status === 'done') {
                  setFile([
                    {
                      ...file.file.response[0],
                      createDate: new Date(),
                      type: selectType,
                      fileType: fileTypeList?.find(el => el.value === selectType)?.label,
                      signDate,
                      createStaffName: userInfo?.userName,
                    },
                  ]);
                } else if (file.file.status === 'error') {
                  message.error(`${file.file.name}上传异常`);
                }
              }}
              onRemove={() => {
                setFile([]);
              }}
            >
              {currentUploadFile?.length === 0 && (
                <Button type="default" disabled={selectType === '2' ? !selectType || !signDate : !selectType}>
                  <Icon type="plus" /> <span>上传</span>
                </Button>
              )}
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
      <FilePreviewerModal
        visible={visible}
        onCancel={() => {
          setVisible(false);
          setFile([]);
        }}
        file={currentUploadFile}
      />
    </>
  );
};

const FileUploadCompWrapper = connect(({ login }) => ({
  user: login.user,
}))(FileUploadComp);

export default React.forwardRef((props, ref) => <FileUploadCompWrapper {...props} refInstance={ref} />);
