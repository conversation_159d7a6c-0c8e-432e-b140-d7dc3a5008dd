import React, { useEffect } from 'react';
// import { useBoolean, useClickAway } from '@umijs/hooks';
import { Select, Form } from 'antd';
import { connect } from 'dva';
import img0 from '../img/list.png';
import img1 from '../img/users.png';
import img2 from '../img/computer.png';
import img3 from '../img/control.png';
import img4 from '../img/shoppingcar.png';
import img5 from '../img/partition.png';
import img6 from '../img/briefcase.png';
import img7 from '../img/line.png';
import img8 from '../img/info.png';
import img9 from '../img/card.png';
// import defaultimg from '../img/default.png'

const FormItem = Form.Item;
const imgarr = [
  'list',
  'users',
  'computer',
  'control',
  'shoppingcar',
  'partition',
  'briefcase',
  'line',
  'info',
  'card',
];
function MenuImg(props) {
  const { disable, formItemLayout, labelName } = props;
  const {
    form: { getFieldDecorator, setFieldsValue },
  } = props;
  // console.log('props.menuImg',props.passData.menuImg );
  useEffect(() => {
    let iconUrl = '';
    // 是否显示图标
    if (props.labelName === '菜单图标') {
      iconUrl = imgarr.indexOf(props.iconUrl) > -1 ? props.iconUrl : undefined;
    } else {
      iconUrl = imgarr.indexOf(props.passData.iconUrl) > -1 ? props.passData.iconUrl : undefined;
    }
    if (iconUrl) {
      iconUrl = {
        key: iconUrl,
      };
    }
    setFieldsValue({
      iconUrl,
    });
  }, [props.labelTag]);

  return (
    <>
      <FormItem label={labelName} {...formItemLayout}>
        {getFieldDecorator('iconUrl', {
          initialValue: undefined,
        })(
          <Select disabled={disable} allowClear labelInValue placeholder="请选择">
            <Select.Option value="list" key="list">
              <img alt="list" src={img0} height="20" width="20" style={{ marginBottom: 3 }} />
              <span style={{ marginLeft: 10 }}>list</span>
            </Select.Option>
            <Select.Option value="users" key="users">
              <img alt="users" src={img1} height="20" width="20" style={{ marginBottom: 3 }} />
              <span style={{ marginLeft: 10 }}>users</span>
            </Select.Option>
            <Select.Option value="computer" key="computer">
              <img alt="computer" src={img2} height="20" width="20" style={{ marginBottom: 3 }} />
              <span style={{ marginLeft: 10 }}>computer</span>
            </Select.Option>
            <Select.Option value="control" key="control">
              <img alt="control" src={img3} height="20" width="20" style={{ marginBottom: 3 }} />
              <span style={{ marginLeft: 10 }}>control</span>
            </Select.Option>
            <Select.Option value="shoppingcar" key="shoppingcar">
              <img
                alt="shoppingcar"
                src={img4}
                height="20"
                width="20"
                style={{ marginBottom: 3 }}
              />
              <span style={{ marginLeft: 10 }}>shoppingcar</span>
            </Select.Option>
            <Select.Option value="partition" key="partition">
              <img alt="partition" src={img5} height="20" width="20" style={{ marginBottom: 3 }} />
              <span style={{ marginLeft: 10 }}>partition</span>
            </Select.Option>
            <Select.Option value="briefcase" key="briefcase">
              <img alt="briefcase" src={img6} height="20" width="20" style={{ marginBottom: 3 }} />
              <span style={{ marginLeft: 10 }}>briefcase</span>
            </Select.Option>
            <Select.Option value="line" key="line">
              <img alt="line" src={img7} height="20" width="20" style={{ marginBottom: 3 }} />
              <span style={{ marginLeft: 10 }}>line</span>
            </Select.Option>
            <Select.Option value="info" key="info">
              <img alt="info" src={img8} height="20" width="20" style={{ marginBottom: 3 }} />
              <span style={{ marginLeft: 10 }}>info</span>
            </Select.Option>
            <Select.Option value="card" key="card">
              <img alt="card" src={img9} height="20" width="20" style={{ marginBottom: 3 }} />
              <span style={{ marginLeft: 10 }}>card</span>
            </Select.Option>
            {/* <Select.Option value="default" key="default">
              <img alt="default" src={defaultimg} height="20" width="20" style={{ marginBottom: 3 }} />
              <span style={{ marginLeft: 10 }}>default</span>
            </Select.Option> */}
          </Select>
        )}
      </FormItem>
      {/* <FormItem label="菜单图标编码" {...formItemLayout} style={{display:'block'}}>
        {getFieldDecorator('menuImgTag')(<Input />)}
      </FormItem> */}
    </>
  );
}

export default connect(({ menuManage }) => ({
  menuImg: menuManage?.menuImg,
}))(MenuImg);
