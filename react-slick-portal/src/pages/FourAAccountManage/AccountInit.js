import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Form, Row, Col, Select, Button, Modal, message } from 'antd';
import EmptyHomePage from '@/components/EmptyHomePage';
import { getItem, isEmptyStr, toArray } from '@/utils/utils';
import { initSubUser } from '@/services/login';
import { DEFAULT_FLAG } from './common';

const Index = props => {
  const {
    form,
    dispatch,
    subSystemList,
  } = props ?? {};

  const { getFieldDecorator } = form;

  const user = getItem('user');
  const { userId: sysUserId } = user.userInfo;

  // 保存没有初始化账号的子系统
  const [targetList, setTargetList] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // 只处理没有初始化账号的子系统
    const _targetList = subSystemList
      // .filter(item => item.defaultUserId === undefined)
      .map(item => ({
        ...item,
        defaultUserId: item?.accountList?.[0]?.externalUserId, // 默认选择数组第一项
        currentUserId: item?.accountList?.[0]?.externalUserId,
        currentUserCode: item?.accountList?.[0]?.externalUserCode,
        disabled: !isEmptyStr(item.defaultUserId),
      }));

    setTargetList(_targetList);
  }, [subSystemList]);

  const getUserCode = (systemInfo, userId) =>
    subSystemList.find(item => item.systemInfoId === systemInfo.systemInfoId)
      .accountList.find(account => account.externalUserId === userId).externalUserCode;

  const handleAccountSelect = (systemInfo, externalUserId) => {
    const newValue = {
      ...systemInfo,
      defaultUserId: externalUserId,
      currentUserId: externalUserId,
      currentUserCode: getUserCode(systemInfo, externalUserId),
    };

    const targetIndex = targetList.findIndex(item => item.systemInfoId === systemInfo?.systemInfoId);

    setTargetList([
      ...targetList.slice(0, targetIndex),
      newValue,
      ...targetList.slice(targetIndex + 1),
    ]);
  };

  const handleOk = async () => {
    form.validateFields(async err => {
      if (err) {
        return;
      }

      const _list = targetList.map(item => ({
        externalUserId: item?.currentUserId,
        externalUserCode: item?.currentUserCode,
        systemInfoId: item?.systemInfoId,
        defaultFlag: DEFAULT_FLAG,
        sysUserId,
      }));

      setLoading(true);
      const res = await initSubUser(_list);
      if (res) {
        setLoading(false);
        dispatch({
          type: 'fourAAccount/update',
          payload: {
            someList: targetList,
          },
        });
      } else {
        message.error('系统异常');
      }
    });
  };

  return (
    <EmptyHomePage>
      <Modal
        title="选择默认登陆账号"
        width={500}
        visible
        closable={false}
        footer={(
          <div style={{ textAlign: 'right' }}>
            <Button type="primary" onClick={handleOk} loading={loading}>
              确认
            </Button>
          </div>
        )}
        destroyOnClose
      >
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 14 }}>
          {
            targetList.map((elem = {}) => (
              <Row key={elem.systemInfoId}>
                <Col span={24}>
                  <Form.Item label={elem.systemName}>
                    {getFieldDecorator(elem.systemNbr, {
                      rules: [
                        {
                          required: true,
                          message: `请选择${elem.systemName}账号`,
                        },
                      ],
                      initialValue: elem?.accountList?.[0]?.externalUserId,
                    })(
                      <Select
                        style={{ width: '100%' }}
                        placeholder="请选择"
                        onChange={value => { handleAccountSelect(elem, value); }}
                        disabled={elem.disabled}
                      >
                        {toArray(elem.accountList).map(item => (
                          <Select.Option key={item.externalUserId} value={item.externalUserId}>
                            {item.externalUserCode}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
              </Row>
            ))
          }
        </Form>
      </Modal>
    </EmptyHomePage>
  );
};

export default connect(({ fourAAccount }) => ({
  subSystemList: fourAAccount.subSystemList,
}))(Form.create()(Index));
