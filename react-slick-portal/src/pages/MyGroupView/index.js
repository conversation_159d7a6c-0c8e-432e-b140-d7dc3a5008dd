import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { useLocation } from 'react-router-dom';
import { Empty } from 'antd';
import { getPageSizeByCardHeight } from '@/utils/utils';
import SiderLayout from '@/components/SiderLayout';
import QueryTable from '@/pages/QueryTable';
import { menuList, formConfig, columns } from './const';

const MyGroupView = props => {
  const {
    size: { height },
  } = props;

  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const menuIndex = query.get('menuIndex');
  const initialMenuIndex = menuIndex ? parseInt(menuIndex, 10) : 0;

  const [activeKeys, setActiveKeys] = useState(['WDJT']);
  const [selectMenu, setSelectMenu] = useState(menuList[initialMenuIndex]);
  const [size, setSize] = useState(getPageSizeByCardHeight(height - 144));

  useEffect(() => {
    setSize(getPageSizeByCardHeight(height - 144));
  }, [height]);

  useEffect(() => {
    const menu = menuList[initialMenuIndex] || menuList[0];
    setSelectMenu(menu);
    setActiveKeys([menu.value]);
  }, [initialMenuIndex]);

  const changeMenu = async item => {
    setActiveKeys([item.key]);
    const menu = menuList.find(list => list.value === item.key);
    setSelectMenu(menu);
  };

  return (
    <SiderLayout
      height={height + 2}
      menuList={menuList}
      needMenuIcon
      activeKeys={activeKeys}
      onChange={changeMenu}
      layoutContent={(
        selectMenu?.requestUrl ? (
          <QueryTable
            key={selectMenu.value} // 添加唯一 key
            cardTitle={selectMenu.name}
            formConfig={formConfig[selectMenu.value]}
            columns={columns[selectMenu.value]}
            pageSize={size}
            requestUrl={selectMenu.requestUrl}
          />
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )
      )}
    />
  );
};
export default connect(({ setting }) => ({
  size: setting.size,
}))(MyGroupView);
