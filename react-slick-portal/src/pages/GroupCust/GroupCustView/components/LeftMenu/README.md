# 菜单图标组件

统一的图标组件，通过index参数选择不同的SVG图标，专为LeftMenu组件设计。

## 特性

- 📦 **统一组件**：一个FrameIcon组件，通过index选择不同图标
- 🔢 **索引选择**：支持index 0-17，对应18个不同的图标
- 🎨 **样式自定义**：支持通过style属性自定义大小和样式
- 🎯 **向后兼容**：保持与原有代码的兼容性

## 可用图标

通过 `FrameIcon` 组件的 `index` 属性选择图标：

- `index={0}` - Frame.svg
- `index={1}` - Frame(1).svg
- `index={2}` - Frame(2).svg
- `index={3}` - Frame(3).svg
- `index={4}` - Frame(4).svg
- `index={5}` - Frame(5).svg
- `index={6}` - Frame(6).svg
- `index={7}` - Frame(7).svg
- `index={8}` - Frame(8).svg
- `index={9}` - Frame(9).svg
- `index={10}` - Frame(10).svg
- `index={11}` - Frame(11).svg
- `index={12}` - Frame(12).svg
- `index={13}` - Frame(13).svg
- `index={14}` - Frame(14).svg
- `index={15}` - Frame(15).svg
- `index={16}` - Frame(16).svg
- `index={17}` - Frame(17).svg

## 基础用法

### 1. 基础使用

```jsx
import { FrameIcon } from './menuIcons';

function MyComponent() {
  return (
    <div>
      <FrameIcon index={0} />
      <FrameIcon index={1} />
      <FrameIcon index={2} />
    </div>
  );
}
```

### 2. 自定义样式

```jsx
import { FrameIcon } from './menuIcons';

function MyComponent() {
  return (
    <FrameIcon
      index={0}
      style={{ width: 24, height: 24, marginRight: 8 }}
    />
  );
}
```

### 3. 在菜单中使用

```jsx
import { FrameIcon } from './menuIcons';

<Menu.SubMenu
  title={(
    <span>
      <FrameIcon index={0} style={{ marginRight: 8 }} />
      <span>菜单项</span>
    </span>
  )}
>
```

### 4. 遍历显示所有图标

```jsx
import { FrameIcon } from './menuIcons';

function AllIcons() {
  return (
    <div>
      {Array.from({ length: 18 }, (_, index) => (
        <FrameIcon key={index} index={index} style={{ margin: 4 }} />
      ))}
    </div>
  );
}
```

## API

### FrameIcon Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| index | number | 0 | 图标索引，范围 0-17 |
| style | object | {width: 16, height: 16} | 自定义样式对象 |
| ...props | any | - | 其他HTML img属性 |

## 迁移指南

### 从原有代码迁移

```jsx
// 旧的用法（有语法错误）
export OpenSvgIcon = props => <Icon component={Frame} {...props} />;

// 新的用法
export const FrameIcon = ({ index = 0, style, ...props }) => {
  // 实现代码
};
```

### 使用新的API

```jsx
// 原有用法
<OpenSvgIcon />

// 新用法 - 通过index选择图标
<FrameIcon index={0} style={{ marginRight: 8 }} />
```

## 注意事项

1. 图标组件基于img标签和SVG文件实现
2. index范围是0-17，超出范围会显示默认图标(index 0)并在控制台警告
3. 保持了与原有 `OpenSvgIcon` 的向后兼容性
4. 支持通过style属性自定义图标大小和样式
