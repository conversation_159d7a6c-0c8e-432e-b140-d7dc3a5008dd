/* 经办人信息 */
import React, { useEffect, useState } from 'react';
import { Descriptions, message, Skeleton } from 'antd';
import { IDEN_CARD_TYPE } from '@/utils/consts';
import { queryHandlerInfo } from '@/pages/GroupCust/GroupCustManage/services';

const HandlerInfo = ({ currentCust }) => {
  const [handlerInfo, setHandlerInfo] = useState({});
  const [loading, setLoading] = useState(false);

  const getHandlerInfo = () => {
    if (!currentCust?.ORGA_ENTERPRISE_ID) return;

    setLoading(true);
    queryHandlerInfo({
      partyId: currentCust?.ORGA_ENTERPRISE_ID,
    }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        setHandlerInfo(resultObject?.rspParam?.busiInfo?.outData);
      } else {
        message.error(resultMsg);
      }
    }).always(() => {
      setLoading(false);
    });
  };

  useEffect(() => {
    if (currentCust?.ORGA_ENTERPRISE_ID) {
      getHandlerInfo();
    }
  }, [currentCust?.ORGA_ENTERPRISE_ID]);

  return (
    <Skeleton active loading={loading}>
      <Descriptions className="right" column={3} bordered={false}>
        <Descriptions.Item label="证件类型">
          {handlerInfo?.OPERATOR_IDEN_TYPE && IDEN_CARD_TYPE[handlerInfo.OPERATOR_IDEN_TYPE] }
        </Descriptions.Item>
        <Descriptions.Item label="证件号码">{handlerInfo?.OPERATOR_IDEN_NR }</Descriptions.Item>
        <Descriptions.Item label="姓名">{handlerInfo?.OPERATOR_NAME }</Descriptions.Item>
        <Descriptions.Item label="地址" span={3}>
          {handlerInfo?.OPERATOR_ADDRESS}
        </Descriptions.Item>
      </Descriptions>
    </Skeleton>
  );
};

export default HandlerInfo;
