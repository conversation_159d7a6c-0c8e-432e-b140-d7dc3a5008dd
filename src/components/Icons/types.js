/**
 * 图标类型定义和常量
 */

// 图标大小预设
export const ICON_SIZES = {
  SMALL: 12,
  DEFAULT: 16,
  MEDIUM: 20,
  LARGE: 24,
  XLARGE: 32,
};

// 图标颜色预设
export const ICON_COLORS = {
  PRIMARY: '#1890ff',
  SUCCESS: '#52c41a',
  WARNING: '#faad14',
  ERROR: '#f5222d',
  INFO: '#1890ff',
  DEFAULT: 'currentColor',
  DISABLED: '#d9d9d9',
};

// 图标分类
export const ICON_CATEGORIES = {
  BASIC: 'basic',           // 基础图标
  NAVIGATION: 'navigation', // 导航图标
  STATUS: 'status',         // 状态图标
  FILE: 'file',            // 文件图标
  USER: 'user',            // 用户图标
  BUSINESS: 'business',     // 业务图标
};

// 图标名称映射
export const ICON_NAMES = {
  // 基础操作
  ADD: 'add',
  DELETE: 'delete',
  EDIT: 'edit',
  SEARCH: 'search',
  FILTER: 'filter',
  REFRESH: 'refresh',
  CLOSE: 'close',
  
  // 导航
  HOME: 'home',
  BACK: 'back',
  FORWARD: 'forward',
  UP: 'up',
  DOWN: 'down',
  LEFT: 'left',
  RIGHT: 'right',
  
  // 状态
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
  LOADING: 'loading',
  
  // 文件
  FILE: 'file',
  FOLDER: 'folder',
  UPLOAD: 'upload',
  DOWNLOAD: 'download',
  
  // 用户
  USER: 'user',
  TEAM: 'team',
  SETTING: 'setting',
  LOGOUT: 'logout',
};
