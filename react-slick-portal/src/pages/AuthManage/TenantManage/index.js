import React, { useReducer, useEffect } from 'react';
import { connect } from 'dva';
import { Card, Form, Row, Col, Select, Divider, Input, Button, Modal, Drawer, message } from 'antd';
import { useAntdTable } from '@umijs/hooks';
import request from '@/utils/request';
import SlickTable from '@/components/SlickTable';
import AddTenant from './components/AddTenant';
import EditTenant from './components/EditTenant';

const initialState = {
  pageSize: 10,
  drawerVisible: false,
  tenantCreate: true,
  editTenantData: null,
  statusCdList: [],
  defaultTenantId: null,
};

function reducer(state, action) {
  const { type, payload = {} } = action;
  switch (type) {
    case 'toggle':
      return {
        ...state,
        ...payload,
      };
    default:
      throw new Error();
  }
}

function TenantManage({ form }) {
  const [state, dispatch] = useReducer(reducer, initialState);
  const { getFieldDecorator } = { ...form };

  function getTableData({ current, pageSize, ...rest }) {
    return request('portal/TenantController/pageInfo.do', {
      data: {
        pageNum: current,
        pageSize,
        ...rest,
      },
    }).then(res => {
      if (res && Array.isArray(res.list)) {
        return {
          total: res.total,
          data: res.list,
        };
      }
      return {
        total: 0,
        data: [],
      };
    });
  }

  const { tableProps, refresh, search } = useAntdTable(
    params => {
      return getTableData({
        ...params,
      });
    },
    [],
    {
      defaultPageSize: state.pageSize,
      form,
    }
  );

  const { pagination, ...restTableProps } = tableProps;
  const { submit, reset } = search;

  const deleteConfirm = tenantId => {
    Modal.confirm({
      content: '确认删除该租户？',
      onOk() {
        request(`portal/TenantController/invalid.do?id=${tenantId}`, { method: 'get' }).then(
          res => {
            if (!res) {
              return;
            }
            if (res.success) {
              message.success('删除成功！');
              refresh();
              return;
            }
            message.error(`删除失败！${res.resultMsg}`);
          }
        );
      },
    });
  };

  const releaseConfirm = tenantId => {
    Modal.confirm({
      content: '确认发布该租户？',
      onOk() {
        request(`portal/TenantController/release.do?id=${tenantId}`, { method: 'get' }).then(
          res => {
            if (!res) {
              return;
            }
            if (res.success) {
              message.success('发布提交成功，等待发布结果');
              refresh();
              return;
            }
            message.error(`发布失败！${res.resultMsg}`);
          }
        );
      },
    });
  };

  const handleDrawerClose = () => {
    dispatch({
      type: 'toggle',
      payload: {
        drawerVisible: false,
      },
    });
  };

  const handleDrawerShow = (type, record) => {
    dispatch({
      type: 'toggle',
      payload: {
        drawerVisible: true,
        tenantCreate: type === 'create',
        editTenantData: record || null,
      },
    });
  };

  useEffect(() => {
    // 获取租户状态枚举值
    request('portal/DomainDataController/getValuesList.do', {
      data: {
        busiNbr: 'Tenant',
        propertyName: 'statusCd',
      },
    }).then(res => {
      if (Array.isArray(res)) {
        dispatch({
          type: 'toggle',
          payload: {
            statusCdList: res,
          },
        });
      }
    });
  }, []);

  const columns = [
    {
      title: '租户名称',
      dataIndex: 'tenantName',
      ellipsis: true,
    },
    {
      title: '租户编码',
      dataIndex: 'tenantNbr',
      ellipsis: true,
    },
    // 预留字段，暂时不展示
    // {
    //   title: '租户管理员',
    //   dataIndex: 'tenantManager',
    //   ellipsis: true,
    // },
    {
      title: '租户状态',
      dataIndex: 'statusCdName',
      ellipsis: true,
    },
    {
      title: '创建人',
      dataIndex: 'createStaffName',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createDate',
      ellipsis: true,
    },
    {
      title: '修改人',
      dataIndex: 'updateStaffName',
      ellipsis: true,
    },
    {
      title: '最后修改时间',
      dataIndex: 'updateDate',
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
    },
    {
      title: '操作',
      render: (text, record) => {
        return (
          <>
            {/* 租户状态为1100-作废、1300-发布中时，不允许修改 */}
            {record.statusCd !== '1300' && record.statusCd !== '1100' && (
              <a
                onClick={() => {
                  handleDrawerShow('edit', record);
                }}
              >
                修改
              </a>
            )}
            {/* 租户状态为1200-待发布时才允许发布 */}
            {record.statusCd === '1200' && (
              <>
                <Divider type="vertical" />
                <a
                  onClick={() => {
                    releaseConfirm(record.id);
                  }}
                >
                  发布
                </a>
              </>
            )}
            {/* 租户状态为1000-已发布和1200-待发布时才允许删除 */}
            {(record.statusCd === '1000' || record.statusCd === '1200') &&
              record.tenantNbr !== 'BCMC' && (
                <>
                  <Divider type="vertical" />
                  <a
                    onClick={() => {
                      deleteConfirm(record.id);
                    }}
                  >
                    删除
                  </a>
                </>
              )}
          </>
        );
      },
    },
  ];

  return (
    <>
      <Card
        title="租户管理"
        className="cute"
        extra={
          <Button
            type="primary"
            onClick={() => {
              handleDrawerShow('create');
            }}
          >
            新增
          </Button>
        }
      >
        <Form className="flow" style={{marginBottom: '20px'}}>
          <Row gutter={{ md: 8, lg: 24, xl: 48 }} style={{ textAlign: 'center' }}>
            <Col span={6} offset={1}>
              <Form.Item label="租户名称">
                {getFieldDecorator('tenantName')(<Input allowClear placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6} offset={1}>
              <Form.Item label="租户编码">
                {getFieldDecorator('tenantNbr')(<Input allowClear placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6} offset={1}>
              <Form.Item label="租户状态">
                {/* 默认展示待发布、已发布、发布中的数据 */}
                {getFieldDecorator('statusCds', { initialValue: ['1000', '1200', '1300'] })(
                  <Select allowClear placeholder="请选择" mode="multiple" showArrow>
                    {state.statusCdList &&
                      state.statusCdList.map(item => (
                        <Select.Option key={item.value} value={item.value}>
                          {item.name}
                        </Select.Option>
                      ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={3}>
              <Button type="primary" className="margin-right" onClick={submit}>
                查询
              </Button>
              <Button type="default" onClick={reset}>
                重置
              </Button>
            </Col>
          </Row>
        </Form>
        <SlickTable
          rowKey={record => record.id}
          columns={columns}
          {...restTableProps}
          data={{
            pagination,
          }}
        />
      </Card>
      <Drawer
        title={state.tenantCreate ? '新增租户' : '修改租户信息'}
        destroyOnClose
        width={620}
        visible={state.drawerVisible}
        onClose={handleDrawerClose}
        bodyStyle={{ paddingBottom: 80 }}
      >
        {state.tenantCreate ? (
          <AddTenant close={handleDrawerClose} refresh={refresh} />
        ) : (
          <EditTenant
            close={handleDrawerClose}
            refresh={refresh}
            tenantData={state.editTenantData}
          />
        )}
      </Drawer>
    </>
  );
}

export default connect()(Form.create()(TenantManage));
