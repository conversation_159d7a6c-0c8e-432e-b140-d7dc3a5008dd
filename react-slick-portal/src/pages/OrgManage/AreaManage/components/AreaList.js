import React, { useState, useEffect, useRef, useLayoutEffect } from 'react';
import { connect } from 'dva';
import { Row, Col, Card, Form, Input, Icon, Button, Tooltip, Modal, Divider } from 'antd';
import PubSub from 'pubsub-js';
import styles from '../styles.less';
import SlickTable from '@/components/SlickTable';
import useSlickTable from '@/hooks/UseSlickTable';
import request from '@/utils/request';
import { newGetDictData } from '@/services/common';

const { Search } = Input;

// 空对象校验
const judgeObj = obj => {
  if (Object.keys(obj).length == 0) return true;
  return false;
};

const namespace = 'areaManage';
const eventName = 'areaManage_AreaList';

function areaList(props) {
  const [allowEdit, setAllowEdit] = useState(false);
  const [params, setParams] = useState({
    parentOrgId: -2,
  });
  // 判断初始化时是否有数据
  const [passData, setPassData] = useState({});

  // 通过dva存当前需要传递的数据
  const savePassData = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/savePassData`,
      payload: params,
    });
  };

  const saveBehavior = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveBehavior`,
      payload: params,
    });
  };

  const { tableProps } = useSlickTable(
    { pageSize: 5 },
    // 'orgauth/OrganizationController/qryOperOrgGridData.do',
    'orgauth/CommonRegionController/qryOperRegionGridData.do',
    params
  );

  // useEffect(()=>{
  //   // PubSub.subscribe(`${eventName}.Update`, () => {
  //   //   setParams({
  //   //     ...params
  //   //   })
  //   // });
  //   // return () => {
  //   //   PubSub.unsubscribe(`${eventName}`);
  //   // };
  // },[])

  useEffect(() => {
    newGetDictData({
      groupCode: 'SYSTEM_VAR',
      paramCode: 'USE_USER_CENTER',
    }).then(res => {
      setAllowEdit(res !== '1'); // 1:不允许编辑
    });
  }, []);

  useEffect(() => {
    if (tableProps.data.list.length && judgeObj(passData)) {
      const [head, ...other] = tableProps.data.list;
      setPassData(head);
      savePassData(head);
    }
    PubSub.subscribe(`${eventName}.Update`, () => {
      setParams({
        ...params,
      });
    });
    return () => {
      PubSub.unsubscribe(`${eventName}`);
    };
  }, [tableProps.data.list]);

  useEffect(() => {
    if (!judgeObj(props.tableData)) {
      setParams({
        ...params,
        parentOrgId: props.tableData.commonRegionId,
        filterVal: '',
        gb_reset: true,
      });
      setPassData({});
    }
  }, [props.tableData]);

  const onSearch = value => {
    setParams({
      ...params,
      filterVal: value,
      gb_reset: true,
    });
  };

  const editor = record => {
    saveBehavior('edit');
  };

  const add = record => {
    saveBehavior('add');
  };

  const rowSelect = record => {
    if (props.behavior === 'disabled' || record.commonRegionId === passData.commonRegionId) {
      setPassData(record);
      savePassData(record);
    } else {
      Modal.info({ content: '请先保存或取消当前操作' });
    }
  };
  const setRowClassName = record => record.commonRegionId === passData.commonRegionId ? `${styles.clickRowStyle}` : '';

  const columns = [
    {
      title: '区域标识',
      dataIndex: 'commonRegionId',
    },
    {
      title: '区域名称',
      dataIndex: 'regionName',
    },
    {
      title: '区域编码',
      dataIndex: 'regionNbr',
    },
    {
      title: '区域路径',
      dataIndex: 'pathName',
    },

    {
      title: '操作',
      render: (text, record) => {
        if (allowEdit) {
          return (
            <span>
              <a
                onClick={() => {
                  editor(record);
                }}
              >
                编辑
              </a>
              <Divider type="vertical" />
              <a
                onClick={() => {
                  add(record);
                }}
              >
                新建下级区域
              </a>
            </span>
          );
        }
      },
    },
  ];

  return (
    <Card
      className="cute"
      title="区域列表"
      extra={<Search placeholder="区域名称搜索" style={{ width: '200px' }} onSearch={onSearch} />}
      style={{ minHeight: '308px' }}
    >
      <SlickTable
        rowKey={record => record.id}
        columns={columns}
        {...tableProps}
        onRow={record => ({
            onClick: () => {
              rowSelect(record);
            },
          })}
        rowClassName={setRowClassName}
      />
    </Card>
  );
}

export default connect(({ areaManage }) => ({
  tableData: areaManage.tableData,
  behavior: areaManage.behavior,
}))(areaList);
