/* eslint-disable import/no-duplicates */
import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>u, Icon, Tooltip, Spin, Badge, message } from 'antd';
import classNames from 'classnames';
import { connect } from 'dva';
import debounce from 'lodash/debounce';
import PubSub from 'pubsub-js';
import { useBoolean } from '@umijs/hooks';
import { getHighRecommendMenus, getRecommendMenusByMenuId, getNavigateMenus } from '@/services/menu';
import { getItem, isEmptyStr, transformToArray, isNumber, openMenu } from '@/utils/utils';
import { getTodoRefreshTime } from '@/services/undo';
import styles from '../../less/menuDrawer.less';
import navigate from '../../img/navigate.png';
import navigate2 from '../../img/navigate2.png';
import often from '../../img/often.png';
import often2 from '../../img/often2.png';
import recommend from '../../img/recommend.png';
import recommend2 from '../../img/recommend2.png';
import wait from '../../img/wait.png';
import wait2 from '../../img/wait2.png';
import collectionIcon from '../../img/collect.png';
import collection2 from '../../img/collect2.png';
import history from '../../img/history.png';
import history2 from '../../img/history2.png';
import knowledge from '../../img/knowledge.svg';
import knowledge2 from '../../img/knowledge.svg';
import help from '../../img/help.svg';
import help2 from '../../img/help2.svg';
import TaskMenuList from './components/TaskMenuList';
import OperationManualList from './components/OperationManualList';
import CollectionManageDrawer from '@/layouts/BlueStyleLayout/components/CollectionManageDrawer';
import request from '@/utils/request';

const TYPE_ENUM = {
  recommend: {
    name: 'recommend',
    file: recommend,
    fileLight: recommend2,
    title: '功能推荐',
    id: 'leftmenudrawer_recommend',
  },
  navigate: {
    name: 'navigate',
    file: navigate,
    fileLight: navigate2,
    title: '导航节点视图',
    id: 'leftmenudrawer_navigate',
  },
  // wait: {
  //   name: 'wait',
  //   file: wait,
  //   fileLight: wait2,
  //   title: '统一待办',
  //   id: 'leftmenudrawer_wait',
  // },
  history: {
    name: 'history',
    file: history,
    fileLight: history2,
    title: '最近访问',
    id: 'leftmenudrawer_history',
  },
  collection: {
    name: 'collection',
    file: collectionIcon,
    fileLight: collection2,
    title: '我的收藏',
    id: 'leftmenudrawer_collection',
  },
  often: {
    name: 'often',
    file: often,
    fileLight: often2,
    title: '高频功能',
    id: 'leftmenudrawer_often',
  },
  help: {
    name: 'help',
    file: help,
    fileLight: help2,
    title: '操作手册',
    id: 'leftmenudrawer_help',
  },
  knowledge: {
    name: 'knowledge',
    file: knowledge,
    fileLight: knowledge2,
    title: '在线知识',
    id: 'leftmenudrawer_knowledge',
  },
};


function MenuDrawer({
  recent,
  collection,
  size,
  currMenu,
  pathname,
  funNavigationIdMap, // 存储形式： {navId: navAttrId}
  dispatch,
  currentOpenLeftMenu,
  showLeftMenuText,
  history: routerHistory,
  showSecondMenu,
  all,
}) {
  const [currentType, setCurrentType] = useState();
  const [menuList, setMenuList] = useState([]);
  const [showRecommendIcon, setShowRecommendIcon] = useState(false); // 展示左侧智能推荐图标
  const [showNavigateIcon, setShowNavigateIcon] = useState(false); // 展示左侧导航节点视图图标
  const [loading, setLoading] = useState(false);
  const [currentChooseKeys, setCurrentChooseKeys] = useState([]); // 保存当前左侧菜单栏中高亮的项的menuCode
  const [highRecommendMenuList, setHighRecommendMenuList] = useState([]);
  const funNavigationIdMapRef = useRef();
  const currentMenuIdRef = useRef(null);
  const currentTypeRef = useRef(null);
  const [waitRefreshInfo, setWaitRefreshInfo] = useState({ loading: false, refreshTime: 5, title: '' });
  const waitRefreshInfoRef = useRef();
  const taskMenuRef = useRef();
  const { userInfo } = getItem('user');

  const user = getItem('user');
  const { userInfo: { roleId } = {} } = user;
  const { state: collectionVisible, setTrue: showCollection, setFalse: hideCollection } = useBoolean(false);

  const triggerResizeEvent = debounce(() => {
    const event = document.createEvent('HTMLEvents');
    event.initEvent('resize', true, false);
    window.dispatchEvent(event);
  }, 300);

  // 菜单是否切换
  const menuTypeIsChange = menuTypeName => currentTypeRef.current.name !== menuTypeName;
  // 页签是否切换
  const tabIsChange = () => currentMenuIdRef.current !== currMenu.menuId;
  // 菜单或页签是否切换
  const menuTypeOrTabIsChange = menuTypeName => tabIsChange() || menuTypeIsChange(menuTypeName);

  const setShowSecondMenu = flag => {
    dispatch({
      type: 'setting/updateSettings',
      payload: { showSecondMenu: flag },
    });
    triggerResizeEvent();
  };

  const getHistoryMenuList = async () => {
    // 原需求：只显示工作台打开方式.filter(item => item.menuOpenMode === '1')
    setMenuList(recent.slice(0, 10));
  };

  const getCollectionMenuList = async () => {
    setMenuList(collection);
  };

  const setShowFirstMenu = () => {
    dispatch({
      type: 'setting/updateSettings',
      payload: {
        currentOpenLeftMenu: true,
      },
    });
    triggerResizeEvent();
  };

  const setShowFirstAndSecondMenu = flag => {
    setShowFirstMenu(flag);
    setShowSecondMenu(flag);
  };

  const getNavMenuList = async () => {
    const _navId = String(pathname).slice(12);
    if (isEmptyStr(_navId) || isEmptyStr(funNavigationIdMapRef.current?.[_navId])) {
      return [];
    }

    const list = await getNavigateMenus(funNavigationIdMapRef.current?.[_navId]);

    return list;
  };

  const getRecommendMenuList = async () => {
    const list = isEmptyStr(currMenu?.menuId) ? [] : await getRecommendMenusByMenuId(currMenu?.menuId);
    if (menuTypeOrTabIsChange(TYPE_ENUM.recommend.name)) {
      return;
    }
    setMenuList(list);
  };

  // 如果在highRecommendMenuList未初始化完成时就点击高频功能图标,那么就在这里完成初始化
  const getHighRecommendList = async () => {
    if (highRecommendMenuList.length === 0) {
      const list = await getHighRecommendMenus(roleId);
      setHighRecommendMenuList(list);

      if (menuTypeIsChange(TYPE_ENUM.often.name) === false) {
        setMenuList(list);
      }
    } else {
      setMenuList(highRecommendMenuList);
    }
  };

  const getAndSetNavList = async () => {
    const list = await getNavMenuList();
    if (menuTypeOrTabIsChange(TYPE_ENUM.navigate.name)) {
      return;
    }
    setMenuList(list);
  };

  const getMenuList = async typeObj => {
    if (typeObj.name !== TYPE_ENUM?.wait?.name) {
      setLoading(true);
    }

    switch (typeObj.name) {
      case TYPE_ENUM.often.name:
        await getHighRecommendList();
        break;
      case TYPE_ENUM.history.name:
        getHistoryMenuList();
        break;
      case TYPE_ENUM.collection.name:
        getCollectionMenuList();
        break;
      case TYPE_ENUM.navigate.name:
        await getAndSetNavList();
        break;
      case TYPE_ENUM.recommend.name:
        await getRecommendMenuList();
        break;
      default:
        setMenuList([]);
    }

    if (typeObj.name !== TYPE_ENUM?.wait?.name) {
      setLoading(false);
    }
  };

  // 左侧菜单图标点击响应
  const handleMeunIconClick = (typeObj, navId) => {
    if (typeObj.id === 'leftmenudrawer_knowledge') {
      let externalUserInfo = userInfo.externalUserInfos.find(item => item.systemNbr === 'CRM' && item.currentFlag === '1000');
      if (!externalUserInfo) {
        externalUserInfo = userInfo.externalUserInfos.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000');
      }
      if (!externalUserInfo) {
        message.warning('无可用CRM用户信息');
        return null;
      }

      request('portal/LocalLoginController/knowledgeCenterSsoLogin.do', {
        method: 'GET',
        data: {
          externalUserCode: externalUserInfo.externalUserCode,
        },
      })
      .then(res => {
        if (res.resultCode === 'TRUE') {
          const url = res.resultObject;
          window.open(url, '_blank');
        } else {
          message.warning(res.resultMsg);
        }
      });
      return;
    }
    setCurrentType(typeObj);
    if (showSecondMenu && currentType?.name === typeObj.name) {
      setShowSecondMenu(false);
    } else {
      setShowSecondMenu(true);
    }
    getMenuList(typeObj, navId);
  };

  const handleNewMenu = async () => {
    // 获取功能推荐菜单列表
    const recommendList = currMenu?.menuId ? await getRecommendMenusByMenuId(currMenu?.menuId) : [];

    if (tabIsChange()) {
      return;
    }

    setShowRecommendIcon(recommendList.length > 0);

    let navlist = [];
    const basePath = String(pathname).slice(0, 12);

    if (basePath === '/navigation/') {
      setShowNavigateIcon(true);
      navlist = await getNavMenuList();
    } else {
      setShowNavigateIcon(false);
    }

    if (tabIsChange()) {
      return;
    }

    if (!showSecondMenu || !currentOpenLeftMenu) {
      // 菜单没展开

      if (navlist.length > 0 || recommendList.length > 0) {
        // 有导航或者功能推荐菜单信息

        setShowFirstAndSecondMenu(true);

        if (navlist.length > 0) {
          setCurrentType(TYPE_ENUM.navigate);
          setMenuList(navlist);
        } else {
          setCurrentType(TYPE_ENUM.recommend);
          setMenuList(recommendList);
        }
      }
    } else if (currentType?.name === TYPE_ENUM.recommend.name) {
      // 二级菜单展开了，且处于功能推荐
      if (recommendList > 0) {
        setMenuList(recommendList);
      } else if (navlist.length > 0) {
        setCurrentType(TYPE_ENUM.navigate);
        setMenuList(navlist);
      } else {
        setMenuList([]);
        setShowSecondMenu(false);
      }
    } else if (currentType?.name === TYPE_ENUM.navigate.name) {
      // 二级菜单展开了，且处于业务导航
      if (navlist > 0) {
        setMenuList(navlist);
      } else if (recommendList.length > 0) {
        setCurrentType(TYPE_ENUM.recommend);
        setMenuList(recommendList);
      } else {
        setMenuList([]);
        setShowSecondMenu(false);
      }
    }
  };

  const openTabWithMenuCode = (menuCode, taskMenuList) => {
    if (menuCode === 'openTheFirst') {
      const menu = taskMenuList?.[0]?.subTaskInfo?.[0];
      openMenu(menu?.menuCode, all, dispatch);
      setTimeout(() => {
        setCurrentChooseKeys([menu?.menuCode]);
      }, 1000);
      return;
    }

    transformToArray(taskMenuList).forEach(item => {
      transformToArray(item.subTaskInfo).forEach(menu => {
        if (menu?.menuCode === menuCode) {
          openMenu(menuCode, all, dispatch);
        }
      });
    });
    setTimeout(() => {
      setCurrentChooseKeys([menuCode]);
    }, 1000);
  };

  // 打开左侧待办菜单
  const openMenuOfWait = (menuCode = 'openTheFirst', taskMenuList) => {
    setShowFirstAndSecondMenu(true);
    setCurrentType(TYPE_ENUM?.wait);
    openTabWithMenuCode(menuCode, taskMenuList);
  };

  // 每次页签切换时
  useEffect(() => {
    // 设置当前页签key用于高亮左侧菜单栏
    setCurrentChooseKeys([currMenu?.menuCode]);

    currentMenuIdRef.current = currMenu.menuId;
    handleNewMenu();
  }, [currMenu]);

  useEffect(() => {
    currentTypeRef.current = currentType;
  }, [currentType]);

  useEffect(() => {
    waitRefreshInfoRef.current = waitRefreshInfo;
  }, [waitRefreshInfo]);

  // 这段的目的是在导航菜单开启时，切换导航细分时，更新导航菜单
  // 这里不是很需要使用useEffect，可以优化为通过PubSub实现
  useEffect(() => {
    // 导航页面内点击不同节点会触发funNavigationIdMap更新，从而重新获取导航菜单，包括关闭导航页面也会触发更新
    funNavigationIdMapRef.current = funNavigationIdMap;
    if (funNavigationIdMap && Object.keys(funNavigationIdMap).length > 0) {
      setTimeout(async () => {
        const list = await getNavMenuList();

        if (tabIsChange()) {
          return;
        }

        if (list.length > 0) {
          setShowFirstAndSecondMenu(true);
          setCurrentType(TYPE_ENUM.navigate);
          setMenuList(list);
        } else if (currentType?.name === TYPE_ENUM.navigate.name) {
          setShowFirstAndSecondMenu(false);
          setMenuList(list);
        }
      }, 0);
    }
  }, [funNavigationIdMap]);

  const getWaitRefreshTime = async () => {
    let time = await getTodoRefreshTime();
    time = parseInt(time, 10);

    if (isNumber(time)) {
      setWaitRefreshInfo({ ...waitRefreshInfo, refreshTime: time });
    }
  };

  const initData = async () => {
    setShowSecondMenu(false);
    const list = await getHighRecommendMenus(roleId);
    setHighRecommendMenuList(list);
    // 统一待办数据
    getWaitRefreshTime();
  };

  useEffect(() => {
    initData();
  }, []);

  const resetHightRecommendMenus = async () => {
    setHighRecommendMenuList([]);
    setShowSecondMenu(false);
    const list = await getHighRecommendMenus(roleId);
    setHighRecommendMenuList(list);
  };

  const refreshWait = async () => {
    if (isEmptyStr(waitRefreshInfo.title)) {
      taskMenuRef.current.initTaskTree();
      setWaitRefreshInfo({ ...waitRefreshInfo, loading: true, title: `间隔${waitRefreshInfo.refreshTime}秒后可再次刷新` });
      setTimeout(() => {
        setWaitRefreshInfo({ ...waitRefreshInfo, loading: false, title: '' });
      }, waitRefreshInfo.refreshTime * 1000);
    }
  };

  useEffect(() => {
    // 订阅"重新获取高频功能"的消息
    PubSub.subscribe('resetHightRecommendMenus', info => {
      if (info === 'resetHightRecommendMenus') {
        resetHightRecommendMenus();
      }
    });

    return () => {
      PubSub.unsubscribe('resetHightRecommendMenus');
    };
  }, [roleId]);

  const renderLeftMenu = () =>
    Object.keys(TYPE_ENUM).map(key => {
      let hide = false;
      const item = TYPE_ENUM[key];
      if ((item.name === TYPE_ENUM.recommend.name && !showRecommendIcon) || (item.name === TYPE_ENUM.navigate.name && !showNavigateIcon)) {
        hide = true;
      }

      const choosed = currentType?.name === item.name && showSecondMenu;

      // 如果默认展示图标文字，就不用悬浮文字提示了
      const res = (
        <Tooltip placement="right" title={!showLeftMenuText && item.title}>
          <img style={{ display: choosed ? 'block' : 'none' }} alt="" width={22} height={22} src={item.fileLight} />
          <img style={{ display: choosed ? 'none' : 'block' }} alt="" width={22} height={22} src={item.file} />
        </Tooltip>
      );

      let finalRes = res;

      // 如果是统一待办，要加上角标（根据需求已屏蔽，后续如果要添加再开放）
      if (item.name === TYPE_ENUM?.wait?.name) {
        finalRes = [
          (
            <div className={styles.waitBadge}>
              <Badge count={taskMenuRef.current?.allCount || 0} />
            </div>
          ),
          res,
        ];
      }

      let height = showLeftMenuText ? 80 : 40;
      if (item.name === TYPE_ENUM.navigate.name) {
        height = showLeftMenuText ? 96 : 40;
      }

      return (
        <div
          id={TYPE_ENUM[key].id}
          className={classNames(styles.unit, { [styles.active]: choosed }, { [styles.notActive]: !choosed })}
          key={key}
          style={{ height, display: hide ? 'none' : 'block' }}
          onClick={() => {
            handleMeunIconClick(item);
          }}
        >
          <div className={styles.icon}>{finalRes}</div>
          {showLeftMenuText && (
            <div className={styles.textWrap}>
              <span className={classNames(styles.text, { [styles.blue]: choosed })}>{item.title}</span>
            </div>
          )}
        </div>
      );
    });

  return (
    <div id="menu-drawer" className={classNames('menu-drawer-basic')}>
      <div className={styles.wrap}>
        <div className={styles.first} style={{ minHeight: size.height + 50 }}>
          {renderLeftMenu()}
        </div>
        <div className={styles.second} style={{ minHeight: size.height, display: showSecondMenu ? 'block' : 'none' }}>
          <div className={styles.header}>
            <span>{currentType?.title}</span>
            <span>
              <span hidden={currentType?.name !== TYPE_ENUM?.wait?.name}>
                <Tooltip title={waitRefreshInfo.title} trigger="hover" placement="bottom">
                  <Icon
                    className="margin-right"
                    type="sync"
                    onClick={refreshWait}
                    spin={waitRefreshInfo.loading}
                    style={{
                      fontSize: '16px',
                      color: isEmptyStr(waitRefreshInfo.title) ? '#4290f7' : '#ccc',
                    }}
                  />
                </Tooltip>
              </span>
              <Icon
                style={{
                  float: 'right',
                  fontSize: '18px',
                  cursor: 'pointer',
                }}
                onClick={() => {
                  setShowSecondMenu(false);
                }}
                type="menu-fold"
              />
              {currentType?.name === 'collection' && (
                <Icon
                  className="margin-right"
                  style={{
                    float: 'right',
                    fontSize: '18px',
                    cursor: 'pointer',
                  }}
                  onClick={() => showCollection()}
                  type="file-add"
                />
              )}
            </span>
          </div>
          <Spin spinning={loading}>
            <div className={styles.list} style={{ height: size.height + 17 }}>
              {/* <TaskMenuList
                setLoading={setLoading}
                selectedKeys={currentChooseKeys}
                setSelectedKeys={setCurrentChooseKeys}
                history={routerHistory}
                show={currentType?.name === TYPE_ENUM?.wait?.name}
                openMenuOfWait={openMenuOfWait}
                user={user}
                cRef={taskMenuRef}
                setWaitRefreshInfo={setWaitRefreshInfo}
                waitRefreshInfoRef={waitRefreshInfoRef}
              /> */}

              <OperationManualList
                setLoading={setLoading}
                selectedKeys={currentChooseKeys}
                setSelectedKeys={setCurrentChooseKeys}
                history={routerHistory}
                show={currentType?.name === TYPE_ENUM.help.name}
                openMenuOfWait={openMenuOfWait}
                user={user}
                cRef={taskMenuRef}
                setWaitRefreshInfo={setWaitRefreshInfo}
                waitRefreshInfoRef={waitRefreshInfoRef}
              />

              {// && currentType?.name !== TYPE_ENUM.help.name
              currentType?.name !== TYPE_ENUM?.wait?.name && currentType?.name !== TYPE_ENUM.help.name && (
                <Menu mode="inline" selectedKeys={currentChooseKeys}>
                  {transformToArray(menuList).map(menu => (
                    <Menu.Item key={menu.menuCode}>
                      <div
                        className="text-ellipsis"
                        title={menu.menuName}
                        data-reg-id={`${currentType?.name}_${menu.menuCode}`}
                        onClick={() => {
                          openMenu(menu, all, dispatch);
                        }}
                      >
                        {menu.menuName}
                      </div>
                    </Menu.Item>
                  ))}
                </Menu>
              )}
            </div>
          </Spin>
        </div>
      </div>
      {collectionVisible && <CollectionManageDrawer visible={collectionVisible} close={hideCollection} />}
    </div>
  );
}

export default connect(({ menu, setting }) => ({
  recent: menu.recent,
  collection: menu.collection,
  newOpenMenuId: menu.newOpenMenuId,
  funNavigationIdMap: menu.funNavigationIdMap,
  all: menu.all,
  size: setting.size,
  currentOpenLeftMenu: setting.currentOpenLeftMenu,
  showLeftMenuText: setting.showLeftMenuText,
  showSecondMenu: setting.showSecondMenu,
}))(MenuDrawer);
