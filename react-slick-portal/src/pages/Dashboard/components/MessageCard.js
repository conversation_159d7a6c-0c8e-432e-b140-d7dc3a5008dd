import React, { useEffect, useState, useLayoutEffect } from 'react';
import { Icon, Empty, Tag, Modal, message, Spin } from 'antd';
import Link from 'umi/link';
import router from 'umi/router';
import moment from 'moment';
import { useDebounceFn } from '@umijs/hooks';
import request from '@/utils/request';
import style from './messageCard.less';

const MessageCard = () => {
  const [messageList, setMessageList] = useState([]);
  const [visible, setVisible] = useState(false);
  const [detail, setDetail] = useState({});
  const [loading, setLoading] = useState(false);
  const [width, setWidth] = useState(0);

  // 防抖，计算当前导航位置
  const { run: setSize } = useDebounceFn(() => {
    const { offsetWidth } = document.getElementById('messageCard');
    setWidth(offsetWidth);
  }, 50);

  // 监听画布size
  useLayoutEffect(() => {
    setSize();
    window.addEventListener('resize', setSize);
    return () => {
      window.removeEventListener('resize', setSize);
    };
  }, [setSize]);

  const getMessageData = async () => {
    setLoading(true);
    const result = await request('portal/RemindMessageController/selectMessageGridData.do', {
      data: {
        statusCd: '1100',
        pageNum: 1,
        rowNum: 5,
        pageSize: 5,
        sortOrder: 'asc',
      },
    });
    if (result) {
      const { list = [] } = result;
      setMessageList(list);
    }
    setLoading(false);
  };

  useEffect(() => {
    getMessageData();
  }, []);

  /**
   * 设置已读
   * @param {object[]}
   */
  const makeAsRead = item => {
    const result = [{ remindMessageId: item.id, statusCd: '1000' }];
    request('portal/RemindMessageController/makeAsRead.do', {
      method: 'put',
      data: result,
    }).then(response => {
      // 回参：0 标记成功
      if (response === 0) {
        message.success('已读成功');
      }
    });
  };

  const actionHandler = (e, record) => {
    e.preventDefault();

    if (record.jumpLinks) {
      router.push(record.jumpLinks);
      if (record.statusCd !== '1000') {
        makeAsRead(record);
      }
    } else {
      setVisible(true);
      setDetail(record);
    }
  };

  const closeModal = () => {
    setVisible(false);
    if (detail.statusCd !== '1000') {
      makeAsRead(detail);
    }
    setDetail({});
    getMessageData();
  };

  const levelTag = level => {
    let color;
    let text;
    switch (String(level)) {
      case '1':
        color = '#e40077';
        text = '紧急';
        break;
      case '2':
        color = '#f3b100';
        text = '重要';
        break;
      case '3':
      default:
        color = '#8fc31f';
        text = '一般';
        break;
    }
    return (
      <Tag
        color={color}
        className={style.tag}
      >
        <span>{text}</span>
      </Tag>
    );
  };

  return (
    <div
      id="消息"
      className={style.messageCard}
      loading={loading}
    >
      <div className={style.header}>
        <div className={style.headerTitle}>我的消息</div>
        <Link to="/notice" style={{ color: '#999999', fontSize: '12px', marginRight: '20px' }} data-reg-id="notify_more">
          <span style={{ verticalAlign: 'middle' }}>查看全部</span>
          <Icon style={{ verticalAlign: 'middle' }} type="right" />
        </Link>
      </div>
      <Spin spinning={loading}>
        <div className={style.noticeContent}>
          {messageList.length > 0 ? (
            <div>
              {
                messageList.map(item => (
                  <div className={style.msgItem}>
                    <div className={style.itemLeft}>
                      {levelTag(item.urgencyLevel)}
                      <div className={style.title} onClick={e => actionHandler(e, item)}>{item.msgTitle ?? ''}</div>
                      {width > 750 ? <div className={style.content}>{item.msgContent}</div> : null}
                    </div>
                    <div
                      className={style.date}
                      style={{ color: item.bulletin?.bulletinLevel === '1000' && 'red' }}
                    >
                      {moment(item.bulletin?.createDate).format('YYYY-MM-DD') ?? ''}
                    </div>
                  </div>
                ))
              }
            </div>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="当前无未读消息" />
          )}
        </div>
      </Spin>

      <Modal
        width={600}
        title={detail.msgTitle}
        visible={visible}
        footer={null}
        onCancel={closeModal}
        destroyOnClose
        centered
      >
        <p>{detail.msgContent}</p>
        <div className="text-right text-gray">{detail.createDate}</div>
      </Modal>
    </div>
  );
};

export default MessageCard;
