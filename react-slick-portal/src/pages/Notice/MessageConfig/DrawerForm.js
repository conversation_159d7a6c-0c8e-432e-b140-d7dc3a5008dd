import React, { useEffect } from 'react';
import {
  Form,
  Select,
  Radio,
  Checkbox,
  InputNumber,
  TimePicker,
  Button,
} from 'antd';
import moment from 'moment';
import styles from './index.less';

const { Option } = Select;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const DrawerForm = props => {
  const { selectData, behavior, messageType, colorsMap, remindModeMap, freqTypeMap, close, save } = props;

  const { getFieldDecorator, setFieldsValue, getFieldValue, resetFields } = props.form;


  useEffect(() => {
    if (behavior === 'edit') {
      setFieldsValue({
        ...selectData,
        startTime: moment(selectData?.startTime, 'HH:mm:ss'),
        endTime: moment(selectData?.endTime, 'HH:mm:ss'),
      });
    } else {
      resetFields();
    }
  }, [behavior]);

  const submitForm = () => {
    props.form.validateFields((err, formValues) => {
      if (err) return;
      let item = {
        ...formValues,
        startTime: formValues?.startTime?.format('HH:mm:ss'),
        endTime: formValues?.endTime?.format('HH:mm:ss'),
        remindMode: typeof formValues?.remindMode === 'string' ? formValues?.remindMode : formValues?.remindMode.join(),
      };
      if (behavior === 'edit') {
        item = {
          ...selectData,
          ...item,
        };
      }
      save(item);
    });
  };

  return (
    <div className={styles.messageConfig}>
      <Form {...formItemLayout}>
        <Form.Item label="消息类型">
          {getFieldDecorator('remindTypeId', {
            rules: [
              {
                required: true,
                message: '请选择消息类型',
              },
            ],
          })(
            <Select
              placeholder="请选择"
              allowClear
              style={{ width: '100%' }}
            >
              {messageType.length
              ? messageType.map(item => (
                <Option value={`${item.value}`} key={item.value}>
                  {item.name}
                </Option>
              ))
              : null}
            </Select>
          )}
        </Form.Item>
        <Form.Item label="优先级">
          {getFieldDecorator('urgencyLevel', {
            rules: [
              {
                required: true,
                message: '请选择优先级',
              },
            ],
          })(
            <Radio.Group>
              {colorsMap.map(item => <Radio.Button value={item.value}>{item.label}</Radio.Button>)}
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="提醒时间范围">
          <Form.Item style={{ display: 'inline-block', width: '48%' }}>
            {getFieldDecorator('startTime', {
              rules: [
                {
                  required: true,
                  message: '请选择提醒开始时间',
                },
                {
                  validator: async (rule, value) => {
                    if (value && getFieldValue('endTime') && value >= getFieldValue('endTime')) {
                      return Promise.reject(new Error('开始时间应小于结束时间'));
                    }
                    return Promise.resolve();
                  },
                },
              ],
            })(
              <TimePicker style={{ width: '100%' }} />
            )}
          </Form.Item>
          <span style={{ display: 'inline-block', width: '2%', textAlign: 'center' }}>-</span>
          <Form.Item style={{ display: 'inline-block', width: '48%' }}>
            {getFieldDecorator('endTime', {
              rules: [
                {
                  required: true,
                  message: '请选择提醒结束时间',
                },
                {
                  validator: async (rule, value) => {
                    if (value && getFieldValue('startTime') && value <= getFieldValue('startTime')) {
                      return Promise.reject(new Error('结束时间应大于开始时间'));
                    }
                    return Promise.resolve();
                  },
                },
              ],
            })(
              <TimePicker style={{ width: '100%' }} />
            )}
          </Form.Item>
        </Form.Item>
        <Form.Item label="提醒方式">
          {getFieldDecorator('remindMode', {
            rules: [
              {
                required: true,
                message: '请选择提醒方式',
              },
            ],
          })(
            <Checkbox.Group options={remindModeMap} />
          )}
        </Form.Item>
        <div className={styles.subTitle}>提醒频率设置</div>
        <Form.Item label="提醒次数">
          {getFieldDecorator('freqCount', {
            rules: [
              {
                required: true,
                message: '请输入提醒次数',
              },
            ],
          })(
            <InputNumber min={0} style={{ width: '100%' }} />
          )}
        </Form.Item>
        <Form.Item label="间隔时间">
          <Form.Item style={{ display: 'inline-block', width: '80%' }}>
            {getFieldDecorator('freqInterval', {
              rules: [
                {
                  required: true,
                  message: '请输入间隔时间',
                },
              ],
            })(
              <InputNumber min={0} style={{ width: '100%' }} />
            )}
          </Form.Item>
          <Form.Item style={{ display: 'inline-block', width: '20%' }}>
            {getFieldDecorator('freqIntervalUnit', {
              rules: [
                {
                  required: true,
                  message: '请选择间隔时间单位',
                },
              ],
            })(
              <Select
                style={{ width: '100%' }}
                options={freqTypeMap}
              >
                {
                  freqTypeMap.map(item => (
                    <Option value={item.value} key={item.value}>
                      {item.label}
                    </Option>
                  ))
                }
              </Select>
            )}
          </Form.Item>
        </Form.Item>
      </Form>
      <div className={styles.btnGroup}>
        <Button type="ghost" onClick={close}>
          取消
        </Button>
        <Button type="primary" className="margin-left" onClick={submitForm}>
          确定
        </Button>
      </div>
    </div>
  );
};
export default (Form.create()(DrawerForm));
