.content {
  display: flex;

  .leftPhoto {
    display: inline-block;
    width: 390px;
    height: 520px;
    vertical-align: top;
    background-image: url(../../../img/companyCert.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .rightInfo {
    display: inline-block;
    width: calc(100% - 390px - 16px);
    margin-left: 16px;
    vertical-align: top;

    .smallCardWrap {
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      justify-content: flex-start;
      height: 100%;

      .moduleBlock {
        width: calc(33.333% - 20px);
        margin-right: 20px;
        margin-bottom: 20px;
        background-color: white;
        border: 1px solid #eee;
        cursor: pointer;

        &:hover {
          box-shadow: 0 1px 7px 0 rgba(0, 0, 0, 0.12);
        }

        .right {
          display: inline-block;
          width: 26%;
          height: 100%;
          margin-right: 9px;
          margin-left: 4px;
          text-align: center;

          .assist {
            display: inline-block;
            width: 0;
            height: 100%;
            vertical-align: middle;
          }

          .photo {
            display: inline-block;
            width: 100%;
            vertical-align: middle;
          }
        }
        .left {
          display: inline-block;
          width: calc(74% - 13px);
          height: 158px;
          padding-top: 30px;
          padding-bottom: 15px;
          padding-left: 32px;
          vertical-align: middle;

          .titleWrap {
            height: 61px;

            .title {
              color: #333;
              font-weight: 600;
              font-size: 16px;
            }
          }

          .detail {
            width: calc(100% - 32px);
            height: 52px;
          }

        }
      }


      .smallCard {
        width: calc(33.333333% - 16px);
        height: calc(33.333333% - 11px);
        margin-right: 16px;
        margin-bottom: 16px;
        background-color: white;
        border: 1px solid #eee;
        cursor: pointer;

        &:hover {
          background-image: linear-gradient(#EFF2F6, #FFF);
          box-shadow: 0 1px 7px 0 rgba(0, 0, 0, 0.08);
        }

        .right {
          display: inline-block;
          width: 25%;
          height: 100%;
          margin-right: 9px;
          margin-left: 4px;
          text-align: center;

          .assist {
            display: inline-block;
            width: 0;
            height: 100%;
            vertical-align: middle;
          }

          .photo {
            display: inline-block;
            width: 100%;
            vertical-align: middle;
          }
        }
        .left {
          display: inline-block;
          width: calc(75% - 13px);
          vertical-align: middle;

          .title {
            margin-top: 17px;
            margin-bottom: 17px;
            margin-left: 32px;
            color: #333;
            font-weight: 600;
            font-size: 16px;
          }

          .detail {
            width: calc(100% - 32px);
            margin-left: 32px;
          }

        }
      }

      .smallCard:nth-last-child(1),
      .smallCard:nth-last-child(2),
      .smallCard:nth-last-child(3){
        margin-bottom: 0;
      }
    }
  }
}
