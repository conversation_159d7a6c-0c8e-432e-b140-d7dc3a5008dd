import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Form, Button, Select, Divider, Modal, message } from 'antd';
import { useAntdTable } from '@umijs/hooks';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
import useContact from '@/hooks/useContact';
import useGetDomainData from '@/hooks/useGetDomainData';
import { buildColumns } from '@/utils/bestSolutionUtils';
import useGetMenu from '@/hooks/useGetMenu';
import { transformToArray, getItem, moreRequest } from '@/utils/utils';
import { qryRegionList, qryRoleList } from '@/services/common';
import Detail from './components/Detail';
import { queryTableData, deleteFuncRecommend } from './service';
import styles from './styles.less';

const Index = props => {
  const { form, menu } = props;
  const [regionList, setRegionList] = useState([]); // 地市列表
  const [roleList, setRoleList] = useState([]);
  const { getFieldDecorator } = form;
  const [loading, setLoading] = useState(false);
  const { subMenuList } = useGetMenu([], menu);
  const statusCdList = useGetDomainData({ busiNbr: 'FuncRecommend', propertyName: 'statusCd' });
  const user = getItem('user');
  const { userInfo: { regionLevel, postRegionId, parRegionId } } = user;

  const initData = async () => {
    const resList = await moreRequest([qryRegionList(), qryRoleList()]);

    setRegionList(resList[0]);
    setRoleList(resList[1]);
  };

  useEffect(() => {
    initData();
  }, []);

  const { tableProps, search: { submit, reset } } = useAntdTable(queryTableData, { form });
  const { pagination, ...restTableProps } = tableProps;

  const {
    openForAdd,
    openForEdit,
    openForReadOnly,
    ...restDetailProps
  } = useContact({
    closeExtra: refresh => {
      if (refresh) {
        submit();
      }
    },
  });
  const { visible: detailVisible } = restDetailProps;

  const deleteFunc = async record => {
    setLoading(true);
    const res = await deleteFuncRecommend(record?.funcRecommendId);
    if (res === true) {
      message.success('删除成功');
      submit();
    } else {
      message.error('删除失败');
    }
    setLoading(false);
  };

  const disabledBtn = regionId => {
    if (regionLevel === 1) {
      return false;
    }

    if (regionLevel === 2) {
      return String(regionId) !== String(postRegionId);
    }

    // regionLevel === 3
    return String(regionId) !== String(parRegionId);
  };

  const columns = buildColumns([
    {
      title: '选定功能',
      dataIndex: 'sourceName',
    },
    {
      title: '推荐功能',
      dataIndex: 'targetName',
    },
    {
      title: '排序',
      dataIndex: 'recommendSort',
    },
    {
      title: '角色',
      dataIndex: 'sysRoleName',
    },
    {
      title: '地市',
      dataIndex: 'regionName',
    },
    {
      title: '状态',
      dataIndex: 'statusCdName',
    },
    {
      title: '操作',
      width: 200,
      fixed: 'right',
      render: (text, record, index) => (
        <div className={styles.handle}>
          <Button
            type="link"
            onClick={() => {
              openForEdit(record, index);
            }}
            disabled={disabledBtn(record.regionId)}
          >
            编辑
          </Button>
          <Divider type="vertical" />
          <Button
            type="link"
            onClick={() => {
              Modal.confirm({
                title: '是否确定删除该功能推荐信息？',
                onOk: () => {
                  deleteFunc(record);
                },
              });
            }}
            disabled={disabledBtn(record.regionId)}
          >
            删除
          </Button>
        </div>
      ),
    },
  ]);

  return (
    <Card
      title="功能推荐管理员设置"
      className="cute"
      extra={(
        <Button type="primary" onClick={() => { openForAdd(); }}>新增</Button>
      )}
    >
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        <Row>
          <Col span={6}>
            <Form.Item label="地市">
              {getFieldDecorator('regionId')(
                <Select
                  style={{ width: '100%' }}
                  allowClear
                  placeholder="请选择"
                >
                  {Array.isArray(regionList) && regionList.map(item => (
                    <Select.Option key={item.commonRegionId} value={item.commonRegionId}>
                      {item.regionName}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="选定功能">
              {getFieldDecorator('sourceId')(
                <Select
                  style={{ width: '100%' }}
                  allowClear
                  showSearch
                  placeholder="请选择"
                  filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                >
                  {transformToArray(subMenuList).map(item => (
                    <Select.Option key={item.menuId} value={item.menuId}>
                      {item.menuName}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="状态">
              {getFieldDecorator('statusCd')(
                <Select style={{ width: '100%' }} allowClear placeholder="请选择">
                  {Array.isArray(statusCdList) && statusCdList.map(item => (
                    <Select.Option key={item.value} value={item.value}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="角色">
              {getFieldDecorator('sysRoleId')(
                <Select style={{ width: '100%' }} allowClear placeholder="请选择">
                  {Array.isArray(roleList) && roleList.map(item => (
                    <Select.Option key={item.sysRoleId} value={item.sysRoleId}>
                      {item.sysRoleName}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={6}>
            <Form.Item label="推荐功能">
              {getFieldDecorator('targetId')(
                <Select
                  style={{ width: '100%' }}
                  allowClear
                  placeholder="请选择"
                  showSearch
                  filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                >
                  {transformToArray(subMenuList).map(item => (
                    <Select.Option key={item.menuId} value={item.menuId}>
                      {item.menuName}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={6} offset={12}>
            <div style={{ textAlign: 'right' }}>
              <Button
                type="primary"
                className="margin-right"
                onClick={submit}
              >
                查询
              </Button>
              <Button type="default" onClick={reset}>
                重置
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
      <SlickTable
        rowKey={record => record.funcRecommendId}
        columns={columns}
        {...restTableProps}
        loading={loading || restTableProps?.loading}
        data={{ pagination }}
        scroll={{ x: 'max-content' }}
      />
      {
        detailVisible && (
          <Detail
            regionList={regionList}
            roleList={roleList}
            statusCdList={statusCdList}
            subMenuList={subMenuList}
            {...restDetailProps}
          />
        )
      }
    </Card>
  );
};

export default connect(({ menu }) => ({
  menu,
}))(Form.create()(Index));
