/* eslint-disable */
import React, { useState, useEffect, useRef, useLayoutEffect } from 'react';
import { connect } from 'dva';
import { Row, Col, Card, Form, Input, Icon, Button, Tooltip, Modal } from 'antd';
import styles from './styles.less';
import SystemMenu from './components/SystemMenu';
import MenuInfo from './components/MenuInfo';
import FunComponents from './components/FunComponents';
import AddMainMenu from './components/AddMainMenu';
import { undefined } from 'check-types';

const namespace = 'menuManage';

function Menu(props) {
  const {dispatch}=props;
  // 获取菜单管理的容器高度 计算出功能组件需沾满的高度
  const menuEl = useRef();
  const funCompEl = useRef();
  const systemEl = useRef();
  // 满屏高度
  const [height, setHeight] = useState('');
  // 功能组件高度
  const [funHeight, setFunHeight] = useState('');

  // modal 模态框状态
  const [visible, setVisible] = useState(false);

  const handleOk = () => {
    systemEl.current.handleAdd().then(res => {
      if (res === true) {
        setVisible(false);
      }
    });
  };
  const handleCancel = () => {
    setVisible(false);
  };
  const addModal = () => {
    setVisible(true);
  };

  // effect 监听窗口变化
  useEffect(() => {
    setHeight(props.size.height);
    setFunHeight(props.size.height - menuEl.current.clientHeight - 8);
  }, [props.size.height]);

  const addRow = () => {
    funCompEl.current.addRow();
  };

  return (
    <div className={styles.menuManage}>
      <div className={styles.menuManageLeft}>
        <Card
          className="cute"
          title="系统菜单"
          style={{ height: height }}
          extra={
            <Tooltip title="新增主目录">
              <Icon type="plus-circle" className={styles.icon_hover} onClick={addModal} />
            </Tooltip>
          }
        >
          <SystemMenu height={height} />
        </Card>

        {/* 新增主目录 */}
        <Modal
          title="新增主目录"
          visible={visible}
          onOk={handleOk}
          onCancel={handleCancel}
          width="600px"
          destroyOnClose
        >
          <AddMainMenu ref={systemEl} cRef={systemEl} />
        </Modal>
        {/* **** */}
      </div>
      <div className={styles.menuManageRight}>
        <div ref={menuEl} style={{ margin: '0 0 8px 8px' }}>
          <Card className="cute" title="菜单管理">
            <MenuInfo />
          </Card>
        </div>
        <div className={styles.funComponents} style={{ marginLeft: '8px' }}>
          <Card
            className="cute"
            title="功能组件"
            style={{ height: funHeight }}
            extra={
              <Button type="primary" onClick={addRow}>
                新增
              </Button>
            }
          >
            <FunComponents height={funHeight} ref={funCompEl} cRef={funCompEl} />
          </Card>
        </div>
      </div>
    </div>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Menu);
