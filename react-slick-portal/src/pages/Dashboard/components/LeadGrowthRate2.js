import React, { useState, useEffect } from 'react';
import { Card, Empty } from 'antd';
import request from '@/utils/request';
import { Bar, ChartCard } from '@/components/Charts';

const cache = {};

/**
 * 异步获取数据后，需要缓存起来。避免组件未销毁时重复发起请求
 */
function LeadGrowthRate2() {
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState([]);

  useEffect(() => {
    async function getInitData() {
      setLoading(true);
      // 查询增长趋势
      const response1 = await request('orgauth/WorkbenchController/qrySalesStatisticsIncrease.do', {
        method: 'get',
        data: { objType: 'LEAD' },
      });

      if (
        response1 !== null &&
        response1.resultCode === '0' &&
        response1.resultObject.data.resultCode === '0'
      ) {
        let data = [];
        const rstData = response1.resultObject.data.resultObject;
        /**
         * 预期rstData格式
         * [
         *  {
         *   createDate: "2019-08",
         *   num: 0
         *  }
         * ]
         */
        if (rstData) {
          rstData.forEach(function(rankData) {
            data.push({ x: rankData.createDate, y: rankData.num });
          });
          // 所有项的y都是0 约等于“没记录”，避免柱状图出现“一条线”
          if (data.filter(item => item.y > 0).length === 0) {
            data = [];
          }
          cache.chartData = data;
          setChartData(data);
        }
      }

      setLoading(false);
    }
    if (Object.keys(cache).length < 2) {
      getInitData();
    }
  }, []);

  let _chartData;

  if (cache.chartData === undefined) {
    _chartData = chartData;
  } else {
    _chartData = cache.chartData;
  }

  return (
    <Card bordered loading={loading}>
      <h4>线索增长趋势</h4>
      {_chartData.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无数据"
          style={{ marginTop: 120 }}
        />
      ) : (
        <Bar
          height={245}
          data={_chartData}
          tooltipFormat={[
            'x*y',
            (x, y) => ({
              name: `${x}`,
              value: `新增线索：${y}个`,
            }),
          ]}
        />
      )}
    </Card>
  );
}

export default LeadGrowthRate2;
