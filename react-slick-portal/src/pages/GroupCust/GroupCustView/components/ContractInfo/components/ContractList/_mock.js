import mockjs from 'mockjs';
import { delay } from 'roadhog-api-doc';
import defaultSettings from '@/defaultSettings';

const proxy = {
  'POST /portal/ContractController/qryContractByCustId.do': (req, res) => {
    const { body } = req;
    const  { pageSize, currentPage } = body.pageInfo;
    res.send(
    );
  },
  'POST /portal/ContractController/qryContractByContractId.do': (req, res) => {
    const { body } = req;
    const  { pageSize, currentPage } = body.pageInfo;
    res.send({"resultCode":"TRUE","resultMsg":"操作成功","resultObject":{"rspParam":{"busiInfo":{"code":"200","result":{"contractFiles":[],"contractInfo":{"textOfClause":"此合同条款正文详见附件信息","contractCheckHistory":[],"offerList":"","contractAmount":"","contractFileList":[{"fileType":"2"}]}}},"pubInfo":{"message":"success","code":"0","reqSerialNo":"2015060611223320345443"}}},"success":true}
    );
  },
  'POST /portal/ContractController/operContractByContractId.do': (req, res) => {
    const { body } = req;
    const  { pageSize, currentPage } = body.pageInfo;
    res.send(
      {"resultCode":"TRUE","resultMsg":"操作成功","resultObject":{"rspParam":{"busiInfo":{"code":"200","result":"success"},"pubInfo":{"message":"success","code":"0","reqSerialNo":"2015060611223320345443"}}},"success":true}
    );
  },
};

export default delay(proxy, defaultSettings.delay);
