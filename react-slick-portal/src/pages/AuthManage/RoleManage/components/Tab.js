import React, { useState, useEffect } from 'react';
import { Card } from 'antd';
import dynamic from 'umi/dynamic';
import LoadingComponent from '@/components/PageLoading/index';
import OutsideSystemRole from './OutsideSystemRole';

// 涉及到的标签组件按需加载
const RoleInfo = dynamic({
  loader: () => import('./RoleInfo'),
  loading: LoadingComponent,
});
const ReleJurdict = dynamic({
  loader: () => import('./ReleJurdict'),
  loading: LoadingComponent,
});
const ReleSystemUser = dynamic({
  loader: () => import('./ReleSystemUser'),
  loading: LoadingComponent,
});

const Tab = props => {
  const { isMainSystem } = props;
  const [menuKey, setMenuKey] = useState('RoleInfo');
  const [contentList, setContentList] = useState({});

  const onTabChange = key => {
    const contentMap = {
      RoleInfo: <RoleInfo />,
      ReleJurdict: <ReleJurdict />,
      ReleSystemUser: <ReleSystemUser />,
      OutsideSystemRole: <OutsideSystemRole />,
    };
    if (!contentList[key]) {
      contentList[key] = contentMap[key];
    }
    setContentList(contentList);
    setMenuKey(key);
  };

  useEffect(() => {
    onTabChange('RoleInfo');
  }, []);

  useEffect(() => {
    if (props.behavior !== 'disabled') {
      onTabChange('RoleInfo');
    }
  }, [props.behavior]);

  const getTabList = () => {
    const res = [
      {
        key: 'RoleInfo',
        tab: '角色信息',
      },
      {
        key: 'ReleJurdict',
        tab: '关联权限',
      },
      {
        key: 'ReleSystemUser',
        tab: '关联系统用户',
      },
    ];

    if (isMainSystem) {
      // res.push({
      //   key: 'OutsideSystemRole',
      //   tab: '外部系统角色',
      // });
    }

    return res;
  };

  return (
    <div>
      <Card
        className="gb_tabs_samll"
        style={{ width: '100%' }}
        tabList={getTabList()}
        activeTabKey={menuKey}
        onTabChange={key => {
          onTabChange(key);
        }}
      >
        {Object.keys(contentList).map(key => (
          <div key={key} style={{ display: menuKey === key ? 'block' : 'none' }}>
            {contentList[key]}
          </div>
        ))}
      </Card>
    </div>
  );
};

export default Tab;
