import { Col, Form, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import { ImageUploader } from '@/components/Uploader';
import ApplicationDetailComp from '../ApplicationDetailComp';
import style from './ConfigDetailComp.module.less';

/**
 * 界面配置组件
 * @param {props}} props
 * @returns
 */
const ConfigDetailComp = props => {
  const { useLibAdd, knowledgeDetailInfo } = props;

  const [bannerList, setBannerList] = useState([]);
  const [iconList, setIconList] = useState([]);

  useEffect(() => {
    const _bannerFile = knowledgeDetailInfo?.bannerFile;
    if (_bannerFile) {
      setBannerList(
        [_bannerFile].map(item => ({
          ...item,
          name: item.docName,
          uid: item.id,
          url: `portal/FileStoreController/download.do?docNbr=${item.docNbr}`,
          type: 'image/png',
        }))
      );
    } else {
      setBannerList([]);
    }
    const _iconFile = knowledgeDetailInfo?.iconFile;
    if (_iconFile) {
      setIconList(
        [_iconFile].map(item => ({
          ...item,
          name: item.docName,
          uid: item.id,
          url: `portal/FileStoreController/download.do?docNbr=${item.docNbr}`,
          type: 'image/png',
        }))
      );
    } else {
      setIconList([]);
    }
  }, [knowledgeDetailInfo]);

  return (
    <div className={style.wrap}>
      <Form labelAlign="left">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Form.Item label="详情BANNER图片">
              <ImageUploader
                uploadUrl="portal/FileStoreController/uploadFile.do"
                title="上传图片"
                maximum={1}
                uploadedFileList={bannerList}
                readonly
                dimension={[1920, 535]}
                accept={['jpg', 'png']}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="首页小图标">
              <ImageUploader
                uploadUrl="portal/FileStoreController/uploadFile.do"
                title="上传图片"
                maximum={1}
                uploadedFileList={iconList}
                readonly
                dimension={[100, 100]}
                accept={['jpg', 'png']}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="拓展信息">
              <ApplicationDetailComp useLibAdd={useLibAdd} knowledgeDetailInfo={knowledgeDetailInfo} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default ConfigDetailComp;
