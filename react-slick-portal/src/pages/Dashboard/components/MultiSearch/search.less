@import '../../../../layouts/YunNanLayout/less/variables.less';

.multiSearch{
  display: flex;
  align-items: center;
  .headerTitle{
    display: flex;
    align-items: center;
    img{
      height: 72px;
      width: 72px;
      margin-right: 6px;
    }
    .useName{
      font-weight: 550;
      font-size: 18px;
      color: #000;
      line-height: 24px;
      margin-bottom: 4px;
    }
    .userInfo{
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 13px;
      color: #666;
      line-height: 22px;
      img{
        height: 12px;
        width: 12px;
      }
    }
  }
}

.searchContainer{
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  .searchGroup{
    // position: relative;
    // margin-bottom: 32px;
    width: 50%;
  }
}

.headerSearch{
  margin: 6px auto;
  line-height: 32px;
  .searchGroup{
    margin-bottom: 0;
  }
}

.searchBox {
  height: 36px;
  border-radius: 6px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  input::placeholder {
    font-size: 14px;
    color: #23A0DE;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 6px;
    padding: 1px;
    background: linear-gradient(180deg, rgba(1, 105, 255, 1), rgba(63, 159, 255, 1), rgba(78, 149, 255, 1), rgba(153, 70, 255, 1));
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
  }

  .input{
    height: 34px;
    width: calc(100% - 2px);
    font-size: 14px;
    border-radius: 6px;
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    background-image: linear-gradient(to right, #D1F9FF, #F6FFFF, #F5FDFF);
    transition: all 0.3s ease;
  }

  .sendBtn{
    width: 40px;
    height: 26px;
    cursor: pointer;
  }

  :global{
    .ant-input-affix-wrapper {
      .ant-input:not(:last-child) {
        padding-right: 50px;
      }
      .ant-input-suffix{
        right: 5px;
        svg{
          margin-right: 6px;
        }
      }
    }
    .ant-input-affix-wrapper.ant-input-affix-wrapper-input-with-clear-btn .ant-input:not(:last-child){
      padding-right: 70px;
    }
    .ant-input{
      border: none;
      background-color: transparent;
      // box-shadow: inset 30px 0px 34px 0px rgba(35, 160, 222, 0.2);
      &:focus{
        border: none;
        box-shadow: none;
      }
    }
  }
}

.quickMenu{
  margin-top: 4px;
  display: flex;
  position: absolute;

  .quickMenuItem{
    margin-right: 20px;
    font-weight: 400;
    font-size: 13px;
    color: #666;
    line-height: 22px;
    cursor: default;
    &:last-child{
      margin-right: 0;
    }
  }
}

.dropdownBox {
  position: absolute;
  max-height: 286px;
  margin-top: 8px;
  padding: 0 12px 12px;
  overflow: hidden;
  font-size: 13px;
  background: #FFF;
  box-shadow: 0px 2px 8px 0px rgba(0,28,56,0.12);
  border-radius: 6px;
  border: 1px solid #E6E6E6;

  :global {
    .ant-tabs-nav-container{
      font-size: 14px;
    }
    .ant-tabs-bar{
      margin-bottom: 8px;
    }
  }

  .tabsContent{
    height: 200px;
    overflow: hidden auto;

    .menuContent{
      column-gap: 24px;
      column-count: 3;
      margin-right: 16px;

      .subMenuName{
        display: flex;
        align-items: center;
        padding: 0 8px 4px;
        font-weight: 550;
        font-size: 14px;
        color: #333;
        line-height: 18px;
        img{
          width: 16px;
          height: 16px;
          margin-right: 10px;
        }
      }
      .menuName{
        display: block;
        width: 100%;
        padding: 8px;
        font-weight: 400;
        font-size: 14px;
        color: #333;
        line-height: 18px;
        cursor: pointer;
        &:hover{
          background: rgba(0,0,0,0.04);
          border-radius: 2px;
        }
      }

    }

    .resultItem{
      display: block;
      width: 98%;
      padding: 8px;
      font-weight: 400;
      font-size: 14px;
      color: #333;
      line-height: 18px;
      cursor: pointer;
      &:hover{
        background: rgba(0,0,0,0.04);
        border-radius: 2px;
      }
    }

    .redWord{
      color: #FC3D34;
    }
  }
}
