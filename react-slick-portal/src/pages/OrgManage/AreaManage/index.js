import React, { useState, useEffect, useRef, useLayoutEffect } from 'react';
import { connect } from 'dva';
import { Row, Col, Card, Form, Input, Icon, Button, Tooltip, Modal } from 'antd';
import styles from './styles.less';
import AreaMenu from './components/AreaMenu';
import AreaList from './components/AreaList';
import AreaTab from './components/AreaTab';

const namespace = 'areaManage';

function AreaManage(props) {
  // 获取菜单管理的容器高度 计算出功能组件需沾满的高度
  const menuEl = useRef();
  // //满屏高度
  const [height, setHeight] = useState('');
  // // 功能组件高度
  const [tabHeight, setTabHeight] = useState('');

  const reset = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/reset`,
      payload: params,
    });
  };

  useEffect(() => {
    return () => {
      reset();
    };
  }, []);

  // effect 监听窗口变化
  useEffect(() => {
    setHeight(props.size.height);
    setTabHeight(props.size.height - menuEl.current.clientHeight - 8);
  }, [props.size.height]);

  return (
    <div className={styles.areaManage}>
      <div className={styles.areaManageLeft}>
        <Card className="cute" title="区域一览" style={{ height: height }}>
          <AreaMenu height={height} />
        </Card>
      </div>

      <div className={styles.areaManageRight}>
        <div ref={menuEl} style={{ margin: '0 0 8px 8px' }}>
          <AreaList />
        </div>
        <div className={styles.areaTab} style={{ marginLeft: '8px' }}>
          <AreaTab height={tabHeight} behavior={props.behavior} />
        </div>
      </div>
    </div>
  );
}

export default connect(({ setting, areaManage }) => ({
  behavior: areaManage.behavior,
  size: setting.size,
}))(AreaManage);
