import dynamic from 'umi/dynamic';
import PageLoading from '@/components/PageLoading';

export const tabs = [
  {
    name: '价值评估问卷',
    key: 'BaseInfo',
    icon: 'icon-jichuxinxi',
    component: dynamic({
      loader: () => import('./components/BaseInfo'),
      loading: PageLoading,
    }),
  },
  {
    name: '我的走访',
    key: 'Members',
    icon: 'icon-jichuxinxi',
    component: dynamic({
      loader: () => import('./components/BaseInfo'),
      loading: PageLoading,
    }),
  },
];
