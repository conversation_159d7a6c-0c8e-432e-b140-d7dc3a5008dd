import request from '@/utils/request';

// 合同列表
export function getList(data) {
  return request('portal/ContractController/queryAllContractInfo.do', {
    method: 'POST',
    data,
  });
}

// 合同详情
export function getDetail(contractId) {
  return request('portal/ContractController/qryContractByContractId.do', {
    method: 'POST',
    data: {
      data: {
        contractId,
      },
    },
  });
}

// 合同新增
export function addContract(data) {
  return request('portal/ContractController/operContractByContractId.do', {
    method: 'POST',
    data: {
      ...data,
    },
  });
}

// 合同修改
export function modContract(data) {
  return request('portal/ContractController/operContractByContractId.do', {
    method: 'POST',
    data: {
      ...data,
    },
  });
}

// 合同删除
export function delContract(data) {
  return request('portal/ContractController/operContractByContractId.do', {
    method: 'POST',
    data: {
      data: {
        action: '3',
        ...data,
      },
    },
  });
}

// 合同归档
export function pigContract(data) {
  return request('portal/ContractController/operContractByContractId.do', {
    method: 'POST',
    data: {
      data: {
        action: '4',
        ...data,
      },
    },
  });
}
