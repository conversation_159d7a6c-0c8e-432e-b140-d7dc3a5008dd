import React, { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Form, Input, Button, Select, DatePicker, Row, Col } from 'antd';
import moment from 'moment';
import { useAntdTable } from '@umijs/hooks';
import request from '@/utils/request';
import SlickTable from '@/components/SlickTable';
import { parseQuery } from '@/utils/utils';
import CommonHeader from '@/pages/Dashboard/components/CommonHeader';
import styles from './index.less';

const { Option } = Select;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const QueryTable = React.memo(props => {
  const {
    pageSize,
    form,
    formConfig = [],
    columns = [],
    requestUrl,
    cardTitle,
    size: { height },
  } = props;

  const { getFieldDecorator, validateFields, setFieldsValue } = form;

  const [formParams, setFormParams] = useState({});

  useEffect(() => {
    const urlParams = parseQuery(window.location.href);
    if (urlParams) {
      const obj = {};
      Object.keys(urlParams).forEach(key => {
        obj[key] = urlParams[key];
      });
      setFormParams(obj);
      setFieldsValue(obj);
    }
  }, [window.location.href]);

  const getTableData = ({ current }) => request(requestUrl, {
    data: {
      page: current,
      pageNum: current,
      pageSize,
      anyProperties: {},
      ...formParams,
    },
  }).then(result => {
    if (result?.list?.length > 0) {
      return {
        total: result.total,
        data: result.list,
      };
    }
    return {
      total: 0,
      data: [],
    };
  });

  const { tableProps, refresh } = useAntdTable(
    query => getTableData(query),
    // 依赖变化会触发请求
    [formParams, height],
    {
      defaultPageSize: pageSize,
    }
  );

  const { pagination, ...restTableProps } = tableProps;

  useEffect(() => {
    refresh();
  }, [requestUrl]);

  const handleReset = () => {
    setFormParams({});
    form.resetFields();
  };

  useEffect(() => {
    handleReset();
  }, [props.location]);

  const handleSubmit = () => {
    validateFields((err, values) => {
      if (!err) {
        const filterValue = {};
        formConfig.forEach(item => {
          if (item.type === 'rangePicker') {
            const [startKey, endKey] = item.paramKey;
            if (values[item.key]?.length > 0) {
              const time = values[item.key];
              const startDate = moment(time[0]).format(item.timeFormat);
              const endDate = moment(time[1]).format(item.timeFormat);
              filterValue[startKey] = startDate;
              filterValue[endKey] = endDate;
            } else {
              filterValue[startKey] = undefined;
              filterValue[endKey] = undefined;
            }
          } else {
            filterValue[item.key] = values[item.key] ? values[item.key] : undefined;
          }
        });
        setFormParams(filterValue);
      }
    });
  };

  const renderFormItem = item => {
    switch (item.type) {
      case 'input':
        return <Input placeholder={item.placeholder} allowClear />;
      case 'select':
        return (
          <Select placeholder={item.placeholder} allowClear>
            {(item.options || []).map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
      case 'rangePicker':
        return (
          <DatePicker.RangePicker
            allowClear
            placeholder={item.placeholder}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className={styles.queryTable} style={{ height }}>
      {
        formConfig.length ? (
          <Form id="formCard" className={styles.formCard}>
            <Row type="flex">
              {
                formConfig.map(item => (
                  <Col span={8} key={item.key}>
                    <Form.Item label={item?.name} {...formItemLayout}>
                      {getFieldDecorator(item.key)(renderFormItem(item))}
                    </Form.Item>
                  </Col>
                ))
              }
              <Col span={(3 - (formConfig.length % 3)) * 8} style={{ textAlign: 'right' }}>
                <Form.Item>
                  <Button onClick={handleReset} style={{ marginRight: 8 }}>
                    重置
                  </Button>
                  <Button type="primary" onClick={handleSubmit}>
                    查询
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        ) : null
      }
      <div className={styles.tableCard}>
        {
          cardTitle ? (
            <CommonHeader
              title={cardTitle}
              isFlag
              style={{ marginTop: 0, marginBottom: 16 }}
            />
          ) : null
        }
        <SlickTable
          rowKey={record => record.id}
          columns={columns}
          {...restTableProps}
          data={{
            pagination: {
              ...pagination,
              pageSize,
            },
          }}
          bordered={false}
          loading={tableProps?.loading}
          scroll={{ x: columns.length * 200 }}
        />
      </div>
    </div>
  );
});
export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(QueryTable));
