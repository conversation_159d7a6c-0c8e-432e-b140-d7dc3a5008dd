import React, { useState, useEffect, useRef } from 'react';
import { Popover, Form, Row, Col, Select, Skeleton, Icon, Input, Button, Popconfirm, DatePicker, message, Drawer, Modal } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import findIndex from 'lodash/findIndex';
import isPlainObject from 'lodash/isPlainObject';
import SlickUpload from '@/components/SlickUpload';
import request from '@/utils/request';
import ComboGrid from '@/components/ComboGrid';
import DraftBox from './DraftBox';
// '1000':所有人 '1100':组织; '1200':用户; '1300':角色； 1400: 区域
function reandeRcvObjNames(rcvType, names, renderOptions, getFieldDecorator, disabled) {
  if (rcvType === undefined) {
    return '请先选择对象类型';
  }
  if (rcvType === '1000') {
    return '所有人';
  }

  if (rcvType === '1100') {
    return getFieldDecorator(
      'rcvObjNames',
      renderOptions({
        initialValue: names,
        rules: [{ required: true, message: '请选择' }],
      })
    )(
      <ComboGrid
        multipleFlag
        disabled={disabled}
        // url="orgauth/OrganizationController/qryOperOrgGridData.do"
        url="orgauth/CommonRegionController/qryOperRegionGridData.do"
        popupStyle={{ width: 600 }}
        placeholder="请选择"
        searchPlaceholder="区域名称类型"
        destroyPopupOnHide
        label="regionName"
        rowKey="commonRegionId"
        columns={[
          {
            title: '区域名称',
            dataIndex: 'regionName',
            ellipsis: true,
          },
          {
            title: '区域编码',
            dataIndex: 'regionNbr',
            ellipsis: true,
          },
          // {
          //   title: '组织类型',
          //   dataIndex: 'orgTypeName',
          // },
        ]}
      />
    );
  }
  if (rcvType === '1200') {
    return getFieldDecorator(
      'rcvObjNames',
      renderOptions({
        initialValue: names,
        rules: [{ required: true, message: '请选择' }],
      })
    )(
      <ComboGrid
        multipleFlag
        disabled={disabled}
        url="orgauth/SystemUserController/selectUserGridData.do"
        popupStyle={{ width: 600 }}
        placeholder="请选择"
        searchPlaceholder="用户名称/账号"
        destroyPopupOnHide
        label="userName"
        rowKey="sysUserId"
        columns={[
          {
            title: '系统用户标识',
            dataIndex: 'sysUserId',
            ellipsis: true,
          },
          {
            title: '账号',
            dataIndex: 'sysUserCode',
            ellipsis: true,
          },
          {
            title: '名称',
            width: 120,
            ellipsis: true,
            dataIndex: 'userName',
          },
          {
            title: '4A账号',
            width: 120,
            ellipsis: true,
            dataIndex: 'loginCode',
          },
        ]}
      />
    );
  }
  if (rcvType === '1300') {
    return getFieldDecorator(
      'rcvObjNames',
      renderOptions({
        initialValue: names,
        rules: [{ required: true, message: '请选择' }],
      })
    )(
      <ComboGrid
        multipleFlag
        disabled={disabled}
        url="orgauth/SystemRolesController/selectSystemRolesGridData.do"
        popupStyle={{ width: 600 }}
        placeholder="请选择"
        searchPlaceholder="角色名称/编码"
        destroyPopupOnHide
        label="sysRoleName"
        rowKey="sysRoleId"
        columns={[
          {
            title: '角色名称',
            dataIndex: 'sysRoleName',
            ellipsis: true,
          },
          {
            title: '角色编码',
            dataIndex: 'sysRoleCode',
            ellipsis: true,
          },
          {
            title: '角色类型',
            width: 120,
            ellipsis: true,
            dataIndex: 'sysRoleTypeName',
          },
          {
            title: '归属系统',
            width: 120,
            ellipsis: true,
            dataIndex: 'sysCodeName',
          },
        ]}
      />
    );
  }
  return null;
}

/**
 *
 * @param {string} mode 可选值：'add','view','edit'
 */
function DetailDrawer({
  dispatch,
  loading,
  mode = 'view',
  close,
  detail = {},
  form: { validateFields, getFieldDecorator, setFieldsValue },
  user: {
    userInfo: { userId },
  },
  initData: { isTopMap, bulletinTypeMap, bulletinlevelMap, rcvTypeMap, rcvObjNames, fileList },
  refresh,
}) {
  const [pubLoading, setPubLoading] = useState(false);
  const [draftLoading, setDraftLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [formValues, setFormValues] = useState(detail);
  const [viewMode, setViewMode] = useState(mode);
  const disabled = viewMode === 'view';
  const [realTimeDisabled, setRealTimeDisabled] = useState(disabled);
  const [rcvType, setRcvType] = useState(formValues.rcvType);
  const [names, setNames] = useState(rcvObjNames);

  // 获取码表数据，防止重复请求
  useEffect(() => {
    if (rcvTypeMap.length === 0) {
      dispatch({
        type: 'bulletin/getMap',
      });
    }
  }, [dispatch, rcvTypeMap.length]);

  /**
   * 1、获取接收对象和附件数据
   * 2、视图销毁时清除数据，保证每次渲染
   */
  useEffect(() => {
    if (viewMode === 'edit' || viewMode === 'view') {
      dispatch({
        type: 'bulletin/getFileListAndRcvObjNames',
        payload: { detail },
      });
    }
    return () => {
      dispatch({
        type: 'bulletin/clean',
      });
    };
  }, [detail, dispatch, viewMode]);

  // rcvObjNames值是异步取到的，需要即时更新
  useEffect(() => {
    setFieldsValue({ rcvObjNames });
  }, [setFieldsValue, rcvObjNames]);

  function handleSubmit(e, isDraft = '0') {
    e.preventDefault();

    validateFields((err, values) => {
      if (err) {
        return;
      }
      const { beginAndEnd, objDocRels, ...rest } = values;
      const { rcvObjNames: newRcvObjNames } = rest;
      const effDate = moment(values.beginAndEnd[0]).format('YYYY-MM-DD HH:mm:ss');
      const expDate = moment(values.beginAndEnd[1]).format('YYYY-MM-DD HH:mm:ss');

      if (rest.createDate) {
        rest.createDate = moment(rest.createDate).format('YYYY-MM-DD HH:mm:ss');
      }

      // 从newRcvObjNames中提取rcvObjNames和bulletinRcvObjRels（如果rcvType没改变，则bulletinRcvObjRels只能保留增量的部分）
      // newRcvObjNames 是一个对象数组，不同的rcvType里面每个对象包含的key不一样
      // rcvObjNames 是有当前newRcvObjNames中的name构成的。不同的rcvType提取的name所在的key不一样
      // '1000':所有人 '1100':区域; '1200':用户; '1300':角色

      if (rcvType === '1000') {
        rest.rcvObjNames = '所有人';
        rest.bulletinRcvObjRels = [
          {
            rcvType: 1000,
            bulletinId: formValues.bulletinId || '',
            action: 'A',
            isAuto: 0,
          },
        ];
      } else {
        const map = {
          1100: {
            name: 'regionName',
            id: 'commonRegionId',
          },
          1200: {
            name: 'userName',
            id: 'sysUserId',
          },
          1300: {
            name: 'sysRoleName',
            id: 'sysRoleId',
          },
        };
        rest.rcvObjNames = newRcvObjNames.map(item => item[map[`${rcvType}`].name]).join(',');
        // 如果rcvType没改变，则bulletinRcvObjRels只能保留增量的部分
        // IMPROVE: 如果提交的rcvType和bulletinRcvObjRels包含的不一致 会导致所有公告数据被清空。这是接口服务bug
        if (detail.rcvType === rest.rcvType) {
          rest.bulletinRcvObjRels = newRcvObjNames
            .filter(item => findIndex(rcvObjNames, { orgId: item[map[`${rcvType}`].id] }) === -1)
            .map(item => {
              return {
                rcvId: item[map[`${rcvType}`].id],
                rcvType,
                bulletinId: formValues.bulletinId || '',
                action: 'A',
                isAuto: 0,
              };
            });
        } else {
          rest.bulletinRcvObjRels = newRcvObjNames.map(item => {
            return {
              rcvId: item[map[`${rcvType}`].id],
              rcvType,
              bulletinId: formValues.bulletinId || '',
              action: 'A',
              isAuto: 0,
            };
          });
        }
      }
      if (isDraft === '0') {
        setPubLoading(true);
      } else if (isDraft === '1') {
        setDraftLoading(true);
      }
      const url = viewMode === 'add' ? 'orgauth/BulletinController/createBulletin.do' : 'orgauth/BulletinController/createBulletin.do';
      const method = viewMode === 'add' ? 'post' : 'post';
      request(url, {
        method,
        data: {
          // bulletinId: formValues.bulletinId || '',
          bulletinId: '',
          prevBulletinId: formValues.bulletinId || '',
          effDate,
          expDate,
          objDocRels: objDocRels.map(val => {
            return { docId: val.docId };
          }),
          ...rest,
          updateDate: moment().format('YYYY-MM-DD HH:mm:ss'),
          isDraft, // '1' 表示保存为草稿；'0'表示正常发布
        },
      })
        .then(res => {
          if (isPlainObject(res)) {
            if (isDraft === '1') {
              message.success('保存成功！');
            } else {
              message.success('发布成功！');
              // 关闭面板
              close();
            }
            if (isDraft === '0') {
              setIsSuccess(true);
            }
            refresh();
          } else {
            message.error('发布失败！');
          }
        })
        .always(() => {
          if (isDraft === '0') {
            setPubLoading(false);
          } else if (isDraft === '1') {
            setDraftLoading(false);
          }
        });
    });
  }

  // 创建时，若使用草稿功能， 需要自动导入初始值
  function renderOptions(options) {
    const { initialValue, ...rest } = options;
    return viewMode === 'add' ? rest : options;
  }

  /**
   * 1、通过设置rcvType值，触发reandeRcvObjNames() 渲染RcvObjNames选项的视图
   * 2、重置接收对象字段的值
   * 3、“创建”模式下，如果接收对象为“所有人”，则禁用“实时选项”，否则开启
   * @param {string} val '1000':所有人 '1100':组织; '1200':用户; '1300':角色； 1400: 区域
   */
  function handleRcvType(val) {
    setRcvType(val);
    setFieldsValue({ rcvObjNames: [] });
    // setNames([]);
    // 只有新增模式下 才存在实时选项
    if (viewMode === 'add') {
      setFieldsValue({ realTime: 'false' });
      // 所有人时，禁用“实时”
      setRealTimeDisabled(val === '1000');
    }
  }

  function renderFooter(viewMode) {
    if (viewMode === 'add') {
      return (
        <>
          <Popover
            destroyPopupOnHide
            content={
              <DraftBox
                onSelectRow={selectedRows => {
                  if (selectedRows.length > 0) {
                    const { bulletinTitle, bulletinContent, isTop, bulletinLevel, bulletinType, remark } = selectedRows[0];

                    // 一定要设置当前form中存在的字段，否则会有警告
                    setFieldsValue({
                      bulletinTitle,
                      bulletinContent,
                      isTop,
                      bulletinLevel,
                      bulletinType,
                      remark,
                    });
                    /**
                     * IMPROVE 无法回填附件和接收对象
                     * 因为草稿表格的行数据中没有保存对应的rcvType
                     */
                    // dispatch({
                    //   type: 'bulletin/getFileListAndRcvObjNames',
                    //   payload: { detail: selectedRows[0] },
                    // });
                  }
                }}
              />
            }
            placement="topLeft"
            title={
              <>
                <Icon type="info-circle" className="text-info margin-right-sm" />
                选中一份草稿，快速回填。
              </>
            }
            trigger="click"
            getPopupContainer={trigger => trigger.parentNode}
          >
            <Button className="pull-left" icon="inbox" type="primary" ghost>
              草稿箱
            </Button>
          </Popover>
          <Button onClick={close} className="margin-right">
            取消
          </Button>
          <Button
            className="margin-right"
            onClick={e => {
              handleSubmit(e, '1');
            }}
            type="primary"
            ghost
            loading={draftLoading}
          >
            保存草稿
          </Button>
          <Button onClick={handleSubmit} type="primary" loading={pubLoading} disabled={isSuccess}>
            {isSuccess ? '发布成功' : '发布'}
          </Button>
        </>
      );
    }

    if (viewMode === 'view') {
      return (
        <>
          <Button onClick={close} className="margin-right">
            取消
          </Button>
          <Button className="margin-right" onClick={() => setViewMode('edit')} type="primary">
            编辑
          </Button>
        </>
      );
    }
    if (viewMode === 'edit') {
      return (
        <>
          <Button onClick={close} className="margin-right">
            取消
          </Button>
          <Button onClick={handleSubmit} type="primary" loading={pubLoading} disabled={isSuccess}>
            {isSuccess ? '发布成功' : '发布'}
          </Button>
        </>
      );
    }
    return null;
  }

  return (
    <Skeleton active paragraph={{ rows: 10 }} loading={loading}>
      <Form layout="vertical" onSubmit={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="公告标题">
              {getFieldDecorator(
                'bulletinTitle',
                renderOptions({
                  initialValue: formValues.bulletinTitle,
                  rules: [{ required: true, message: '请输入' }],
                })
              )(<Input allowClear placeholder="请输入" disabled={disabled} />)}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="是否置顶">
              {getFieldDecorator('isTop', {
                initialValue: viewMode === 'add' ? '0' : `${formValues.isTop}`,
                normalize: val => {
                  if (val !== undefined) {
                    return val.toString();
                  }
                  return '';
                },
                rules: [{ required: true, message: '请选择' }],
              })(
                <Select allowClear placeholder="请选择" disabled={disabled}>
                  {isTopMap.map(item => (
                    <Select.Option key={item.value}>{item.name}</Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="公告等级">
              {getFieldDecorator('bulletinLevel', {
                initialValue: viewMode === 'add' ? '1200' : `${formValues.bulletinLevel}`,
                rules: [{ required: true, message: '请选择' }],
              })(
                <Select allowClear placeholder="请选择" disabled={disabled}>
                  {bulletinlevelMap.map(item => (
                    <Select.Option key={item.value}>{item.name}</Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="公告类型">
              {getFieldDecorator(
                'bulletinType',
                renderOptions({
                  initialValue: formValues.bulletinType,
                  rules: [{ required: true, message: '请选择' }],
                })
              )(
                <Select allowClear placeholder="请选择" disabled={disabled}>
                  {bulletinTypeMap.map(item => (
                    <Select.Option key={item.value}>{item.name}</Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="接收对象类型">
              {getFieldDecorator(
                'rcvType',
                renderOptions({
                  initialValue: formValues.rcvType,
                  rules: [{ required: true, message: '请选择' }],
                })
              )(
                <Select
                  placeholder="请选择"
                  allowClear
                  disabled={disabled}
                  onChange={val => {
                    handleRcvType(val);
                  }}
                >
                  {rcvTypeMap.map(item => (
                    <Select.Option key={item.value}>{item.name}</Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="接收对象">{reandeRcvObjNames(rcvType, names, renderOptions, getFieldDecorator, disabled)}</Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="生失效时间">
              {getFieldDecorator('beginAndEnd', {
                initialValue: [
                  viewMode === 'add' ? moment() : moment(formValues.effDate, 'YYYY-MM-DD HH:mm:ss'),
                  viewMode === 'add' ? moment().add(30, 'years') : moment(formValues.expDate, 'YYYY-MM-DD HH:mm:ss'),
                ],
                rules: [{ required: true, message: '请选择' }],
              })(
                <DatePicker.RangePicker
                  disabled={disabled}
                  showTime
                  style={{ width: '100%' }}
                  getPopupContainer={trigger => trigger.parentNode}
                  disabledDate={current => {
                    // 新增模式 禁选今天之前的
                    if (viewMode === 'add') {
                      return current && current < moment().add(-1, 'days');
                    }
                    // 编辑或查看模式 禁选生效日之前的
                    return current && current < moment(formValues.effDate);
                  }}
                />
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="备注信息">
              {getFieldDecorator(
                'remark',
                renderOptions({
                  initialValue: formValues.remark,
                })
              )(<Input allowClear placeholder="请输入" disabled={disabled} />)}
            </Form.Item>
          </Col>
        </Row>
        {viewMode === 'add' && (
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="是否实时">
                {getFieldDecorator('realTime', {
                  initialValue: 'false',
                })(
                  <Select placeholder="请选择" disabled={realTimeDisabled} allowClear>
                    <Select.Option key="true">是</Select.Option>
                    <Select.Option key="false">否</Select.Option>
                  </Select>
                )}
              </Form.Item>
            </Col>
            {/* <Col span={12}>
              <Form.Item label="使用草稿">
                {getFieldDecorator('isDraft')(
                  <ComboGrid
                    pick="radio"
                    url="orgauth/BulletinController/selectBulletin4DraftGridData.do"
                    params={{ launchStaffId: userId }}
                    popupStyle={{ width: 600 }}
                    placeholder="请选择"
                    searchPlaceholder="标题"
                    destroyPopupOnHide
                    label="bulletinTitle"
                    rowKey="bulletinId"
                    columns={[
                      {
                        title: '公告标题',
                        dataIndex: 'bulletinTitle',
                        ellipsis: true,
                      },
                      {
                        title: '公告内容',
                        dataIndex: 'bulletinContent',
                        ellipsis: true,
                      },
                    ]}
                    onConfirm={selectedRows => {
                      if (selectedRows.length > 0) {
                        const {
                          bulletinTitle,
                          bulletinContent,
                          isTop,
                          bulletinLevel,
                          bulletinType,
                          remark,
                        } = selectedRows[0];

                        // 一定要设置当前form中存在的字段，否则会有警告
                        setFieldsValue({
                          bulletinTitle,
                          bulletinContent,
                          isTop,
                          bulletinLevel,
                          bulletinType,
                          remark,
                        });
                      }
                    }}
                  />
                )}
              </Form.Item>
            </Col> */}
          </Row>
        )}
        {viewMode !== 'add' && (
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="创建时间">
                {getFieldDecorator('createDate', {
                  initialValue: moment(formValues.createDate, 'YYYY-MM-DD HH:mm:ss'),
                })(<DatePicker style={{ width: '100%' }} mode="time" showTime disabled getPopupContainer={trigger => trigger.parentNode} />)}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="上次修改时间">
                {getFieldDecorator('updateDate', {
                  initialValue: moment(formValues.updateDate, 'YYYY-MM-DD HH:mm:ss'),
                })(<DatePicker style={{ width: '100%' }} disabled showTime getPopupContainer={trigger => trigger.parentNode} />)}
              </Form.Item>
            </Col>
          </Row>
        )}
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="公告内容">
              {getFieldDecorator(
                'bulletinContent',
                renderOptions({
                  initialValue: formValues.bulletinContent,
                  rules: [
                    {
                      required: true,
                      message: '请输入',
                    },
                  ],
                })
              )(<Input.TextArea allowClear rows={4} placeholder="请输入" disabled={disabled} />)}
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="附件">
              {getFieldDecorator('objDocRels', {
                initialValue: fileList,
                valuePropName: 'fileList',
              })(<SlickUpload multiple disabled={viewMode === 'view'} />)}
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <div
        style={{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
        }}
      >
        {renderFooter(viewMode)}
      </div>
    </Skeleton>
  );
}

export default connect(({ setting, bulletin, login, loading }) => ({
  size: setting.size,
  user: login.user,
  initData: bulletin,
  loading: loading.effects['bulletin/getMap'],
}))(Form.create()(DetailDrawer));
