/* eslint-disable import/no-dynamic-require */
import React, { useEffect } from 'react';
import { connect } from 'dva';
import { Empty, Modal, Tooltip, message } from 'antd';
import { openMenu } from '@/utils/utils';
import ConfigManage from './ConfigManage';
import setting from '../../img/customNavigation/setting.png';
import CommonHeader from '../CommonHeader';
import styles from './styles.less';

const namespace = 'customNavigation';
const { innerWidth } = window;
// const universalPageSize = 5; // 通用导航每页展示五组
// const scenePageSize = 5; // 场景专区只展示六个

const CustomNavigation = props => {
  const { allMenu, customNavigation = {}, submitLoading, dispatch } = props;
  const { configVisible, universalData } = customNavigation;

  const initData = () => {
    dispatch({
      type: `${namespace}/initCustomConfig`,
    });
  };

  useEffect(() => {
    initData();
  }, []);

  useEffect(() => {
    dispatch({
      type: 'dashboard/getSystemLink',
    });
    dispatch({
      type: 'dashboard/getAllSystemLink',
    });
  }, []);

  const saveVisible = visible => {
    dispatch({
      type: `${namespace}/toggleVisible`,
      payload: visible,
    });
  };

  // 菜单跳转
  const handleMenuOpen = menu => {
    openMenu(menu, allMenu, dispatch);
  };

  const handleSubmit = () => {
    const { universalTempData } = customNavigation;
    let isSuccess = true;
    universalTempData.forEach(item => {
      const { belongName, list } = item;
      if (!belongName) {
        isSuccess = false;
        message.error('存在组名未填的分组，请检查。');
      } else if (list.length === 0) {
        isSuccess = false;
        message.error(`组【${belongName}】未选择菜单，请选择。`);
      }
    });
    if (isSuccess) {
      dispatch({
        type: `${namespace}/saveCustomConfig`,
      }).then(() => {
        saveVisible(false);
      });
    }
  };

  return (
    <>
      <CommonHeader
        title="360全景导航"
        isFlag
        extra={(
          <Tooltip title="自定义">
            <img src={setting} height={20} width={20} alt="" onClick={() => saveVisible(true)} style={{ cursor: 'pointer' }} />
          </Tooltip>
          )}
      />
      {
        universalData.length ? (
          // <div className={styles.universalContainer}>
          <div className={styles.groups}>
            {
              universalData.map((group, index) => (
                <div key={group.belongCode} className={styles.groupItem}>
                  <div className={styles.groupName}>
                    <img src={require(`@/pages/Dashboard/img/nmgIcon/nav${index % 7}.png`)} alt="" />
                    <span>{group.belongName}</span>
                  </div>
                  <div className={styles.menuList}>
                    {
                      (group?.list || []).map(item => (
                        <div
                          key={item.menuCode}
                          className={styles.menuItem}
                          title={item.menuName}
                          onClick={() => handleMenuOpen(item)}
                        >
                          {item.menuName}
                        </div>
                      ))
                    }
                  </div>
                </div>
              ))
            }
          </div>
          // </div>
        ) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      }
      <Modal
        title="自定义全景导航"
        visible={configVisible}
        onCancel={() => saveVisible(false)}
        onOk={handleSubmit}
        okButtonProps={{ loading: submitLoading }}
        destroyOnClose
        mask={false}
        maskClosable={false}
        bodyStyle={{ padding: 20 }}
        width={innerWidth * 0.66}
      >
        <ConfigManage />
      </Modal>
    </>
  );
};
export default connect(({ menu, customNavigation, loading }) => ({
  allMenu: menu.all,
  customNavigation,
  initLoading: loading.effects['customNavigation/initCustomConfig'],
  submitLoading: loading.effects['customNavigation/saveCustomConfig'],
}))(CustomNavigation);
