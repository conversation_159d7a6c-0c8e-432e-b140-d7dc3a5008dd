import { delay } from 'roadhog-api-doc';
import defaultSettings from '@/defaultSettings';

const proxy = {
  'POST /portal/GroupEnterpriseController/qryGroupEnterpriseManagerByGroupId.do': (req, res) => {
    const {
      body: { operType },
    } = req;
    let result = {};
    res.status(200).send({"resultCode":"TRUE","resultMsg":"操作成功","resultObject":{"rspParam":{"busiInfo":{"outData":[{"REL_TYPE":"1","DEPT_ID":"","PROVIDER_TYPE":"1","OPERATOR_ID":"1071","BILL_ID":"15104736691","RELA_ID":"20005392644","MGMT_DISTRICT":"473","CUST_MGR_ID":"1071","EMAIL":"<EMAIL>","STAFF_NAME":"测试31","MGMT_COUNTY":"7303","GROUP_ID":"471710052745"},{"REL_TYPE":"2","DEPT_ID":"","PROVIDER_TYPE":"1","OPERATOR_ID":"71020032","BILL_ID":"15921811924","RELA_ID":"20005394547","MGMT_DISTRICT":"471","CUST_MGR_ID":"10010205","EMAIL":"<EMAIL>","STAFF_NAME":"陈曦","MGMT_COUNTY":"4000","GROUP_ID":"471710052745"}]},"pubInfo":{"message":"success","code":"0","reqSerialNo":"2015060611223320345443"}}},"success":true});
  },
  'POST /portal/GroupEnterpriseController/queryHistoryGroupManagerAllByGroupId.do': (req, res) => {
    res.status(200).send({"resultCode":"TRUE","resultMsg":"操作成功","resultObject":{"rspParam":{"busiInfo":{"outData":[{"REL_TYPE":"2","DEPT_ID":"","PROVIDER_TYPE":"1","VALID_DATE":"2024-03-28 17:53:07.0","ROW_INDEX":"1","DATA_STATUS_1":"1","DATA_STATUS_2":"1","EMAIL":"<EMAIL>","MGMT_COUNTY":"7303","GROUP_ID":"471710052548","OPERATOR_ID":"1071","BILL_ID":"15104736691","RELA_ID":"20005394170","MGMT_DISTRICT":"473","CUST_MGR_ID":"1071","STAFF_NAME":"测试31"},{"REL_TYPE":"2","DEPT_ID":"","PROVIDER_TYPE":"1","VALID_DATE":"2024-08-16 08:57:45.0","ROW_INDEX":"2","DATA_STATUS_1":"1","DATA_STATUS_2":"0","EMAIL":"<EMAIL>","MGMT_COUNTY":"4000","GROUP_ID":"471710052548","OPERATOR_ID":"71010315","BILL_ID":"13754090990","RELA_ID":"20005394273","MGMT_DISTRICT":"471","CUST_MGR_ID":"71010315","STAFF_NAME":"李惠"},{"REL_TYPE":"2","DEPT_ID":"","PROVIDER_TYPE":"1","VALID_DATE":"2024-08-16 09:00:25.0","ROW_INDEX":"3","DATA_STATUS_1":"1","DATA_STATUS_2":"0","EMAIL":"<EMAIL>","MGMT_COUNTY":"4000","GROUP_ID":"471710052548","OPERATOR_ID":"71010315","BILL_ID":"13754090990","RELA_ID":"20005394274","MGMT_DISTRICT":"471","CUST_MGR_ID":"71010315","STAFF_NAME":"李惠"},{"REL_TYPE":"2","DEPT_ID":"","PROVIDER_TYPE":"1","VALID_DATE":"2025-02-26 16:41:42.0","ROW_INDEX":"4","DATA_STATUS_1":"1","DATA_STATUS_2":"1","EMAIL":"<EMAIL>","MGMT_COUNTY":"4000","GROUP_ID":"471710052548","OPERATOR_ID":"71020032","BILL_ID":"15921811924","RELA_ID":"20005395001","MGMT_DISTRICT":"471","CUST_MGR_ID":"10010205","STAFF_NAME":"陈曦"},{"REL_TYPE":"2","DEPT_ID":"","PROVIDER_TYPE":"1","VALID_DATE":"2023-11-07 09:13:23.0","ROW_INDEX":"5","DATA_STATUS_1":"1","DATA_STATUS_2":"0","EMAIL":"<EMAIL>","MGMT_COUNTY":"7303","GROUP_ID":"471710052548","OPERATOR_ID":"1071","BILL_ID":"15104736691","RELA_ID":"20005393017","MGMT_DISTRICT":"473","CUST_MGR_ID":"1071","STAFF_NAME":"测试31"}]},"pubInfo":{"message":"success","code":"0","reqSerialNo":"2015060611223320345443"}}},"success":true});
  },
};

export default delay(proxy, defaultSettings.delay);
