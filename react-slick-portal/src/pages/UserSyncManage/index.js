import React, { useState, useEffect } from 'react';
import moment from 'moment';
import { Card, Form, Row, Col, Button, message, Descriptions, DatePicker } from 'antd';
import request from '@/utils/request';
import styles from './index.less';

/**
 * 文件同步名称类型fileType:：
 * 1:bup_user_crm 用户
 * 2:bup_user_roles 用户角色
 * 3:bup_user_organization 用户组织
 * 4:bup_roles 角色
 * 5:bup_organization 组织
*/
const fileTypeObj = {
  1: 'bup_user_crm',
  2: 'bup_user_roles',
  3: 'bup_user_organization',
  4: 'bup_roles',
  5: 'bup_organization',
};

function UserSyncManage({ form }) {
  const { getFieldDecorator } = form;

  const [workbenchData, setWorkbenchData] = useState({}); // 数据来源是工作台的数据
  const [roleData, setRoleData] = useState([]); // 业务系统角色数据
  const [busiData, setBusiData] = useState([]); // 数据来源是业务系统的数据

  // 获取标签选择数据
  const getInitData = async date => {
    let createDate = date;
    if (!createDate) {
      createDate = moment().format('YYYY-MM-DD');
    }
    const result = await request('orgauth/FileSyncAuditController/selectFileSyncAuditList.do', {
      method: 'POST',
      data: {
        createDate,
      },
    });
    if (result) {
      // dataSourceType(1:数据来源为工作台  2:数据来源为业务系统)

      // 对应工作台-同步信息-用户组织
      const result1 = result.filter(item => item.dataSourceType === '1' && item.systemInfoId === undefined);

      // 对应工作台 - 同步信息 - 业务系统角色;
      const result2 = result.filter(item => item.dataSourceType === '1' && item.systemInfoId !== undefined);
      setRoleData(result2);

      // 对应业务系统-同步信信息
      const result3 = result.filter(item => item.dataSourceType === '2' && item.systemInfoId !== undefined);

      // 工作台的数据
      const workObj = {};
      result1.forEach(row => {
        const syncAddNumber = row.syncAddNumber || 0;
        const syncUpdateNumber = row.syncUpdateNumber || 0;
        const syncDeleteNumber = row.syncDeleteNumber || 0;
        const syncTotalNumber = row.syncTotalNumber || 0;
        workObj[row.fileType] = {
          dataStr: `新增${syncAddNumber} 修改${syncUpdateNumber} 删除${syncDeleteNumber} 总${syncTotalNumber}`,
          syncAddNumber,
          syncUpdateNumber,
          syncDeleteNumber,
          syncTotalNumber,
        };
      });
      setWorkbenchData(workObj);

      // 业务系统的数据
      const busiObj = {};
      const systemObj = {};
      result3.forEach(row => {
        const { needReport } = row; // 是否需要同步
        const { hasReport } = row; // 是否同步成功
        const syncAddNumber = row.syncAddNumber || 0;
        const syncUpdateNumber = row.syncUpdateNumber || 0;
        const syncDeleteNumber = row.syncDeleteNumber || 0;
        const syncTotalNumber = row.syncTotalNumber || 0;

        const dataStr = `新增${syncAddNumber} 修改${syncUpdateNumber} 删除${syncDeleteNumber} 总${syncTotalNumber}`;
        const objValue = {
          needReport,
          hasReport,
          dataStr,
          syncAddNumber,
          syncUpdateNumber,
          syncDeleteNumber,
          syncTotalNumber,
        };
        if (busiObj[row.systemInfoId]) {
          busiObj[row.systemInfoId][row.fileType] = objValue;
        } else {
          const temp = { [row.fileType]: objValue };
          busiObj[row.systemInfoId] = temp;
          systemObj[row.systemInfoId] = row.sysCodeName;
        }
      });
      const data = Object.keys(busiObj).map(key => ({
        systemInfoId: key,
        sysCodeName: systemObj[key],
        busiDataObj: busiObj[key],
      }));
      setBusiData(data);
    }
  };

  useEffect(() => {
    getInitData();
  }, []);

  const changeDate = date => {
    let createDate = moment().format('YYYY-MM-DD');
    if (date) {
      createDate = moment(date).format('YYYY-MM-DD');
    }
    getInitData(createDate);
  };

  // 下载文件
  const downloadFiles = (fileType, systemInfoId) => {
    let createDate = form.getFieldValue('createDate').format('YYYY-MM-DD');
    if (!createDate) {
      createDate = moment().format('YYYY-MM-DD');
    }

    if (fileType !== '2' || (fileType === '2' && systemInfoId)) {
      const fileTypeName = fileTypeObj[fileType];

      const a = document.createElement('a');
      a.href = `orgauth/FileSyncAuditController/downloadFtpFile.do?createDate=${createDate}&fileTypeName=${fileTypeName}${fileType === '2' ? `&systemInfoId=${systemInfoId}` : ''}`;
      a.target = '_blank';
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      a.remove();
    } else {
      message.error('下载信息不完整');
    }
  };

  // 下载上游同步文件
  const downloadTraceFiles = () => {
    let createDate = form.getFieldValue('createDate').format('YYYY-MM-DD');
    if (!createDate) {
      createDate = moment().format('YYYY-MM-DD');
    }

    const a = document.createElement('a');
    a.href = `orgauth/FileSyncAuditController/downloadTraceLog.do?createDate=${createDate}`;
    a.target = '_blank';
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    a.remove();
  };

  // 导出实时同步接口数据
  const handleExport = () => {
    let createDate = form.getFieldValue('createDate').format('YYYY-MM-DD');
    if (!createDate) {
      createDate = moment().format('YYYY-MM-DD');
    }

    const a = document.createElement('a');
    a.href = `orgauth/FileSyncAuditController/expFileSyncAuditDataToExcel.do?createDate=${createDate}`;
    a.target = '_blank';
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    a.remove();
  };

  const userWorkbenchNum = type => (
    <>
      <span>{workbenchData[type]?.dataStr || '无'}</span>
      <a onClick={() => downloadFiles(type)}>&nbsp;&nbsp;下载文件</a>
    </>
  );

  // 业务系统同步信息，包含与工作台差异比较
  const systemBusiNum = (row, type) => {
    if (row.busiDataObj[type]) {
      // 业务系统同步返回信息
      const {
        needReport,
        hasReport,
        syncAddNumber,
        syncUpdateNumber,
        syncDeleteNumber,
        syncTotalNumber,
      } = row.busiDataObj[type];

      if (!needReport) {
        return <span>无需同步该文件</span>;
      }
      if (!hasReport) {
        return <span style={{ color: '#FAAF0C' }}>未上报</span>;
      }
      let addNumber = 0;
      let updateNumber = 0;
      let deleteNumber = 0;
      let totalNumber = 0;
      if (type === '2') {
        // 业务系统角色比对
        const busiItem = roleData.find(item => item.systemInfoId === row.systemInfoId);
        if (busiItem) {
          addNumber = busiItem.syncAddNumber;
          updateNumber = busiItem.syncUpdateNumber;
          deleteNumber = busiItem.syncDeleteNumber;
          totalNumber = busiItem.syncTotalNumber;
        }
      } else if (workbenchData[type]) {
        // 用户组织信息比对
        addNumber = workbenchData[type].syncAddNumber;
        updateNumber = workbenchData[type].syncUpdateNumber;
        deleteNumber = workbenchData[type].syncDeleteNumber;
        totalNumber = workbenchData[type].syncTotalNumber;
      }

      const addDiff = Number(addNumber) - Number(syncAddNumber);
      const updateDiff = Number(updateNumber) - Number(syncUpdateNumber);
      const deleteDiff = Number(deleteNumber) - Number(syncDeleteNumber);
      const totalDiff = Number(totalNumber) - Number(syncTotalNumber);

      // 无差异
      if (addDiff === 0 && updateDiff === 0 && deleteDiff === 0 && totalDiff === 0) {
        return <span style={{ color: '#8FC31F' }}>{row.busiDataObj[type].dataStr}&nbsp;&nbsp;同步正常</span>;
      }

      const diffArr = [];
      if (addDiff !== 0) {
        diffArr.push(`新增 ${addDiff > 0 ? '+' : '-'}${Math.abs(addDiff)}`);
      }
      if (updateDiff !== 0) {
        diffArr.push(`修改 ${updateDiff > 0 ? '+' : '-'}${Math.abs(updateDiff)}`);
      }
      if (deleteDiff !== 0) {
        diffArr.push(`删除 ${deleteDiff > 0 ? '+' : '-'}${Math.abs(deleteDiff)}`);
      }
      if (totalDiff !== 0) {
        diffArr.push(`总 ${totalDiff > 0 ? '+' : '-'}${Math.abs(totalDiff)}`);
      }
      return <span>{row.busiDataObj[type].dataStr}<span style={{ color: '#F23030' }}>&nbsp;&nbsp;同步异常（{diffArr.join('，')}）</span></span>;
    }
    return <span>无数据</span>;
  };

  return (
    <div className={styles.userSyncManage}>
      <Form className="flow fix-label">
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item label="日期">
              {getFieldDecorator('createDate', {
                  initialValue: moment(),
                })(<DatePicker placeholder="请选择" onChange={changeDate} />)}
            </Form.Item>
          </Col>
          <Col span={18} className="text-right">
            <Button onClick={downloadTraceFiles}>下载上游同步文件</Button>
            <Button style={{ marginLeft: '8px' }} onClick={handleExport}>导出实时同步接口数据</Button>
          </Col>
        </Row>
      </Form>

      <div className={styles.selfCard}>
        <Card>
          <Row gutter={[{ xs: 8, sm: 16, md: 24, lg: 32 }, 20]}>
            <Col span={24}>
              <Card title="用户组织信息" className="cute">
                {
                    JSON.stringify(workbenchData) !== '{}' && (
                      <Descriptions size="small" column={3}>
                        <Descriptions.Item label="用户同步">{userWorkbenchNum('1')}</Descriptions.Item>
                        <Descriptions.Item label="组织同步">{userWorkbenchNum('5')}</Descriptions.Item>
                        <Descriptions.Item label="角色同步">{userWorkbenchNum('4')}</Descriptions.Item>
                        <Descriptions.Item label="用户组织同步">{userWorkbenchNum('3')}</Descriptions.Item>
                      </Descriptions>
                    )
                  }
              </Card>
            </Col>
          </Row>
          <Row gutter={[{ xs: 8, sm: 16, md: 24, lg: 32 }, 20]}>
            <Col span={24}>
              <Card title="业务系统角色" className="cute">
                <Descriptions size="small" className={styles.label} column={3}>
                  {roleData.length && roleData.map(row => (
                    <Descriptions.Item label={row.sysCodeName} key={row.id}>
                      <span>新增{row.syncAddNumber} 修改{row.syncUpdateNumber} 删除{row.syncDeleteNumber} 总{row.syncTotalNumber}</span>
                      <a onClick={() => downloadFiles(row.fileType, row.systemInfoId)}>&nbsp;&nbsp;下载文件</a>
                    </Descriptions.Item>
                      ))}
                </Descriptions>
              </Card>
            </Col>
          </Row>
        </Card>
      </div>

      <Row type="flex" justify="space-between" align="middle" gutter={[16, 16]}>
        {busiData?.map(row => (
          <Col span={12} key={row.systemInfoId}>
            <Card title={row.sysCodeName}>
              <Descriptions size="small" column={1}>
                <Descriptions.Item label="用户同步">{systemBusiNum(row, '1')}</Descriptions.Item>
                <Descriptions.Item label="组织同步">{systemBusiNum(row, '5')}</Descriptions.Item>
                <Descriptions.Item label="角色同步">{systemBusiNum(row, '4')}</Descriptions.Item>
                <Descriptions.Item label="用户组织关系同步">{systemBusiNum(row, '3')}</Descriptions.Item>
                <Descriptions.Item label="业务系统角色">{systemBusiNum(row, '2')}</Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
            ))}
      </Row>
    </div>
  );
}

export default Form.create()(UserSyncManage);
