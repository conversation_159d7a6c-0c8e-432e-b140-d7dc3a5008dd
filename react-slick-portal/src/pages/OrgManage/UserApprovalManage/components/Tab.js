/* eslint-disable */
import React, { useState, useEffect, useContext } from 'react';
import { Row, Col, Card, Form, Input, Icon, Button, Table, Tabs, Modal } from 'antd';
import LoadingComponent from '@/components/PageLoading/index';
import dynamic from 'umi/dynamic';
const { TabPane } = Tabs;

// 涉及到的标签组件按需加载
const SysUserInfo = dynamic({
  loader: () => import('./UserInfo'),
  loading: LoadingComponent,
});

function Tab(props) {
  const [menuKey, setMenuKey] = useState('SysUserInfo');
  const [contentList, setContentList] = useState({});

  const tabList = [
    {
      key: 'SysUserInfo',
      tab: '系统用户信息',
    },
  ];

  useEffect(() => {
    onTabChange('SysUserInfo');
  }, []);

  useEffect(() => {
    if (props.behavior !== 'disabled') {
      onTabChange('SysUserInfo');
    }
  }, [props.behavior]);

  const onTabChange = key => {
    const contentMap = {
      SysUserInfo: <SysUserInfo />,
    };
    if (!contentList[key]) {
      contentList[key] = contentMap[key];
    }
    setContentList(contentList);
    setMenuKey(key);
  };

  return (
    <div>
      <Card
        className="gb_tabs_samll"
        style={{ width: '100%' }}
        tabList={tabList}
        activeTabKey={menuKey}
        onTabChange={key => {
          onTabChange(key);
        }}
      >
        {Object.keys(contentList).map(key => (
          <div key={key} style={{ display: menuKey === key ? 'block' : 'none' }}>
            {contentList[key]}
          </div>
        ))}
      </Card>
    </div>
  );
}
export default Tab;
