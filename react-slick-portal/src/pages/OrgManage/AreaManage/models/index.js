export default {
  namespace: 'areaManage',
  state: {
    tableData: {},
    passData: {},
    behavior: 'disabled',
  },

  effects: {},

  reducers: {
    saveAreaData(state, { payload: params }) {
      return {
        ...state,
        tableData: params,
      };
    },

    savePassData(state, { payload: params }) {
      return {
        ...state,
        passData: params,
      };
    },

    saveBehavior(state, { payload: params }) {
      return {
        ...state,
        behavior: params,
      };
    },

    // 销毁时重置
    reset(state) {
      return {
        tableData: {},
        passData: {},
        behavior: 'disabled',
      };
    },
  },
};
