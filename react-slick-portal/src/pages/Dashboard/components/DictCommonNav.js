/* eslint-disable no-console */
/* eslint-disable no-nested-ternary */
import React, { useEffect, useMemo, useState, useRef } from 'react';
import { connect } from 'dva';
import {
  Button,
  Badge,
  Input,
  Form,
  Select,
  Table,
  Spin,
  Modal,
  Icon,
  Divider,
  Tooltip,
  Empty,
} from 'antd';
import { openMenuByValue, openMenu } from '@/utils/utils';
import CommonHeader from './CommonHeader';
import commonNav from '../img/nmgIcon/commonNav.png';
import commonNav1 from '../img/nmgIcon/commonNav1.png';
import style from './DictCommonNav.less';

const InputGroup = Input.Group;
const { Option } = Select;

// const formItemLayout = {
//   labelCol: {
//     xs: { span: 24 },
//     sm: { span: 8 },
//   },
//   wrapperCol: {
//     xs: { span: 24 },
//     sm: { span: 16 },
//   },
// };

const DictCommonNav = props => {
  const {
    dispatch,
    portlet = {},
    dictCommonNav: { sceneList, sceneNodeTableInfo = {}, sceneNodeQuantity },
    user,
    // form: { getFieldDecorator },
    allMenu,
  } = props;

  const { menuCode = '' } = portlet;

  const {
    queryAreaButton = [],
    queryAreaCondition = {},
    tableHeader = [],
    tableParams,
  } = sceneNodeTableInfo;

  const [activeScene, setActiveScene] = useState(null); // 选中的场景
  const [activeSceneNode, setActiveSceneNode] = useState(null); // 选中的原子节点
  const [childSceneNode, setChildSceneNode] = useState(null); // 选中的二级环节
  const [iframeInfo, setIframeInfo] = useState(null); // iframeUrl

  const [sceneReqType, setsceneReqType] = useState(null);
  const [sceneReqInput, setSceneReqInput] = useState(null);
  const [searchValue, setSearchValue] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  // const [isFocus, setIsFocus] = useState(false);

  const searchBoxRef = useRef();

  const scrollRef = useRef(null);
  const isInTable = useRef(false); // 鼠标是否在表格内

  // 监听点击外部事件，控制搜索框隐藏
  useEffect(() => {
    const handleClickOutside = event => {
      if (searchBoxRef.current && !searchBoxRef.current.contains(event.target)) {
        setShowSearch(false); // 隐藏搜索框
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleWheel = e => {
    e.stopPropagation();
    if (Math.abs(e.deltaY) > 0) {
      e.preventDefault();
      const tableBody = scrollRef.current?.querySelector('.ant-table-body');
      if (tableBody) {
        tableBody.scrollLeft += e.deltaY;
      }
    }
  };

  // 鼠标进入表格时标记状态
  const handleMouseEnter = () => {
    isInTable.current = true;
    document.body.style.overflow = 'hidden'; // 禁用全局滚动
  };

  // 鼠标离开表格时恢复
  const handleMouseLeave = () => {
    isInTable.current = false;
    document.body.style.overflow = 'auto'; // 恢复全局滚动
  };

  // 处理url携带参数
  const dealUrl = url => {
    const { sessionId, userCode, orgInfo, userInfo } = user;
    let _url = url;
    if (url.indexOf('?') === -1) {
      _url += `?bss3SessionId=${sessionId}&sysUserCode=${userCode}`;
    } else if (url.indexOf('?') === url.length - 1) {
      _url += `bss3SessionId=${sessionId}&sysUserCode=${userCode}`;
    } else {
      _url += `&bss3SessionId=${sessionId}&sysUserCode=${userCode}`;
    }
    if (orgInfo?.orgCode) _url += `&orgNbr=${orgInfo.orgCode}`;
    if (userInfo?.postRegionId) _url += `&regionNbr=${userInfo?.postRegionId}`;
    return _url;
  };

  // 修改模块state
  const setDictCommonNavState = async params => {
    await dispatch({
      type: 'dictCommonNav/setDictCommonNavState',
      payload: params,
    });
  };

  const getBsiId = data => {
    // 已选中二级环节 直接返回
    if (childSceneNode) return childSceneNode.bsiId;
    // 选中一级环节，如果有二级环节返回二级环节
    if (data && data.childSceneNodeInfo && data.childSceneNodeInfo.length) {
      return data.childSceneNodeInfo[0].bsiId;
    }
    return data.bsiId;
  };

  const getFormsValues = () => {
    const obj = {};
    if (sceneReqType && sceneReqInput) {
      obj[sceneReqType] = sceneReqInput;
    }

    const paramMap = props?.form?.getFieldsValue();
    Object.keys(paramMap).forEach(i => {
      if (paramMap[i] === undefined || paramMap[i] === '') delete paramMap[i];
    });

    return { ...paramMap, ...obj };
  };

  const getSceneMenuQuantity = () => {
    dispatch({
      type: 'dictCommonNav/getSceneMenuQuantity',
      payload: {
        params: (sceneReqType && sceneReqInput) ?
          ({ [sceneReqType]: sceneReqInput }) : {},
      },
    });
  };

  const sceneGroup = useMemo(() => menuCode.split('_').pop(), [menuCode]);

  // 加工查询条件并查询表格数据
  const doBusiSystemTempTransform = (pageNum = 1, pageSize = 7) => {
    const paramMap = getFormsValues();

    if (activeSceneNode) {
      const params = {
        bsiId: getBsiId(activeSceneNode),
        requestObject: {
          pageNum,
          pageSize,
          paramMap,
        },
      };
      getSceneMenuQuantity();
      dispatch({
        type: 'dictCommonNav/doBusiSystemTempTransform',
        payload: params,
      }).then(res => {
        console.log(res);
      });
    }
  };

  // 智能表格配置
  const getSceneNodeTableInfo = params => {
    dispatch({
      type: 'dictCommonNav/getSceneNodeTableInfo',
      payload: params,
    }).then(() => {
      doBusiSystemTempTransform();
    });
  };

  const getInitData = () => {
    dispatch({
      type: 'dictCommonNav/getInitData',
      payload: {
        sceneGroup,
      },
    }).then(res => {
      setActiveScene(res[0]);
    });
  };

  // 初始化 默认选中第一个场景
  useEffect(() => {
    getInitData();
  }, [sceneGroup]);

  // 点击原子能力，调用查询二级环节接口
  const clickSceneNode = node => {
    if (node && activeSceneNode?.sceneNodeId !== node.sceneNodeId) {
      // 选中原子能力，先清空智能表格数据及二级环节数据
      console.log('选中原子能力', node);
      switch (node.sceneNodeShowType) {
        case '1':
          setActiveSceneNode(node);
          // 如果有二级环节
          if (node.childSceneNodeInfo && node.childSceneNodeInfo.length) {
            setChildSceneNode(node.childSceneNodeInfo[0]);
          } else {
            setChildSceneNode(null);
          }
          break;
        case '2':
          openMenuByValue('TYMH_MENU_XTGL_IFRAMEBOX', allMenu, dealUrl(node?.sceneNodeUrl));
          break;
        case '3':
          setIframeInfo({
            iframeUrl: dealUrl(node?.sceneNodeUrl),
            title: node?.sceneNodeName,
          });
          break;
        case '4':
          openMenu(node?.sceneNodeUrl, allMenu, dispatch);
          break;
        default:
          break;
      }
    }
  };
  // 监听原子能力变化， 查询智能表格
  useEffect(() => {
    if (activeSceneNode) {
      if (activeSceneNode.childSceneNodeInfo && activeSceneNode.childSceneNodeInfo.length) {
        setChildSceneNode(activeSceneNode.childSceneNodeInfo[0]);
      } else {
        getSceneNodeTableInfo({ sceneNodeId: activeSceneNode?.sceneNodeId });
      }
    }
  }, [JSON.stringify(activeSceneNode)]);

  // 二级环节变化， 查询智能表格
  useEffect(() => {
    if (childSceneNode) {
      getSceneNodeTableInfo({ sceneNodeId: childSceneNode?.sceneNodeId });
    }
  }, [JSON.stringify(childSceneNode)]);

  // 监听场景变化， 默认选中第一个原子能力
  useEffect(() => {
    if (activeScene) {
      if (activeScene?.childrenNodeList?.length && activeScene?.childrenNodeList[0].sceneNodeShowType === '1') {
        clickSceneNode(activeScene?.childrenNodeList[0]);
      }

      if (activeScene?.queryAreaConditionJson) {
        const { attr } = activeScene.queryAreaConditionJson;
        setsceneReqType(attr[0].condCode);
      }
    }
  }, [JSON.stringify(activeScene)]);

  useEffect(() => {
    doBusiSystemTempTransform();
  }, [sceneReqInput]);

  // 渲染表格
  const renderTable = () => {
    const {
      list = [],
      total = 0,
      pageSize = 7,
      pageNum = 1,
    } = tableParams || {};
    const columns = tableHeader.map((item, index) => {
      const { tHeaderName, tHeaderCode, width, tHeaderType, operationValue } = item;
      if (index === 0) {
        return {
          dataIndex: tHeaderCode,
          title: <span style={{ fontWeight: '600' }}>{tHeaderName}</span>,
          width,
          ellipsis: true,
          render: text => (
            <div style={{ display: 'flex', alignItems: 'center' }} className={style.tableTd}>
              {text ? <img src={commonNav1} alt="" width={24} height={18} style={{ marginRight: 4 }} /> : null}
              <span>{text}</span>
            </div>
          ),
        };
      }
      if (tHeaderType === '1') {
        return {
          dataIndex: tHeaderCode,
          title: <span style={{ fontWeight: '600' }}>{tHeaderName}</span>,
          width,
          ellipsis: true,
          render: text => (
            <div className={style.tableTd}>
              {text}
            </div>
          ),
        };
      }
      return {
        dataIndex: tHeaderCode,
        title: <span style={{ fontWeight: '600' }}>{tHeaderName}</span>,
        ellipsis: true,
        fixed: 'right',
        width: operationValue?.length > 2 ? 150 : (operationValue?.length > 1 ? 108 : 62),
        render: (text, record) => (
          <div className={style.tableTd}>
            {operationValue.map((itemOpt, optIndex) => (
              <>
                {optIndex !== 0 ? <Divider type="vertical" /> : null }
                <a
                  onClick={() => {
                    const { buttonUrl, buttonUrlParam = {} } = itemOpt;
                    const keys = Object.keys(buttonUrlParam) || [];
                    const params = keys.map(key => `${key}=${record[buttonUrlParam[key]]}`).join('&');
                    const url = `${dealUrl(buttonUrl)}&${params}`;
                    openMenuByValue('TYMH_MENU_XTGL_IFRAMEBOX', allMenu, url);
                  }}
                  style={{ color: '#0085D0' }}
                >
                  {itemOpt.buttonName}
                </a>
              </>
            ))}
          </div>
        ),
      };
    });
    return (
      <div
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onWheel={handleWheel}
        ref={scrollRef}
        style={{ width: '100%' }}
      >
        <Table
          rowKey="id"
          columns={columns}
          dataSource={list}
          pagination={{
            total,
            pageSize,
            current: pageNum,
            // showTotal: () => `共${total}条`,
            // pageSizeOptions: ['5', '10', '20', '30', '40'],
            onChange: (a, b) => {
              doBusiSystemTempTransform(a, b);
            },
            hideOnSinglePage: true,
          }}
          scroll={{ x: document.getElementById('tableContainer')?.offsetWidth + 150 }}
        />
      </div>
    );
  };

  // 根据类型渲染所需组件
  // const renderFormItemsByType = (attr, onChange) => {
  //   const { condType, value } = attr;
  //   const attrParams = {
  //     placeholder: '请选择',
  //     onChange: e => onChange(e, attr),
  //     allowClear: true,
  //   };
  //   switch (condType) {
  //     case '1':
  //       return <Input {...attrParams} placeholder="请输入" />;
  //     case '2':
  //       return (
  //         <Select {...attrParams}>
  //           {value && value.length && value.map(i => <Option value={i?.enumValue}>{i?.enumName}</Option>)}
  //         </Select>
  //       );
  //     case '3':
  //       return <DatePicker {...attrParams} style={{ width: '100%' }} />;
  //     default:
  //       return null;
  //   }
  // };

  // 表单按钮点击事件
  const onButtonClick = item => {
    console.log(item, '点击按钮');
    const { buttonFuncType, buttonUrl } = item;
    switch (buttonFuncType) {
      case '1':
        // 查询
        doBusiSystemTempTransform();
        return;
      case '2':
        console.log('新增');
        return;
      case '3':
        // 打开菜单
        if (buttonUrl) {
          const [urlCode, urlParams] = buttonUrl.split('?');
          openMenu(urlCode, allMenu, dispatch, urlParams ? `?${urlParams}` : '');
        }
        return;
      default:
        console.log('未匹配事件');
    }
  };

  // 渲染表单按钮
  const renderFormButton = (attr, onClick) => (
    attr.map(item => {
      const { buttonName, buttonColor, buttonFuncType } = item;
      const buttonProps = {
        type: Number(buttonColor) === 1 ? 'primary' : '',
        htmlType: Number(buttonFuncType) === 1 ? 'submit' : 'button',
        onClick: () => onClick(item),
        style: { marginRight: 12 },
        size: 'small',
      };
      return (
        <Button key={buttonName} {...buttonProps}>
          {buttonName}
        </Button>
      );
    })
  );

  // 表单组件onchange
  // const onAttrChange = (e, attr) => {
  //   console.log(e, attr);
  // };

  // 渲染查询条件区域
  // const renderFormArea = formInfo => {
  //   const { column, attr } = formInfo;
  //   if (!Array.isArray(attr) || attr.length === 0) return null;
  //   const width = 24 / column;
  //   const attrs = attr.filter(i => Number(i?.condHidden) !== 1) || [];
  //   const querySpan = (column - (attrs.length % column)) * width;
  //   return (
  //     attrs.length ? (
  //       <Form>
  //         <Row>
  //           {
  //             attrs.map(item => {
  //               const { condName, condCode, condRequird } = item;
  //               return (
  //                 <Col span={width}>
  //                   <Form.Item label={condName} {...formItemLayout}>
  //                     {getFieldDecorator(condCode, {
  //                       rules: [
  //                         {
  //                           required: condRequird,
  //                         },
  //                       ],
  //                     })(renderFormItemsByType(item, onAttrChange))}
  //                   </Form.Item>
  //                 </Col>
  //               );
  //             })
  //           }
  //           <Col span={querySpan} style={{ textAlign: 'right' }}>
  //             {queryAreaButton && renderFormButton(queryAreaButton, onButtonClick)}
  //           </Col>
  //         </Row>
  //       </Form>
  //     ) : null
  //   );
  // };

  // 渲染列表区域
  const renderAreaCondition = () => (
    <div className={style.tableContainer} id="tableContainer">
      {/* {renderFormArea(formInfo)} */}
      {tableHeader.length ? renderTable() : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
    </div>
  );

  const renderSceneForm = () => {
    const { attr } = activeScene?.queryAreaConditionJson;
    // 如果不显示搜索框，显示图标
    if (!showSearch) {
      return (
        <Icon type="search" style={{ fontSize: 16, color: '#333' }} onClick={() => { setShowSearch(true); }} />
      );
    }
    return (
      <div ref={searchBoxRef}>
        <InputGroup compact>
          <Select
            style={{ width: 120 }}
            value={sceneReqType}
            onChange={value => setsceneReqType(value)}
            size="small"
          >
            {attr.map(i => <Option value={i.condCode}>{i.condName}</Option>)}
          </Select>
          <Input.Search
            placeholder="请输入"
            style={{ width: 180 }}
            value={searchValue}
            onChange={e => {
              setSearchValue(e?.target?.value || '');
            }}
            onSearch={value => {
              setSceneReqInput(value);
            }}
            size="small"
          />
        </InputGroup>
      </div>
    );
  };

  // 场景切换
  const onTabsChange = async sceneId => {
    const activeTab = sceneList.find(i => i.sceneId === sceneId);
    if (activeTab.sceneType === '3') {
      setIframeInfo({
        iframeUrl: dealUrl(activeTab?.sceneUrl),
        title: activeTab?.sceneName,
      });
      return;
    }
    if (activeTab.sceneType === '2') {
      openMenuByValue('TYMH_MENU_XTGL_IFRAMEBOX', allMenu, dealUrl(activeTab.sceneUrl));
      return;
    }
    if (activeTab.sceneType === '4') {
      openMenu(activeTab?.sceneUrl, allMenu, dispatch);
      return;
    }
    if (sceneReqType) setsceneReqType(null);
    if (sceneReqInput) setSceneReqInput(null);
    if (searchValue) setSearchValue('');
    await setDictCommonNavState({
      sceneNodeTableInfo: {},
    });
    setActiveScene(activeTab);
  };

  return (
    <>
      <Spin spinning={props?.modelLoading}>
        <CommonHeader
          title={(
            <div className={style.sceneTabs}>
              {(sceneList || []).map(item => {
                const { sceneId, sceneName } = item;
                return (
                  <div
                    key={sceneId}
                    onClick={() => onTabsChange(sceneId)}
                    className={`${style.sceneTabItem} ${activeScene?.sceneId === sceneId ? style.tabActive : ''}`}
                  >
                    {sceneName}
                  </div>
                );
              })}
            </div>
          )}
          isFlag
          extra={(
            <div className={style.searchBox}>
              {queryAreaButton && renderFormButton(queryAreaButton, onButtonClick)}
              {activeScene?.queryAreaConditionJson?.attr && renderSceneForm()}
              <Tooltip title="刷新">
                <Icon
                  type="redo"
                  style={{ fontSize: 16, color: '#333', transform: 'rotate(-80deg)', marginLeft: '12px' }}
                  onClick={() => {
                    console.log('刷新');
                    doBusiSystemTempTransform();
                  }}
                />
              </Tooltip>
            </div>
            )}
        />
        {
          activeScene?.childrenNodeList && activeScene?.childrenNodeList.length ? (
            <div className={style.sceneNodeContainer}>
              <div className={style.sceneNodeList}>
                {
                  activeScene.childrenNodeList.map((item, index) => {
                    const { sceneNodeId, sceneNodeCode, sceneNodeName } = item;
                    return (
                      <>
                        {index !== 0 ? <img src={commonNav} alt="" /> : null}
                        <Badge count={sceneNodeQuantity[sceneNodeCode]}>
                          <Button
                            type={activeSceneNode?.sceneNodeId === sceneNodeId ? 'primary' : null}
                            size="default"
                            shape="round"
                            disabled={!item.hasAuth}
                            onClick={() => clickSceneNode(item)}
                            className={style.sceneNodeItemBtn}
                          >
                            {sceneNodeName}
                          </Button>
                        </Badge>
                      </>
                    );
                  })
                }
              </div>

            </div>
          ) : null
        }
        {renderAreaCondition(queryAreaCondition)}
      </Spin>
      {
        iframeInfo?.iframeUrl ? (
          <Modal
            title={iframeInfo.title}
            width={window.innerWidth - 10}
            visible={iframeInfo.iframeUrl}
            onCancel={() => setIframeInfo(null)}
            destroyOnClose
            mask={false}
            // centered
            footer={null}
            bodyStyle={{ padding: '0' }}
            style={{ top: 0, border: '1px solid #d9d9d9', padding: 0 }}
          >
            <iframe
              title={iframeInfo.title}
              src={iframeInfo.iframeUrl}
              frameBorder="0"
              width="100%"
              height={window.innerHeight - 65}
              style={{ backgroundColor: '#fff' }}
            />
          </Modal>
        ) : null
      }
    </>
  );
};

export default connect(({ dictCommonNav, menu, login, loading }) => ({
  dictCommonNav,
  allMenu: menu.all,
  user: login.user,
  initLoading: loading.effects['dictCommonNav/getInitData'],
  modelLoading: loading.models?.dictCommonNav,
}))(Form.create()(DictCommonNav));
