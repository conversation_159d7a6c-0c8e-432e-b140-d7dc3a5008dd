import request from '@/utils/request';

// 非结构化资料查询
export function queryGrpUnstructuredByCondiction(data) {
  return request('portal/GroupCommonExtController/queryGrpUnstructuredByCondiction.do', {
    method: 'POST',
    data,
  });
}

// 非结构化资料新增
export function addGrpUnstructured(data) {
  return request('portal/GroupCommonExtController/addeGrpUnstructured.do', {
    method: 'POST',
    data,
  });
}

// 非结构化资料修改
export function modGrpUnstructured(data) {
  return request('portal/GroupCommonExtController/modGrpUnstructured.do', {
    method: 'POST',
    data,
  });
}
// 非结构化资料删除
export function delGrpUnstructured(data) {
  return request('portal/GroupCommonExtController/delGrpUnstructured.do', {
    method: 'POST',
    data,
  });
}

// 绑定专线延期账务关停文件
export function bindingFileApi(data) {
  return request('portal/GroupCommonExtController/saveSLineAuditFile.do', {
    method: 'POST',
    data,
  });
}
