/**
 * 图标组件统一导出
 * 使用方式：
 * import { AddIcon, DeleteIcon, ICON_SIZES, ICON_COLORS } from '@/components/Icons';
 */

// 导出基础组件
export { default as IconBase } from './IconBase';

// 导出类型和常量
export * from './types';

// 导出基础操作图标
export {
  AddIcon,
  DeleteIcon,
  EditIcon,
  SearchIcon,
  FilterIcon,
  RefreshIcon,
  CloseIcon,
  MoreIcon,
  SaveIcon,
} from './components/BasicIcons';

// 导出导航图标
export {
  HomeIcon,
  BackIcon,
  ForwardIcon,
  UpIcon,
  DownIcon,
  LeftIcon,
  RightIcon,
  MenuIcon,
  ExpandIcon,
  CollapseIcon,
} from './components/NavigationIcons';

// 导出状态图标
export {
  SuccessIcon,
  ErrorIcon,
  WarningIcon,
  InfoIcon,
  LoadingIcon,
  CheckIcon,
  QuestionIcon,
  NotificationIcon,
} from './components/StatusIcons';

// 导出文件和用户图标
export {
  FileIcon,
  FolderIcon,
  UploadIcon,
  DownloadIcon,
  UserIcon,
  TeamIcon,
  SettingIcon,
  LogoutIcon,
} from './components/FileUserIcons';

// 图标映射对象，便于动态使用
export const IconMap = {
  // 基础操作
  add: AddIcon,
  delete: DeleteIcon,
  edit: EditIcon,
  search: SearchIcon,
  filter: FilterIcon,
  refresh: RefreshIcon,
  close: CloseIcon,
  more: MoreIcon,
  save: SaveIcon,
  
  // 导航
  home: HomeIcon,
  back: BackIcon,
  forward: ForwardIcon,
  up: UpIcon,
  down: DownIcon,
  left: LeftIcon,
  right: RightIcon,
  menu: MenuIcon,
  expand: ExpandIcon,
  collapse: CollapseIcon,
  
  // 状态
  success: SuccessIcon,
  error: ErrorIcon,
  warning: WarningIcon,
  info: InfoIcon,
  loading: LoadingIcon,
  check: CheckIcon,
  question: QuestionIcon,
  notification: NotificationIcon,
  
  // 文件和用户
  file: FileIcon,
  folder: FolderIcon,
  upload: UploadIcon,
  download: DownloadIcon,
  user: UserIcon,
  team: TeamIcon,
  setting: SettingIcon,
  logout: LogoutIcon,
};

// 动态图标组件
export const DynamicIcon = ({ name, ...props }) => {
  const IconComponent = IconMap[name];
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found`);
    return null;
  }
  return <IconComponent {...props} />;
};
