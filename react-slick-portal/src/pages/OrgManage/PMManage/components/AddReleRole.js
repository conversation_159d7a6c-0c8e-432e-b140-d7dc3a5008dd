import React, { useState, useEffect, useImperativeHandle } from 'react';
import {
  Row,
  Col,
  Form,
  Input,
  Select,
  message,
  Spin,
} from 'antd';
import request from '@/utils/request';
import ComboGrid from '@/components/ComboGrid';
import { operActionLog } from '@/utils/utils';

const { TextArea } = Input;
const { Option } = Select;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const textAreaFormItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

function AddReleRole(props) {
  const { getFieldDecorator } = props.form;
  const [spinning, setSpinning] = useState(false);
  const [visiable, setVisiable] = useState(false);
  const [valuesList, setValuesList] = useState([[], [], []]);

  async function queryValueList() {
    let result;
    await Promise.all([
      request('portal/DomainDataController/getValuesList.do', {
        data: {
          busiNbr: 'SystemUserRole',
          propertyName: 'manageClass',
        },
      }),
      request('portal/DomainDataController/getValuesList.do', {
        data: {
          busiNbr: 'SystemUserRole',
          propertyName: 'operType',
        },
      }),
      request('portal/DomainDataController/getValuesList.do', {
        data: {
          busiNbr: 'SystemUserRole',
          propertyName: 'defaultFlag',
        },
      }),
    ]).then(res => {
      result = res;
    });
    return result;
  }

  useEffect(() => {
    setSpinning(true);
    queryValueList().then(res => {
      setSpinning(false);
      setValuesList(res);
    });
  }, []);

  const onConfirm = value => {
    if (value.length > 0) {
      if (value[0].sysCode === '727001' || value[0].sysCode === '727003') {
        setVisiable(true);
      } else {
        setVisiable(false);
      }
    }
  };

  // 新增
  useImperativeHandle(props.cRef, () => ({
    handleAdd: () => new Promise(resolve => {
      props.form.validateFields((err, fieldsValue) => {
        if (err) return;

        const obj = {
          ...fieldsValue,
          defaultFlag: fieldsValue.defaultFlag ? fieldsValue.defaultFlag !== '0' : '',
          sysUserId: props.sysUserId,
          sysRoleId: fieldsValue.sysRoleId[0].sysRoleId,
        };
        setSpinning(true);
        request('portal/UserAndRoleAndLabelController/addUserAndRole', { data: obj }).then(res => {
          if (res.resultCode === '200') {
            setSpinning(false);
            message.success('新增成功');
            // 地址和角色的一样，需要特殊处理
            operActionLog({ actionType: '1100', actionModule: '3290500', action: '1900', handResult: '', actionMsg: '' });
            resolve(true);
          } else {
            setSpinning(false);
            operActionLog({ actionType: '1100', actionModule: '3290500', action: '1900', handResult: '', actionMsg: res.retMsg });
            message.error(res.result);
          }
        });
      });
    }),
  }));

  return (
    <Spin spinning={spinning}>
      <Form>
        <Row>
          <Col span={12}>
            <Form.Item label="角色名称" {...formItemLayout}>
              {getFieldDecorator('sysRoleId', {
                rules: [
                  {
                    required: true,
                    message: '用户名称不能为空',
                  },
                ],
              })(
                <ComboGrid
                  url="portal/UserAndRoleAndLabelController/queryConfigurationRoleList"
                  popupStyle={{ width: 560 }}
                  placeholder="请选择角色"
                  searchPlaceholder="请输入角色名称进行搜索"
                  label="sysRoleName"
                  rowKey="id"
                  pick="radio"
                  params={{ filterCol: 'sysRoleName,sysRoleCode', sysUserId: props.sysUserId || undefined }}
                  onConfirm={onConfirm}
                  columns={[
                    {
                      title: '角色名称',
                      dataIndex: 'sysRoleName',
                      ellipsis: true,
                    },
                    {
                      title: '角色编码',
                      dataIndex: 'sysRoleCode',
                      ellipsis: true,
                    },
                    {
                      title: '角色类型',
                      ellipsis: true,
                      dataIndex: 'sysRoleTypeName',
                    },
                    {
                      title: '归属系统',
                      ellipsis: true,
                      dataIndex: 'sysCodeName',
                    },
                  ]}
                />
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="授权管理分类" {...formItemLayout}>
              {getFieldDecorator('manageClass', {
                rules: [
                  {
                    required: true,
                    message: '授权管理分类不能为空',
                  },
                ],
              })(
                <Select placeholder="请选择">
                  {valuesList[0].length
                    ? valuesList[0].map(item => (
                      <Option value={`${item.value}`} key={item.value}>
                        {item.name}
                      </Option>
                    ))
                    : null}
                </Select>
                // <Input placeholder="请输入" />
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={12}>
            <Form.Item label="授权操作类型" {...formItemLayout}>
              {getFieldDecorator('operType', {
                rules: [
                  {
                    required: true,
                    message: '授权操作类型不能为空',
                  },
                ],
              })(
                <Select placeholder="请选择">
                  {valuesList[1].length
                    ? valuesList[1].map(item => (
                      <Option value={`${item.value}`} key={item.value}>
                        {item.name}
                      </Option>
                    ))
                    : null}
                </Select>
              )}
            </Form.Item>
          </Col>
          {
            visiable ? (
              <Col span={12}>
                <Form.Item label="是否默认角色" {...formItemLayout}>
                  {getFieldDecorator('defaultFlag', {
                    rules: [
                      {
                        required: true,
                        message: '默认角色不能为空',
                      },
                    ],
                  })(
                    <Select placeholder="请选择">
                      {valuesList[2].length
                        ? valuesList[2].map(item => (
                          <Option value={`${item.value}`} key={item.value}>
                            {item.name}
                          </Option>
                        ))
                        : null}
                    </Select>
                  )}
                </Form.Item>
              </Col>
            ) : (
              null
            )
          }
        </Row>
        <Row>
          <Col span={24}>
            <Form.Item label="备注" {...textAreaFormItemLayout}>
              {getFieldDecorator('remark')(<TextArea rows={4} placeholder="请输入" />)}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Spin>
  );
}

export default Form.create()(AddReleRole);
