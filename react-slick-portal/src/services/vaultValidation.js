import { message } from 'antd';
import { queryIsVault } from '@/services/vaultApproval';

/**
 * 通用金库验证函数
 */
export const checkVaultValidation = async (dispatch, functionString = 'default', params) => {
  try {
    const resp = await queryIsVault(params);
    if (resp.resultCode === 'TRUE' && resp.resultObject.GOLDBANK_STATE !== 'false') {
      if (resp.resultObject.appOperJKStatus && resp.resultObject.appOperJKStatus.next === 'auth') {
        const approvalCreate = {
          isShowSecond: true,
          isTimesPass: false,
          approvalCreate: resp.resultObject.appOperJKStatus,
          functionString,
        };
        dispatch({
          type: 'vaultApproval/VaultUpData',
          payload: { approvalCreate },
        });
        return false;
      }

      const approvalOptionParams = {
        isShowfirst: true,
        isTimesPass: false,
        approvalOption: resp.resultObject,
        functionString,
      };
      dispatch({
        type: 'vaultApproval/VaultUpData',
        payload: { approvalOptionParams },
      });
      return false;
    }
    if (!resp.success) {
      message.error(resp.resultMsg);
      return false;
    }
    return true;
  } catch (error) {
    message.error('金库审批异常');
    return false;
  }
};
