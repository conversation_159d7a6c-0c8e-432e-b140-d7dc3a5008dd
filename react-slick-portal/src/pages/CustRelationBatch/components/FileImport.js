import React, { memo, useCallback, useState } from 'react';
import { Button, Drawer, Form, message, Select, Upload } from 'antd';
import PropTypes from 'prop-types';
import request from '@/utils/request';
import { TASK_TYPE } from '@/pages/CustRelationBatch/const';
import styles from './index.less';

const { Option } = Select;

const ALLOWED_FILE_TYPES = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];

const FileImport = memo(({ visible, onClose, form }) => {
  const { getFieldDecorator, validateFields, resetFields } = form;
  const [fileList, setFileList] = useState([]);
  const [uploading, setUploading] = useState(false);

  const handleDownloadTemplate = useCallback(() => {
    try {
      window.open('/portal/CustRelationBatchController/exportTemplate.do', '_blank');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('下载失败:', error);
      message.error('下载模板失败');
    }
  }, []);

  const handleRemoveFile = useCallback(() => {
    setFileList([]);
  }, []);

  const handleBeforeUpload = useCallback(file => {
    const isExcel = ALLOWED_FILE_TYPES.includes(file.type);
    if (!isExcel) {
      message.error('只能上传Excel文件！');
      return false;
    }
    setFileList([file]);
    return false;
  }, []);

  const handleUpload = useCallback(async () => {
    if (fileList.length === 0) {
      message.error('请选择要上传的文件');
      return;
    }

    try {
      // 先校验任务类型
      const values = await new Promise((resolve, reject) => {
        validateFields((err, formValues) => {
          if (err) {
            reject(new Error('请先选择任务类型'));
            return;
          }
          resolve(formValues);
        });
      });

      if (!values.taskType || !TASK_TYPE[values.taskType]) {
        message.error('请选择有效的任务类型');
        return;
      }

      setUploading(true);
      const { taskType } = values;

      const formData = new FormData();
      formData.append('file', fileList[0]);
      formData.append('dto', JSON.stringify({ taskType }));

      const response = await request('portal/CustRelationBatchController/custRelationDataImport.do', {
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
      });

      if (response.resultCode === 'TRUE') {
        setFileList([]);
        resetFields();
        message.success('上传成功');
        onClose();
      } else {
        message.error(response.resultMsg || '上传失败');
      }
    } catch (error) {
      message.error(error.message || '上传失败');
    } finally {
      setUploading(false);
    }
  }, [fileList, validateFields, resetFields, onClose]);

  const handleClose = useCallback(() => {
    resetFields();
    setFileList([]);
    onClose();
  }, [resetFields, onClose]);

  const uploadProps = {
    onRemove: handleRemoveFile,
    beforeUpload: handleBeforeUpload,
    fileList,
    maxCount: 1,
    showUploadList: {
      showRemoveIcon: true,
      showDownloadIcon: false,
    },
  };

  return (
    <Drawer
      title="文件导入"
      placement="right"
      width={400}
      onClose={handleClose}
      visible={visible}
      extra={(
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button onClick={handleClose}>取消</Button>
          <Button type="primary" onClick={handleUpload} loading={uploading}>
            确定
          </Button>
        </div>
      )}
    >
      <Form layout="vertical">
        <div className={styles.content}>
          <Form.Item label="任务类型" help={form.getFieldError('taskType')?.[0]} validateStatus={form.getFieldError('taskType') ? 'error' : ''}>
            {getFieldDecorator('taskType', {
              rules: [
                { required: true, message: '请选择任务类型' },
                {
                  validator: (_, value) => {
                    if (!value || !TASK_TYPE[value]) {
                      return Promise.reject(new Error('请选择有效的任务类型'));
                    }
                    return Promise.resolve();
                  },
                },
              ],
            })(
              <Select placeholder="请选择任务类型">
                {Object.entries(TASK_TYPE).map(([key, label]) => (
                  <Option key={key} value={key}>
                    {label}
                  </Option>
                ))}
              </Select>
            )}
          </Form.Item>

          <div className={styles.item}>
            <div className={styles.label}>模板下载</div>
            <Button onClick={handleDownloadTemplate} style={{ width: '100%' }}>
              下载模板
            </Button>
          </div>

          <div className={styles.item}>
            <div className={styles.label}>文件上传</div>
            <Upload {...uploadProps}>
              <Button style={{ width: '100%' }} disabled={fileList.length > 0}>
                选择文件
              </Button>
            </Upload>
            {fileList.length > 0 && (
              <Button type="primary" onClick={handleUpload} loading={uploading} style={{ width: '100%', marginTop: 8 }}>
                开始上传
              </Button>
            )}
          </div>
        </div>
      </Form>
    </Drawer>
  );
});

FileImport.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  form: PropTypes.object.isRequired,
};

FileImport.displayName = 'FileImport';

export default Form.create()(FileImport);
