.container {
  position: relative;
  padding: 8px;
}

.stepsContainer {
  display: flex;
  margin-bottom: 10px;
  gap: 10px;
  padding: 15px 20px 15px 8px;
  background: #FFFFFF;
  border-radius: 3px;

  .stepItem {
    flex: 1;
    height: 26px;
    line-height: 26px;
    text-align: center;
    background: #f0f2f5;
    color: rgba(0, 0, 0, 0.65);
    position: relative;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    cursor: default;

    &::before {
      content: '';
      position: absolute;
      left: 0px;
      top: 0;
      width: 0;
      height: 0;
      border-top: 13px solid transparent;
      border-bottom: 13px solid transparent;
      border-left: 13px solid #fff;
      z-index: 2;
    }

    &::after {
      content: '';
      position: absolute;
      right: -12px;
      top: 0;
      width: 0;
      height: 0;
      border-top: 13px solid transparent;
      border-bottom: 13px solid transparent;
      border-left: 13px solid #f0f2f5;
      z-index: 1;
    }

    &.active {
      background: linear-gradient(to right, #23A0DE, #72AAF8);
      color: #fff;

      &::after {
        border-left-color: #72AAF8;
      }
    }

    &.finished {
      background: #e6f7ff;
      color: #23A0DE;

      &::after {
        border-left-color: #e6f7ff;
      }
    }
  }
}

.footer {
  position: fixed;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 42px;
  border-top: 1px solid #e9e9e9;
  padding: 10px 16px;
  background: #fff;
  text-align: right;
}
