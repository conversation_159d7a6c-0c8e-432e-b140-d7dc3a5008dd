/**
  * @ Name:   未知高度垂直居中组件
  * @ Usage:  支持图片，单行或多行文字，以及图文混排
  * @ Note:   保证未知高度的.middle-item（以及它的子元素）垂直居于它所在的容器
              继承自http://thx.github.io/cube/doc/layout#section-4
  * @ 兼容:   IE7及以上
  * @ 用例：
  *   <div className="middle-item">
  *     <b className="middle-item-hack" />
  *     <div className="middle-item-body">
  *       insert content here
  *     </div>
  *   </div>
*/

/* 去除 inline-block 的空隙 */
.middle-item {
  font-size: 0;
  *word-spacing: -1px; /* IE6、7 */
  height: 100%; /* 继承父级高度 */
}
/* 修复 Safari 5- inline-block 的空隙 */
@media (-webkit-min-device-pixel-ratio: 0) {
  .middle-item {
    letter-spacing: -5px;
  }
}
/* 使用空标签生成一个高度100%的参照元素 */
.middle-item .middle-item-hack {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  font-size: 0;
  width: 0;
  height: 100%;
  vertical-align: middle;
}
.middle-item .middle-item-body {
  letter-spacing: normal;
  word-spacing: normal;
  display: inline-block;
  *display: inline;
  *zoom: 1;
  font-size: 12px;
  vertical-align: middle; /* 保证文字垂直居中 */
  padding: 0 !important; /* 防止设置边距导致居中失效 */
  margin: 0 !important;
  width: 100%; /* 保证连续字符也能居中 */
  white-space: normal; /* 保证连续字符换行 */
  word-wrap: break-word;
}
.middle-item .middle-item-img {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  width: 100%;
  text-align: center; /* 图片默认水平居中 */
  vertical-align: middle;
  padding: 0 !important; /* 防止设置边距导致居中失效 */
  margin: 0 !important;
  font-size: 0;
}
.middle-item img {
  vertical-align: middle; /* 去除现代浏览器 img 底部空隙 */
}
