import request from '@/utils/request';

export async function judgeMenu(params) {
  return request(
    `orgauth/FuncMenuController/judgeMenuPrivGrantById.do?menuId=${params.menuId}`,
    {
      data: { menuId: params.menuId },
      method: 'DELETE',
    }
  );
}

// 新增
export async function selectManualInfo(operationDocConfId) {
  return request(
    'portal/OperationDocConfController/selectById.do',
    {
      data: { operationDocConfId },
      method: 'GET',
    }
  );
}

// 新增
export async function addData(params) {
  return request(
    'portal/OperationDocConfController/addData.do',
    {
      data: { ...params },
      method: 'POST',
    }
  );
}

// 获取归属系统
export async function getSystemInfoList() {
  return request(
    'orgauth/SystemInfoController/getSystemInfoList.do',
    {
      method: 'GET',
    }
  );
}

/**
 * 获取业务数据枚举值
 * @returns
 */
export async function getValuesList({ busiNbr, propertyName }) {
  return request(
    'portal/DomainDataController/getValuesList.do',
    {
      data: {
        busiNbr,
        propertyName,
      },
      method: 'POST',
    }
  );
}

/**
 * 更新配置
 * @returns
 */
export async function updateData(params) {
  return request('portal/OperationDocConfController/updateData.do', {
    data: { ...params },
    method: 'POST',
  });
}

/**
 * 删除配置
 * @returns
 */
export async function deleteData(params) {
  return request('portal/OperationDocConfController/deleteData.do', {
    data: { ...params },
    method: 'POST',
  });
}

// 删除文件
export async function deleteFile(docNbr) {
  return request(
    `portal/FileStoreController/delete.do?docNbr=${docNbr}`,
    {
      method: 'GET',
    }
  );
}

// 获取文件数据
export async function queryByObjId(objId) {
  return request(
    `portal/FileStoreController/queryByObjId.do?objId=${objId}&objType=1800`,
    {
      method: 'GET',
    }
  );
}

// 获取文件数据
export async function downloadFile(docNbr) {
  return request(
    `portal/FileStoreController/download.do?docNbr=${docNbr}`,
    {
      method: 'GET',
    }
  );
}
