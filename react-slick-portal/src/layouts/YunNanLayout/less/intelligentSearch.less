@import './variables.less';

@media screen and (max-width: 1020px) {
  .searchInputBox {
    .searchBox {
      display: none;
    }
    .Icon {
      display: block;
    }
  }
}

@media screen and (min-width: 1021px) {
  .searchInputBox {
    .searchBox {
      display: block;
    }
    .Icon {
      display: none;
    }
  }
}


.searchInputBox {
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 400px;
  min-width: 60px;
  margin: 10px auto;
  line-height: 32px;


  .searchBox {
    flex: 1;
    overflow: hidden;
    height: 40px;
    background-color: white;
    border-radius: 4px;
    border: 1px solid transparent;
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    background-image: linear-gradient(to right, #fff, #fff), linear-gradient(180deg, rgba(1, 105, 255, 1), rgba(63, 159, 255, 1), rgba(78, 149, 255, 1), rgba(153, 70, 255, 1));

    input::placeholder {
      color: #23A0DE;
      font-size: 12px;
    }

    &.searchFocusBox {
      height: auto;
      border-radius: 4px;
      overflow: hidden;

      input{
        border-bottom: 1px solid #ddd;

        &:hover{
          border-color: #ddd;
        }
      }
    }
  }
  .input{
    height: 40px;
  }

  :global(.ant-input:not(:last-child)) {
    padding-right: 60px;
  }

  .letter-panel {
    position: relative;
    overflow: hidden;
    z-index: 1000;
    height: 280px;
    .letter-search {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      height: 36px;
      padding: 6px 16px;
    }
    .letter-category {
      position: absolute;
      top: 10px;
      right: 0;
      bottom: 0;
      left: 0;
      padding: 0 16px 8px;
      overflow-x: hidden;
      overflow-y: auto;
      .letter-title {
        padding: 6px 8px;
        color: @blue-6;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
      }
      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        li {
          &.ng-item {
            a {
              display: block;
              padding: 6px 8px;
              color: #666;
              line-height: 20px;
            }
          }
        }
        &::after {
          display: block;
          clear: both;
          height: 0;
          font-size: 0;
          visibility: hidden;
          content: ' ';
        }
      }
    }
  }

  :global{
    .ant-input{
      border: none;
      box-shadow: inset 30px 0px 34px 0px rgba(35, 160, 222, 0.2);
      &:focus{
        border: none;
        box-shadow: none;
      }

    }
    .ant-input-suffix{
      right: 8px;
      svg{
        margin-right: 6px;
      }
    }
  }
}
