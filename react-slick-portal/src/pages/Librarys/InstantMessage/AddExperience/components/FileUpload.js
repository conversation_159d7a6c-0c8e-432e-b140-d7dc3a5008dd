import React, { useState } from 'react';
import { connect } from 'dva';
import { Modal, Select, Upload, Form, message, Button, Icon } from 'antd';

const FileUpload = props => {
  const { user, form, showUpload, setShowUpload, fileList = [], setFileList, fileTypeList } = props;
  const { userInfo } = user;
  const { getFieldValue, setFieldsValue } = form;

  // 经验分类
  const knowledgeType = getFieldValue('knowledgeType');

  const [selectType, setSelectType] = useState('');
  const [curFile, setCurFile] = useState([]);

  return (
    <Modal
      title="附件上传"
      visible={showUpload}
      zIndex="1013"
      destroyOnClose
      onCancel={() => {
        setShowUpload(false);
      }}
      onOk={() => {
        const resultFiles = [...fileList, ...curFile];
        setFileList(resultFiles);
        setFieldsValue({
          files: resultFiles.map(item => ({
            attachId: item.docId,
            objType: item.type,
            keyValue1: '',
          })),
        });
        setShowUpload(false);
      }}
      okButtonProps={{ disabled: !curFile }}
    >
      <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
        <Form.Item label="附件类型" required>
          <Select
            value={selectType}
            onChange={v => {
                setSelectType(v);
              }}
          >
            {fileTypeList?.map(item => {
                const isNeed = fileTypeList[knowledgeType]?.includes(item.value);
                return (
                  <Select.Option value={item.value} key={item.value}>
                    <span style={isNeed ? { color: 'red' } : {}}>
                      {item.label}
                      {isNeed ? '（该类型文件必传）' : ''}
                    </span>
                  </Select.Option>
                );
              })}
          </Select>
        </Form.Item>
        <Form.Item label="附件" required>
          <Upload
            action="portal/FileStoreController/uploadFile.do"
            multiple
            beforeUpload={file => {
              const curFileExist = curFile.find(item => item.fileName === file.name && item.type === selectType);
              const allFileExist = fileList.find(item => item.fileName === file.name && item.type === selectType);
              if (curFileExist || allFileExist) {
                message.error(`${file.name}已上传`);
                return Promise.reject();
              }
              // 50MB = 50 * 1MB = 50 * 1024KB = 50 * 1024 * 1024
              if (file.size >= 50 * 1024 * 1024) {
                message.error('请上传小于50MB的文件');
                return Promise.reject();
              }
              if (file.size === 0) {
                message.error('请勿上传空文件!');
                return Promise.reject();
              }
              return Promise.resolve();
            }}
            onChange={fileInfo => {
              const { file } = fileInfo;
              if (file.status === 'done') {
                const fileObj = {
                  ...file.response[0],
                  createDate: new Date(),
                  type: selectType,
                  fileType: fileTypeList?.find(el => el.value === selectType)?.label,
                  createStaffName: userInfo?.userName,
                };
                setCurFile([...curFile, fileObj]);
              } else if (file.status === 'error') {
                message.error(`${file.name}上传异常`);
              }
            }}
            onRemove={() => {
              setCurFile([]);
            }}
          >
            <Button type="default" disabled={!selectType}>
              <Icon type="plus" /> <span>上传</span>
            </Button>
          </Upload>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default connect(({ login }) => ({ user: login.user }))(FileUpload);
