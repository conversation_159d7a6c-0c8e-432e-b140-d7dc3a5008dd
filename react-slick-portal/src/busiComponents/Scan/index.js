/* eslint-disable jsx-a11y/iframe-has-title */
/* 无纸化扫描 */
import React, { useEffect, useState } from 'react';
import { Icon, Input, message, Modal } from 'antd';
import { connect } from 'dva';
import { getDataDictByCode } from '@/services/common';
import request from '@/utils/request';

const Scan = props => {
  const {
    value,
    onChange,
    onCallback,
    certType,
    idenNbr,
    disabled = false,
    isNeedPhoto = '0',
    user: {
      userInfo: { externalUserInfos },
    },
  } = props;

  const [scanUrl, setScanUrl] = useState('');
  const getScanUrl = async () => {
    const res = await getDataDictByCode({
      groupCode: 'BUSINESS_SYSTEM_URL',
      paramCode: 'SCAN_URL',
    });
    if (res) {
      setScanUrl(res.paramValue);
    }
  };
  useEffect(() => {
    getScanUrl();
  }, []);
  const [visible, setVisible] = useState(false);
  const [iframeUrl, setIframeUrl] = useState('');
  const [timeStamp, setTimeStamp] = useState('');
  const [opId, setOpId] = useState('');
  const handleScan = () => {
    // 00 集团证件 04 经办人人像
    if (['00', '04'].includes(certType) && !idenNbr) {
      message.error('请输入证件号码!');
      return;
    }
    const timeStampTemp = new Date().getTime();
    setTimeStamp(timeStampTemp);
    const opIdStr = externalUserInfos.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000')?.externalUserId;
    setOpId(opIdStr);
    const params = {
      work_no: opIdStr,
      org_info: 710001,
      certType,
      time_stamp: timeStampTemp,
      img_flag: isNeedPhoto ? 'img_flag' : '',
      localization: 'Y',
    };

    if (!scanUrl) {
      message.error('证件扫描地址获取异常，请关闭重试');
      return;
    }
    setIframeUrl(`${scanUrl}&${new URLSearchParams(params)}`);
    setVisible(true);
  };

  // 获取身份扫描结果
  const handleSuccess = () => {
    request('portal/NoPaperInfoController/getImages.do', {
      method: 'POST',
      data: {
        OP_ID: opId,
        TIME_STAMP: timeStamp,
        // OP_ID: opId,
        // TIME_STAMP: 1743146187975,
      },
    }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE') {
        onChange(resultObject?.IDEN_NR); //  证件号码 IDEN_NR
        // 3, 回填证件信息
        onCallback(
          {
            idenNr: resultObject?.IDEN_NR, //  证件号码 IDEN_NR
            name: resultObject?.INDIVIDUAL_NAME, // 证件名称 INDIVIDUAL_NAME
            address: resultObject?.IDEN_ADDRESS, // 证件地址 IDEN_ADDRESS
            baseStr: resultObject?.BASE_STRING, // 芯片照 BASE_STRING
            imageStr: resultObject?.IMAGE_STRING, // 现场照 IMAGE_STRING
            nation: resultObject?.CUST_NATION, // 籍贯 CUST_NATION
          },
          timeStamp
        );
        setVisible(false);
      } else {
        message.error(resultMsg);
      }
    });
  };
  return (
    <div>
      <Input readOnly value={value} addonAfter={!disabled && <Icon type="scan" onClick={handleScan} />} disabled={disabled} />
      <Modal title="拍照" visible={visible} onCancel={() => setVisible(false)} onOk={handleSuccess} width="80%" style={{ height: 'auto' }}>
        <iframe
          src={iframeUrl}
          width="100%"
          height="100%"
          style={{ height: '100vh' }} // 使iframe高度自适应
        />
      </Modal>
    </div>
  );
};
export default connect(({ login }) => ({
  user: login.user,
}))(Scan);
