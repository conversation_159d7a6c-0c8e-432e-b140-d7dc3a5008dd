/* 订购列表 */
import React, { useEffect, useState } from 'react';
import { Tabs } from 'antd';
import classNames from 'classnames';
import { tabs } from './consts';
import { Subscriber } from '@/utils/PubSubHelper';

const { TabPane } = Tabs;
const Index = () => {
  // 添加当前激活的标签页状态
  const [activeKey, setActiveKey] = useState(tabs[0].key);
  // 是否显示当前第二级选项卡（显示第三级时隐藏第二级）
  const [visibleCurrentTab, setVisibleCurrentTab] = useState(true);
  // 处理标签页切换
  const handleTabChange = key => {
    setActiveKey(key);
  };
  useEffect(() => {
    const subscriber = new Subscriber({
      handlers: {
        toggleTab: payload => {
          setVisibleCurrentTab(payload.visible);
        },
      },
    });
    return () => {
      subscriber.unsubscribeAll();
    };
  });
  return (
    <>
      <div>
        <Tabs
          activeKey={activeKey}
          onChange={handleTabChange}
          className={classNames('tabsCustom2', {
            hideTab: !visibleCurrentTab,
          })}
        >
          {tabs.map(({ name, key, component: Component }) => (
            <TabPane tab={name} key={key}>
              <Component />
            </TabPane>
          ))}
        </Tabs>
      </div>
    </>
  );
};
export default Index;
