import mockjs from 'mockjs';
import { delay } from 'roadhog-api-doc';
import defaultSettings from '@/defaultSettings';

const proxy = {
  'POST /portal/GroupCommonExtController/queryTaxPayerInfoByCond.do': (req, res) => {
    res.status(200).send({"resultCode":"TRUE","resultMsg":"操作成功","resultObject":{"rspParam":{"busiInfo":{"outData":[{"PRINT":"1","REGION_ID":"478","CREATE_OP_ID":"1","CUST_ID":"**********","TAX_BANK_ACCT":"工行铁路支行","VALID_DATE":"2015-10-10 11:27:59","ORG_ID":"1","SERV_PROTOCOL_PATH":"","CONTACTOR_PHONE":"","ENTERPRISE_NAME":"呼和浩特铁路局呼和浩特通信段","OP_ID":"1","TAX_QUALIFICATION":"1","TAX_ADDRESS":"内蒙古自治区呼和浩特市新城区车站西街22号","CERT_END_DATE":"","REMARKS":"NEWCRM数据割接 by guo","LICENSE_BEGIN_DATE":"2013-12-13 00:00:00","CLASS_ID":"01","INVOICE_PRINT_TYPE":"2","SERV_END_DATE":"","TAX_STATUS":"1","MGMT_DISTRICT":"478","CERT_BEGIN_DATE":"","TAX_ID":"91150102699477020L","CREATE_ORG_ID":"1","EXPIRE_DATE":"2099-12-31 00:00:00","CERTIFICATE_FILE_PATH":"","SPECIAL_TICKET_CONTACTOR":"","TAX_ACCT_NUM":"06020**************-103","CUSTOMER_SERV_LEVEL":"1","DONE_DATE":"2017-03-17 01:15:44","DONE_CODE":"","LAST_AUDIT_DATE":"2017-01-09 11:22:05","ENTERPRISE_ATTR":"0","PRE_PRINT":"0","MGMT_COUNTY":"7805","DATA_STATUS":"1","CREATE_DATE":"2015-10-10 11:27:59","SERV_BEGIN_DATE":"","IS_DIRECT":"0","DIRECT_CUST_CODE":"","TAX_ACCT_NAME":"呼和************","TAX_TEL":"04712248916","LICENSE_END_DATE":"2032-12-31 00:00:00"}]},"pubInfo":{"message":"success","code":"0","reqSerialNo":"2015060611223320345443"}}},"success":true});
  },
};

export default delay(proxy, defaultSettings.delay);
