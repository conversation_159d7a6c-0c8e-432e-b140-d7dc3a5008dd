import request from '@/utils/request';

// 在途工单列表
export function getUnfinishOrderList(data) {
  return request('portal/GroupTransitOrderController/queryGroupTransitOrder.do', {
    method: 'POST',
    data,
  });
}
// 撤单 cancelSdWanOrder
export function cancelSdWanOrder(data) {
  return request('portal/ContractController/qryContractByCustId.do', {
    method: 'POST',
    data,
  });
}
// 在途预受理工单列表
export function getUnfinishPreOrderList(data) {
  return request('portal/GroupOrderController/queryOrderFromOrderCenter.do', {
    method: 'POST',
    data,
  });
}
