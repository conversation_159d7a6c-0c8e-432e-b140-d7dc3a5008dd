import React, { useState, useEffect } from 'react';
import { Card, Empty } from 'antd';
import { Chart, Geom, Axis, Tooltip } from 'bizcharts';
import forEach from 'lodash/forEach';
import request from '@/utils/request';

const cache = {};

/**
 * 异步获取数据后，需要缓存起来。避免组件未销毁时重复发起请求
 */
function OrderGrowthRate2() {
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState([]);

  useEffect(() => {
    async function getInitData() {
      setLoading(true);
      // 订单增长趋势数据获取
      const response1 = await request('orgauth/WorkbenchController/qryOrderWorkNumOfMonth.do', {
        method: 'get',
      });

      if (
        response1 !== null &&
        response1.resultCode === '0' &&
        response1.resultObject.data.resultCode === '0'
      ) {
        let data = [];
        const rstData = response1.resultObject.data.resultObject;
        if (rstData) {
          forEach(rstData, (value, key) => data.push({ x: key, y: value }));
          // 所有项的y都是0 约等于“没记录”，避免柱状图出现“一条线”
          if (data.filter(item => item.y > 0).length === 0) {
            data = [];
          }
          cache.chartData = data;
          setChartData(data);
        }
      }
      setLoading(false);
    }
    if (Object.hasOwnProperty.call(cache, 'chartData') === false) {
      getInitData();
    }
  }, []);

  let _chartData;

  if (cache.chartData === undefined) {
    _chartData = chartData;
  } else {
    _chartData = cache.chartData;
  }
  return (
    <Card bordered loading={loading}>
      <h4>订单增长趋势</h4>
      {_chartData.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无数据"
          style={{ marginTop: 80 }}
        />
      ) : (
        <Chart
          animate
          padding={[36, 25, 30, 35]}
          scale={{
            x: {
              type: 'cat',
              range: [0, 1],
            },
            y: {
              min: 0,
            },
          }}
          height={254}
          forceFit
          data={_chartData}
        >
          <Axis key="axis-x" name="x" />
          <Axis key="axis-y" name="y" />
          <Tooltip showTitle={false} crosshairs={false} />
          <Geom
            type="area"
            position="x*y"
            color="rgba(24, 144, 255, 0.2)"
            tooltip
            shape="smooth"
            style={{
              fillOpacity: 1,
            }}
          />
          <Geom
            type="line"
            position="x*y"
            shape="smooth"
            color="#1089ff"
            size={2}
            tooltip={[
              'x*y',
              (x, y) => ({
                name: `${x}`,
                value: `新增订单：${y}个`,
              }),
            ]}
          />
        </Chart>
      )}
    </Card>
  );
}

export default OrderGrowthRate2;
