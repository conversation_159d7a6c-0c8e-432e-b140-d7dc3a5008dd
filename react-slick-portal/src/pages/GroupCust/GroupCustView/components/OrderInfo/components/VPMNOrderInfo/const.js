/* VPMN产品订购信息 */

import { ACTIVATE_TYPE, IS_OR_NO_TYPE, REGION_ID_TYPE, REMOVE_TYPE, SCPID_TYPE, SPECIAL_GROUP_TYPE, VPMN_GROUP_TYPE } from '@/utils/consts';

export const VPMNOrderInfoFields = [
  // 基础信息
  { name: '用户标识', key: 'GRP_SUBSCRIBER_INS_ID', span: 1 },
  { name: '客户编码', key: 'CUST_ID', span: 1 },
  { name: '集团编码', key: 'GROUP_ID', span: 1 },
  { name: '集团名称', key: 'VPN_NAME', span: 1 },

  { name: 'VPMN集团号', key: 'VPN_NO', span: 1 },
  { name: '业务区', key: 'SERVICE_AREA_CODE', span: 1 },
  { name: '归属SCP', key: 'SCP_NAME', span: 1, options: SCPID_TYPE },
  { name: 'VPMN集团类型', key: 'GROUP_TYPE_NAME', span: 1, options: VPMN_GROUP_TYPE },

  { name: '特殊集团类型', key: 'SPECIAL_GROUP_TYPE_NAME', span: 1, options: SPECIAL_GROUP_TYPE },
  { name: '归属省份', key: 'PROVINCE', span: 1 },
  { name: '业务激活标志', key: 'SUB_STATE', span: 1, options: ACTIVATE_TYPE },
  { name: '是否联合V网', key: 'IS_UN_VPMN', span: 1, options: IS_OR_NO_TYPE },

  { name: '是否IMS业务', key: 'IS_IMS', span: 1, options: IS_OR_NO_TYPE },
  { name: '主叫号码显示方式', key: 'DISPLAY', span: 1 },
  { name: '集团包月功能标识', key: 'MONTHFLAG', span: 1 },
  { name: '集团月费用限额', key: 'LIMIT_FEE', span: 1 },

  { name: '设备生产商', key: 'FACTORY', span: 1 },
  { name: '费率索引', key: 'FEEINDEX', span: 1 },
  { name: '计费服务计划', key: 'BILL_SERV_PLAN_ID', span: 1 },
  { name: '话单优惠方式编码', key: 'BILL_DISCOUNT_TYPE_ID', span: 1 },
  { name: '联合V网短号前缀长度', key: 'PREFIXNUMLEN', span: 1 },

  { name: '最大闭合用户群数', key: 'MAX_CLOSE_NUM', span: 1 },
  { name: '单个闭合用户群容量', key: 'MAX_NUM_CLOSE', span: 1 },
  { name: '用户最大闭合群数', key: 'PERSON_MAXCLOSE', span: 1 },
  { name: '最大网外号码总数', key: 'MAX_OUTNUM', span: 1 },
  { name: '集团最大用户数', key: 'MAX_USERS', span: 1 },
  { name: '资费套餐类型', key: 'PKG_TYPE', span: 1 },

  { name: '封锁标志', key: 'LOCK_TAG', span: 1, options: IS_OR_NO_TYPE },
  { name: '主叫漫游权限', key: 'CALL_ROAM_TYPE', span: 1 },
  { name: '被叫漫游权限', key: 'BYCALL_ROAM_TYPE', span: 1 },

  { name: '集团短号范围最小值', key: 'START_ISDN', span: 1 },
  { name: '集团短号范围最大值', key: 'END_ISDN', span: 1 },

  { name: '联合V网集团前缀', key: 'PREFIXNUM', span: 1 },
  { name: '加入联合V网时间', key: 'JOIN_UN_VPMN_DATE', span: 1 },
  { name: '联合V网VPMN编码', key: 'COMPVPMN_ID', span: 1 },
  { name: '联合V网用户编码', key: 'COMP_SUBINSID', span: 1 },
  { name: '联合V网类型', key: 'UN_VPMN_FLAG', span: 1 },
  { name: '联合V网费率索引', key: 'COMPFEEINDEX', span: 1 },

  { name: '集团自有服务中心号码', key: 'CNTRXCALLCNTR', span: 1 },
  { name: '集团自有人工台号码', key: 'CNTRXATT', span: 1 },
  { name: '集团总机接续前缀', key: 'ADMMANNO', span: 1 },
  { name: 'PBXCONNECT前缀', key: 'PBXCONNPREF', span: 1 },
  { name: '总机服务时间', key: 'EXCHGSTART', span: 1 }, // 组合字段
  { name: '集团总机业务标志', key: 'EXCHGFLG_CLASS', span: 1 }, // 组合字段

  { name: '促销计划', key: 'SPROM_PLAN', span: 1 },
  { name: '建档时间', key: 'OPEN_DATE', span: 1 },
  { name: '注销标志', key: 'REMOVE_TAG', span: 1, options: REMOVE_TYPE },
  { name: '注销时间', key: 'REMOVE_DATE', span: 1 },
  { name: '生效时间', key: 'VALID_DATE', span: 1 },
  { name: '失效时间', key: 'EXPIRE_DATE', span: 1 },
  { name: '状态', key: 'STATUS', span: 1, options: REMOVE_TYPE },
  { name: '创建日期', key: 'CREATE_DATE', span: 1 },
  { name: '创建操作员', key: 'CREATE_OP_ID', span: 1 },
  { name: '创建组织', key: 'CREATE_ORG_ID', span: 1 },
  { name: '操作日期', key: 'DONE_DATE', span: 1 },
  { name: '操作员', key: 'OP_ID', span: 1 },
  { name: '操作组织编号', key: 'ORG_ID', span: 1 },
  { name: '管理地区', key: 'MGMT_DISTRICT', span: 1, options: REGION_ID_TYPE },
  { name: '管理县市', key: 'MGMT_COUNTY', span: 1 },
  { name: '数据归属地区', key: 'REGION_NAME', span: 1, options: REGION_ID_TYPE },
  { name: '备注', key: 'REMARK', span: 3 },
];
