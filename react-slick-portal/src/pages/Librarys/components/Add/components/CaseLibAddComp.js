import React, { useState, useEffect } from 'react';
import { Col, Form } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { renderFormItem, renderRows, validatePhone } from '../const';
import ProdMultiModal from './ProdMultiModal';
import ProjectMultiModal from './ProjectMultiModal';
import { TYPE_VALUE, MODE } from '@/pages/Librarys/const';
import SignDepartComp from './SignDepartComp';
import PartnerSelect from './PartnerSelectComp';
import { isEmptyStr } from '@/utils/utils';

const fields = [
  {
    label: '案例名称',
    type: 'input',
    key: 'knowledgeName',
    span: 2,
    rules: [{ required: true, message: '请输入' }],
  },
  {
    label: '案例编号',
    type: 'input',
    key: 'knowledgeNbr',
    span: 1,
    rules: [
      // { required: true, message: '请输入' },
    ],
    disabled: true,
  },
  {
    label: '项目名称',
    type: 'select',
    key: 'projectName',
    custom: true,
    span: 1,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '项目类别',
    type: 'input',
    key: 'projectType',
    span: 1,
    disabled: true,
  },
  {
    label: '项目全网编码',
    type: 'input',
    key: 'projectCode',
    span: 1,
    disabled: true,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '归属地市',
    type: 'select',
    key: 'regionId',
    placeholder: '请选择',
    span: 1,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '归属区县',
    type: 'select',
    key: 'areaId',
    placeholder: '请先选择归属地市',
    span: 1,
    allowClear: true,
    rules: [
      // { required: true, message: '请选择' },
    ],
  },
  {
    label: '案例类型',
    type: 'select',
    key: 'knowledgeType',
    placeholder: '请选择',
    span: 1,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '9大行业',
    type: 'select',
    key: 'industryType',
    placeholder: '请选择',
    span: 1,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '一级行业',
    type: 'select',
    key: 'industryFirstType',
    placeholder: '请先选择9大行业',
    span: 1,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '二级行业',
    type: 'select',
    key: 'industrySubType',
    placeholder: '请先选择一级行业',
    span: 1,
    allowClear: true,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '所属产品',
    type: 'select',
    key: 'prodType',
    span: 1,
    custom: true,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '落地单位',
    type: 'select',
    key: 'osnprovince',
    placeholder: '请选择',
    span: 1,
    custom: true,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '落地年份',
    type: 'yearPicker',
    key: 'osnYear',
    placeholder: '请选择',
    span: 1,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '客户名称',
    type: 'input',
    key: 'customer',
    span: 1,
    rules: [{ required: true, message: '请输入' }],
  },
  {
    label: '是否投招标',
    type: 'select',
    key: 'isinvite',
    placeholder: '请选择',
    span: 1,
    rules: [
      // { required: true, message: '请选择' },
    ],
    optionKey: 'isinvite',
  },
  {
    label: '签约主体',
    type: 'select',
    key: 'signdepart',
    placeholder: '请选择',
    span: 1,
    custom: true,
    rules: [
      // { required: true, message: '请选择' },
    ],
  },
  {
    label: '中标金额（万元）',
    type: 'input',
    key: 'tonedmoney',
    span: 1,
    rules: [
      // { required: true, message: '请输入' },
    ],
  },
  {
    label: '合作方名称',
    type: 'select',
    key: 'partnerName',
    span: 1,
    custom: true,
    rules: [{ required: true, message: '请输入' }],
  },
  {
    label: '合作方联系人',
    type: 'input',
    key: 'workerName',
    span: 1,
    disabled: true,
  },
  {
    label: '合作方联系人电话',
    type: 'input',
    key: 'workerTel',
    span: 1,
    disabled: true,
  },
  {
    label: '合作方类型',
    type: 'select',
    key: 'workerDepart',
    optionKey: 'workerDepart',
    span: 1,
    disabled: true,
  },
  {
    label: '案例联系人姓名',
    type: 'input',
    key: 'contactName',
    span: 1,
    rules: [{ required: true, message: '请输入' }],
  },
  {
    label: '案例联系人电话',
    type: 'input',
    key: 'contactPhone',
    span: 1,
    rules: [{ required: true, message: '请输入' }, { validator: validatePhone }],
  },
  {
    label: '案例联系人邮箱',
    type: 'input',
    key: 'contactEmail',
    span: 1,
    rules: [
      // { required: true, message: '请输入' },
    ],
  },
  {
    label: '案例维护人姓名',
    type: 'input',
    key: 'createStaffName',
    span: 1,
    rules: [{ required: true, message: '请输入' }],
    disabled: true,
  },
  {
    label: '案例维护人电话',
    type: 'input',
    key: 'defandTel',
    span: 1,
    rules: [
      // { required: true, message: '请输入' },
    ],
    disabled: true,
  },
  {
    label: '案例维护人邮箱',
    type: 'input',
    key: 'defandMail',
    span: 1,
    rules: [
      // { required: true, message: '请输入' },
    ],
    disabled: true,
  },
  {
    label: '生效时间(上架)',
    type: 'input',
    key: 'shelfTime',
    span: 1,
    disabled: true,
  },
  {
    label: '失效时间(下架)',
    type: 'input',
    key: 'shelvesTime',
    span: 1,
    disabled: true,
  },
  {
    label: '归属人单位',
    type: 'select',
    key: 'userDepart',
    custom: true,
    span: 1,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '通用标签',
    type: 'select',
    key: 'generalizedLabel',
    optionKey: 'caseGeneralizedLabel',
    span: 1,
  },
  {
    label: '案例关键词(标签)',
    type: 'input',
    key: 'knowledgeKeyword',
    span: 3,
    // offset: 1,
    placeholder: '请用半角逗号，如：电力,5G,智慧工厂,尊享',
    rules: [{ required: true, message: '请输入' }],
  },
  {
    label: '案例简介',
    type: 'textarea',
    key: 'knowledgeDesc',
    span: 3,
    // offset: 1,
    placeholder: '请输入',
    rules: [{ required: true, message: '请输入' }],
  },
];

/**
 * 案例库基本信息组件
 * @param {props}} props
 * @returns
 */
const CaseLibAddComp = props => {
  const { form, useLibAdd, type, mode, colFields, knowledgeDetailInfo, user } = props;
  const {
    cityList,
    regionList,
    industryList,
    subIndustryList,
    firstIndustryList,
    onChangeCity,
    onChangeIndustry,
    onChangeIndustryFirst,
    attrs,
  } = useLibAdd;
  const { getFieldDecorator, setFieldsValue } = form;

  const [dateOpen, setDateOpen] = useState(false);
  const [colFieldsChange, setColFieldsChange] = useState(false);
  const [partnerInitMark, setPartnerInitMark] = useState(true);

  // 监听colFields并以此驱动详情页内ComboGrid更新，防止详情页已渲染而colFields值尚为空的情况
  useEffect(() => {
    setColFieldsChange(!colFieldsChange);
  }, [colFields]);

  const getInitValue = key => {
    // 编辑模式
    if (mode === MODE.UPDATE) {
      if (colFields.includes(key)) {
        const _attrVal = knowledgeDetailInfo?.knowledgeAttrs?.find(el => el.attrNbr === key)?.attrValue;
        if (isEmptyStr(_attrVal)) {
          return undefined;
        }

        if (['osnYear'].includes(key)) {
          return moment(_attrVal);
        }
        // 落地单位、签约主体默认值
        if (['osnprovince', 'signdepart'].includes(key)) {
          const _nameVal = knowledgeDetailInfo?.knowledgeAttrs?.find(el => el.attrNbr === `${key}Name`)?.attrValue;
          return [{ org_code: _attrVal, org_name: _nameVal }];
        }
        // 纵表字段
        return _attrVal;
      }
      // 特殊纵表字段：归属人单位默认值
      if (key === 'userDepart') {
        const _idVal = knowledgeDetailInfo?.knowledgeAttrs?.find(el => el.attrNbr === 'userDepartId')?.attrValue;
        const _nameVal = knowledgeDetailInfo?.knowledgeAttrs?.find(el => el.attrNbr === 'userDepartName')?.attrValue;
        return _idVal && [{ org_code: _idVal, org_name: _nameVal }];
      }
      // 合作方名称默认值
      if (key === 'partnerName') {
        return knowledgeDetailInfo?.partnerName && [{ partnerName: knowledgeDetailInfo?.partnerName }];
      }
      return knowledgeDetailInfo[key];
    }
    // 维护人信息默认回显当前用户，新增时不传值
    if (key === 'createStaffName') {
      return user?.userInfo?.userName;
    }
    if (key === 'defandTel') {
      return user?.userInfo?.mobilePhone;
    }
    if (key === 'defandMail') {
      return user?.staffInfo?.email;
    }
    return undefined;
  };
  const project = selectedRows => {
    setFieldsValue({
      projectType: selectedRows[0].projectType,
    });
    setFieldsValue({
      projectCode: selectedRows[0].projectCode,
    });
    setFieldsValue({
      projectName: selectedRows[0].projectName,
    });
  };

  const renderFields = row => {
    const onChange = e => {
      const val = e?.target?.value || e;
      if (row.key === 'regionId') {
        onChangeCity(val);
        setFieldsValue({
          areaId: undefined,
        });
      } else if (row.key === 'industryType') {
        const pcode = industryList?.filter(item => item.value === val)?.[0].PCODE;
        onChangeIndustry(pcode);
        setFieldsValue({
          industryFirstType: undefined,
          industrySubType: undefined,
        });
      } else if (row.key === 'industryFirstType') {
        const pcode = firstIndustryList?.filter(item => item.value === val)?.[0].pcode;
        onChangeIndustryFirst(pcode);
        setFieldsValue({
          industrySubType: undefined,
        });
      }
    };
    let comp = null;
    let options = [];
    switch (row.key) {
      case 'regionId':
        options = cityList?.map(item => ({ ...item, value: item.id, label: item.name }));
        break;
      case 'areaId':
        options = regionList?.map(item => ({ ...item, value: item.id, label: item.name }));
        break;
      case 'industryType':
        options = industryList;
        break;
      case 'industryFirstType':
        options = firstIndustryList;
        break;
      case 'industrySubType':
        options = subIndustryList;
        break;
      case 'knowledgeType':
        options = attrs.knowledgeType?.filter(item => item.desc === TYPE_VALUE[type]);
        break;
      default:
        // 从主数据获取
        options = attrs[row?.optionKey] || [];
        break;
    }

    if (row.custom) {
      // 产品
      if (row.key === 'prodType') {
        comp = <ProdMultiModal />;
      }
      if (row.key === 'projectName') {
        comp = <ProjectMultiModal list={knowledgeDetailInfo.knowledgeAttrs} project={project} />;
      }
      if (['osnprovince', 'signdepart', 'userDepart'].includes(row.key)) {
        comp = <SignDepartComp colFieldsChange={colFieldsChange} />;
      }
      if (['partnerName'].includes(row.key)) {
        comp = (
          <PartnerSelect
            colFieldsChange={colFieldsChange}
            onChange={val => {
              // 编辑模式下，赋初始时跳过，因为该组件还未发送请求，无该对象数据
              if (mode === MODE.UPDATE && partnerInitMark) {
                setPartnerInitMark(false);
                return;
              }
              setFieldsValue({
                workerName: val?.[0]?.contactName,
                workerTel: val?.[0]?.contactPhone,
                workerDepart: val?.[0]?.workerDepart ? val[0].workerDepart : '9',
              });
            }}
          />
        );
      }
    } else {
      comp = renderFormItem({
        ...row,
        onChange,
        options,
        dateOpen,
        setDateOpen,
        setFieldsValue,
      });
    }

    // 编辑模式下默认值获取
    const initialValue = getInitValue(row.key);
    return (
      <Col span={(row.span || 1) * 8} offset={(row?.offset || 0) * 8} key={row.key}>
        <Form.Item label={row.label}>
          {getFieldDecorator(row.key, {
            rules: row?.rules || [],
            initialValue,
          })(comp)}
        </Form.Item>
      </Col>
    );
  };

  return (
    <div>
      {/* <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}> */}
      <Form>{renderRows(fields, renderFields)}</Form>
    </div>
  );
};

export default connect(({ login }) => ({
  user: login.user,
}))(CaseLibAddComp);
