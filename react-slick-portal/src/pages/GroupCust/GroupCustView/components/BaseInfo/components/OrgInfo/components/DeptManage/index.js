import React, { useEffect, useRef } from 'react';
import { Card, Icon, Tooltip, Modal } from 'antd';
import { connect } from 'dva';
import styles from './styles.less';
import DeptTree from './components/DeptTree';
import DeptInfo from './components/DeptInfo';
import DeptMember from './components/DeptMember';

const DeptManageContent = ({ size, dispatch, deptBehavior, loading }) => {
  const memberEl = useRef();
  const handleAdd = () => {
    // 检查当前是否有未保存的编辑状态
    if (deptBehavior !== 'view') {
      Modal.confirm({
        title: '提示',
        content: '当前有未保存的编辑内容，是否继续？',
        onOk: () => {
          dispatch({ type: 'deptManage/setDeptInfo', payload: null });
          dispatch({ type: 'deptManage/selectDept', payload: null });
          dispatch({ type: 'deptManage/setDeptBehavior', payload: 'add' });
          dispatch({ type: 'deptManage/setMembers', payload: [] });
        },
      });
    } else {
      dispatch({ type: 'deptManage/setDeptInfo', payload: null });
      dispatch({ type: 'deptManage/selectDept', payload: null });
      dispatch({ type: 'deptManage/setDeptBehavior', payload: 'add' });
      dispatch({ type: 'deptManage/setMembers', payload: [] });
    }
  };
  // 初始化
  useEffect(() => {
    dispatch({ type: 'deptManage/setDeptInfo', payload: null });
    dispatch({ type: 'deptManage/selectDept', payload: null });
    dispatch({ type: 'deptManage/setMembers', payload: [] });
    dispatch({ type: 'deptManage/setDeptBehavior', payload: 'view' });
  }, []);

  // 计算内容区域最大高度
  const contentHeight = size.height - 60;

  return (
    <div className={styles.deptManage}>
      <div className={styles.deptManageLeft}>
        <Card
          className="cute2"
          title="集团部门"
          style={{ height: contentHeight, overflow: 'auto' }}
          extra={(
            <Tooltip title="新增部门">
              <Icon type="plus-circle" className={styles.icon_hover} disabled={loading} onClick={handleAdd} />
            </Tooltip>
          )}
        >
          <DeptTree />
        </Card>
      </div>
      <div className={styles.deptManageRight}>
        <DeptInfo />
        <DeptMember ref={memberEl} cRef={memberEl} />
      </div>
    </div>
  );
};

const mapStateToProps = ({ deptManage, setting, loading }) => ({
  size: setting.size,
  deptBehavior: deptManage.deptBehavior,
  selectedDeptId: deptManage.selectedDeptId,
  loading: loading.effects['deptManage/fetchDeptTree'],
});

export default connect(mapStateToProps)(DeptManageContent);
