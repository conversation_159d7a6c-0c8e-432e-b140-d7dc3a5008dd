/* eslint-disable dot-notation */
import React, { useEffect, useRef, useState } from 'react';
import { Button, Card, Form, message, Modal, Spin } from 'antd';
import { connect } from 'dva';
import cloneDeep from 'lodash/cloneDeep';
import moment from 'moment';
import PlanLibAddComp from './components/PlanLibAddComp';
import PermissionAddComp from './components/PermissionAddComp';
import style from './Add.module.less';
import FileUploadComp from './components/FileUploadeComp';
import useLibAdd from './useLibAdd';
import { addLib, updateLib, getLibDetail, queryLibFileds, getSubInsdustryList } from '../../services';
import { CONF_NAME, TYPE_VALUE, KNOWLEDGE_STATUS, LIBRARYS_TYPE, MODE } from '../../const';
import PageLoading from '@/components/PageLoading';
import CaseLibAddComp from './components/CaseLibAddComp';
import BidLibAddComp from './components/BidLibAddComp';
import CertLibAddComp from './components/CertLibAddComp';
import ProdLibAddComp from './components/ProdLibAddComp';
import CoopLibAddComp from './components/CoopLibAddComp';
import HardwareLibAddComp from './components/HardwareLibAddComp';
import ExperienceLibAddComp from './components/ExperienceLibAddComp';
import LinkAddComp from './components/LinkAddComp/LinkAddComp';
import ConfigAddComp from './components/ConfigAddComp';
import useAttrs from '../../useAttrs';
import CoopLibComp from './components/SpecialComp/CoopLibComp';

/**
 * 渲染基础信息表单
 * @param {*} param
 * @returns
 */
export const renderLibAddComp = ({ type, props, addFormRef }) => {
  switch (type) {
    case LIBRARYS_TYPE.PLAN:
      return <PlanLibAddComp {...props} />;
    case LIBRARYS_TYPE.CASE:
      return <CaseLibAddComp {...props} />;
    case LIBRARYS_TYPE.BID:
      return <BidLibAddComp {...props} />;
    case LIBRARYS_TYPE.CERT:
      return <CertLibAddComp {...props} />;
    case LIBRARYS_TYPE.PROD:
      return <ProdLibAddComp {...props} />;
    case LIBRARYS_TYPE.COOP:
      return <CoopLibAddComp {...props} />;
    case LIBRARYS_TYPE.HARDWARE:
      return <HardwareLibAddComp {...props} />;
    case LIBRARYS_TYPE.EXPERIENCE:
      return <ExperienceLibAddComp ref={addFormRef} {...props} />;
    default:
      return null;
  }
};

/**
 * 渲染权限和其他信息
 * @param {*} param
 * @returns
 */
export const renderPermissionComp = ({ type, props }) => {
  switch (type) {
    default:
      return <PermissionAddComp {...props} />;
  }
};

/**
 * 渲染环节处理
 * @param {*} param
 * @returns
 */
export const renderLinkComp = ({ type, props }) => {
  switch (type) {
    default:
      return <LinkAddComp {...props} />;
  }
};

/**
 * 渲染界面配置
 * @param {*} param
 * @returns
 */
export const renderConfigComp = ({ type, props }) => {
  switch (type) {
    default:
      return <ConfigAddComp {...props} />;
  }
};

/**
 * 新增组件
 * @param {props} props
 * @returns
 */
const Add = props => {
  const { type, title, form, mode = MODE.ADD, knowledgeInfoId, caseId, newBackfilling } = props;
  const useLibAddRes = useLibAdd({ type });
  const fileRef = useRef();
  const addFormRef = useRef();

  // 纵表字段
  const [fields, setFields] = useState([]);

  // 操作状态
  const [loading, setLoading] = useState(false);

  const [knowledgeDetailInfo, setKnowledgeDetailInfo] = useState({});
  const [syncVisible, setSyncVisible] = useState(false);
  const [spinning, setSpinning] = useState(false);

  const attrs = useAttrs();

  const loadInfo = async () => {
    const res = await getLibDetail(knowledgeInfoId);
    if (res?.resultCode === 'TRUE') {
      setKnowledgeDetailInfo(res.resultObject || {});

      // 初始化区县数据
      if (res.resultObject.regionId) {
        useLibAddRes.onChangeCity(res.resultObject.regionId);
      }
      // 初始化一级行业数据
      if (res.resultObject.industryType) {
        const _pcode = attrs.industryType?.find(item => item.value === res.resultObject.industryType)?.PCODE;
        useLibAddRes.onChangeIndustry(_pcode);
      }
      // 初始化二级行业数据
      if (res.resultObject.industryFirstType) {
        const _industryPcode = attrs.industryType?.find(item => item.value === res.resultObject.industryType)?.PCODE;
        const subRes = await getSubInsdustryList({
          stype: _industryPcode,
        });
        const _pcode = subRes?.find(item => item.value === res.resultObject.industryFirstType)?.pcode;
        useLibAddRes.onChangeIndustryFirst(_pcode);
      }
    }
  };

  useEffect(() => {
    if (mode === MODE.UPDATE) {
      loadInfo();
    }
  }, [knowledgeInfoId]);

  useEffect(() => {
    if (mode === MODE.ADD && newBackfilling) {
      setKnowledgeDetailInfo(newBackfilling);
    }
  }, [newBackfilling]);

  /**
   * 纵表字段值获取
   */
  const loadFields = async () => {
    const res = await queryLibFileds(TYPE_VALUE[type]);
    if (Array.isArray(res) && res?.length > 0) {
      setFields(res?.map(item => item.attrNbr));
    }
  };

  // 进行提交前的相关处理
  const getVal = value => {
    const val = cloneDeep(value);

    val.knowledgeClassType = TYPE_VALUE[type];
    if (val?.osnYear) {
      val.osnYear = moment(val.osnYear).format('YYYY');
    }
    // 日期参数格式化
    const dates = ['issueDate', 'expireDate', 'replaceDate'];
    dates.forEach(item => {
      if (val?.[item]) {
        val[item] = moment(val[item]).format('YYYY-MM-DD');
      }
    });
    const dates2 = ['storageTime', 'setUpTime'];
    dates2.forEach(item => {
      if (val?.[item]) {
        val[item] = moment(val[item]).format('YYYY-MM-DD HH:mm:ss');
      }
    });

    // 单位参数取值
    const orgs = ['osnprovince', 'signdepart', 'issueDepart'];
    orgs.forEach(item => {
      if (val?.[item]) {
        val[`${item}Name`] = val[item]?.[0]?.['org_name'];
        val[item] = val[item]?.[0]?.['org_code'];
      }
    });

    // 处理归属人单位信息
    val.userDepartId = val.userDepart?.[0]?.['org_code'];
    val.userDepartName = val.userDepart?.[0]?.['org_name'];
    delete val.userDepart;

    // 处理营业期限
    if (val.businessTime) {
      val.businessStartTime = moment(val.businessTime[0]).format('YYYY-MM-DD');
      val.businessEndTime = moment(val.businessTime[1]).format('YYYY-MM-DD');
      delete val.businessTime;
    }

    // 处理场景子库
    if (val.sceneSub) {
      val.sceneSub = val.sceneSub.length === props.attrs?.sceneSubNum ? props.attrs?.sceneSubValue : val.sceneSub.join();
    }

    // 处理公司资质
    if (val.companyQualify) {
      val.companyQualify = val.companyQualify.join();
    }

    // 处理合作方名称
    if (val.partnerName) {
      val.partnerName = val.partnerName[0].partnerName;
    }

    if (val.registerProvinceId) {
      val.registerProvinceName = useLibAddRes.getRegisterAreaNameByVal(val.registerProvinceId);
    }

    if (val.registerCityId) {
      val.registerCityName = useLibAddRes.getRegisterAreaNameByVal(val.registerCityId);
    }

    if (val.registerAreaId) {
      val.registerAreaName = useLibAddRes.getRegisterAreaNameByVal(val.registerAreaId);
    }

    delete val.createStaffName;

    if (type === 'experience') {
      val.detail = addFormRef?.current?.editor?.getHtml() || '';
    }
    return val;
  };

  const submit = isSync => {
    const value = form.getFieldsValue();
    const val = getVal(value);

    // 组装纵表字段
    const knowledgeAttrs = [];
    const keys = Object.keys(val);
    keys.forEach(key => {
      if (fields.includes(key)) {
        knowledgeAttrs.push({
          attrNbr: key,
          attrValue: val[key],
        });
        delete val[key];
      }
    });
    val.knowledgeAttrs = knowledgeAttrs;
    setLoading(true);
    setSpinning(true);
    if (mode === MODE.UPDATE) {
      if (knowledgeDetailInfo?.knowledgeProgress === '1000') {
        val.knowledgeProgress = '1100';
      }
      val.knowledgeInfoId = knowledgeInfoId;
      val.isSync = type === LIBRARYS_TYPE.PROD ? false : isSync;
      updateLib(val).then(res => {
        if (res.resultCode === 'TRUE') {
          message.success('操作成功', 1, () => {
            window.sessionStorage.setItem('schemeSharing', '');
            props.goToStep(1);
          });
        }
        setLoading(false);
      });
    } else {
      addLib(val).then(res => {
        if (res.resultCode === 'TRUE') {
          message.success('操作成功', 1, () => {
            window.sessionStorage.setItem('schemeSharing', '');
            props.goToStep(1);
          });
        } else {
          message.error(res?.resultMsg);
        }
        setLoading(false);
      });
    }
    setSpinning(false);
    setSyncVisible(false);
  };

  useEffect(() => {
    if (type) {
      loadFields();
    }
  }, [type]);

  if (mode === MODE.UPDATE && JSON.stringify(knowledgeDetailInfo) === '{}') {
    return <PageLoading />;
  }

  // 通过key获取纵表数据
  const getAttrValue = key => knowledgeDetailInfo?.knowledgeAttrs?.find(el => el.attrNbr === key)?.attrValue;
  // 通过key获取主表数据
  const getMainValue = key => knowledgeDetailInfo?.[key];

  return (
    <Spin spinning={loading}>
      <div className={style.wrap}>
        <Card title={`${title}${mode === MODE.UPDATE ? '编辑' : '新增'}`} bodyStyle={{ paddingBottom: '40px' }}>
          <Card title="资料基本信息">
            {renderLibAddComp({
              type,
              props: {
                form,
                type,
                useLibAdd: useLibAddRes,
                mode,
                knowledgeDetailInfo,
                colFields: fields,
                getAttrValue,
                getMainValue,
              },
              addFormRef,
            })}
          </Card>
          {type === LIBRARYS_TYPE.COOP && <CoopLibComp form={form} attrs={attrs} getAttrValue={getAttrValue} getMainValue={getMainValue} />}
          <FileUploadComp form={form} type={type} mode={mode} useLibAdd={useLibAddRes} ref={fileRef} knowledgeDetailInfo={knowledgeDetailInfo} />
          <Card title="权限和其他信息">
            {renderPermissionComp({
              type,
              props: {
                form,
                type,
                useLibAdd: useLibAddRes,
                mode,
                knowledgeDetailInfo,
                colFields: fields,
              },
            })}
          </Card>
          <Card title={CONF_NAME[type]}>
            {renderConfigComp({
              type,
              props: {
                form,
                type,
                useLibAdd: useLibAddRes,
                mode,
                knowledgeDetailInfo,
                colFields: fields,
              },
            })}
          </Card>
          {/* {
            mode !== 'add' && (
              <Card title="环节处理">
                {renderLinkComp({
                  type,
                  props: {
                    form,
                    type,
                    useLibAdd: useLibAddRes,
                    mode,
                    knowledgeDetailInfo,
                    colFields: fields,
                  },
                })}
              </Card>
            )
          } */}
          <div className={style.btn}>
            <div className={style.right}>
              {mode === MODE.ADD || knowledgeDetailInfo?.knowledgeProgress === '1000' ? (
                <Button
                  type="primary"
                  onClick={() => {
                    const value = form.getFieldsValue();
                    const val = getVal(value);

                    // 组装纵表字段
                    const knowledgeAttrs = [];
                    const keys = Object.keys(val);
                    keys.forEach(key => {
                      if (fields.includes(key)) {
                        knowledgeAttrs.push({
                          attrNbr: key,
                          attrValue: val[key],
                        });
                        delete val[key];
                      }
                    });
                    val.knowledgeAttrs = knowledgeAttrs;
                    setLoading(true);
                    if (mode === MODE.UPDATE) {
                      val.knowledgeInfoId = knowledgeInfoId;
                      updateLib({
                        ...val,
                        knowledgeProgress: '1000',
                      }).then(res => {
                        if (res.resultCode === 'TRUE') {
                          message.success('操作成功', 1, () => {
                            window.sessionStorage.setItem('schemeSharing', '');
                            props.goToStep(1);
                          });
                        } else {
                          message.error(res?.resultMsg);
                        }
                        setLoading(false);
                      });
                    } else {
                      addLib({
                        ...val,
                        knowledgeProgress: '1000',
                      }).then(res => {
                        if (res.resultCode === 'TRUE') {
                          message.success('操作成功', 1, () => {
                            window.sessionStorage.setItem('schemeSharing', '');
                            props.goToStep(1);
                          });
                        }
                        setLoading(false);
                      });
                    }
                  }}
                >
                  保存
                </Button>
              ) : null}
              <Button
                type="primary"
                onClick={() => {
                  // 校验必传文件是否已经添加
                  if (fileRef?.current?.onValidate()) {
                    form.validateFields(err => {
                      if (!err) {
                        // 如果存在caseId，且不为产品库\合作伙伴\硬件终端，需要确认是否触发集团同步
                        if (
                          caseId &&
                          knowledgeDetailInfo.statusCd !== KNOWLEDGE_STATUS.offShelf &&
                          ![LIBRARYS_TYPE.PROD, LIBRARYS_TYPE.COOP, LIBRARYS_TYPE.HARDWARE].includes(type)
                        ) {
                          setSyncVisible(true);
                        } else if (mode === MODE.UPDATE && knowledgeDetailInfo?.statusCd === '1001') {
                          Modal.confirm({
                            title: '是否确认提交',
                            onOk: async () => {
                              submit(false);
                            },
                          });
                        } else {
                          submit(false);
                        }
                      }
                    });
                  }
                }}
                style={{ marginLeft: '8px' }}
              >
                提交
              </Button>
              <Button
                onClick={() => {
                  window.sessionStorage.setItem('schemeSharing', '');
                  props.goToStep(1);
                }}
                style={{ marginLeft: '8px' }}
              >
                返回
              </Button>
            </div>
          </div>
        </Card>
        <Modal
          loading
          title="提示"
          visible={syncVisible}
          className={style.putModal}
          onCancel={() => {
            setSyncVisible(false);
          }}
          footer={(
            <Spin spinning={spinning}>
              <Button
                size="default"
                type="primary"
                onClick={() => {
                  submit(true);
                }}
              >
                提交，同步集团
              </Button>
              <Button
                size="default"
                type="primary"
                onClick={() => {
                  submit(false);
                }}
              >
                提交，不同步集团
              </Button>
              <Button
                size="default"
                onClick={() => {
                  setSyncVisible(false);
                }}
              >
                取消
              </Button>
            </Spin>
          )}
        >
          <font style={{ fontSize: '15px' }}>本次资料提交是否触发集团同步，请确认！</font>
        </Modal>
      </div>
    </Spin>
  );
};

export default connect(({ librarys }) => ({
  ...librarys,
}))(Form.create()(Add));
