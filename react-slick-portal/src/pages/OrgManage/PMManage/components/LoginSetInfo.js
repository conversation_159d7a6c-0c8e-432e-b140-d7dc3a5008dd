/* eslint-disable */
import React, { useState, useEffect, createContext, useRef } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Card,
  Form,
  Input,
  Icon,
  Button,
  Table,
  Modal,
  message,
  Select,
  Popover,
} from 'antd';
import SlickTable from '@/components/SlickTable';
import SlickUpload from '@/components/SlickUpload';
import useSlickTable from '@/hooks/UseSlickTable';
import styles from '../styles.less';
import PubSub from 'pubsub-js';
import request from '@/utils/request';
import pick from 'lodash/pick';
import omit from 'lodash/omit';
import AddLoginInfo from './AddLoginInfo';
const { Option } = Select;
const Search = Input.Search;

// 空对象校验
const judgeObj = obj => {
  if (Object.keys(obj).length == 0) return true;
  return false;
};

function LoginSetInfo(props) {
  const loginSetEl = useRef();
  const [visible, setVisible] = useState(false);
  const [params, setParams] = useState({
    sysUserId: props.tableData.sysUserId,
  });
  const [selectRows, setSelectRows] = useState([]);
  // 初始化变量
  const [variable, setVariable] = useState(false);

  const { tableProps } = useSlickTable(
    { pageSize: 5 },
    'orgauth/SysuserLoginLimitController/selectGridData.do',
    params
  );

  useEffect(() => {
    if (!judgeObj(props.tableData) && variable) {
      setParams({
        ...params,
        sysUserId: props.tableData.sysUserId,
        gb_reset: true,
      });
      setSelectRows([]);
    }
  }, [props.tableData]);

  useEffect(() => {
    setVariable(true);
  }, []);

  const handleSelectRows = value => {
    setSelectRows(value);
  };

  // 列表删除
  const handleDelete = () => {
    Modal.confirm({
      content: '确定进行删除？',
      onOk() {
        let arr = [];
        selectRows.map(item => {
          arr.push({
            id: item.id,
          });
        });
        request('orgauth/SysuserLoginLimitController/deleteLoginLimitsByIds.do', {
          data: arr,
          method: 'DELETE',
        }).then(res => {
          if (res) {
            message.success('删除成功！');
            setParams({
              ...params,
            });
            setSelectRows([]);
          } else {
            message.error(`删除失败！`);
          }
        });
      },
    });
  };

  const handleAdd = () => {
    if (props.tableData.statusCd === '1100') {
      message.error('用户已失效不能进行相关操作！');
    } else {
      setVisible(true);
    }
    //
  };

  const handleTakeEff = () => {
    let arr = [];
    for (let i in selectRows) {
      if (selectRows[i].statusCd === '1000') {
        arr = [];
        break;
      } else {
        arr.push({ loginLimitId: selectRows[i].loginLimitId });
      }
    }
    if (arr.length > 0) {
      request('orgauth/SysuserLoginLimitController/activateLoginLimits.do', {
        data: arr,
        method: 'PUT',
      }).then(res => {
        if (res) {
          message.success('生效成功！');
          setParams({
            ...params,
          });
          setSelectRows([]);
        } else {
          message.error('生效失败！');
        }
      });
    } else {
      message.error('所选中的值含有效状态，请重新选择！');
    }
  };

  const handleLoseEff = () => {
    let arr = [];
    for (let i in selectRows) {
      if (selectRows[i].statusCd === '1100') {
        arr = [];
        break;
      } else {
        arr.push({ loginLimitId: selectRows[i].loginLimitId });
      }
    }
    if (arr.length > 0) {
      request('orgauth/SysuserLoginLimitController/invalidLoginLimits.do', {
        data: arr,
        method: 'PUT',
      }).then(res => {
        if (res) {
          message.success('失效成功！');
          setParams({
            ...params,
          });
          setSelectRows([]);
        } else {
          message.error('失效失败！');
        }
      });
    } else {
      message.error('所选中的值含无效状态，请重新选择！');
    }
  };

  const handleOk = () => {
    loginSetEl.current.handleAdd().then(res => {
      if (res === true) {
        setVisible(false);
        setParams({
          ...params,
          gb_reset: true,
        });
      }
    });
  };

  const handleCancel = () => {
    setVisible(false);
  };

  // const importIdContent = (
  //   <div style={{ width: 400 }}>
  //     <SlickUpload action="orgauth/SysuserLoginLimitController/impFile.do" />
  //   </div>
  // );

  const columns = [
    {
      title: '登录设置类型',
      dataIndex: 'loginLimitTypeName',
      render: (text, record) => {
        return <span>{text ? text : 'IP地址'}</span>;
      },
    },
    {
      title: '起始IP值',
      dataIndex: 'limitIpBegin',
    },
    {
      title: '终止IP值',
      dataIndex: 'limitIpEnd',
    },
    {
      title: '生效时间',
      dataIndex: 'effDate',
    },
    {
      title: '失效时间',
      dataIndex: 'expDate',
    },
    {
      title: '状态',
      dataIndex: 'statusCdName',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
  ];
  return (
    <div>
      <div className={styles.systemExtra}>
        <Button type="primary" onClick={handleAdd}>
          新增
        </Button>
        {/* <Popover
          placement="bottomRight"
          title={
            <div>
              请选择你要导入的文件
              <a
                href="portal/FileStoreController/download.do?docNbr=NybF7uEDF4HwdUMJHPrUZ2"
                target="_blank"
                style={{float:'right'}}
              >
                模板下载
              </a>
            </div>
          }
          content={importIdContent}
          trigger="click"
        >
          <Button  type="primary" className="margin-left">导入IP</Button>
        </Popover> */}
      </div>
      <SlickTable
        rowKey={record => record.id}
        columns={columns}
        pick="checkbox"
        {...tableProps}
        extra={
          selectRows.length > 0 ? (
            <div>
              {/* <Button type="primary" ghost>
                导出IP
              </Button> */}
              <Button type="danger" ghost onClick={handleDelete} className="margin-left">
                删除
              </Button>
              <Button type="primary" ghost onClick={handleTakeEff} className="margin-left">
                生效
              </Button>
              <Button type="primary" ghost onClick={handleLoseEff} className="margin-left">
                失效
              </Button>
            </div>
          ) : null
        }
        onSelectRow={handleSelectRows}
        selectedRows={selectRows}
      />
      <Modal
        title="新增登录设置信息"
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width="650px"
        destroyOnClose
      >
        <AddLoginInfo ref={loginSetEl} cRef={loginSetEl} sysUserId={props.tableData.sysUserId} />
      </Modal>
    </div>
  );
}

export default connect(({ userManage }) => ({
  tableData: userManage.tableData,
}))(LoginSetInfo);
