import request from '@/utils/request';

export async function changeOrg(orgId) {
  return request('portal/LoginController/changeOrg.do', {
    data: { orgId },
  }).then(res => res);
}

export async function queryCurrStaffOrg() {
  return request('orgauth/StaffOrgRelController/queryCurrStaffOrg.do', {
    method: 'GET',
  }).then(res => res);
}

export async function queryAllStaffOrg() {
  return request('portal/OrganizationOutExtController/queryCrmPostOrg.do', {
    method: 'GET',
  }).then(res => res);
}

export async function updateCrmPostOrg(crmOrgId) {
  return request('portal/OrganizationOutExtController/updateCrmPostOrg.do', {
    method: 'POST',
    data: { crmOrgId },
  }).then(res => res);
}
