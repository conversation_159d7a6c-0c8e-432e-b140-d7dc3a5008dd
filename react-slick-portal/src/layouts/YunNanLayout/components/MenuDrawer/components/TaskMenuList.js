import React, { useState, useEffect, useImperativeHandle } from 'react';
import { Menu, Icon } from 'antd';
import { connect } from 'dva';
import PubSub from 'pubsub-js';
import styles from '../../../less/menuDrawer.less';
import { getWaitMenuList } from '@/services/menu';
import { toArray, openMenu } from '@/utils/utils';

const TaskMenuList = props => {
  const { all, selectedKeys, setSelectedKeys, show, setLoading, dispatch, openMenuOfWait, waitRefreshInfoRef, setWaitRefreshInfo } = props;
  const [openKeys, setOpenKeys] = useState([]);
  const [allCount, setAllCount] = useState(0); // 待办任务总数
  const [taskMenuList, setTaskMenuList] = useState([]);

  const openTaskWithSubTaskCode = subTaskCode => {
    if (!subTaskCode) {
      return;
    }

    if (subTaskCode === 'openTheFirst') {
      setSelectedKeys([taskMenuList?.[0]?.subTaskInfo?.[0]?.menuCode]);
      setOpenKeys([taskMenuList?.[0]?.menuCode]);
    }

    const taskCode = toArray(taskMenuList)
      .find(item => toArray(item?.subTaskInfo).findIndex(menu => menu?.menuCode === subTaskCode) !== -1)?.menuCode;

    setOpenKeys([taskCode]);
  };

  // 获取待办任务数量
  const getWaitTaskCount = list => {
    let count = 0;

    count = toArray(list).reduce((pre, cur) => pre + Number(cur.todo), 0);

    return count;
  };

  const initTaskTree = async (showLoading, firstLoading) => {
    if (showLoading && !firstLoading) {
      setLoading(true);
    }

    const taskList = await getWaitMenuList();
    setTaskMenuList(toArray(taskList));

    dispatch({
      type: 'undo/save',
      payload: {
        authorityUndoList: taskList,
      },
    });

    setAllCount(getWaitTaskCount(taskList));

    if (showLoading) {
      setLoading(false);
      setWaitRefreshInfo({ ...waitRefreshInfoRef.current, loading: false });
    }
    return taskList;
  };

  useImperativeHandle(props.cRef, () => ({
    initTaskTree: () => {
      initTaskTree(true, false);
    },
    allCount,
  }));

  useEffect(() => {
    openTaskWithSubTaskCode(selectedKeys?.[0]);
  }, [selectedKeys]);

  const initData = async () => {
    initTaskTree(true, true);
  };

  useEffect(() => {
    initData();
  }, []);


  useEffect(() => {
    // 订阅"打开左侧待办任务菜单列表，并打开相应页签"的消息
    PubSub.subscribe('openWaitTaskListAndTab', (info, menuCode) => {
      if (info === 'openWaitTaskListAndTab' && taskMenuList.length > 0) {
        openMenuOfWait(menuCode, taskMenuList);
      }
    });

    return () => {
      PubSub.unsubscribe('openWaitTaskListAndTab');
    };
  }, [taskMenuList]);

  const isOpened = key => openKeys.includes(String(key));
  const getIconType = key => isOpened(key) ? 'down' : 'right';

  return (
    <div className={styles.taskMenuList} style={{ display: show ? 'block' : 'none' }}>
      <Menu
        mode="inline"
        openKeys={openKeys}
        onOpenChange={_openKeys => { setOpenKeys(_openKeys); }}
        selectedKeys={selectedKeys}
      >
        {
          toArray(taskMenuList).map(item => (
            <Menu.SubMenu
              key={item.menuCode}
              title={(
                <span>
                  <Icon type={getIconType(item.menuCode)} style={{ marginRight: 4, fontSize: 10 }} />
                  <span>
                    <span className={styles.menuText}>{item.taskTypeName}</span>
                    <span style={{ color: (Number(item.todo) > 0 || item.todo === '999+') ? 'red' : 'inherit' }}>({item?.todo ?? 0})</span>
                  </span>
                </span>
              )}
            >
              {
                toArray(item.subTaskInfo).map(menu => (
                  <Menu.Item key={menu.menuCode}>
                    <div
                      className="text-ellipsis"
                      title={menu.taskTypeName}
                      data-reg-id={`wait_${menu.menuCode}`}
                      onClick={() => {
                        openMenu(menu.menuCode, all, dispatch);
                      }}
                    >
                      <span>
                        <span className={styles.menuText}>{menu.taskTypeName}</span>
                        <span style={{ color: Number(menu.todo) > 0 ? 'red' : 'inherit' }}>({menu?.todo ?? 0})</span>
                      </span>
                    </div>
                  </Menu.Item>
                ))
              }
            </Menu.SubMenu>
          ))
        }
      </Menu>
    </div>
  );
};

export default connect(({ menu }) => ({
  all: menu.all,
}))(TaskMenuList);
