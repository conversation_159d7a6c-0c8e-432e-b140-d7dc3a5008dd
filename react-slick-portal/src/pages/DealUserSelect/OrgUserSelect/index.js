import React, { useState, useEffect } from 'react';
import { Card, Input, Button, Modal, Select, Divider, Tree, Row, Col, Form, Icon, Spin } from 'antd';
import SlickTable from '@/components/SlickTable';
import request from '@/utils/request';
import styles from '../styles.less';
import { sendUserInfoToParent, closeUserSelect, buildTree, filterTree } from '../components/userSelectServices';
const { Search } = Input;
const { info } = Modal;

function OrgUserSelect(props) {
  let [params] = useState({
    orgId: '',
    queryUser: '',
  });
  let [treeParams] = useState({});

  const [selectRows, setSelectRows] = useState([]); // 保存选中用户记录
  const [tableData, setTableData] = useState([]); // 用户列表
  const [tableLoading, setTableLoading] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null); // 保存选中用户id

  const [treeData, setTreeData] = useState([]);
  const [allTreeData, setAllTreeData] = useState([]);
  const [pageLoading, setPageLoading] = useState([]);
  const [treeExpandedKeys, setTreeExpandedKeys] = useState([]);
  const [curOrgId, setCurOrgId] = useState(null);
  const [isLowCode, setIsLowCode] = useState(false);

  const [scrollHeight, setScrollHeight] = useState(400);

  useState(() => {
    setScrollHeight(window.document.body.scrollHeight - 180);
  }, []);

  // 请求地址: http://localhost:8000/#/dealUserSelect/OrgUserSelect?orgId=85300014&queryUser=1&startLevel=-3&endLevel=-1&selectOrgId=85300003&viewMode=inner
  // queryUser、startLevel、endLevel 为非必填
  useEffect(() => {
    let query = props.location.query;
    params.orgId = query.selectOrgId;
    params.queryUser = query.queryUser;

    treeParams.orgId = query.orgId;
    if (query.startLevel) {
      treeParams.startLevel = query.startLevel;
    }
    if (query.endLevel) {
      treeParams.endLevel = query.endLevel;
    }
    setIsLowCode(query.lowCode === '1');
    initTree(params.orgId);
  }, [props.location]);

  const initTree = selectedKey => {
    setPageLoading(true);
    request('orgauth/OrganizationController/getOrgTree.do', {
      data: treeParams,
      complete: () => setPageLoading(false),
    }).then(res => {
      // 初始化树
      let treeData = res.resultObject;
      let expandedKeys = [];
      buildTree(treeData, expandedKeys, selectedKey, 'orgId', 'orgName', 'parentOrgId');
      setTreeData(treeData);
      setAllTreeData(treeData);
      setTreeExpandedKeys(expandedKeys);

      // 设置参数, 刷新表格
      props.form.setFieldsValue({
        queryUser: params.queryUser,
        username: '',
      });
      if (params.orgId) {
        // orgId 为必填项，如果url中没有，那么只初始化页面
        setCurOrgId([params.orgId]);
        refreshTable();
      } else {
        setCurOrgId([]);
      }
    });
  };

  const onSearch = () => {
    let form = props.form.getFieldsValue();
    params.queryUser = form.queryUser;
    refreshTable();
  };

  const refreshTable = () => {
    if (!params.orgId) {
      info({ title: '请选择组织后进行查询' });
      return;
    }
    setTableLoading(true);
    request('orgauth/LabelsController/queryManageUserByOrgOrRole.do', {
      data: params,
      complete: () => setTableLoading(false),
    }).then(rsp => {
      let list = rsp.resultObject;
      let userList = [];
      if (list.managerList) {
        userList.push(...list.managerList);
      }
      if (list.secondManagerList) {
        userList.push(...list.secondManagerList);
      }
      if (list.userInfoList) {
        userList.push(...list.userInfoList);
      }
      let filterName = props.form.getFieldValue('username');
      userList = userList.filter(e => e.staffName.includes(filterName) || e.userName.includes(filterName) || e.sysUserCode.includes(filterName));
      setTableData(userList);
    });
  };

  const onSearchTree = val => {
    let newTreeData = [];
    let expandedKeys = [];
    filterTree(allTreeData, val, newTreeData, expandedKeys);
    setTreeData(newTreeData);
    setTreeExpandedKeys(expandedKeys);
  };

  const onTreeSelect = selectedKeys => {
    if (selectedKeys.length === 0) {
      return;
    }
    setCurOrgId(selectedKeys);
    params.orgId = selectedKeys[0];
    refreshTable();
  };

  const onExpandTree = expandedKeys => {
    setTreeExpandedKeys(expandedKeys);
  };

  const onConfirm = () => {
    if (!selectedRow) {
      info({ title: '请选择处理人' });
      return;
    }
    sendUserInfoToParent(selectedRow, isLowCode);
  };

  const onCancel = () => {
    closeUserSelect(isLowCode);
  };

  const columns = [
    {
      title: '用户名',
      dataIndex: 'userName',
    },
    {
      title: '用户编码',
      dataIndex: 'sysUserCode',
    },
    {
      title: '组织',
      dataIndex: 'orgName',
    },
    {
      title: '员工名称',
      dataIndex: 'staffName',
    },
  ];

  const { getFieldDecorator } = props.form;

  const formItemLayout = {
    wrapperCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 14,
      },
    },
    labelCol: {
      xs: {
        span: 0,
      },
      sm: {
        span: 10,
      },
    },
  };

  return (
    <Spin spinning={pageLoading}>
      <Card
        title={isLowCode ? '' : '处理人选择'}
        className={styles['deal-user-card']}
        extra={!isLowCode && <Icon type="close" className={styles['close-icon']} onClick={onCancel} />}
      >
        <div className={styles.main}>
          <div className={styles.left}>
            <div className={styles.title}>组织一览</div>
            <Search placeholder="请输入关键字搜索" onSearch={onSearchTree} className={styles.search} />
            <div className={styles['left-tree']} style={{ height: scrollHeight }}>
              <Tree
                // switcherIcon={<DownOutlined />}
                onSelect={onTreeSelect}
                selectedKeys={curOrgId}
                onExpand={onExpandTree}
                expandedKeys={treeExpandedKeys}
                treeData={treeData}
              />
            </div>
          </div>

          <div className={styles.right}>
            <Form layout="inline" {...formItemLayout}>
              <Form.Item label="是否查询其他用户" style={{ width: '250px', marginBottom: '6px' }}>
                {getFieldDecorator(
                  'queryUser',
                  {}
                )(
                  <Select allowClear placeholder="是否查询其他用户">
                    <Select.Option key={1}>是</Select.Option>
                    <Select.Option key={0}>否</Select.Option>
                  </Select>
                )}
              </Form.Item>
              <Form.Item wrapperCol={{ xs: { span: 24 }, sm: { span: 24 } }}>
                {getFieldDecorator('username', {})(<Input placeholder="请输入用户查询信息" allowClear />)}
              </Form.Item>
              <Form.Item>
                <Button type="primary" onClick={onSearch}>
                  查询
                </Button>
              </Form.Item>
            </Form>
            <SlickTable
              rowKey={record => record.sysUserId}
              columns={columns}
              dataSource={tableData}
              pagination={{ pageSize: 5 }}
              pick={'radio'}
              loading={tableLoading}
              rowSelection={{
                selectedRowKeys: selectRows,
                onSelect: record => {
                  setSelectRows([record.sysUserId]);
                  setSelectedRow(record);
                },
              }}
              onRow={record => ({
                onClick: () => {
                  setSelectRows([record.sysUserId]);
                  setSelectedRow(record);
                },
              })}
            />
          </div>
        </div>
        <Divider className={styles['bottom-divider']} />
        <Row>
          <Col offset={16} span={8} className={styles['bottom-area']}>
            <Button type="default" style={{ marginRight: '8px' }} onClick={onCancel}>
              取消
            </Button>
            <Button type="primary" onClick={onConfirm}>
              确认
            </Button>
          </Col>
        </Row>
      </Card>
    </Spin>
  );
}

export default Form.create()(OrgUserSelect);
