import React from 'react';
import { connect } from 'dva';
// import BasicLayout from './BasicLayout';
import BlueStyleLayout from './BlueStyleLayout';
import YunNanLayout from './YunNanLayout';
import { THEME_STYLE } from '@/enums';

const ThemeLayout = props => {
  const { login } = props;
  switch (login?.user?.userInfo?.moduleStyle || THEME_STYLE.DEFAULT) {
    case THEME_STYLE.LIGHT:
      return <BlueStyleLayout {...props} />;
    default:
      // return <BasicLayout {...props} />
      return <YunNanLayout {...props} />;
  }
};

export default connect(state => ({
  login: state.login,
}))(ThemeLayout);
