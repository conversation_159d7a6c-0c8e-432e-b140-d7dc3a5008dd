import request from '@/utils/request';

export function sceneConfPage(data) {
  return request('portal/DesensitizationConfController/sceneConfPage.do', {
    data,
  }).then(res => {
    if (res && res.success) {
      return {
        total: res.resultObject.total,
        data: res.resultObject.list,
      };
    }
    return {
      total: 0,
      data: [],
    };
  });
}

export async function addSceneConf(data) {
  return request('portal/DesensitizationConfController/addSceneConf.do', {
    data,
  }).then(res => res);
}

export async function editSceneConf(data) {
  return request('portal/DesensitizationConfController/updateSceneConf.do', {
    data,
  }).then(res => res);
}

export async function updateSceneState(data) {
  return request('portal/DesensitizationConfController/updateSceneState.do', {
    data,
  }).then(res => res);
}

export async function deleteSceneConfById(data) {
  return request('portal/DesensitizationConfController/deleteSceneConfById.do', {
    data,
  }).then(res => res);
}

export async function getSystemList(data) {
  return request('orgauth/SystemInfoController/getSystemInfoList.do', {
    method: 'GET',
    data,
  }).then(res => res);
}

export function ruleConfPage(data) {
  return request('portal/DesensitizationConfController/ruleConfPage.do', {
    data,
  }).then(res => {
    if (res && res.success) {
      return {
        total: res.resultObject.total,
        data: res.resultObject.list,
      };
    }
    return {
      total: 0,
      data: [],
    };
  });
}

export async function addRuleConf(data) {
  return request('portal/DesensitizationConfController/addRuleConf.do', {
    data,
  }).then(res => res);
}

export async function updateRuleConf(data) {
  return request('portal/DesensitizationConfController/updateRuleConf.do', {
    data,
  }).then(res => res);
}

export async function deleteRuleConfByIds(data) {
  return request('portal/DesensitizationConfController/deleteRuleConfByIds.do', {
    data,
  }).then(res => res);
}

export async function queryAllRuleCode(code) {
  return request('portal/DesensitizationConfController/queryAllRuleCode.do', {
    method: 'get',
    data: { code },
  }).then(res => res);
}

export async function refreshCache() {
  return request('portal/DesensitizationConfController/refreshCache.do', {
  }).then(res => res);
}
