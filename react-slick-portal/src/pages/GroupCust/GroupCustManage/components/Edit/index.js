/* eslint-disable no-console */
/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Button, Card, Form, message, Modal, Spin } from 'antd';
import { connect } from 'dva';
import { useLocation } from 'react-router-dom';
import style from './index.less';
import CertInfo from './components/CertInfo';
import BaseInfo from './components/BaseInfo';
import StandardAddr from './components/StandardAddr';
import HandlerInfo from './components/HandlerInfo';
import Cmiot from './components/Cmiot';
import BBossInfo from './components/BBossInfo';
import {
  checkGroupName,
  editContentBack,
  queryCustomerManager,
  queryFileIsUploadGroup,
  queryFileVal,
  submitAddForm,
} from '@/pages/GroupCust/GroupCustManage/services';
import { getItem } from '@/utils/utils';
import { queryNewRecordByExtOrderNbr } from '@/pages/Approval/service';
import AuditModal from '@/pages/Approval/components/AuditModal';
import IndustryInfo from '@/pages/GroupCust/GroupCustManage/components/Edit/components/IndustryInfo';

const MODE = {
  ADD: 'add',
  EDIT: 'edit',
  VIEW: 'view',
};

const Edit = props => {
  const {
    groupName,
    groupCode,
    size: { height },
    viewMode: propsViewMode /* add 建档 mode 修改  view 查看 */,
    form,
    dispatch,
    formData,
    otherData,
    editGroupId,
    extOrderNbr: propsExtOrderNbr,
    isBackButton = true,
  } = props;
  const [loading, setLoading] = useState(false);
  const [editable, setEditable] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);

  const [submitDisabled, setSubmitDisabled] = useState(false);

  const [isCmiot, setIsCmiot] = useState(false); // 是否显示 Cmiot

  const [customerInfo, setCustomerInfo] = useState(null); // 客户经理信息

  const [custId, setCustId] = useState('');

  const [isProcessing, setIsProcessing] = useState(false);

  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [auditRecord, setAuditRecord] = useState('');

  const location = useLocation();
  // 路由获取审批单号，如果props中没有则从location.query中获取
  const { extOrderNbr: queryExtOrderNbr, viewMode: queryViewMode } = location?.query || {};
  const extOrderNbr = propsExtOrderNbr || queryExtOrderNbr;
  const viewMode = propsViewMode || queryViewMode;

  const judgeFileIsUploadGroup = async fieldId => {
    const resp = await queryFileIsUploadGroup(fieldId);
    return resp;
  };
  // const checkGroupLience = () => {
  //   const licenceType = form.getFieldValue('GROUP_BUSI_LICENCE_TYPE'); // 集团证件类型
  //   const licenceNo = form.getFieldValue('GROUP_LICENCE_NO'); // 集团证件号码
  //   const licenceGroupName = form.getFieldValue('GROUP_LICENCE_NAME'); // 集团单位名称
  //   const licenceGroupAddress = form.getFieldValue('form.getFieldValue'); // 集团单位证件地址
  //   if (!licenceType || !licenceNo || !licenceGroupName || !licenceGroupAddress) {
  //     message.warn('集团证件信息不能为空');
  //     return false;
  //   }
  //   const needCompare = form.getFieldValue('NEED_COMPARE');
  //   const jumpCompare = form.getFieldValue('JUMP_COMPARE');
  //   const compareFlag = form.getFieldValue('COMPARE_FLAG');
  //   if (needCompare === '1' && jumpCompare !== 'true' && compareFlag === '0') {
  //     message.warn('集团证件信息需要进行在线比对');
  //     return false;
  //   }
  //   return true;
  // };
  useEffect(() => {
    setEditable(viewMode !== MODE.VIEW);
  }, []);
  const resetEnterpriseSize = (entEmployeeCount, yearGain) => {
    // 确保参数有效且转换为数字
    const userCount = parseInt(entEmployeeCount, 10);
    const annualGain = parseInt(yearGain, 10);

    // 如果任一参数无效，则直接返回
    if (Number.isNaN(userCount) || Number.isNaN(annualGain)) {
      return;
    }

    // 根据用户数确定企业规模
    let userPoint;
    if (userCount < 50) {
      userPoint = 3; // 微小型
    } else if (userCount < 100) {
      userPoint = 2; // 中型
    } else if (userCount < 1000) {
      userPoint = 1; // 大型
    } else {
      userPoint = 0; // 特大型
    }

    // 根据年营业额确定企业规模
    let gainPoint;
    if (annualGain < 1000) {
      gainPoint = 3; // 微小型
    } else if (annualGain < 3000) {
      gainPoint = 2; // 中型
    } else if (annualGain < 30000) {
      gainPoint = 1; // 大型
    } else {
      gainPoint = 0; // 特大型
    }

    // 取较小的点数（较大的企业规模）
    const enterpriseSizeCode = Math.min(gainPoint, userPoint).toString();
  };

  const okAddResPop = () => {
    console.log('知道了');
  };

  // 提交
  const handleSubmit = async values => {
    const params = {
      aeInfo: {
        ORGA_ENTERPRISE_ID: viewMode === 'edit' ? editGroupId : '',
        GROUP_ID: viewMode === 'edit' ? groupCode : '',
        CUST_ID: viewMode === 'edit' ? custId : '',
        GROUP_TYPE: values.GROUP_TYPE, // 集团类型
        ENT_EMPLOYEE_COUNT: values.ENT_EMPLOYEE_COUNT, // 企业员工数：
        PROVINCE_CODE: values.PROVINCE_CODE, // 集团所在省份
        clustScenUserDefine: values.clustScenUserDefine, // 场景子类（自定义）
        CMIOT_COUNTRY_ID: values?.CmiotAreaInfo && (values?.CmiotAreaInfo.length !== 0 ? values.CmiotAreaInfo[1] : ''), // cmiot -- 归属区县
        CUST_MGR_ID: values.CUST_MGR_ID, // 客户经理 id
        POWER_OF_ATTORNEY: values.POWER_OF_ATTORNEY, // 授权委托书拍照
        CHANNEL_ID: '1000',
        needOaAudit: otherData.needOaAudit, // 需要oa审批
        CALLING_AREA_B: values.CALLING_AREA_INFO[2], // 行业中类
        IS_CMIOT: formData.IS_CMIOT === '1' ? '1' : '0', // 是否cmiot集团
        CMIOT_STAFF_NUMBER: values.CMIOT_STAFF_NUMBER, // cmiot 客户经理
        CALLING_AREA_A: values.CALLING_AREA_INFO[1], // 国标行业大类
        DIRECT_CUST_ID: values.DIRECT_CUST_ID, // 直管客户编号
        GROUP_LICENCE_NO: values.GROUP_LICENCE_NO, // 证件号码
        PRE_ORDER_ID: '', // 预受理订单号
        AUDIT_STAFF_NAME: values.AUDIT_STAFF_NAME, // 审核人名称
        CMIOT_REGION_ID: values?.CmiotAreaInfo && (values?.CmiotAreaInfo.length !== 0 ? values.CmiotAreaInfo[0] : ''), // cmiot归属地市
        AGENT_IMAGE: values.AGENT_IMAGE, // 经办人拍照
        OSS_BUILD_NAME: values.OSS_BUILD_NAME || values.fourthAddress.length > 2 ? values.fourthAddress[2] : '', // 所属楼栋号
        JK_GROUP_TYPE: values.JK_GROUP_TYPE, // 集客企业类型
        AUDIT_STAFF_ID_TEXT: values.AUDIT_STAFF_ID_TEXT, // 审核人
        CHIP_AREA_CODE: values.BelongingAreaInfo.length > 2 ? values.BelongingAreaInfo[2] : '', // 片区
        GROUP_LEVEL_NEW: values.GROUP_LEVEL_NEW, // 集团客户级别
        GROUP_ORG_CODE: values.GROUP_ORG_CODE, // 组织机构代码
        BUSICOUNTYID: values.BUSILOCATIONS[2], // 生产经营所在区县
        ENTERPRISE_SCOPE: values.ENTERPRISE_SCOPE, // 经营区域范围
        OSS_PROVINCE_NAME: '内蒙古', // 所属省份
        IMPORT_FLAG: values.syncWlwStatus || '0', // 是否升级物联网
        GROUP_IDEN_PHOTO: values.GROUP_IDEN_PHOTO, // 集团证件标识
        // BUSICOUNTYNAME: '新城区123', // 新增？ -- 生产经营所在区县
        IS_BRANCH: values.IS_STRATEGIC_CUSTOMER, // 是否战略客户
        CALLING_TYPE: values.CALLING_TYPE, // 行业类型
        CUST_MANAGER_TYPE: values.CUST_MANAGER_TYPE, // 客户经理
        GROUP_HALF_LEVEL: values.GROUP_HALF_LEVEL, // 集团半年级别
        MGMT_COUNTY: values.BelongingAreaInfo.length > 1 ? values.BelongingAreaInfo[1] : '', // 区县
        GROUP_NAME: values.GROUP_NAME, // 集团名称
        REGMANADEPARTMENT: values.REGMANADEPARTMENT, // 登记管理部门
        OSS_NINE_ADDR_NAME: values.NineAddress, // 九级地址
        GROUP_CONTACT_PHONE: values.GROUP_CONTACT_PHONE, // 联系电话
        CMIOT_MOBILE_PHONE: values.CMIOT_MOBILE_PHONE, // cmiot电话
        OSS_VILLAGE_NAME: values.OSS_VILLAGE_NAME || values.fourthAddress.length > 0 ? values.fourthAddress[0] : '', // 所属道路
        clusteringScenario: values.clusteringScenario, // 商客场景
        YEAR_GAIN: values.YEAR_GAIN,
        CREDIT_LEVEL: values.CREDIT_LEVEL, // 信用级别
        GROUP_PAY_MODE: values.GROUP_PAY_MODE, // 支付方式
        buIndustry: values.buIndustry, // 政企客户行业门类
        JURISTIC_IDEN_NR: values.JURISTIC_IDEN_NR, // 法人身份证号
        MULTI_PROVINCE: values.MULTI_PROVINCE, // 跨省类型
        IS_EXPORT_AND_IMPORT: values.IS_EXPORT_AND_IMPORT, // CMIOT 进出口客户
        OSS_ADDRESS_ID: values.OSS_ADDRESS_ID, // 九级地址编号
        buIndustrySub: values.buIndustrySub, // 政企客户行业类型
        MGMT_DISTRICT: values.BelongingAreaInfo.length > 0 ? values.BelongingAreaInfo[0] : '', // 归属区域 -- 地市
        EcCustLicenceInfo: values.EcCustLicenceInfo[0].fileId, // 集团证件信息 -- 附件
        BUSILOCATION: values.BUSILOCATIONS[1], // 生产经营所在地市
        CHANCE_ID: '', //  新增证件 客户商机执行的商机id
        IS_SMALLMICRO: values.IS_SMALLMICRO, // 小微企业
        CMIOT_ATTACHMENTS: values.CMIOT_ATTACHMENTS, // cmiot业务附件
        ID_ADDR: values.ID_ADDR, // cmiot 证件地址
        OSS_ADDR_COVER_STATE: values.OSS_ADDR_COVER_STATE, // 九级地址资源覆盖程度
        ATTRIBUTION_SCENE: values.ATTRIBUTION_SCENE, // 归属场景
        AGENT_LICENCE_NAME: values.AGENT_LICENCE_NAME, // 经办人姓名
        GROUP_LICENCE_ADDRESS: values.GROUP_LICENCE_ADDRESS, // 生产经营具体地址
        PRE_SELL_ORDER_ID: values.PRE_SELL_ORDER_ID, // 新增证件？ 预甩单
        IS_DIRECT: values.IS_DIRECT, // 是否直管客户 默认0
        OSS_COUNTY_NAME: values.OSS_CITY_NAME_INFO.length > 2 ? values.OSS_CITY_NAME_INFO[2] : '', // 所属街道
        OSS_DISTRICT_NAME: values.OSS_DISTRICT_NAME || values.fourthAddress.length > 1 ? values.fourthAddress[1] : '', // 所属小区
        AGENT_LICENCE_ADDRESS: values.AGENT_LICENCE_ADDRESS, // 经办人地址
        SUB_ATTRIBUTION_SCENE: values.SUB_ATTRIBUTION_SCENE, // 归属场景
        ENTERPRISE_SIZE_CODE: values.ENTERPRISE_SIZE_CODE, // 集团规模
        CALLING_AREA: values.CALLING_AREA_INFO[0], // 行业类型
        CUST_MGR_INFO: values.CUST_MGR_INFO, // 客户经理姓名
        CMIOT_ORG_ID: values.CMIOT_ORG_ID, // cmiot组织机构
        OSS_FLOOR_NAME: values.OSS_FLOOR_NAME || values.fourthAddress.length > 3 ? values.fourthAddress[3] : '', // 所属房号
        AGENT_LICENCE_NO: values.AGENT_LICENCE_NO, // 经办人 -- 证件号码
        GROUP_CUST_TYPE: values.GROUP_CUST_TYPE, // 客户类型（是否直管客户）默认 *********
        BUSICOMPANYID: values.BUSILOCATIONS[0], // 生产经营所在省
        IDEN_DATA: {
          // 新增证件？
          CHECK_MESSAGE: otherData.SCAN_DESCRIPTION || '', // 新增证件 -- 失败原因
          IDEN_EXP_DATE: values.idenExpDate || values.IDEN_EXP_DATE, // 证件失效时间
          IDEN_ADDRESS: values.idenAddress || values.GROUP_LICENCE_ADDRESS, // 新增 -- 证件地址
          ENT_RESULT: otherData.SCAN_RESULT || '', // 核验状态
          IDEN_EFF_DATE: values.idenEffDate || values.GROUP_IDEN_EXP_DATE, // 证件生效时间
          ENT_SERIAL_NUMBER: otherData.ENT_SERIAL_NUMBER || '', // 验真流水
          CUST_TYPE: values.custType || '', //  新增证件 -- 客户类型
          iden_EcCustLicenceInfo: values.ecCustLicenceInfoPop || '', // 客户证件扫描附件上传
          IDEN_TYPE_CODE: values.idenTypeCode || values.GROUP_BUSI_LICENCE_TYPE, // 证件类型
          SCAN_DESCRIPTION: otherData.SCAN_DESCRIPTION || '', // 扫描结果描述
          SCAN_RESULT: otherData.SCAN_RESULT || '', // 扫描结果
          JURISTIC_NAME: values.ADD_JURISTIC_NAME || values.JURISTIC_NAME, // 法人代表姓名
          IDEN_LONG_EFF: values.denLongEff, // 证件是否长期有效
          PHOTO: values.photo || '', // 证件拍照
          IDEN_NAME: values.idenName || values.GROUP_LICENCE_NAME, // 客户名称
          IDEN_NR: values.idenNr || values.GROUP_LICENCE_NO, // 证件号码
          remarks: values.remarks || '', // 备注
        },
        AUDIT_STAFF_ID: '', // 审核人id
        CreateGroupChannelInfoJson: values.CreateGroupChannelInfoJson, // 渠道信息汇总
        OSS_CITY_NAME: values.OSS_CITY_NAME_INFO.length > 0 ? values.OSS_CITY_NAME_INFO[0] : '', // 所属地市
        IS_SEND_BBOSS: '1', // 是否升级为全网集团
        GROUP_COMPARE_STATUS: values.GROUP_COMPARE_STATUS, // 是否通过在线公司核验
        SERV_LEVEL: values.SERV_LEVEL, // 客户服务等级
        REG_MONEY: values.REG_MONEY, // 注册资金
        IS_SEND_ManageOne: '0', // 是否升级华为云平台
        OSS_AREA_NAME: values.OSS_CITY_NAME_INFO.length > 1 ? values.OSS_CITY_NAME_INFO[1] : '', // 所属区县
        CUST_MANAGER_TYPE_RADIO: '1', // 客户经理
        IS_STRATEGIC_CUSTOMER: values.IS_STRATEGIC_CUSTOMER, // 是否战略客户
        ENT_NAME: values.ENT_NAME, // 企业标准名称
        clustScenTypeTwo: values.clustScenTypeTwo, // 细分客群类别
        GROUP_IDEN_EXP_DATE: values.GROUP_IDEN_EXP_DATE, // 集团证件失效时间
        AGENT_LICENCE_TYPE: '100001', // 经办人证件类型
        JURISTIC_NAME: values.AGENT_LICENCE_TYPE, // 法人代表姓名
        GROUP_LICENCE_NAME: values.GROUP_LICENCE_NAME, // 证件名称
        GROUP_IDEN_EFF_DATE: values.GROUP_IDEN_EFF_DATE, // 集团证件生效时间
        GROUP_BUSI_LICENCE_TYPE: values.GROUP_BUSI_LICENCE_TYPE, // 证件类型
        SUPER_GROUP_ID: values.SUPER_GROUP_ID, // 上级机构集团编码
        SUPER_GROUP_NAME: values.SUPER_GROUP_NAME, // 上级机构名称
      },
      operType: viewMode === 'edit' ? '2' : '1',
      busiCode: 'ESOP_APP_EC_CUST_OPER',
      operCode: '1',
      extOrderNbr,
    };
    setSubmitLoading(true);
    const resp = await submitAddForm(params);
    setSubmitLoading(false);
    if (resp.resultCode === 'TRUE' && resp.resultObject) {
      if (viewMode === 'edit') {
        Modal.success({
          title: `集团建档修改成功,集团编码：${groupCode},集团名称：${values.GROUP_NAME}`,
          // content: { addResPopText(resp.resultObject.rspParam.busiInfo)},
          onOk() {
            okAddResPop();
          },
        });
        return;
      }
      Modal.success({
        title: `集团建档成功,集团编码：${resp.resultObject.groupId},集团名称：${resp.resultObject.GROUP_NAME}`,
        // content: { addResPopText(resp.resultObject.rspParam.busiInfo)},
        onOk() {
          okAddResPop();
        },
      });
    } else if (resp?.resultCode === '110' || resp?.resultCode === '180') {
      setApprovalModalVisible(true); // 显示弹窗
      setAuditRecord(resp?.resultObject);
    } else {
      message.error(resp.resultMsg);
    }
  };

  const checkedForm = () => {
    // 业务类型：busiCode : "ESOP_APP_EC_CUST_OPER";
    form.validateFields(async (err, values) => {
      console.log('提交表单', err, values);
      if (err) return;
      const { IS_CMIOT, CMIOT_STAFF_NUMBER, allowSubmit, EcCustLicenceInfo, ENT_EMPLOYEE_COUNT, YEAR_GAIN } = values;
      // if (allowSubmit === '0') {
      //   message.error('证件工商登记状态异常，不允许提交');
      // }
      const oldGroupName = otherData.OLD_GROUP_NAME;
      const newGroupName = values.GROUP_NAME;
      if (viewMode === 'edit' && oldGroupName !== newGroupName) {
        const response = await checkGroupName(values.GROUP_NAME);
        if (response.resultCode === 'TRUE') {
          if (response.resultObject.isExit === '1') {
            message.error('集团名称已经存在！请更换名称！');
            return;
          }
        } else {
          message.error('集团名称校验失败');
          return;
        }
      } else if (viewMode === 'add') {
        const response = await checkGroupName(values.GROUP_NAME);
        if (response.resultCode === 'TRUE') {
          if (response.resultObject.isExit === '1') {
            message.error('集团名称已经存在！请更换名称！');
            return;
          }
        } else {
          message.error('集团名称校验失败');
          return;
        }
      }

      if (EcCustLicenceInfo) {
        try {
          const EcCustLicenceInfoSuccess = await judgeFileIsUploadGroup({ fileId: EcCustLicenceInfo[0].fileId });
          // 文件验证通过，继续后续操作
          console.log('文件校验', EcCustLicenceInfoSuccess);

          if (EcCustLicenceInfoSuccess.resultCode === 'TRUE' && EcCustLicenceInfoSuccess.resultObject.isUpload === '1') {
            message.error('文件还未上传到BBOSS或查询文件信息失败。');
            return;
          }
        } catch (error) {
          // 处理错误情况
          message.error(error);
        }
      }

      // 重写集团规模
      const userCount = parseInt(ENT_EMPLOYEE_COUNT, 10);
      const annualGain = parseInt(YEAR_GAIN, 10);
      // console.log('userCount,annualGain', userCount, annualGain);

      // 如果任一参数无效，则直接返回
      if (Number.isNaN(userCount) || Number.isNaN(annualGain)) {
        return;
      }

      // 根据用户数确定企业规模
      let userPoint;
      if (userCount < 50) {
        userPoint = 3; // 微小型
      } else if (userCount < 100) {
        userPoint = 2; // 中型
      } else if (userCount < 1000) {
        userPoint = 1; // 大型
      } else {
        userPoint = 0; // 特大型
      }

      // 根据年营业额确定企业规模
      let gainPoint;
      if (annualGain < 1000) {
        gainPoint = 3; // 微小型
      } else if (annualGain < 3000) {
        gainPoint = 2; // 中型
      } else if (annualGain < 30000) {
        gainPoint = 1; // 大型
      } else {
        gainPoint = 0; // 特大型
      }

      // 取较小的点数（较大的企业规模）
      const enterpriseSizeCode = Math.min(gainPoint, userPoint).toString();
      form.setFieldsValue({ ENTERPRISE_SIZE_CODE: enterpriseSizeCode });

      // 校验集团证件
      // if (!checkGroupLience()) {
      //   return;
      // }
      // 构造json,提交表单
      handleSubmit(values);
    });
  };

  const handleOk = () => {
    Modal.confirm({
      title: `确定要${viewMode === 'edit' ? '修改' : '新增'}集团吗`,
      async onOk() {
        setIsProcessing(true);
        try {
          await checkedForm();
        } finally {
          setIsProcessing(false);
        }
      },
    });
  };

  const handleScan = () => {
    // todo: openNav('无纸化系统', 'sales.person.vc.sonbrquery.edocSonbrQry','initPage',"");
  };

  /* 获取详情信息 */
  const getDetailInfo = () => {};

  /**
   * 返回
   */
  const goBack = () => {
    props.goToStep(1);
  };
  const renderCardTitle = () => {
    if (viewMode === MODE.EDIT) {
      return `${groupName}修改`;
    }
    if (viewMode === MODE.ADD) {
      return '集团客户新增';
    }
    return `${groupName}详情`;
  };
  // 切换cmiot
  useEffect(() => {
    if (groupCode) {
      getDetailInfo();
    }
  }, [groupCode]);
  useEffect(() => {
    if (viewMode === MODE.ADD || viewMode === MODE.EDIT) {
      setEditable(true);
    }
  }, [viewMode]);
  const getCustomerInfo = async id => {
    const params = {
      operatorId: id,
    };
    const resp = await queryCustomerManager(params);
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.outData.length !== 0) {
      setCustomerInfo(resp.resultObject.rspParam.busiInfo.outData[0]);
    } else {
      message.error('获取客户经理信息失败');
    }
  };
  useEffect(() => {
    if (viewMode === 'edit') return;
    const offerCode = getItem('user').userInfo.externalUserInfos.filter(e => e.defaultFlag === '1000');
    if (offerCode.length !== 0) {
      getCustomerInfo(offerCode[0].externalUserId);
    }
  }, []);

  useEffect(() => {
    dispatch({
      type: 'AddGroupCust/updataInitialData',
      payload: {},
    });
  }, [dispatch]);

  // 编辑 -- 获取客户经理信息
  const queryCustInfo = async id => {
    const params = {
      custMgrId: id,
    };
    const resp = await queryCustomerManager(params);
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.outData.length !== 0) {
      setCustomerInfo(resp.resultObject.rspParam.busiInfo.outData[0]);
    } else {
      message.error('获取客户经理信息失败');
    }
  };

  // 获取文件信息接口
  const queryFildValue = async fileId => {
    const params = { fileId };
    const resp = await queryFileVal(params);
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.ret.length !== 0) {
      const fileInfo = resp.resultObject.rspParam.busiInfo.ret[0];
      form.setFieldsValue({
        EcCustLicenceInfo: [
          {
            uid: fileInfo.FILE_ID,
            fileId: fileInfo.FILE_ID,
            name: fileInfo.FILE_NAME,
            status: 'done',
          },
        ],
      });
    }
  };

  // 编辑 -- 获取集团建档信息
  const queryEditInfo = async () => {
    const resp = await editContentBack(editGroupId);

    if (resp.resultCode === 'TRUE' && resp.resultObject.groupInfo) {
      const { groupInfo } = resp.resultObject;
      setCustId(groupInfo.CUST_ID);
      if (groupInfo.CUST_MGR_ID) queryCustInfo(groupInfo.CUST_MGR_ID);
      const editParams = {
        ...groupInfo,
        CALLING_AREA_INFO: [groupInfo.CALLING_AREA, groupInfo.CALLING_AREA_A, groupInfo.CALLING_AREA_B],
        BUSILOCATIONS: [groupInfo.BUSICOMPANYID, groupInfo.BUSILOCATION, groupInfo.BUSICOUNTYID],
        // BelongingAreaInfo: [groupInfo.MGMT_DISTRICT, groupInfo.MGMT_COUNTY, groupInfo.CHIP_AREA_CODE],
        ATTRIBUTION_SCENE: groupInfo.EXT3, // 归属场景
        SUB_ATTRIBUTION_SCENE: groupInfo.EXT4, // 归属场景子类
        clusteringScenario: groupInfo.SCENE_CATEGORY, // 场所类别
        clustScenTypeTwo: groupInfo.SCENE_SUBCLASS, // 细分客群类别
        clustScenUserDefine: groupInfo.SCENE_USER_DEFINE, // 特色客群（自定义）
        OSS_CITY_NAME_INFO: [groupInfo.OSS_CITY_NAME, groupInfo.OSS_AREA_NAME, groupInfo.OSS_COUNTY_NAME],
        fourthAddress: [groupInfo.OSS_VILLAGE_NAME, groupInfo.OSS_DISTRICT_NAME, groupInfo.OSS_BUILD_NAME, groupInfo.OSS_FLOOR_NAME],
        NineAddress: groupInfo.OSS_NINE_ADDR_NAME,
        CmiotAreaInfo: [groupInfo.CMIOT_REGION_ID, groupInfo.CMIOT_COUNTRY_ID], // cmiot 区域信息
      };
      // 回填赋值
      form.setFieldsValue(editParams);
      // 获取集团证件附件
      if (groupInfo.LICENCEINFO) {
        queryFildValue(groupInfo.LICENCEINFO);
      }
      dispatch({
        type: 'AddGroupCust/updataOther',
        payload: { OLD_GROUP_NAME: groupInfo.GROUP_NAME }, // 保存返回的集团名
      });
    } else {
      message.error('获取集团信息失败，请重试~');
      props.goToStep(1);
    }
  };

  const approvalModalVisibleChange = value => {
    setApprovalModalVisible(value);
  };

  // 审批 -- 获取集团建档信息
  const queryApprovalInfo = async () => {
    const resp = await queryNewRecordByExtOrderNbr(extOrderNbr);
    if (resp.resultCode === 'TRUE' && resp.resultObject.crmReqData) {
      try {
        // 解析 crmReqData 字符串为对象
        const crmData = JSON.parse(resp.resultObject.crmReqData);
        const { aeInfo } = crmData;

        // 构造回填参数对象
        const editParams = {
          ...aeInfo,
          // 处理数组类型的字段
          CALLING_AREA_INFO: [aeInfo.CALLING_AREA, aeInfo.CALLING_AREA_A, aeInfo.CALLING_AREA_B],
          BUSILOCATIONS: [aeInfo.BUSICOMPANYID, aeInfo.BUSILOCATION, aeInfo.BUSICOUNTYID],
          BelongingAreaInfo: [aeInfo.MGMT_DISTRICT, aeInfo.MGMT_COUNTY, aeInfo.CHIP_AREA_CODE],
          OSS_CITY_NAME_INFO: [aeInfo.OSS_CITY_NAME, aeInfo.OSS_AREA_NAME, aeInfo.OSS_COUNTY_NAME],
          fourthAddress: [aeInfo.OSS_VILLAGE_NAME, aeInfo.OSS_DISTRICT_NAME, aeInfo.OSS_BUILD_NAME, aeInfo.OSS_FLOOR_NAME],
          // 处理证件相关信息
          EcCustLicenceInfo: aeInfo.EcCustLicenceInfo
            ? [
              {
                uid: aeInfo.EcCustLicenceInfo,
                fileId: aeInfo.EcCustLicenceInfo,
                name: '证件附件',
                status: 'done',
              },
            ]
            : undefined,

          // 处理特殊字段
          ATTRIBUTION_SCENE: aeInfo.ATTRIBUTION_SCENE, // 归属场景
          SUB_ATTRIBUTION_SCENE: aeInfo.SUB_ATTRIBUTION_SCENE, // 归属场景子类
          clusteringScenario: aeInfo.clusteringScenario, // 场所类别
          clustScenTypeTwo: aeInfo.clustScenTypeTwo, // 细分客群类别

          // 确保 GROUP_COMPARE_STATUS 正确回填
          GROUP_COMPARE_STATUS: aeInfo.GROUP_COMPARE_STATUS,

          // IDEN_DATA 相关字段回填
          GROUP_IDEN_EFF_DATE: aeInfo.IDEN_DATA.IDEN_EFF_DATE,
          GROUP_IDEN_EXP_DATE: aeInfo.IDEN_DATA.IDEN_EXP_DATE,
          GROUP_IDEN_PHOTO: aeInfo.IDEN_DATA.PHOTO,
        };

        // console.log('CreateGroupChannelInfoJson 4344444', aeInfo.CreateGroupChannelInfoJson);
        // 回填表单数据
        form.setFieldsValue(editParams);
        // console.log('CreateGroupChannelInfoJson 2233333', form.getFieldValue('OSS_AREA_NAME'));

        // 更新其他数据到 store
        dispatch({
          type: 'AddGroupCust/updataOther',
          payload: {
            OLD_GROUP_NAME: aeInfo.GROUP_NAME,
            SCAN_DESCRIPTION: aeInfo.IDEN_DATA?.SCAN_DESCRIPTION,
            SCAN_RESULT: aeInfo.IDEN_DATA?.SCAN_RESULT,
            ENT_SERIAL_NUMBER: aeInfo.IDEN_DATA?.ENT_SERIAL_NUMBER,
          },
        });

        // 如果有客户经理信息，获取客户经理详情
        if (aeInfo.CUST_MGR_ID) {
          queryCustInfo(aeInfo.CUST_MGR_ID);
        }

        // 如果有证件附件信息，获取文件详情
        if (aeInfo.EcCustLicenceInfo) {
          queryFildValue(aeInfo.EcCustLicenceInfo);
        }
      } catch (error) {
        console.error('解析审批数据失败:', error);
        message.error('解析审批数据失败，请重试');
        props.goToStep(1);
      }
    } else {
      message.error('获取审批信息失败，请重试');
      props.goToStep(1);
    }
  };

  useEffect(() => {
    if (viewMode === 'edit') {
      queryEditInfo();
    }
    if (extOrderNbr) {
      queryApprovalInfo();
    }
  }, [viewMode]);

  /**
   * 更新表单数据到store
   * @param changedValues 表单数据 {fieldName: componentName.fieldName, value: value}
   * @param syncFlag  是否触发form.setFieldValue
   */
  const handleValuesChange = (changedValues, syncFlag = false) => {
    if (changedValues['Cmiot.IS_CMIOT']) {
      if (changedValues['Cmiot.IS_CMIOT'] === '1') {
        // 展示Cmiot
        setIsCmiot(true);
      } else {
        setIsCmiot(false);
      }
    }
    Object.entries(changedValues).forEach(([field, value]) => {
      dispatch({
        type: 'AddGroupCust/updateField',
        payload: {
          fieldName: field,
          value,
          syncFlag,
        },
      });
    });
  };

  const renderExtra = () => (
    // if (viewMode === MODE.EDIT) {
    //   return <Button onClick={goBack}>返回</Button>;
    // }
    <></>
  );
  return (
    <Spin spinning={loading}>
      <Card title={renderCardTitle()} className="cute" style={{ minHeight: height }} extra={renderExtra()}>
        <div className={style.content}>
          {/* 集团证件信息 */}
          <CertInfo form={form} onValuesChange={handleValuesChange} editable={editable} viewMode={viewMode} />
          {/* 基本信息 */}
          <BaseInfo
            customerInfo={customerInfo}
            isCmiot={isCmiot}
            form={form}
            onValuesChange={handleValuesChange}
            viewMode={viewMode}
            editable={editable}
          />
          {/* CMIOT信息 */}
          {isCmiot && <Cmiot form={form} onValuesChange={handleValuesChange} viewMode={viewMode} editable={editable} />}
          {/* 集团其他信息 */}
          <IndustryInfo viewMode={viewMode} form={form} onValuesChange={handleValuesChange} editable={editable} />
          {/* 标准地址 */}
          <StandardAddr form={form} onValuesChange={handleValuesChange} editable={editable} viewMode={viewMode} />
          {/* 经办人信息 */}
          <HandlerInfo form={form} onValuesChange={handleValuesChange} editable={editable} viewMode={viewMode} />
          {viewMode === 'edit' && <BBossInfo form={form} />}
          {/* 审批信息  */}
          {/* {editable && <AuditInfo form={form} onValuesChange={handleValuesChange} editable={editable} />} */}

          <div className={style.btn}>
            <div className={style.right}>
              {editable && (
                <>
                  <Button type="primary" onClick={() => handleOk()} loading={submitLoading} disabled={submitDisabled} style={{ marginLeft: '8px' }}>
                    提交
                  </Button>
                  {/* <Button type="primary" onClick={handleScan} style={{ marginLeft: '8px' }}>
                    无纸化档案录入
                  </Button> */}
                </>
              )}
              {isBackButton && (
                <Button onClick={goBack} style={{ marginLeft: '8px' }}>
                  返回
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>
      <AuditModal approvalModalVisible={approvalModalVisible} approvalModalVisibleChange={approvalModalVisibleChange} auditRecord={auditRecord} />
    </Spin>
  );
};

export default connect(({ setting, AddGroupCust, groupCustView }) => ({
  size: setting.size,
  formData: AddGroupCust.currentForm,
  otherData: AddGroupCust.otherData,
  ...groupCustView,
}))(Form.create()(Edit));
