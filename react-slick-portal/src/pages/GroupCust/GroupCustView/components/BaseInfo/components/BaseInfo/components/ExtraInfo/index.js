/* 基本信息-附加信息 */
import { Button, Card, Col, Descriptions, Drawer, Form, Input, message, Row, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { useBoolean } from '@umijs/hooks';
import { connect } from 'dva';
import { ExtraInfoFields } from '@/pages/GroupCust/GroupCustView/components/BaseInfo/components/BaseInfo/const';
import { modifyEnterpriseExpandInfo, qryEnterpriseExpandInfo } from '@/pages/GroupCust/GroupCustView/service';
import style from './index.less';
import { ENTERPRISE_IMPORT_FLAG, GROUP_TYPE, GROUP_TYPE_DETAIL } from '@/utils/consts';

const ExtraInfo = props => {
  const { currentCust, form } = props;
  const { getFieldDecorator } = form;
  const { state: modalVisible, setTrue: show, setFalse: hide } = useBoolean(false);
  const [loading, setLoading] = useState(false);
  const [expandInfo, setExpandInfo] = useState({});
  const handleEdit = () => {
    form.validateFields((err, values) => {
      if (!err) {
        setLoading(true);
        const params = {
          ...values,
          orgaEnterpriseId: currentCust.ORGA_ENTERPRISE_ID,
        };
        modifyEnterpriseExpandInfo(params)
          .then(res => {
            const { resultCode, resultMsg } = res;
            if (resultCode === 'TRUE') {
              message.success('修改成功');
              // eslint-disable-next-line no-use-before-define
              getDetail();
              hide();
            } else {
              message.error(resultMsg);
            }
          })
          .always(() => {
            setLoading(false);
          });
      }
    });
  };
  const getDetail = () => {
    qryEnterpriseExpandInfo({ orgaEnterpriseId: currentCust.ORGA_ENTERPRISE_ID, maskFlag: '0' }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        setExpandInfo(resultObject?.rspParam?.busiInfo?.outData);
      } else {
        message.error(resultMsg);
      }
    });
  };
  useEffect(() => {
    if (currentCust.ORGA_ENTERPRISE_ID) {
      getDetail(currentCust.ORGA_ENTERPRISE_ID);
    }
  }, [currentCust.ORGA_ENTERPRISE_ID]);
  return (
    <Card title="附加信息" id="panel-3" className="cute2" style={{ marginTop: 16 }}>
      <Row>
        <Col className="text-right" span={24}>
          <Button onClick={show}>编辑</Button>
        </Col>
      </Row>
      <Descriptions className="right" column={{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }}>
        {ExtraInfoFields.map(({ name, key, span, options }) => (
          <Descriptions.Item label={name} span={span} key={key}>
            {!options ? expandInfo[key] : options[expandInfo[key]]}
          </Descriptions.Item>
        ))}
      </Descriptions>
      {modalVisible && (
        <Drawer title="附加信息修改" width={520} onClose={hide} visible={modalVisible} destroyOnClose>
          <Form layout="horizontal">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="是否拍照集团">
                  {getFieldDecorator('isPhoto', {
                    initialValue: expandInfo.IS_PHOTO,
                  })(
                    <Select allowClear>
                      {Object.keys(ENTERPRISE_IMPORT_FLAG)
                        .filter(key => key !== '')
                        .map(item => (
                          <Select.Option value={item}>{ENTERPRISE_IMPORT_FLAG[item]}</Select.Option>
                        ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="是否100强集团">
                  {getFieldDecorator('isTop100', {
                    initialValue: expandInfo.IS_TOP100,
                  })(
                    <Select allowClear>
                      {Object.keys(ENTERPRISE_IMPORT_FLAG)
                        .filter(key => key !== '')
                        .map(item => (
                          <Select.Option value={item}>{ENTERPRISE_IMPORT_FLAG[item]}</Select.Option>
                        ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="跨省集团编号">
                  {getFieldDecorator('mpGroupCode', {
                    initialValue: expandInfo.MP_GROUP_CODE,
                  })(<Input placeholder="请输入" allowClear />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="欠费告警阈值（元）">
                  {getFieldDecorator('arrWarn', {
                    initialValue: expandInfo.ARR_WARN,
                  })(<Input placeholder="请输入" allowClear />)}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="总机号码">
                  {getFieldDecorator('switchPhone', {
                    initialValue: expandInfo.SWITCH_PHONE,
                  })(<Input placeholder="请输入" allowClear />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="集团类型">
                  {getFieldDecorator('groupType', {
                    initialValue: expandInfo.GROUP_TYPE,
                  })(
                    <Select allowClear>
                      {Object.keys(GROUP_TYPE).map(item => (
                        <Select.Option value={item}>{GROUP_TYPE[item]}</Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <Form.Item label="集团详细类型">
                  {getFieldDecorator('groupTypeDetail', {
                    initialValue: expandInfo.GROUP_TYPE_DETAIL,
                  })(
                    <Select allowClear>
                      {Object.keys(GROUP_TYPE_DETAIL).map(item => (
                        <Select.Option value={item}>{GROUP_TYPE_DETAIL[item]}</Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Form>
          <div className={style['form-drawer-bottom']}>
            <Button style={{ marginRight: '8px' }} loading={loading} onClick={handleEdit} type="primary">
              {' '}
              确定
            </Button>
            <Button onClick={hide}>取消</Button>
          </div>
        </Drawer>
      )}
    </Card>
  );
};

export default connect(({ groupCustView }) => ({
  currentCust: groupCustView.currentCust,
}))(Form.create()(ExtraInfo));
