import React, { useRef, Fragment } from 'react';
import { useBoolean, useClickAway } from '@umijs/hooks';
import { Input, AutoComplete, Select, Icon } from 'antd';
import { formatMessage } from 'umi/locale';
import classNames from 'classnames';
import { connect } from 'dva';
import Link from 'umi/link';
import styles from '../less/header.less';
import defaultSettings from '@/defaultSettings';

function SearchIcon() {
  const { state: expend, setTrue, setFalse } = useBoolean(false);

  const ref = useClickAway(() => {
    setFalse();
  });
  return (
    <span ref={ref} id="searchbar">
      <Input.Group
        compact
        className={classNames(styles.searchWrapper, {
          [styles.active]: expend,
        })}
      >
        <Select
          style={{
            width: 80,
          }}
          defaultValue="1"
          getPopupContainer={() => document.getElementById('searchbar')}
        >
          <Select.Option value="1">公告</Select.Option>
          <Select.Option value="2">合同</Select.Option>
          <Select.Option value="3">菜单</Select.Option>
          <Select.Option value="4">商机</Select.Option>
          <Select.Option value="5">客户</Select.Option>
        </Select>
        <AutoComplete
          dataSource={[]}
          style={{
            width: 240,
          }}
          getPopupContainer={() => document.getElementById('searchbar')}
          placeholder="输入关键字"
        >
          <Input.Search />
        </AutoComplete>
      </Input.Group>
      <span
        className={classNames(styles.notice, styles.action, {
          [styles.hide]: expend,
        })}
        onClick={() => {
          setTrue();
        }}
      >
        <Icon type="search" />
      </span>
    </span>
  );
}

export default connect(({ login }) => ({
  user: login.user,
}))(SearchIcon);
