/* eslint-disable no-shadow */
import React, { useEffect, useRef, useState } from 'react';
import { Form, Row, Col, Input, Button, message, Drawer, Select, Divider } from 'antd';
import { connect } from 'dva';
import { useAntdTable } from '@umijs/hooks';
// import moment from 'moment';
import moment from 'moment';
import SlickTable from '@/components/SlickTable';
import { getPageSizeByCardHeight } from '@/utils/utils';
import { getList, singOutApi, addApi, editApi, queryCommonRefl, queryCreditworthiness, checkGroup } from '../services';
import { AcctClass, CreditLevel } from '@/utils/consts';
import { payTypeList, acctClassList } from './const';
import CustomSpace from '@/components/CustomSpace';
import styles from '@/pages/GroupCust/GroupCustView/components/BaseInfo/components/AuthManage/index.less';
import AuditModal from '@/pages/Approval/components/AuditModal';


const tipText =
  '请注意:根据规范要求，信誉度到期导致停机后，再变更账户信誉度级别，无法触发开机。例如，后付费3个月，用户累计欠费到达3个月后，则第4个月的1日起代付费关系暂停;若第4个月的28日0点前未缴清欠费，在第5个月的1日代付费关系失效，欠费将产生在成员号码上，成员停机，此后变更集团账户信誉度也无法触发开机。';

const getTableData = ({ current, pageSize, ...rest }) => {
  const { groupId, accountNum } = rest; // 在params里 accountNum
  const params = {
    groupId,
    queryType: 'groupId',
    pageInfo: {
      currentPage: current,
      pageSize,
    },
  };
  if (accountNum !== '') {
    params.acctId = accountNum;
    params.queryType = 'groupId';
  }
  return getList(params).then(res => {
    const { resultCode, resultObject, resultMsg } = res;
    if (resultCode === 'TRUE' && resultObject?.acctList) {
      return {
        total: resultObject?.count,
        data: resultObject?.acctList,
      };
    }
    message.error(resultMsg);
    return {
      total: 0,
      data: [],
    };
  });
};

const Index = ({ size: { height }, form, ...otherProps }) => {
  const [size, setSize] = useState(getPageSizeByCardHeight(height));
  // eslint-disable-next-line no-unused-vars
  const { refreshFlag, currentCust, goToStep, toggleParentTab } = otherProps;
  const [drawerTitle, setDrawerTitle] = useState(''); // 弹窗名称
  const [drawerPop, setDrawerPop] = useState(false); // 打开弹窗
  const [groupAcctType, setGroupAcctType] = useState([]); // 付费类型
  const [groupCreditLevel, setGroupCreditLevel] = useState([]); // 信用度级别
  const [loadingPop, setLoadingPop] = useState(false);
  const [loadingAcctType, setLoadingAcctType] = useState(false);
  const [loadingCreditLevel, setLoadingCreditLevel] = useState(false);
  const [detail, setDetail] = useState(null);
  const tableRef = useRef(null);

  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [auditRecord, setAuditRecord] = useState('');

  const approvalModalVisibleChange = value => {
    setDrawerPop(false);
    setApprovalModalVisible(value);
  };

  const { getFieldDecorator } = form;
  const {
    tableProps,
    refresh,
    search: { submit, reset },
  } = useAntdTable(
    params => getTableData({ ...params, pageSize: size, groupId: currentCust.GROUP_ID }),
    [height], // 查询条件变化会发生请求 高度变化 加载
    {
      defaultPageSize: size,
      form,
    }
  );

  const { pagination, ...restTableProps } = tableProps;

  useEffect(() => {
    if (refreshFlag) {
      refresh();
    }
  }, [refreshFlag]);

  // 根据size.height 变化即时更新pageSize
  useEffect(() => {
    setSize(getPageSizeByCardHeight(height));
  }, [height]);

  // 判断是否可以操作
  const checkGroupOption = async record => {
    const params = {
      acctId: record.acctId,
      groupId: currentCust.GROUP_ID,
    };
    const resp = await checkGroup(params);
    if (resp.resultCode === 'TRUE' && resp?.resultObject === 'true') {
      return {
        status: true,
        mesageText: '',
      };
    }
    return {
      status: false,
      mesageText: resp.resultMsg,
    };
  };

  const handleSingOut = async record => {
    const isOption = await checkGroupOption(record);
    if (!isOption.status) {
      message.error(isOption.mesageText);
      return;
    }
    const parmas = {
      acctId: record.acctId,
      groupId: currentCust.GROUP_ID,
    };
    const resp = await singOutApi(parmas);
    const { resultCode, resultMsg } = resp;
    if (resultCode === 'TRUE') {
      message.success('注销成功');
      refresh();
    } else {
      message.error(resultMsg);
    }
  };

  // 编辑
  const handleEdit = async record => {
    const isOption = await checkGroupOption(record);
    if (!isOption.status) {
      message.error(isOption.mesageText);
      return;
    }
    const params = {
      acctId: record.acctId,
      acctName: record.acctName,
      acctClass: record.acctClass * 1,
      // acctType: (record.acctType),
      payType: record.payType,
      // creditLevel: (record.creditLevel) * 1,
    };
    setDetail(params);
    setDrawerTitle('edit');
    setDrawerPop(true);
    setLoadingAcctType(true);
    setLoadingCreditLevel(true);
    const paramsGroupAcctType = `GROUP_ACCT_TYPE[${record.acctClass}]`;
    const paramsGroupCreditLevel = { acctType: record.acctType, groupId: currentCust.GROUP_ID };
    // 并行请求接口数据
    const [respAcctType, respCreditLevel] = await Promise.all([queryCommonRefl(paramsGroupAcctType), queryCreditworthiness(paramsGroupCreditLevel)]);
    setLoadingAcctType(false);
    setLoadingCreditLevel(false);
    // 付费类型
    if (respAcctType.resultCode === 'TRUE' && respAcctType.resultObject.rspParam.busiInfo.outData) {
      const temArr = respAcctType.resultObject.rspParam.busiInfo.outData;
      setGroupAcctType(temArr);
      // eslint-disable-next-line eqeqeq
      const foundValue = temArr.find(value => value.STATIC_KEY == record.acctType);
      if (!foundValue) {
        setDetail(val => ({ ...val, acctType: foundValue }));
      } else {
        setDetail(val => ({ ...val, acctType: record.acctType }));
      }
    } else {
      setGroupAcctType([]);
    }
    // 信用度级别
    if (respCreditLevel.resultCode === 'TRUE' && respCreditLevel.resultObject.rspParam.busiInfo.outData) {
      const temArr = respCreditLevel.resultObject.rspParam.busiInfo.outData;
      setGroupCreditLevel(temArr);
      // eslint-disable-next-line eqeqeq
      const foundValue = temArr.find(value => value.CODE_VALUE == record.creditLevel);
      if (!foundValue) {
        setDetail(val => ({ ...val, creditLevel: foundValue }));
      } else {
        setDetail(val => ({ ...val, creditLevel: record.creditLevel }));
      }
    } else {
      setGroupCreditLevel([]);
    }
  };

  // 新增
  const addAccount = () => {
    setDrawerTitle('add');
    setDrawerPop(true);
  };

  // 关联账户
  const addAccountRel = () => {
    toggleParentTab(false);
    goToStep(2);
  };

  // 抽屉弹窗
  const handlePopSubmit = () => {
    setLoadingPop(true);
    form.validateFields(async (err, value) => {
      if (!err) {
        if (drawerTitle === 'edit') {
          const params = {
            acctId: detail.acctId,
            acctType: value.acctType,
            acctClass: value.acctClass,
            creditLevel: value.creditLevel,
            payType: value.payType,
            acctName: value.acctName,
            groupId: currentCust.GROUP_ID,
          };
          const { resultCode, resultObject, resultMsg } = await editApi(params);
          setLoadingPop(false);
          if (resultCode === 'TRUE' && resultObject.rspParam.busiInfo.code === '200') {
            message.success('修改成功');
            setDrawerPop(false);
            refresh();
          } else if (resultCode === '110' || resultCode === '180') {
            setApprovalModalVisible(true); // 显示弹窗
            setAuditRecord(resultObject);
          } else {
            message.error(resultMsg);
          }
        } else {
          const params = {
            creditLevel: value.creditLevel,
            payType: value.payType,
            acctClass: value.acctClass,
            acctType: value.acctType,
            groupId: currentCust.GROUP_ID,
            acctName: value.acctName,
          };
          const resp = await addApi(params);
          setLoadingPop(false);
          if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.code === '200') {
            message.success('新增成功');
            setDrawerPop(false);
            refresh();
          } else {
            message.error(`${resp.resultMsg}`);
          }
        }
      }
      setLoadingPop(false);
    });
  };

  // 账户级别变化
  const handleAcctClass = async e => {
    setLoadingAcctType(true);
    setGroupAcctType([]);
    setGroupCreditLevel([]);
    const paramsForm = {
      acctType: undefined,
      creditLevel: undefined,
    };
    form.setFieldsValue(paramsForm);
    const paramsGroupAcctType = `GROUP_ACCT_TYPE[${e}]`;
    const resp = await queryCommonRefl(paramsGroupAcctType);
    setLoadingAcctType(false);
    // 付费类型
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.outData) {
      const temArr = resp.resultObject.rspParam.busiInfo.outData;
      setGroupAcctType(temArr);
    } else {
      message.error(resp.resultMsg);
      setGroupAcctType([]);
    }
  };

  // 付费类型变化  => 信用度级别
  const handleAcctType = async e => {
    setLoadingCreditLevel(true);
    setGroupCreditLevel([]);
    const paramsForm = {
      creditLevel: undefined,
    };
    form.setFieldsValue(paramsForm);
    const paramsGroupCreditLevel = { acctType: e, groupId: currentCust.GROUP_ID };
    const resp = await queryCreditworthiness(paramsGroupCreditLevel);
    setLoadingCreditLevel(false);
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.outData) {
      const temArr = resp.resultObject.rspParam.busiInfo.outData;
      setGroupCreditLevel(temArr);
    } else {
      setGroupCreditLevel([]);
    }
  };
  return (
    <>
      <div
        style={{
          width: '100%',
          height: 'max-content',
          backgroundColor: '#DA599E',
          color: 'white',
          boxSizing: 'border-box',
          padding: '5px 10px',
          marginBottom: '10px',
        }}
      >
        {tipText}
      </div>
      <Form className="flow fix-label" onSubmit={submit}>
        <Row gutter={24} style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Col span={12} style={{ display: 'flex' }}>
            <Form.Item label="账户编码" />
            <Form.Item>
              {getFieldDecorator('accountNum', {
                initialValue: '',
              })(<Input placeholder="请输入账户编码" style={{ width: 260 }} allowClear />)}
            </Form.Item>
          </Col>
          <Col span={12} className="text-right">
            <Button className="margin-left" onClick={reset}>
              重置
            </Button>
            <Button className="margin-left" onClick={() => addAccount()}>
              新增
            </Button>
            <Button className="margin-left" onClick={() => addAccountRel()}>
              关联账户
            </Button>
            <Button type="primary" className="margin-left" htmlType="submit" loading={tableProps.loading}>
              查询
            </Button>
          </Col>
        </Row>
      </Form>
      <SlickTable
        bordered={false}
        style={{ marginTop: 10 }}
        ref={tableRef}
        scroll={{ x: 'max-content' }}
        rowKey={record => record.acctId}
        {...restTableProps}
        data={{
          pagination: {
            ...pagination,
            pageSize: size,
          },
        }}
        columns={[
          {
            title: '账户编码',
            dataIndex: 'acctId',
            width: 150,
          },
          {
            title: '账户名称',
            dataIndex: 'acctName',
            width: 200,
          },
          {
            title: '付费类型',
            dataIndex: 'acctTypeName',
            width: 150,
          },
          {
            title: '信用级别',
            dataIndex: 'creditLevel',
            width: 200,
            render: text => CreditLevel[text],
          },
          {
            title: '信用度变更人',
            dataIndex: 'creditChangeName',
            width: 150,
          },
          {
            title: '信用度变更时间',
            dataIndex: 'doneDate',
            width: 200,
            render: text => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
          },
          {
            title: '账户创建时间',
            dataIndex: 'validDate',
            width: 200,
            render: text => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
          },
          {
            title: '账户级别',
            dataIndex: 'acctClass',
            width: 150,
            render: text => AcctClass[text],
          },
          {
            title: '账户创建人',
            dataIndex: 'createStaffName',
            width: 150,
          },
          {
            title: '操作',
            dataIndex: 'index',
            key: 'index',
            fixed: 'right',
            align: 'center',
            render: (text, record) => (
              <CustomSpace align="center" wrap split={<Divider type="vertical" />}>
                <a onClick={() => handleEdit(record)}>修改</a>
                <a onClick={() => handleSingOut(record)}>注销</a>
              </CustomSpace>
            ),
          },
        ]}
      />
      <Drawer
        visible={drawerPop}
        title={drawerTitle === 'edit' ? '编辑' : '新增'}
        placement="right"
        closable={false}
        onClose={() => {
          setDrawerPop(false);
          setGroupAcctType([]);
          setGroupCreditLevel([]);
        }}
        width={500}
        style={{
          padding: 0,
          height: '100vh',
        }}
        destroyOnClose
      >
        <Form layout="horizontal">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="账户名称">
                {getFieldDecorator('acctName', {
                  initialValue: detail?.acctName || '',
                  rules: [{ required: true, message: '请输入' }],
                })(<Input allowClear />)}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="账户级别">
                {getFieldDecorator('acctClass', {
                  initialValue: detail?.acctClass,
                  rules: [{ required: true, message: '请选择' }],
                })(
                  <Select placeholder="请选择" onChange={e => handleAcctClass(e)}>
                    {acctClassList.map(e => (
                      <Select.Option key={e.value} value={e.value}>
                        {e.key}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="付费类型">
                {getFieldDecorator('acctType', {
                  initialValue: detail?.acctType || undefined,
                  rules: [{ required: true, message: '请选择' }],
                })(
                  <Select placeholder="请选择" onChange={e => handleAcctType(e)} loading={loadingAcctType}>
                    {groupAcctType.map(e => (
                      <Select.Option key={e.STATIC_KEY} value={e.STATIC_KEY}>
                        {e.STATIC_VALUE}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="付费方式">
                {getFieldDecorator('payType', {
                  initialValue: detail?.payType,
                  rules: [{ required: true, message: '请选择' }],
                })(
                  <Select placeholder="请选择">
                    {payTypeList.map(e => (
                      <Select.Option key={e.value} value={e.value}>
                        {e.key}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="信用度级别">
                {getFieldDecorator('creditLevel', {
                  initialValue: detail?.creditLevel || undefined,
                  rules: [{ required: true, message: '请选择' }],
                })(
                  <Select placeholder="请选择" loading={loadingCreditLevel}>
                    {groupCreditLevel.map(e => (
                      <Select.Option key={e.CODE_VALUE} value={e.CODE_VALUE}>
                        {e.CODE_NAME}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <div className={styles.formDrawerBottom}>
          <Button type="primary" style={{ marginRight: '10px' }} loading={loadingPop} onClick={() => handlePopSubmit()}>
            确认
          </Button>
          <Button onClick={() => setDrawerPop()}>取消</Button>
        </div>
      </Drawer>
      <AuditModal approvalModalVisible={approvalModalVisible} approvalModalVisibleChange={approvalModalVisibleChange} auditRecord={auditRecord} />
    </>
  );
};

export default connect(({ setting, groupCustView }) => ({
  size: setting.size,
  currentCust: groupCustView.currentCust,
}))(Form.create()(Index));
