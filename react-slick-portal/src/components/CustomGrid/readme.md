# CustomGrid 组件

CustomGrid 是一个高级表格选择组件，支持单选和多选，可以通过下拉表格的方式选择数据。

## 功能特点

- 支持单选和多选模式
- 支持自定义搜索字段和搜索表单
- 支持自定义表格列配置
- 支持外部控制弹出层的显示和隐藏
- 支持自定义弹出层样式
- 支持表单校验

## 使用示例

```jsx
import React from 'react';
import { Form } from 'antd';
import CustomGrid from '@/components/CustomGrid';

const Demo = ({ form }) => {
  const { getFieldDecorator } = form;
  
  return (
    <Form>
      <Form.Item label="组织标识">
        {getFieldDecorator('orgId', {
          rules: [
            {
              required: true,
              message: '组织标识不能为空',
            },
          ]
        })(
          <CustomGrid
            url="orgauth/OrganizationController/qryManageOrgGridData.do"
            popupStyle={{ width: 560 }}
            placeholder="组织标识"
            searchPlaceholder="请输入组织名称进行搜索"
            label="orgName"
            rowKey="orgId"
            pick="radio"
            params={{ createType: '1100' }}
            onConfirm={(value) => console.log(value)}
            columns={[
              {
                title: '组织标识',
                dataIndex: 'orgId',
              },
              {
                title: '组织名称',
                dataIndex: 'orgName',
                ellipsis: true,
              },
              {
                title: '组织路径',
                dataIndex: 'pathName',
                ellipsis: true,
              },
              {
                title: '组织层级',
                dataIndex: 'orgLevel',
              },
            ]}
          />
        )}
      </Form.Item>
    </Form>
  );
};

export default Form.create()(Demo);
```

## API

| 参数(*非必填)       | 说明                                                                                                            | 类型                        | 默认值                       |
| ------------------- | --------------------------------------------------------------------------------------------------------------- | --------------------------- | ---------------------------- |
| url                 | 请求地址                                                                                                        | string                      | -                            |
| label               | 选中行后，哪个字段的值显示在input上                                                                             | string                      | -                            |
| rowKey              | 表格设置key，与table的key用法一样                                                                               | string                      | -                            |
| columns             | 列配置，与table的columns用法一样                                                                                | object[]                    | -                            |
| *method             | 请求方式,可选值：'post','get'                                                                                   | string                      | 'post'                       |
| *method             | 请求方式,可选值：'post','get'                                                                                   | string                      | 'post'                       |
| *value              | 默认值，传入对象数组，对象必须包含label和rowKey所指定的2个属性                                                  | object[]                    | []                           |
| *popupPlacement     | 下拉框对齐方式。可选值：'top','right','bottom','right','topLeft','topRight','bottomLeft','bottomRight'          | string                      | 'bottomRight'                |
| *popupStyle         | 下拉框样式                                                                                                      | object                      | { width:600, height:'auto' } |
| *placeholder        | 对应input                                                                                                       | string                      | -                            |
| *searchPlaceholder  | 对应搜索框                                                                                                      | string                      | -                            |
| *destroyPopupOnHide | 收起下拉时是否销毁                                                                                              | boolean                     | false                        |
| *disabled           | 禁用                                                                                                            | boolean                     | false                        |
| *tokenSeparators    | 分隔符                                                                                                          | string                      | ','                          |
| *extra              | 把react元素显示在左下脚，比如放一些按钮；<br/>如果值为function 会往该函数传入当前的所有选中项以便实现复杂的逻辑 | React.ReactNode 或 function | null                         |
| *params             | 异步请求url时 带入的额外入参                                                                                    | object[]                    | {}                           |
| *onConfirm          | 收起dropdown时，执行的回调函数，并往该函数传入当前选中项的所有数据                                              | function(selectedRows)      | ()=>{}                       |

更多[props](https://github.com/react-component/trigger)

## demo

与form结合使用

```jsx
import React from 'react';
import { Form } from 'antd';
import CustomGrid from '@/components/CustomGrid';

class Demo extends React.Component {
  componentDidMount() {
    // 模拟异步获取数据后回显
    setTimeout(() => {
      this.props.form.setFieldsValue({
        orgId: [{ orgId: '123', orgName: '测试组织' }]
      });
    }, 1000);
  }

  render() {
    const { getFieldDecorator } = this.props.form;

    return (
      <Form>
        <Form.Item label="组织">
          {getFieldDecorator('orgId')(
            <CustomGrid
              url="orgauth/OrganizationController/qryManageOrgGridData.do"
              label="orgName"
              rowKey="orgId"
              columns={[
                { title: '组织ID', dataIndex: 'orgId' },
                { title: '组织名称', dataIndex: 'orgName' }
              ]}
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default Form.create()(Demo);
```

