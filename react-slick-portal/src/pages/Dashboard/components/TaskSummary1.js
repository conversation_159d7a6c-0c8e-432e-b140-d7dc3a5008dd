import React, { useState, useEffect } from 'react';

import { Card, Empty } from 'antd';
import { Pie } from '@/components/Charts';
import request from '@/utils/request';

const cache = {};

function TaskSummary1() {
  const [loading, setLoading] = useState(false);
  const [total1, setTotal1] = useState(0);
  const [chart1, setChart1] = useState([]);

  useEffect(() => {
    async function getInitData() {
      setLoading(true);
      // 任务概要本月新增
      const response1 = await request('orgauth/WorkbenchController/qryTaskSummary.do', {
        method: 'get',
      });

      if (
        response1 !== null &&
        response1.resultCode === '0' &&
        response1.resultObject.data.taskListInfo
      ) {
        const rstData = response1.resultObject.data.taskListInfo;

        if (rstData !== undefined) {
          const mapData = [];
          let totalCount = 0;
          rstData.forEach(function(rankData) {
            totalCount += rankData.totalAddCount;

            mapData.push({
              x: rankData.taskName,
              y: rankData.totalAddCount,
              name: rankData.taskName,
            });
          });
          cache.chart1 = mapData;
          cache.total1 = totalCount;
          setTotal1(totalCount);
          setChart1(mapData);
        }
      }

      setLoading(false);
    }
    if (Object.hasOwnProperty.call(cache, 'chart1') === false) {
      getInitData();
    }
  }, []);

  let _chart1;
  let _total1;
  if (cache.chart1 === undefined) {
    _chart1 = chart1;
    _total1 = total1;
  } else {
    _chart1 = cache.chart1;
    _total1 = cache.total1;
  }
  return (
    <Card bordered title="任务概要（本月新增）" loading={loading} bodyStyle={{ height: '356px' }}>
      {_total1 === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="任务概要本月增长0个"
          style={{ marginTop: 120 }}
        />
      ) : (
        <Pie
          hasLegend
          title="任务新增数"
          subTitle="任务新增数"
          total={<span>{_total1}个</span>}
          // colors={['#8543e0', '#13c2c2', '#facc14']}
          data={_chart1}
          tooltipFormat={[
            'x*y*percent',
            (x, y, p) => ({
              name: `${x} ${y}个`,
              value: `${(p * 100).toFixed(2)}%`,
            }),
          ]}
          valueFormat={val => <span dangerouslySetInnerHTML={{ __html: `${val}个` }} />}
          height={294}
        />
      )}
    </Card>
  );
}

export default TaskSummary1;
