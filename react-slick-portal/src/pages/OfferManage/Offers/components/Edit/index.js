import React, { useRef, useState, useEffect } from 'react';
import {
  <PERSON><PERSON>, <PERSON><PERSON>, Card, Spin, message,
} from 'antd';
import BaseInfo from './BaseInfo';
import ExpandInfo from './ExpandInfo';
import FooterToolbar from '@/components/FooterToolbar';
import style from './style.less';
import { detailById, steEdit } from '@/services/offer';
import { ITEM_NBR, ITEM_VALUE, ITEM_LABEL } from '@/pages/OfferManage/Offers/const';

const { TabPane } = Tabs;
const Edit = props => {
  const [baseInfo, setBaseInfo] = useState({});
  const [expandInfo, setExpandInfo] = useState([]);
  const [loading, setLoading] = useState(false);
  const Bcref = useRef();
  const Scref = useRef();

  const getCaseDetail = async () => {
    setLoading(true);
    try {
      const result = await detailById(props.id);
      if (result.resultCode === 'TRUE' && result.resultObject) {
        setBaseInfo({
          ...result.resultObject.baseInfo,
          showFlag: (result.resultObject.offerGroupRelDTOS?.find(item => item.groupId === ITEM_NBR.posterRecords) || {}).showFlag,
          posterRecords: (result.resultObject.offerGroupRelDTOS?.find(item => item.groupId === ITEM_NBR.posterRecords) || {}).posterRecords || [],
        } || {});
        setExpandInfo(result.resultObject.offerGroupRelDTOS?.filter(item => item.groupId !== '1') || []);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (props.id) {
      getCaseDetail();
    }
  }, [props.id]);

  const handleSubmit = async () => {
    const params = {};
    params.baseInfo = baseInfo;
    delete params.baseInfo.showFlag;
    delete params.baseInfo.posterRecords;
    params.offerGroupRelDTOS = expandInfo;
    if (Bcref.current) {
      const res = await Bcref.current.submitBaseInfo();
      if (!res) {
        return;
      }
      params.baseInfo = { ...params.baseInfo, ...res.baseInfo };
      params.offerGroupRelDTOS = params.offerGroupRelDTOS.length === 0 ? res.offerGroupRelDTOS : params.offerGroupRelDTOS.map(item => {
        if (item.groupId === '8') {
          item = { ...item, ...res.offerGroupRelDTOS[0] };
        }
        return item;
      });
      const data = params.offerGroupRelDTOS.filter(item => item.groupId === '8');
      if (data.length === 0) {
        params.offerGroupRelDTOS.push(res.offerGroupRelDTOS[0]);
      }
    }
    if (Scref.current) {
      const res2 = await Scref.current.submitExpandInfo();
      if (!res2) {
        return;
      }
      const posterItem = params.offerGroupRelDTOS.filter(item => item.groupId === '8');
      params.offerGroupRelDTOS = res2.concat(posterItem);
    }
    let isShowMes = false;
    params.offerGroupRelDTOS.map(item => {
      if (item.showFlag === '1' && item[ITEM_VALUE[item.groupId]].length <= 0) {
        isShowMes = true;
        return message.warn(`${ITEM_LABEL[ITEM_VALUE[item.groupId]]}不能为空`);
      }
      return null;
    });
    const { files } = params.baseInfo;
    const defaultPicture = (files || []).find(item => item.docSubType === '1' && item.statusCd === '1000') || {};
    const backgroundPicture = (files || []).find(item => item.docSubType === '2' && item.statusCd === '1000') || {};
    const isPicture = Object.keys(defaultPicture).length > 0 && Object.keys(backgroundPicture).length > 0;
    if (isShowMes || !isPicture) {
      isShowMes = false;
      if (!isPicture) {
        message.warn(`${OFFER_FLAG}图片/背景图片都必须有一张生效图片`);
      }
    } else {
      setLoading(true);
      try {
        await steEdit(params);
        message.success('编辑成功');
        setLoading(false);
        props.goToStep(1);
        props.refresh();
      } catch (e) {
        setLoading(false);
        // 保存数据失败
      }
    }
  };

  return (
    <div>
      <Spin spinning={loading}>
        <Card
          title={`${OFFER_FLAG}编辑`}
          className="cute"
        >
          <div className={style.wrap}>
            <Tabs type="card">
              <TabPane tab="基本信息" key="1">
                <BaseInfo ref={Bcref} cref={Bcref} datas={baseInfo} />
              </TabPane>
              <TabPane tab="拓展信息" key="2">
                <ExpandInfo sref={Scref} datas={expandInfo} offerId={baseInfo.offerId} />
              </TabPane>
            </Tabs>
          </div>
        </Card>
      </Spin>
      <FooterToolbar>
        <Button
          type="primary"
          loading={loading}
          onClick={() => {
            handleSubmit();
          }}
        >
          提交
        </Button>
        <Button
          type="default"
          loading={loading}
          onClick={() => props.goToStep(1)}
        >
          返回
        </Button>
      </FooterToolbar>
    </div>
  );
};

export default (Edit);
