import React, { useEffect, useState } from 'react';
import { Button, Modal, Icon, Table, Form, Switch, Input } from 'antd';
import { connect } from 'dva';
import style from './ApplicationComp.module.less';
import { ImageUploader } from '@/components/Uploader';
import { MODE } from '@/pages/Librarys/const';

/**
 * 应用场景组件
 * @param {props} props
 */
const ApplicationComp = props => {
  const { mode, form, knowledgeDetailInfo } = props;
  const { setFieldsValue } = form;
  const [showModal, setShowModal] = useState(false);
  const [appMode, setAppMode] = useState('');

  const [curInfo, setCurInfo] = useState({});
  const [applicationList, setApplicationList] = useState([]);

  const [title, setTitle] = useState('');
  const [desc, setDesc] = useState('');
  const [curIdx, setCurIdx] = useState(0);

  const [isChecked, setIsChecked] = useState(false);
  const [imgList, setImgList] = useState([]);

  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    const rootStyle = document.getElementById('root').style;
    if (showModal) {
      rootStyle.overflow = 'hidden';
      rootStyle.position = 'fixed';
    } else {
      rootStyle.overflowY = 'auto';
      rootStyle.position = '';
    }
  }, [showModal]);

  const columns = [
    {
      title: '标题',
      width: '40%',
      dataIndex: 'title',
      ellipsis: true,
    },
    {
      title: '描述',
      width: '50%',
      dataIndex: 'desc',
      ellipsis: true,
    },
    {
      title: '操作',
      width: '10%',
      render(v, r, idx) {
        const arr = [];
        arr.push(
          <Button
            type="link"
            style={{ paddingLeft: '0' }}
            onClick={() => {
              setCurInfo(r);
              setCurIdx(idx);
              setTitle(r.title);
              setDesc(r.desc);
              setImgList(r.imgList);
              setAppMode('edit');
              setShowModal(true);
            }}
          >
            编辑
          </Button>
        );
        arr.push(
          <Button
            type="link"
            style={{ padding: '0' }}
            onClick={() => {
              const _fileList = applicationList.filter(item => item.id !== r.id);
              setApplicationList([..._fileList]);
              setFieldsValue({
                sceneGroupRel: {
                  sceneGroupRelId: knowledgeDetailInfo?.sceneGroupRel?.sceneGroupRelId,
                  showFlag: isChecked ? 1 : 0,
                  sceneDisplayRecordVos: _fileList.map(item => ({
                    title: item.title,
                    desc: item.desc,
                    docNbr: item.imgList.length > 0 ? item?.imgList?.[0].response?.[0].docNbr || item?.imgList?.[0].docNbr : null,
                    docId: item.imgList.length > 0 ? item?.imgList?.[0].response?.[0].docId || item?.imgList?.[0].docId : null,
                  })),
                },
              });
            }}
          >
            删除
          </Button>
        );
        return arr;
      },
    },
  ];

  useEffect(() => {
    // 编辑模式初始化数据
    if (mode === MODE.UPDATE) {
      const sceneDisplay = knowledgeDetailInfo?.sceneGroupRel?.sceneDisplayRecordVos || [];
      const _showFlag = knowledgeDetailInfo?.sceneGroupRel?.showFlag;
      setFieldsValue({
        sceneGroupRel: {
          sceneGroupRelId: knowledgeDetailInfo?.sceneGroupRel?.sceneGroupRelId,
          showFlag: _showFlag,
          sceneDisplayRecordVos: sceneDisplay,
        },
      });
      setIsChecked(_showFlag === '1');
      setApplicationList(
        sceneDisplay.map(item => ({
          id: new Date(),
          title: item.title,
          desc: item.desc,
          imgList: item.docFile
            ? [item.docFile].map(_item => ({
              ..._item,
              name: _item.docName,
              uid: _item.id,
              url: _item.docLink,
              type: 'image/png',
              response: [
                {
                  docNbr: _item.docNbr,
                  docId: _item.docId,
                },
              ],
            }))
            : [],
        }))
      );
    }
  }, []);

  return (
    <>
      <div>
        <span style={{ fontSize: '14px', marginRight: '8px' }}>应用场景</span>
        <Switch
          checked={isChecked}
          onChange={checked => {
            setIsChecked(checked);
            setFieldsValue({
              sceneGroupRel: {
                sceneGroupRelId: knowledgeDetailInfo?.sceneGroupRel?.sceneGroupRelId,
                showFlag: checked ? 1 : 0,
                sceneDisplayRecordVos: applicationList.map(item => ({
                  title: item.title,
                  desc: item.desc,
                  docNbr: item.imgList.length > 0 ? item?.imgList?.[0].response?.[0].docNbr || item?.imgList?.[0].docNbr : null,
                  docId: item.imgList.length > 0 ? item?.imgList?.[0].response?.[0].docId || item?.imgList?.[0].docId : null,
                })),
              },
            });
          }}
        />
      </div>
      <Button
        onClick={() => {
          setShowModal(true);
        }}
        style={{ margin: '8px 0' }}
      >
        <Icon type="plus-circle" theme="filled" />
        添加
      </Button>
      <Table bordered dataSource={applicationList} rowKey={(r, idx) => `${idx}`} columns={columns} pagination={false} />
      <Modal
        title={`应用场景${appMode === 'edit' ? '编辑' : '新增'}`}
        visible={showModal}
        destroyOnClose
        onCancel={() => {
          setShowModal(false);
          setAppMode('');
          setTitle('');
          setDesc('');
          setImgList([]);
        }}
        width={800}
        className={style.modalWrap}
        onOk={() => {
          if (appMode === 'edit') {
            const _appList = applicationList;
            const obj = _appList.find(item => item.id === curInfo.id);
            obj.title = title;
            obj.desc = desc;
            obj.imgList = imgList;
            _appList.splice(curIdx, 1, obj);
            setApplicationList(_appList);
            setFieldsValue({
              sceneGroupRel: {
                sceneGroupRelId: knowledgeDetailInfo?.sceneGroupRel?.sceneGroupRelId,
                showFlag: isChecked ? 1 : 0,
                sceneDisplayRecordVos: _appList.map(item => ({
                  title: item.title,
                  desc: item.desc,
                  docNbr: item.imgList.length > 0 ? item?.imgList?.[0].response?.[0].docNbr || item?.imgList?.[0].docNbr : null,
                  docId: item.imgList.length > 0 ? item?.imgList?.[0].response?.[0].docId || item?.imgList?.[0].docId : null,
                })),
              },
            });
          } else {
            const _applicationList = [
              ...applicationList,
              {
                id: new Date(),
                title,
                desc,
                imgList,
              },
            ];
            setApplicationList(_applicationList);
            setFieldsValue({
              sceneGroupRel: {
                sceneGroupRelId: knowledgeDetailInfo?.sceneGroupRel?.sceneGroupRelId,
                showFlag: isChecked ? 1 : 0,
                sceneDisplayRecordVos: _applicationList.map(item => ({
                  title: item.title,
                  desc: item.desc,
                  docNbr: item.imgList.length > 0 ? item?.imgList?.[0].response?.[0].docNbr || item?.imgList?.[0].docNbr : null,
                  docId: item.imgList.length > 0 ? item?.imgList?.[0].response?.[0].docId || item?.imgList?.[0].docId : null,
                })),
              },
            });
          }
          setTitle('');
          setDesc('');
          setImgList([]);
          setAppMode('');
          setShowModal(false);
        }}
        okButtonProps={{ disabled: title === '' || desc === '' || uploading }}
      >
        <Form labelCol={{ span: 2 }} wrapperCol={{ span: 22 }}>
          <Form.Item label="标题" required>
            <Input value={title} onChange={e => setTitle(e.target.value)} />
          </Form.Item>
          <Form.Item label="图片">
            <ImageUploader
              uploadUrl="portal/FileStoreController/uploadFile.do"
              title="上传图片"
              maximum={1}
              uploadedFileList={imgList}
              onFileListChange={file => {
                if (file.file.status === 'uploading') {
                  setUploading(true);
                } else {
                  setUploading(false);
                }
                setImgList(file.fileList);
              }}
              dimension={[600, 300]}
              accept={['jpg', 'png']}
              fileSizeForKB={200}
            />
            <span className={style.tips}>图片大小为600*300px，不超过200k，格式为.png或.jpg</span>
          </Form.Item>
          <Form.Item label="描述" required>
            <Input.TextArea value={desc} onChange={e => setDesc(e.target.value)} rows={4} maxLength={2000} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

const ApplicationCompWrapper = connect(({ login }) => ({
  user: login.user,
}))(ApplicationComp);

export default React.forwardRef((props, ref) => <ApplicationCompWrapper {...props} refInstance={ref} />);
