import React, { useState } from 'react';
import { Button, message } from 'antd';
import request from '@/utils/request';

const InfoCheck = ({
  onBeforeCompare,
  onSuccess,
  onFail,
  checkData = {},
  buttonText = '信息核验',
}) => {
  const [loading, setLoading] = useState(false);
  // 执行信息核验
  const handleCheck = async () => {
    // 执行前置处理
    if (onBeforeCompare && typeof onBeforeCompare === 'function') {
      const canContinue = onBeforeCompare();
      if (canContinue === false) {
        return;
      }
    }
    console.log('点击核验的值', checkData);

    // 获取核验参数
    const { name, idenNr } = onBeforeCompare();

    // 验证参数
    if (!name || !idenNr) {
      message.error('姓名和证件号码不能为空');
      return;
    }

    // 开始加载
    setLoading(true);

    try {
      // 构建请求参数
      const params = {
        checkType: '2',
        busiData: {
          idenName: name,
          idenNr,
        },
      };
      // 发送请求
      const response = await request('portal/CrmSystemController/onlineComparison.do', {
        method: 'POST',
        data: params,
      });

      // 处理响应
      if (response.resultCode === 'TRUE') {
        // 调用成功回调
        if (onSuccess && typeof onSuccess === 'function') {
          onSuccess(response.resultObject);
        }
      } else {
        message.error(`信息核验不通过，原因：${response.resultMsg}`);
        // 调用失败回调
        if (onFail && typeof onFail === 'function') {
          onFail(response);
        }
      }
    } catch (error) {
      message.error('核验请求失败，请稍后重试');

      // 调用失败回调
      if (onFail && typeof onFail === 'function') {
        onFail({ resultCode: 'FALSE', resultMsg: '请求异常' });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      type="primary"
      onClick={handleCheck}
      loading={loading}
      // disabled={checkData.infoCheckResult !== null}
    >
      {/* {checkData.infoCheckResult ? '已完成核验' : buttonText} */}
      {buttonText}
    </Button>
  );
};

export default InfoCheck;
