import React from 'react';
import FileViewer from 'react-file-viewer';
import ExcelPreview from '../components/FilePreviewerModal/ExcelPreview';
import styles from './PreviewerForLowCode.less';

const PreviewerForLowCode = ({ location = {} }) => {
  const { query = {} } = location;
  const { knowledgeInfoId, docNbr, fileName = '', nowatermark = 'false' } = query;
  const fileType = fileName.split('.')[fileName.split('.')?.length - 1];

  const nowatermarkUrl = `portal/FileStoreController/download.do?docNbr=${docNbr}`;
  const fileUrl = `portal/KIKnowledgeDownloadController/downloadDocument.do?knowledgeInfoId=${knowledgeInfoId}&docNbr=${docNbr}&type=0&_=${new Date().getTime()}`;
  const finalUrl = JSON.parse(nowatermark) ? nowatermarkUrl : fileUrl;
  const handleFileType = type => {
    switch (type) {
      case 'xls':
        return 'xlsx';
      case 'doc':
      case 'docx':
      case 'wps':
      case 'ppt':
      case 'pptx':
        return 'pdf';
      default:
        return type;
    }
  };

  return fileType === 'png' || fileType === 'jpg' ? (
    <div className={styles.contentWrap}>
      <img
        alt="avatar"
        src={finalUrl}
        style={{
          maxWidth: '100%',
        }}
      />
    </div>
  ) : (
    <div style={{ backgroundColor: 'white', height: fileType === 'xls' || fileType === 'xlsx' ? '100%' : 'initial' }}>
      {fileType !== 'xls' && fileType !== 'xlsx' && (
        <FileViewer fileType={handleFileType(fileType)} filePath={finalUrl} onError={() => {}} style={{ margin: '0 auto' }} />
      )}
      {(fileType === 'xls' || fileType === 'xlsx') && <ExcelPreview url={finalUrl} watermark={!JSON.parse(nowatermark)} fromLowCode />}
    </div>
  );
};

export default PreviewerForLowCode;
