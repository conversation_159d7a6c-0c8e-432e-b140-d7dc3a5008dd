import React, { useState } from 'react';
import { Card, Row, Col, Slider, Select, Space, Typography } from 'antd';
import {
  // 基础操作图标
  AddIcon, DeleteIcon, EditIcon, SearchIcon, FilterIcon, RefreshIcon, CloseIcon, MoreIcon, SaveIcon,
  // 导航图标
  HomeIcon, BackIcon, ForwardIcon, UpIcon, DownIcon, LeftIcon, RightIcon, MenuIcon, ExpandIcon, CollapseIcon,
  // 状态图标
  SuccessIcon, ErrorIcon, WarningIcon, InfoIcon, LoadingIcon, CheckIcon, QuestionIcon, NotificationIcon,
  // 文件和用户图标
  FileIcon, FolderIcon, UploadIcon, DownloadIcon, UserIcon, TeamIcon, SettingIcon, LogoutIcon,
  // 常量和动态组件
  ICON_SIZES, ICON_COLORS, DynamicIcon, IconMap
} from './index';

const { Title, Paragraph } = Typography;
const { Option } = Select;

const IconDemo = () => {
  const [iconSize, setIconSize] = useState(ICON_SIZES.LARGE);
  const [iconColor, setIconColor] = useState(ICON_COLORS.PRIMARY);

  // 图标分组
  const iconGroups = {
    '基础操作': [
      { name: 'add', component: AddIcon, label: '添加' },
      { name: 'delete', component: DeleteIcon, label: '删除' },
      { name: 'edit', component: EditIcon, label: '编辑' },
      { name: 'search', component: SearchIcon, label: '搜索' },
      { name: 'filter', component: FilterIcon, label: '过滤' },
      { name: 'refresh', component: RefreshIcon, label: '刷新' },
      { name: 'close', component: CloseIcon, label: '关闭' },
      { name: 'more', component: MoreIcon, label: '更多' },
      { name: 'save', component: SaveIcon, label: '保存' },
    ],
    '导航': [
      { name: 'home', component: HomeIcon, label: '首页' },
      { name: 'back', component: BackIcon, label: '返回' },
      { name: 'forward', component: ForwardIcon, label: '前进' },
      { name: 'up', component: UpIcon, label: '向上' },
      { name: 'down', component: DownIcon, label: '向下' },
      { name: 'left', component: LeftIcon, label: '向左' },
      { name: 'right', component: RightIcon, label: '向右' },
      { name: 'menu', component: MenuIcon, label: '菜单' },
      { name: 'expand', component: ExpandIcon, label: '展开' },
      { name: 'collapse', component: CollapseIcon, label: '收起' },
    ],
    '状态': [
      { name: 'success', component: SuccessIcon, label: '成功' },
      { name: 'error', component: ErrorIcon, label: '错误' },
      { name: 'warning', component: WarningIcon, label: '警告' },
      { name: 'info', component: InfoIcon, label: '信息' },
      { name: 'loading', component: LoadingIcon, label: '加载' },
      { name: 'check', component: CheckIcon, label: '检查' },
      { name: 'question', component: QuestionIcon, label: '问号' },
      { name: 'notification', component: NotificationIcon, label: '通知' },
    ],
    '文件和用户': [
      { name: 'file', component: FileIcon, label: '文件' },
      { name: 'folder', component: FolderIcon, label: '文件夹' },
      { name: 'upload', component: UploadIcon, label: '上传' },
      { name: 'download', component: DownloadIcon, label: '下载' },
      { name: 'user', component: UserIcon, label: '用户' },
      { name: 'team', component: TeamIcon, label: '团队' },
      { name: 'setting', component: SettingIcon, label: '设置' },
      { name: 'logout', component: LogoutIcon, label: '退出' },
    ],
  };

  const renderIconGroup = (groupName, icons) => (
    <Card key={groupName} title={groupName} style={{ marginBottom: 16 }}>
      <Row gutter={[16, 16]}>
        {icons.map(({ name, component: IconComponent, label }) => (
          <Col key={name} span={6} style={{ textAlign: 'center' }}>
            <div style={{ padding: 16, border: '1px solid #f0f0f0', borderRadius: 4 }}>
              <IconComponent size={iconSize} color={iconColor} />
              <div style={{ marginTop: 8, fontSize: 12 }}>{label}</div>
              <div style={{ fontSize: 10, color: '#999' }}>{name}</div>
            </div>
          </Col>
        ))}
      </Row>
    </Card>
  );

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>图标组件系统</Title>
      
      <Card title="控制面板" style={{ marginBottom: 24 }}>
        <Space size="large">
          <div>
            <label>大小: {iconSize}px</label>
            <Slider
              style={{ width: 200, marginLeft: 16 }}
              min={12}
              max={48}
              value={iconSize}
              onChange={setIconSize}
            />
          </div>
          <div>
            <label>颜色: </label>
            <Select
              style={{ width: 120, marginLeft: 8 }}
              value={iconColor}
              onChange={setIconColor}
            >
              <Option value={ICON_COLORS.PRIMARY}>主色</Option>
              <Option value={ICON_COLORS.SUCCESS}>成功</Option>
              <Option value={ICON_COLORS.WARNING}>警告</Option>
              <Option value={ICON_COLORS.ERROR}>错误</Option>
              <Option value={ICON_COLORS.DEFAULT}>默认</Option>
              <Option value="#ff6b6b">自定义红</Option>
              <Option value="#4ecdc4">自定义青</Option>
            </Select>
          </div>
        </Space>
      </Card>

      {Object.entries(iconGroups).map(([groupName, icons]) => 
        renderIconGroup(groupName, icons)
      )}

      <Card title="使用示例">
        <Paragraph>
          <Title level={4}>1. 基础使用</Title>
          <pre>{`import { AddIcon, DeleteIcon } from '@/components/Icons';

<AddIcon size={24} color="#1890ff" />
<DeleteIcon size={20} color="#f5222d" />`}</pre>
        </Paragraph>

        <Paragraph>
          <Title level={4}>2. 动态使用</Title>
          <pre>{`import { DynamicIcon } from '@/components/Icons';

<DynamicIcon name="add" size={24} color="#1890ff" />
<DynamicIcon name="delete" size={20} color="#f5222d" />`}</pre>
        </Paragraph>

        <Paragraph>
          <Title level={4}>3. 使用预设常量</Title>
          <pre>{`import { AddIcon, ICON_SIZES, ICON_COLORS } from '@/components/Icons';

<AddIcon size={ICON_SIZES.LARGE} color={ICON_COLORS.PRIMARY} />`}</pre>
        </Paragraph>
      </Card>
    </div>
  );
};

export default IconDemo;
