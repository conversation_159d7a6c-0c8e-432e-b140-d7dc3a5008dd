import React, { useEffect, useMemo, useState } from 'react';
import { But<PERSON>, Card, Col, DatePicker, Descriptions, Drawer, Form, Input, message, Row, Select, Switch } from 'antd';
import moment from 'moment';
import { TaxpayerInfoFields } from '@/pages/GroupCust/GroupCustView/components/BaseInfo/components/BaseInfo/const';
import {
  modQualificationInfo,
  queryPropertyInfo,
} from '@/pages/GroupCust/GroupCustView/components/BaseInfo/components/BaseInfo/components/TaxpayerInfo/service';
import style from '@/pages/GroupCust/GroupCustView/components/BaseInfo/components/BaseInfo/components/ExtraInfo/index.less';
import { ENTERPRISE_ATTR, INVOICE_PRINT_TYPE, PRINT_QUALIFICATIONS, TAX_QUALIFICATION } from '@/utils/consts';
import { disableEndDate, disableStartDate, validateTaxId } from '@/utils/validator';

const { Option } = Select;
const PropertyInfo = ({
  currentCust,
  form,
  user: {
    userInfo: { externalUserInfos },
  },
}) => {
  const { getFieldDecorator } = form;
  const [visible, setVisible] = useState(false);
  const [info, setInfo] = useState({});
  const [loading, setLoading] = useState(false);
  const [invoicePrintTypeOptions, setInvoicePrintTypeOptions] = useState({});
  const getInfo = () => {
    queryPropertyInfo({
      custId: currentCust.CUST_ID || 1,
    }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        setInfo(resultObject?.rspParam?.busiInfo?.outData?.[0] || {});
      } else {
        message.error(resultMsg);
      }
    });
  };
  const handleChangeTaxQual = () => {
    form.setFieldsValue({
      invoicePrintType: '',
    });
  };
  // 发票打印类型 选项
  useEffect(() => {
    const taxQualification = form.getFieldValue('taxQualification');
    setInvoicePrintTypeOptions(INVOICE_PRINT_TYPE[taxQualification] || {});
  }, [form.getFieldValue('taxQualification')]);

  /* const invoicePrintTypeOptions = useMemo(() => {
    const taxQualification = form.getFieldValue('taxQualification');
    return INVOICE_PRINT_TYPE[taxQualification] || {};
  }, [form.getFieldValue('taxQualification')]); */
  const handleOk = () => {
    form.validateFields((errors, values) => {
      if (!errors) {
        setLoading(true);
        const { licenseBeginDate, licenseEndDate, taxStatus } = values;
        modQualificationInfo({
          ...values,
          taxStatus: taxStatus ? '3' : '2', // 2,否 更新状态 3,是 失效状态
          licenseBeginDate: licenseBeginDate && moment(licenseBeginDate).format('YYYY-MM-DD'),
          licenseEndDate: licenseEndDate && moment(licenseEndDate).format('YYYY-MM-DD'),
          nextAuditId: '-1',
          nextAuditName: '',
          custId: currentCust.CUST_ID,
          applyOpId: externalUserInfos.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000')?.externalUserId,
          applyOpName: externalUserInfos.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000')?.externalUserCode,
          groupId: currentCust.GROUP_ID,
          groupName: currentCust.GROUP_NAME,
        })
          .then(res => {
            const { resultCode, resultMsg } = res;
            if (resultCode === 'TRUE') {
              message.success('编辑成功');
              getInfo();
              setVisible();
            } else {
              message.error(resultMsg);
            }
          })
          .always(() => {
            setLoading(false);
          });
      }
    });
  };
  const handleCancel = () => {
    setVisible(false);
  };

  /**
   * 根据 INVOICE_PRINT_TYPE 和 ENTERPRISE_ATTR 判断是否需要税号
   *
   * @returns {boolean} - True if the tax ID is required, false otherwise.
   */
  const checkTaxIdRequired = useMemo(() => {
    const invoicePrintType = form.getFieldValue('INVOICE_PRINT_TYPE');
    const enterpriseAttr = form.getFieldValue('ENTERPRISE_ATTR');
    // 增值税电子普通发票 && 非企业性单位 不需要填写
    const flag = !(`${invoicePrintType}` === '4' && `${enterpriseAttr}` === '3');
    return flag;
  }, [form.getFieldValue('INVOICE_PRINT_TYPE'), form.getFieldValue('ENTERPRISE_ATTR')]);
  useEffect(() => {
    getInfo();
  }, [currentCust]);
  const renderExtra = () => (
    <Button
      type={Object.keys(info).length ? undefined : 'primary'}
      onClick={() => setVisible(true)}
    >
      {Object.keys(info).length ? '编辑' : '新增'}
    </Button>
  );
  return (
    <Card className="cute" title="属性信息" extra={renderExtra()}>
      <div className="cute" title="属性信息" style={{ marginTop: 20 }}>
        <Descriptions className="right" column={{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }}>
          {TaxpayerInfoFields.map(({ name, key, span, options }) => (
            <Descriptions.Item label={name} span={span} key={key}>
              {!options ? info?.[key] : options.find(item => item.value === info?.[key])?.label}
            </Descriptions.Item>
          ))}
        </Descriptions>
      </div>
      <Drawer title="编辑纳税人属性信息" visible={visible} onOk={handleOk} onClose={handleCancel} width="520px" destroyOnClose>
        <Form layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="纳税人资质属性">
                {getFieldDecorator('taxQualification', {
                  initialValue: info.TAX_QUALIFICATION,
                  rules: [{ required: true, message: '请输入纳税人资质属性!' }],
                })(
                  <Select placeholder="请选择" allowClear onChange={handleChangeTaxQual}>
                    {Object.keys(TAX_QUALIFICATION).map(item => (
                      <Select.Option value={item}>{TAX_QUALIFICATION[item]}</Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="发票打印类型">
                {getFieldDecorator('invoicePrintType', {
                  initialValue: info.INVOICE_PRINT_TYPE,
                  rules: [{ required: true, message: '请选择发票打印类型!' }],
                })(
                  <Select placeholder="请选择" allowClear>
                    {Object.keys(invoicePrintTypeOptions).map(item => (
                      <Select.Option value={item}>{invoicePrintTypeOptions[item]}</Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="企业属性">
                {getFieldDecorator('enterpriseAttr', {
                  initialValue: info.ENTERPRISE_ATTR,
                  rules: [{ required: true, message: '请输入企业属性!' }],
                })(
                  <Select placeholder="请选择" allowClear>
                    {Object.keys(ENTERPRISE_ATTR).map(item => (
                      <Option value={item} key={item}>
                        {ENTERPRISE_ATTR[item]}
                      </Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="企业名称">
                {getFieldDecorator('enterpriseName', {
                  initialValue: info.ENTERPRISE_NAME,
                  rules: [{ required: true, message: '请输入企业名称!' }],
                })(<Input placeholder="请输入" allowClear />)}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="纳税人识别号">
                {getFieldDecorator('taxId', {
                  initialValue: info.TAX_ID,
                  rules: [
                    {
                      required: checkTaxIdRequired,
                      message: '请输入纳税人识别号!',
                    },
                    { validator: validateTaxId },
                  ],
                })(<Input placeholder="请输入" allowClear />)}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="纳税人电话（固话）">
                {getFieldDecorator('taxTel', {
                  initialValue: info.TAX_TEL,
                  rules: [{ required: true, message: '请输入纳税人电话!' }],
                })(<Input placeholder="请输入" allowClear />)}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="纳税人开户行">
                {getFieldDecorator('taxBankAcct', {
                  initialValue: info.TAX_BANK_ACCT,
                  rules: [{ required: true, message: '请输入纳税人开户行!' }],
                })(<Input placeholder="请输入" allowClear />)}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="纳税人账号">
                {getFieldDecorator('taxAcctNum', {
                  initialValue: info.TAX_ACCT_NUM,
                  rules: [{ required: true, message: '请输入纳税人账号!' }],
                })(<Input placeholder="请输入" allowClear />)}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="纳税人账户名称">
                {getFieldDecorator('taxAcctName', {
                  initialValue: info.TAX_ACCT_NAME,
                  rules: [{ required: true, message: '请输入纳税人账户名称!' }],
                })(<Input placeholder="请输入" allowClear />)}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="营业执照开始时间">
                {getFieldDecorator('licenseBeginDate', {
                  initialValue: info.LICENSE_BEGIN_DATE && moment(info.LICENSE_BEGIN_DATE),
                })(
                  <DatePicker
                    format="YYYY-MM-DD"
                    allowClear
                    disabledDate={disableStartDate(form.getFieldValue('licenseEndDate'))}
                    style={{ width: '100%' }}
                  />
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="营业执照结束时间">
                {getFieldDecorator('licenseEndDate', {
                  initialValue: info.LICENSE_END_DATE && moment(info.LICENSE_END_DATE),
                })(
                  <DatePicker
                    format="YYYY-MM-DD"
                    allowClear
                    disabledDate={disableEndDate(form.getFieldValue('licenseBeginDate'))}
                    style={{ width: '100%' }}
                  />
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="是否失效">
                {getFieldDecorator('taxStatus', {
                  valuePropName: 'checked',
                  initialValue: info.TAX_STATUS === '3' || false, // 默认是否-2，
                  rules: [{ required: true, message: '请选择!' }],
                })(<Switch checkedChildren="是" unCheckedChildren="否" />)}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="纳税人地址">
                {getFieldDecorator('taxAddress', {
                  initialValue: info.TAX_ADDRESS,
                  rules: [{ required: true, message: '请输入纳税人地址!' }],
                })(<Input placeholder="请输入" allowClear />)}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="打印增值税资质">
                {getFieldDecorator('print', {
                  initialValue: info.PRINT,
                  rules: [{ required: true, message: '请输入打印增值税资质!' }],
                })(
                  <Select placeholder="请选择" allowClear>
                    {Object.keys(PRINT_QUALIFICATIONS).map(item => (
                      <Option value={item} key={item}>
                        {PRINT_QUALIFICATIONS[item]}
                      </Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="预打印增值税资质">
                {getFieldDecorator('prePrint', {
                  initialValue: info.PRE_PRINT,
                  rules: [{ required: true, message: '请输入预打印增值税资质!' }],
                })(
                  <Select placeholder="请选择" allowClear>
                    {Object.keys(PRINT_QUALIFICATIONS).map(item => (
                      <Option value={item} key={item}>
                        {PRINT_QUALIFICATIONS[item]}
                      </Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <div className={style['form-drawer-bottom']}>
          <Button style={{ marginRight: '8px' }} loading={loading} onClick={handleOk} type="primary">
            确定
          </Button>
          <Button onClick={handleCancel}>取消</Button>
        </div>
      </Drawer>
    </Card>
  );
};

export default Form.create()(PropertyInfo);
