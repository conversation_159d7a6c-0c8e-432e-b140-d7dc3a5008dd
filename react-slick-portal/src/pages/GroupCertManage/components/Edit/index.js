/* eslint-disable no-console */
import React, { useEffect, useMemo, useState } from 'react';
import { Button, Card, Col, DatePicker, Form, Input, message, Modal, Row, Select, Switch, Popover, Icon } from 'antd';
import moment from 'moment';
import { connect } from 'dva';
import { CM_OPERATOR_MODE, custTypeAndCertTypeRules, GROUP_CARD_TYPE, GROUP_CUST_TYPE_CODE, IDEN_CARD_TYPE } from '@/utils/consts';
import GroupFileUpload from '@/components/GroupFileUpload';
import {
  disableEndDate,
  disableStartDate,
  validateHandleIdenNbr,
  validateIdenAddressByIdenType,
  validateIdenNameByIdenType,
  validateIdenNbr,
} from '@/utils/validator';
import TakePhoto from '@/busiComponents/TakePhoto';
import Scan from '@/busiComponents/Scan';
import styles from '../../index.less';
import { businessCheck } from '@/pages/GroupCust/GroupCustManage/services';
import FooterToolbar from '@/components/FooterToolbar';
import { queryDisticts } from '@/pages/GroupCertManage/services';

const formItemLayout = {
  // labelCol: {
  //   xs: { span: 24 },
  //   sm: { span: 8 },
  // },
  // wrapperCol: {
  //   xs: { span: 24 },
  //   sm: { span: 16 },
  // },
};

const popTip = (
  <>
    <div>营业执照、组织机构代码证、事业单位证书、统一社会代码证、个人有效证件等类型上传，证件附件格式必须为JPG格式</div>
    <div>图片短边像素必须700以上，文件大小在100KB以上3M以下</div>
  </>
);

const FormItem = Form.Item;
const Edit = props => {
  const {
    mode,
    goToStep,
    form,
    record = {},
    size: { height },
  } = props;
  const { getFieldDecorator, setFieldsValue, getFieldValue } = form;
  const [groupCerFiltertList, setGroupCertFilterList] = useState(Object.entries(GROUP_CARD_TYPE));

  const [editable, setEditable] = useState(false);
  const [loading, setLoading] = useState(false);

  const [disInfos, setDisInfos] = useState([]);

  /* 保存按钮 */
  const [saveButtonVisible, setSaveButtonVisible] = useState(false);

  useEffect(() => {
    queryDisticts().then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.resultInfo) {
        setDisInfos(resultObject?.rspParam?.busiInfo?.resultInfo || []);
      } else {
        message.error(resultMsg);
      }
    });
  }, []);

  /* 企业置信按钮 */
  const [checkButtonVisible, setCheckButtonVisible] = useState(true);
  useEffect(() => {
    setEditable(props.mode !== 'view');
  }, [props]);

  const handleSubmit = () => {};
  const handleCheck = () => {
    form.validateFields(['juristicName', 'idenNr', 'idenTypeCode', 'idenName'], (err, values) => {
      if (!err) {
        const { juristicName: name, idenNr, idenTypeCode: idenCode, idenName } = values;
        setLoading(true);
        businessCheck({
          IDEN_TYPE_CODE: idenCode === '200010' ? '200002' : idenCode,
          uniscId: idenNr,
          legalPersonName: name,
          entName: idenName,
        })
          .then(res => {
            let veriResut = ''; //
            const { resultCode, resultObject, resultMsg } = res;
            if (resultCode === 'TRUE') {
              const { reqSerialNo } = resultObject?.rspParam?.busiInfo;
              veriResut = resultObject?.rspParam?.busiInfo;
              if (veriResut === '0') {
                message.error(`该证件企业致信未通过！流水：${reqSerialNo}`);
              } else if (veriResut === '1') {
                message.success(`该证件企业致信通过，流水：${reqSerialNo}`);
              } else {
                message.success(`该证件类型不支持企业致信，流水：${reqSerialNo}`);
              }
            } else {
              message.error(resultMsg);
            }

            form.setFieldsValue({
              entResult: veriResut,
            });
          })
          .always(() => {
            setLoading(false);
          });
      }
    });
  };
  const handleOk = () => {
    form.validateFields((err, values) => {
      if (!err) {
        const { entResult } = values;
        if (entResult !== '1') {
          Modal.confirm({
            content: '此证件未通过企业致信核验，继续提交将无法办理普通开户NG版业务，是否继续？',
            onOk() {
              handleSubmit();
            },
          });
        } else {
          handleSubmit();
        }
      }
    });
  };
  // 切换证件类型
  const idenTypeCodeOnChange = val => {
    if (val === '200006' || val === '300002' || val === '200002' || val === '200009') {
      setSaveButtonVisible(false);
      setCheckButtonVisible(true);
    } else {
      setSaveButtonVisible(true);
      setCheckButtonVisible(false);
    }
  };
  const handleChangeCertEff = val => {
    if (val) {
      form.setFieldsValue({
        idenExpDate: moment('2099-12-31'),
      });
    } else {
      form.setFieldsValue({
        idenExpDate: null,
      });
    }
  };

  /**
   * 修改客户类型级联修改证件类型列表
   * @param custType
   */
  const handleChangeCertType = custType => {
    // 清除当前选择
    setFieldsValue({
      certificateType: '',
    });
    // 重置列表
    const rules = custTypeAndCertTypeRules[custType];
    if (rules) {
      // eslint-disable-next-line no-unused-vars
      const list = groupCerFiltertList.filter((certType, _) => rules.some(rule => rule.code === certType));
      setGroupCertFilterList(list);
    }
  };

  /**
   * 经办人校验
   */
  const customAgentValidator = (rule, value, callback) => {
    const fields = [];

    const allEmpty = fields.every(field => !getFieldValue(field)); // 检查是否全部为空
    const someNotEmpty = fields.some(field => getFieldValue(field)); // 检查是否有非空值

    if (someNotEmpty && !allEmpty) {
      // 如果有非空值且不是全部为空
      const anyFieldEmpty = fields.some(field => !getFieldValue(field)); // 检查是否有空值
      if (anyFieldEmpty) {
        callback('请填写完整经办人信息！');
      }
    }

    callback();
  };
  const formatTitle = useMemo(() => {
    if (mode === 'view') {
      return '证件详情';
    }
    if (mode === 'add') {
      return '新增证件信息';
    }
    if (mode === 'edit') {
      return '修改证件信息';
    }
    return '';
  }, [mode]);
  return (
    <div className={styles.formContent} style={{ height }}>
      <div style={{ height: height - 40 }}>
        <Form className="flow2 fix-label">
          <Card className="cute" title={formatTitle} bordered={false} extra={<Button onClick={() => goToStep(1)}>返回</Button>}>
            <Row gutter={16}>
              {/* 客户类型 */}
              <Col span={8} hidden={!editable}>
                <FormItem label="客户类型" {...formItemLayout}>
                  {getFieldDecorator('custType', {
                    initialValue: record?.CUST_TYPE || '100001',
                    rules: [{ required: true, message: '客户类型不能为空' }],
                  })(
                    <Select
                      allowClear
                      showSearch
                      onChange={handleChangeCertType}
                      filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      placeholder="请选择"
                      className={styles.inputStyle}
                    >
                      {Object.keys(GROUP_CUST_TYPE_CODE).map(key => (
                        <Select.Option key={key} value={key}>
                          {GROUP_CUST_TYPE_CODE[key]}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              </Col>
              {/* 证件类型 */}
              <Col span={8}>
                <FormItem label="证件类型" {...formItemLayout}>
                  {getFieldDecorator('idenTypeCode', {
                    initialValue: record?.IDEN_TYPE_CODE,
                    rules: [{ required: true, message: '证件类型不能为空' }],
                  })(
                    <Select
                      allowClear
                      showSearch
                      disabled={!editable}
                      onChange={idenTypeCodeOnChange}
                      filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      placeholder="请选择"
                      className={styles.inputStyle}
                    >
                      {Object.keys(GROUP_CARD_TYPE).map(key => (
                        <Select.Option key={key} value={key}>
                          {GROUP_CARD_TYPE[key]}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              </Col>
              {/* 证件扫描附件 */}
              <Col span={8}>
                <FormItem
                  label="证件扫描附件"
                  {...formItemLayout}
                >
                  {getFieldDecorator('EcCustLicenceInfos', {
                    initialValue: record?.SCAN,
                    rules: [{ required: true, message: '证件扫描附件不能为空' }],
                  })(
                    <GroupFileUpload
                      disabled={!editable}
                      accept=".jpg;.png"
                      fileType="1"
                      extraIcon={(
                        <Popover
                          onClick={e => {
                            e.stopPropagation();
                          }}
                          content={popTip}
                          placement="right"
                          trigger="hover"
                        >
                          <Icon
                            onClick={e => {
                              e.stopPropagation();
                            }}
                            type="question-circle"
                            style={{ 'margin-left': '5px' }}
                          />
                        </Popover>
                      )}
                    />

                  )}
                </FormItem>
              </Col>
              {/* 证件号码 */}
              <Col span={8}>
                <FormItem label="证件号码" {...formItemLayout}>
                  {getFieldDecorator('idenNr', {
                    initialValue: record?.IDEN_NR,
                    rules: [
                      {
                        required: true,
                        message: '证件号码不能为空',
                      },
                      { validator: validateIdenNbr(getFieldValue('idenTypeCode')) },
                    ],
                  })(<Input className={styles.inputStyle} disabled={!editable} />)}
                </FormItem>
              </Col>
              {/* 客户名称 */}
              <Col span={8}>
                <FormItem label="客户名称" {...formItemLayout}>
                  {getFieldDecorator('idenName', {
                    initialValue: record?.IDEN_NAME,
                    rules: [
                      { required: true, message: '客户名称不能为空' },
                      { validator: validateIdenNameByIdenType(getFieldValue('idenTypeCode')) },
                    ],
                  })(<Input className={styles.inputStyle} disabled={!editable} />)}
                </FormItem>
              </Col>
              {/* 证件地址 */}
              <Col span={8}>
                <FormItem label="证件地址" {...formItemLayout}>
                  {getFieldDecorator('idenAddress', {
                    initialValue: record?.IDEN_ADDRESS,
                    rules: [
                      { required: true, message: '证件地址不能为空' },
                      { validator: validateIdenAddressByIdenType(getFieldValue('idenTypeCode')) },
                    ],
                  })(<Input className={styles.inputStyle} disabled={!editable} />)}
                </FormItem>
              </Col>
              {/* 法人代表姓名 */}
              <Col span={8}>
                <FormItem label="法人代表姓名">
                  {getFieldDecorator('juristicName', {
                    initialValue: record?.JURISTIC_NAME,
                    rules: [{ required: true, message: '法人代表姓名不能为空' }],
                  })(<Input className={styles.inputStyle} disabled={!editable} />)}
                </FormItem>
              </Col>
              <Col span={8} hidden={editable}>
                {/* 企业置信结果 */}
                <FormItem label="企业置信结果" {...formItemLayout}>
                  {getFieldDecorator('entResult', {
                    initialValue: record?.ENT_RESULT,
                  })(<Input className={styles.inputStyle} disabled />)}
                </FormItem>
              </Col>
              <Col span={8} hidden={!editable}>
                <FormItem label="证件是否长期有效" {...formItemLayout}>
                  {getFieldDecorator('idenLongEff', {
                    initialValue: record?.IDEN_LONG_EFF === '1',
                    valuePropName: 'checked',
                  })(<Switch checkedChildren="是" unCheckedChildren="否" onChange={handleChangeCertEff} />)}
                </FormItem>
              </Col>
              {/* 证件有效日期 */}
              <Col span={8} hidden={!editable}>
                <FormItem label="证件生效日期">
                  {getFieldDecorator('idenEffDate', {
                    initialValue: record?.IDEN_EFF_DATE,
                  })(<DatePicker className={styles.inputStyle} format="YYYY-MM-DD" disabledDate={disableStartDate(form.getFieldValue('idenExpDate'))} placeholder="生效时间" />)}
                </FormItem>
              </Col>
              <Col span={8} hidden={!editable} {...formItemLayout}>
                <FormItem label="证件失效日期">
                  {getFieldDecorator('idenExpDate', {
                    initialValue: record?.IDEN_EXP_DATE,
                  })(
                    <DatePicker
                      disabled={getFieldValue('denLongEff')}
                      disabledDate={disableEndDate(form.getFieldValue('idenEffDate'))}
                      format="YYYY-MM-DD"
                      placeholder="失效时间"
                      className={styles.inputStyle}
                    />
                  )}
                </FormItem>
              </Col>
              {/* 证件拍照 todo: hasPriv('52000016') required */}
              <Col span={8}>
                <FormItem label="证件拍照" hidden={!editable} {...formItemLayout}>
                  {getFieldDecorator('photo', { rules: [{ required: true, message: '法人代表姓名不能为空' }] })(
                    <div className={styles.inputStyle}>
                      <TakePhoto certType="00" idenNbr={getFieldValue('idenNr')} onCallback={() => console.log('拍照完成回调')} />
                    </div>
                  )}
                </FormItem>
              </Col>
            </Row>
          </Card>
          <Card className="cute" title="经办人信息" bordered={false}>
            <p>&nbsp;如果填写经办人信息，请全部填写</p>
            <Row gutter={16}>
              {/* 经办人 */}
              {/* todo: !hasPriv('56000025') required */}
              {/* todo: operatorInfo.OPERATOR_NAME */}
              <Col span={8}>
                <FormItem label="经办人" {...formItemLayout}>
                  {getFieldDecorator('operatorName', {
                    initialValue: record?.OPERATOR_NAME,
                    rules: [{ validator: customAgentValidator }],
                  })(<Input className={styles.inputStyle} disabled={mode !== 'add'} />)}
                </FormItem>
              </Col>
              {/* 地市 */}
              <Col span={8}>
                <FormItem label="地市" {...formItemLayout}>
                  {getFieldDecorator('regionId', {
                    initialValue: record?.REGION_ID,
                    rules: [{ validator: customAgentValidator }],
                  })(
                    <Select
                      allowClear
                      showSearch
                      disabled={!editable}
                      filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      placeholder="请选择"
                      className={styles.inputStyle}
                    >
                      {disInfos.map(({ REGION_ID, DISTRICT_NAME }) => (
                        <Select.Option key={REGION_ID} value={REGION_ID}>
                          {DISTRICT_NAME}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              </Col>
              {/* 证件类型 */}
              <Col span={8}>
                <FormItem label="证件类型" {...formItemLayout}>
                  {getFieldDecorator('operatorIdenType', {
                    initialValue: record?.OPERATOR_IDEN_TYPE || '100001',
                    rules: [{ validator: customAgentValidator }],
                  })(
                    <Select disabled={mode !== 'add'} placeholder="请选择" className={styles.inputStyle}>
                      {Object.keys(IDEN_CARD_TYPE).map(key => (
                        <Select.Option key={key} value={key}>
                          {IDEN_CARD_TYPE[key]}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              </Col>
              {/* 证件号码 */}
              <Col span={8}>
                {/*! hasPriv('56000025') disabled */}
                <FormItem label="证件号码" {...formItemLayout}>
                  {getFieldDecorator('operatorIdenNr', {
                    initialValue: record?.OPERATOR_IDEN_NR,
                    rules: [
                      { validator: customAgentValidator },
                      { validator: validateIdenNbr(getFieldValue('operatorIdenType')) },
                      { validator: validateHandleIdenNbr(getFieldValue('idenTypeCode'), getFieldValue('idenNr')) },
                    ],
                  })(
                    <div className={styles.inputStyle}>
                      <Scan
                        disabled={mode !== 'add'}
                        onCallback={({ name, addddress, idenNr }) =>
                          form.setFieldsValue({
                            operatorName: name,
                            operatorAddress: addddress,
                            operatorIdenNr: idenNr,
                          })
                      }
                      />
                    </div>

                  )}
                </FormItem>
              </Col>
              {/* 经办人拍照 */}
              <Col span={8}>
                <FormItem label="经办人拍照" {...formItemLayout}>
                  {getFieldDecorator('partyId', {
                    rules: [{ validator: customAgentValidator }],
                  })(
                    <div className={styles.inputStyle}>
                      <TakePhoto
                        disabled={mode !== 'add'}
                        certType="04"
                        idenNbr={getFieldValue('operatorIdenNr')}
                        onCallback={() => console.log('拍照完成回调')}
                      />
                    </div>
                  )}
                </FormItem>
              </Col>
              {/* 联系地址 */}
              {/* hasPriv('56000025') disabled */}
              <Col span={8}>
                <FormItem label="联系地址" {...formItemLayout}>
                  {getFieldDecorator('operatorAddress', {
                    initialValue: record?.OPERATOR_ADDRESS,
                    rules: [{ validator: customAgentValidator }, { validator: validateIdenAddressByIdenType(getFieldValue('operatorIdenType')) }],
                  })(<Input className={styles.inputStyle} disabled={mode !== 'add'} />)}
                </FormItem>
              </Col>
              {/* 联系电话 */}
              <Col span={8}>
                <FormItem label="联系电话" {...formItemLayout}>
                  {getFieldDecorator('operatorNumber', {
                    initialValue: record?.OPERATOR_NUMBER,
                    rules: [{ validator: customAgentValidator }],
                  })(<Input className={styles.inputStyle} disabled />)}
                </FormItem>
              </Col>
              {/* 邮箱地址 */}
              <Col span={8}>
                <FormItem label="邮箱地址" {...formItemLayout}>
                  {getFieldDecorator('operatorEmail', {
                    initialValue: record?.OPERATOR_EMAIL,
                    rules: [{ validator: customAgentValidator }],
                  })(<Input className={styles.inputStyle} disabled />)}
                </FormItem>
              </Col>
              {/* 经办方式 */}
              <Col span={8}>
                <FormItem label="经办方式" {...formItemLayout}>
                  {getFieldDecorator('operatorMode', {
                    initialValue: record?.OPERATOR_MODE,
                    rules: [{ validator: customAgentValidator }],
                  })(
                    <Select
                      allowClear
                      showSearch
                      disabled={!editable}
                      filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      placeholder="请选择"
                      className={styles.inputStyle}
                    >
                      {Object.keys(CM_OPERATOR_MODE).map(key => (
                        <Select.Option key={key} value={key}>
                          {CM_OPERATOR_MODE[key]}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              </Col>
            </Row>
            <Row gutter={16}>
              {/* 备注 */}
              <Col span={8}>
                <FormItem label="备注" className={styles.textAreaFormItem} {...formItemLayout}>
                  {getFieldDecorator('remarks', {
                    initialValue: record?.REMARKS,
                    rules: [{ validator: customAgentValidator }],
                  })(<Input.TextArea maxLength={500} disabled={!editable} className={styles.inputStyle} />)}
                </FormItem>
              </Col>
            </Row>
            <Row gutter={16}>
              {/* 经办描述 */}
              <Col span={8}>
                <FormItem label="经办描述" className={styles.textAreaFormItem} {...formItemLayout}>
                  {getFieldDecorator('operatorDesc', {
                    initialValue: record?.OPERATOR_DESC,
                    rules: [{ validator: customAgentValidator }],
                  })(<Input.TextArea maxLength={500} disabled={!editable} className={styles.inputStyle} />)}
                </FormItem>
              </Col>
            </Row>
          </Card>
        </Form>
      </div>
      {editable && (
        <FooterToolbar className={styles.formDrawerBottom}>
          {saveButtonVisible && (
            <Button
              loading={loading}
              style={{
                marginRight: 8,
              }}
              onClick={handleOk}
              type="primary"
            >
              保存
            </Button>
          )}
          {checkButtonVisible && (
            <Button
              loading={loading}
              style={{
                marginRight: 8,
              }}
              onClick={handleCheck}
              type="primary"
            >
              企业致信
            </Button>
          )}
          <Button
            loading={loading}
            style={{
              marginRight: 8,
            }}
            onClick={handleCheck}
            type="primary"
          >
            无纸化档案录入
          </Button>
        </FooterToolbar>
      )}
    </div>
  );
};
export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(Edit));
