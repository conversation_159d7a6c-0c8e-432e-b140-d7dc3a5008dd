// .extra {
//   display: table;
//   div:first-child {
//     :global {
//       .ant-input-group-compact {
//         width: 450px;
//       }
//     }
//   }
//   div:nth-child(2) {
//     display: table-cell;
//   }
// }
.extra {
  display: flex;
  align-items: center;
  .inputgroup{
    display: inline-block;
    :global {
      .ant-input-group-compact {
        width: 450px;
      }
    }
  }
  .button{
    display: table-cell;
  }
}

.systemExtra {
  width: 100%;
  text-align: right;
  margin-bottom: 8px;
}

.releRoleExtra {
  text-align: right;
  width: 100%;
  margin-bottom: 8px;
  div:first-child {
    :global {
      .ant-input-group-compact {
        width: 300px;
      }
    }
  }
  div:nth-child(2),
  .firstDiv {
    display: inline-block;
    vertical-align: middle;
  }
}

.resize_transfer {
  :global {
    .ant-transfer-list-header > .ant-transfer-list-header-selected {
      span:first-child {
        display: none;
      }
      span:nth-child(2) {
        position: relative;
        // left: 12px;
        right: 0px;
      }
    }
  }
}

.grant_perList {
  margin-top: 8px;
  :global {
    .ant-form-item-label {
      text-overflow: ellipsis;
    }
  }
  .remark {
    margin-bottom: 0;
    :global {
      .ant-form-item-label {
        width: 40px;
      }
      .ant-form-item-control-wrapper {
        width: calc(100% - 40px);
      }
    }
  }
  .operType,
  .manageClass {
    margin-bottom: 0;
    // :global{
    //   .ant-form-item-label{
    //     width: 100px;
    //   }
    //   .ant-form-item-control-wrapper{
    //     width:calc(100% - 100px);
    //   }
    // }
  }
}

.clickRowStyle {
  background-color: #e6f7ff;
}

.formItem{
  :global{
    .ant-form-item-control{
      line-height: 22px;
    }
  }
}

.dateRequired::before{
  margin-right: 2px;
  color: red;
  content: '*';
}

.batchAssociation{
  .formContent{
    padding: 10px 16px;
  }
  .btnGroup{
    padding: 10px 16px;
    text-align: right;
    border-top: 1px solid #e8e8e8;
    border-radius: 0 0 2px 2px;
  }
  :global{
    .ant-checkbox-wrapper{
      color: red;
    }
  }
}

.confirmRoles{
  max-height: 120px;
  overflow: auto;
  .confirmItem{
    color: #666;
    font-size: 13px;
  }
}
