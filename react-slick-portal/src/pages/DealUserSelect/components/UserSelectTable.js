import React, { useState, useEffect } from 'react';
import { Input, Button, Select, Form, Cascader, message, Tag, Spin, Switch } from 'antd';
import SlickTable from '@/components/SlickTable';
import request from '@/utils/request';
import styles from '../styles.less';
// import message from '@/pages/OfferCenter/message';

function UserSelectTable(props) {
  const { queryParams, roleParams, paramsOpt, form } = props;
  const { getFieldDecorator } = form;

  let [params] = useState({});

  const [selectRows, setSelectRows] = useState([]); // 保存选中用户记录
  const [tableData, setTableData] = useState([]); // 用户列表
  const [tableLoading, setTableLoading] = useState(false);

  const [selectLabels, setSelectedLabels] = useState([]);
  const [selectLabelItems, setSelectLabelItems] = useState([]);
  const [roleOpts, setRoleOpts] = useState([]);
  const [labelOpts, setLabelOpts] = useState([]);

  const [isInit, setIsInit] = useState(false);

  const checkParams = param => {
    if (!param.systemNbr) {
      message.error('参数错误，缺少系统编码');
      return false;
    }
    return true;
  };

  const refreshTable = () => {
    if (!checkParams(params)) {
      return;
    }
    setTableLoading(true);
    request('orgauth/LabelsController/queryUserByRegionOrRoleOrLabel.do', {
      data: params,
      complete: () => setTableLoading(false),
    }).then(rsp => {
      setTableLoading(false);
      let list = rsp.resultObject;

      let i = 1;
      list.map(item => {
        item.key = i;
        i++;
        return item;
      });

      const filterName = props.form.getFieldValue('username') || '';
      list = list.filter(e => e.staffName?.includes(filterName) || e.userName?.includes(filterName) || e.sysUserCode?.includes(filterName));
      setTableData(list);
    });
  };

  const queryRolesList = () =>
    request('orgauth/LabelsController/queryUserRoleBySystemNbr.do', {
      data: roleParams,
    });

  const queryLabels = () => request('orgauth/LabelsController/getLabelTree.do', {});

  const initLabels = res => {
    const newLabelOpts = [];
    const labelMap = {};
    const labels = Object.keys(res);
    for (let i = 0; i < labels.length; i++) {
      const curLabel = labels[i];
      const children = res[curLabel];
      newLabelOpts.push({
        label: curLabel,
        value: curLabel,
        children,
      });
      for (let i = 0; i < children.length; i++) {
        labelMap[children[i].value] = children[i].label;
      }
    }

    setLabelOpts(newLabelOpts);
    const selectedLabels = [];
    const selectLabelItem = [];
    if (params.labels) {
      for (let i = 0; i < params.labels.length; i++) {
        const cur = params.labels[i];
        const labelItem = [labelMap[cur] || cur, cur];
        selectedLabels.push(labelItem[1]);
        selectLabelItem.push(labelItem);
      }
    }
    setSelectedLabels(selectedLabels);
    setSelectLabelItems(selectLabelItem);
  };

  const init = () => {
    Promise.all([queryRolesList(), queryLabels()])
      .then(res => {
        setIsInit(true);
        // 设置角色列表
        const list = res[0].resultObject;
        if (!res[0].success) {
          message.error(res[0].resultMsg);
          return;
        }
        const newOpts = [];
        for (let i = 0; i < list.length; i++) {
          newOpts.push(<Select.Option key={list[i].sysRoleId}>{list[i].sysRoleName}</Select.Option>);
        }
        setRoleOpts(newOpts);

        if (!res[1].success) {
          message.error('获取标签列表失败');
          return;
        }
        // 设置标签级联框
        initLabels(res[1].resultObject);

        // 设置表单数据
        props.form.setFieldsValue({
          roleId: params.roleId,
          impOrgFlag: params.impOrgFlag === '1000',
          labelsUnionFlag: params.labelsUnionFlag === '1000',
          username: '',
        });
        refreshTable();
      })
      .finally(() => setIsInit(true));
  };

  const onSearch = () => {
    const formValue = form.getFieldsValue();
    params = {
      ...queryParams,
      labels: paramsOpt.labels?.isShow ? selectLabels : queryParams.labels,
      roleId: paramsOpt.role?.isShow ? formValue.roleId : queryParams.roleId,
      impOrgFlag: formValue.impOrgFlag ? '1000' : undefined,
      labelsUnionFlag: formValue.labelsUnionFlag ? '1000' : undefined,
    };
    refreshTable();
  };

  useEffect(() => {
    params = queryParams;
    if (params === null) {
      return;
    }

    if (isInit) {
      onSearch();
    } else {
      init();
    }
  }, [queryParams]);

  const columns = [
    {
      title: '用户名',
      dataIndex: 'userName',
    },
    {
      title: '用户编码',
      dataIndex: 'sysUserCode',
    },
    {
      title: '组织',
      dataIndex: 'orgName',
    },
    {
      title: '员工名称',
      dataIndex: 'staffName',
    },
  ];

  const formItemLayout = {
    wrapperCol: {
      xs: {
        span: 24,
      },
      sm: {
        span: 18,
      },
    },
    labelCol: {
      xs: {
        span: 0,
      },
      sm: {
        span: 6,
      },
    },
  };

  const changeHandler = (value, label) => {
    const labels = [...selectLabels];
    labels.push(value[1]);

    const labelItems = [...selectLabelItems];
    labelItems.push([label, value[1]]);
    setSelectedLabels(labels);
    setSelectLabelItems(labelItems);
  };

  const deleteChangeHandler = index => {
    const labels = [...selectLabels];
    labels.splice(index, 1);
    const labelItems = [...selectLabelItems];
    labelItems.splice(index, 1);
    setSelectedLabels(labels);
    setSelectLabelItems(labelItems);
  };

  const maxLabel = 2;

  return (
    <>
      <div className={styles.right}>
        <Spin spinning={!isInit}>
          <Form layout="inline" {...formItemLayout} style={{ marginBottom: '6px' }}>
            {
              paramsOpt.role?.isShow && (
              <Form.Item label="角色" style={{ width: '180px', marginBottom: '6px' }}>
                {getFieldDecorator(
                'roleId',
                {}
              )(
                <Select
                  allowClear
                  showSearch
                  placeholder="选择角色"
                  disabled={paramsOpt.role?.isReadonly}
                  filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                >
                  {roleOpts}
                </Select>
              )}
              </Form.Item>
              )
            }
            {
              paramsOpt.labels?.isShow && (
              <Form.Item label="标签" style={{ minWidth: '150px', maxWidth: '350px' }}>
                <Cascader
                  onChange={(itemValue, itemInitial) => {
                  if (!selectLabels.includes(itemValue[1])) {
                    // const showValue = itemValue.reduce(
                    //   (previousValue, currentValue, currentIndex) => currentIndex ? previousValue + '/' + itemInitial[currentIndex]['label']
                    //     : previousValue + itemInitial[currentIndex]['label'], ''
                    // )
                    changeHandler(itemValue, itemInitial[1].label);
                  }
                }}
                  options={labelOpts}
                  placeholder="选择标签"
                  allowClear
                  disabled={paramsOpt.labels?.isReadonly}
                >
                  <div
                    style={{
                    // // 设置最大高度，当内容过多时进行滚动显示
                    // maxHeight: '60px',
                    // height: selectLabels.length === 0 && '30px',
                    display: 'flex',
                    padding: '0 6px 0 2px',
                    height: '24px',
                    overflow: 'hidden',
                    border: '1px solid #d9d9d9',
                    borderRadius: '2px',
                  }}
                  >
                    {' '}
                    {(selectLabelItems || []).map((item, index) => {
                    if (index >= maxLabel) {
                      return <></>;
                    }
                    return (
                      <Tag
                        closable={!paramsOpt.labels?.isReadonly}
                        key={item[1]}
                        onClose={() => {
                          // 删除对应索引元素
                          deleteChangeHandler(index);
                        }}
                      >
                        {item[0]}
                      </Tag>
                    );
                  })}
                    {selectLabelItems.length > maxLabel && (
                    <>
                      <Tag key="more">{`+${selectLabelItems.length - maxLabel}...`}</Tag>
                    </>
                  )}
                  </div>
                </Cascader>
              </Form.Item>
)
}
            <Form.Item label="取标签交集" labelCol={{ offset: 2 }} wrapperCol={{ offset: 1 }} style={{ width: '180px' }}>
              {getFieldDecorator('labelsUnionFlag', { valuePropName: 'checked' })(<Switch />)}
            </Form.Item>
            <Form.Item label="只显示重客" labelCol={{ offset: 2 }} wrapperCol={{ offset: 1 }} style={{ width: '180px' }}>
              {getFieldDecorator('impOrgFlag', { valuePropName: 'checked' })(<Switch disabled={paramsOpt.impOrgFlag?.isReadonly} />)}
            </Form.Item>
            <Form.Item wrapperCol={{ xs: { span: 24 }, sm: { span: 24 } }}>
              {getFieldDecorator('username', {})(<Input placeholder="请输入用户查询信息" allowClear />)}
            </Form.Item>
            <Form.Item>
              <Button type="primary" onClick={onSearch}>
                查询
              </Button>
            </Form.Item>
          </Form>
          <SlickTable
            rowKey={record => record.key}
            columns={columns}
            dataSource={tableData}
            pagination={{ pageSize: 5 }}
            pick="radio"
            loading={tableLoading}
            rowSelection={{
              selectedRowKeys: selectRows,
              onSelect: record => {
                setSelectRows([record.key]);
                if (props.onRowClick) {
                  props.onRowClick(record);
                }
              },
            }}
            onRow={record => ({
              onClick: () => {
                setSelectRows([record.sysUserId]);
                if (props.onRowClick) {
                  props.onRowClick(record);
                }
              },
            })}
          />
        </Spin>
      </div>
    </>
  );
}

export default Form.create()(UserSelectTable);
