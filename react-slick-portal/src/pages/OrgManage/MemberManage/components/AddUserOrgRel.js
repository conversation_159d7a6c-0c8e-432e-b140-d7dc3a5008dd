import React, { useState, useEffect, useRef, useImperativeHandle } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Card,
  Form,
  Input,
  Icon,
  Button,
  Select,
  Modal,
  message,
  Spin,
  InputNumber,
  DatePicker
} from 'antd';
import request from '@/utils/request';
import omit from 'lodash/omit';
import styles from '../styles.less';
import ComboGrid from '@/components/ComboGrid';
const InputGroup = Input.Group;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Option } = Select;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6},
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const textAreaFormItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

function AddUserOrgRel(props) {
  const { getFieldDecorator } = props.form;
  const [spinning, setSpinning] = useState(true);

  useEffect(() => {
    setSpinning(false);
  }, []);



  // 新增
  useImperativeHandle(props.cRef, () => ({
    handleAdd: () => {
      return new Promise(function(resolve) {
        props.form.validateFields((err, fieldsValue) => {
          if (err) return;
          console.log(fieldsValue)
          const obj = {
            orgId: fieldsValue.orgId[0].orgId,
            staffId: props.staffId,
            statusCd: fieldsValue.orgId[0].statusCd,
          };
          setSpinning(true);
          request('orgauth/StaffOrgRelController/checkOrgStaff.do', { data: obj }).then(res => {
            if (res) {
              request('orgauth/StaffOrgRelController/create.do', { data: obj }).then(res => {
                if(res){
                  setSpinning(false);
                  resolve(true);
                  message.success('新增成功！');
                }else{
                  //message.info('员工组织关系新增失败！');
                }
              })
            } else {
              setSpinning(false);
              message.info('该员工已经关联了组织！');
            }
          });
        });
      });
    },
  }));

  const onConfirm = (value) =>{
    if(value.length>0){
      props.form.setFieldsValue({
        orgName:value[0].orgName,
        pathName:value[0].pathName,
        orgLevel:value[0].orgLevel
      })
    }
  }



  return (
    <Spin spinning={spinning}>
      <Form>
        <Row>
          <Col span={24}>
            <Form.Item label="组织标识" {...formItemLayout}>
              {getFieldDecorator('orgId', {
                rules: [
                  {
                    required: true,
                    message: '组织标识不能为空',
                  },
                ]
              })(
                <ComboGrid
                  url="orgauth/OrganizationController/qryManageOrgGridData.do"
                  popupStyle={{ width: 560 }}
                  placeholder="组织标识"
                  searchPlaceholder="请输入组织名称进行搜索"
                  label="orgId"
                  rowKey="orgId"
                  pick="radio"
                  params={{ createType: '1100' }}
                  onConfirm={onConfirm}
                  columns={[
                    {
                      title: '组织标识',
                      dataIndex: 'orgId',
                    },
                    {
                      title: '组织名称',
                      dataIndex: 'orgName',
                      ellipsis: true,
                    },
                    {
                      title: '组织路径',
                      dataIndex: 'pathName',
                      ellipsis: true,
                    },
                    {
                      title: '组织层级',
                      dataIndex: 'orgLevel',
                    },
                  ]}
                />
              )}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="组织名称" {...formItemLayout}>
              {getFieldDecorator('orgName')(
                <Input disabled/>
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Form.Item label="组织路径" {...formItemLayout}>
              {getFieldDecorator('pathName')(<Input disabled/>)}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="组织层级" {...formItemLayout}>
              {getFieldDecorator('orgLevel')(<Input disabled/>)}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Spin>
  );
}

export default connect(({}) => ({}))(Form.create()(AddUserOrgRel));
