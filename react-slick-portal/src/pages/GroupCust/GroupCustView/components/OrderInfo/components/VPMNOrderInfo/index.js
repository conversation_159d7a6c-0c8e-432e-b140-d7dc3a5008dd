import { connect } from 'dva';
import React, { useEffect, useRef, useState } from 'react';
import { Descriptions, message, Spin } from 'antd';
// import { useLocation } from 'react-router-dom';
import { VPMNOrderInfoFields } from './const';
import { queryVPMNProdInfos } from '@/pages/GroupCust/GroupCustView/components/OrderInfo/components/VPMNOrderInfo/service';

const Index = props => {
  const { activeTab, groupId } = props;
  const containerRef = useRef(null);
  const [VPMNOrderData, setVPMNOrderData] = useState({});
  const [loading, setLoading] = useState(false);
  // const location = useLocation();
  // const { groupId } = location?.query;
  console.log('activeTab========', activeTab);

  useEffect(() => {
    console.log(loading);
  }, [loading]);
  // 依赖props.activeTab 可以实现区域切换时，自动刷新
  useEffect(() => {
    setLoading(true);
    queryVPMNProdInfos({
      groupId,
      flag: '1',
    }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        setVPMNOrderData(resultObject?.rspParam?.busiInfo?.outData);
        setLoading(false);
      } else {
        setLoading(false);
        message.error(resultMsg);
      }
    });
  }, [groupId]);

  return (
    <div style={{ position: 'relative' }} id="baseInfo">
      <div ref={containerRef}>
        <Spin spinning={!!loading}>
          <div>
            <Descriptions className="cute" column={{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }}>
              {VPMNOrderInfoFields.map(({ name, key, span, options }) => (
                <Descriptions.Item label={name} span={span} key={key}>
                  {!options ? VPMNOrderData?.[key] : options[VPMNOrderData?.[key]]}
                </Descriptions.Item>
              ))}
            </Descriptions>
          </div>
        </Spin>
      </div>
    </div>
  );
};
export default connect(({ groupCustView }) => ({
  groupId: groupCustView.currentCust.GROUP_ID,
}))(Index);
