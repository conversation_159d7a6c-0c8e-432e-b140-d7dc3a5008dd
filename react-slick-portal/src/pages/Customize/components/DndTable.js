import React, { useState, useEffect, useRef } from 'react';
import { Form, Col, Row, Popover, Tooltip, Input, Button, Card, message } from 'antd';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { connect } from 'dva';
import { DndProvider, DragSource, DropTarget } from 'react-dnd';
import HTML5Backend from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import isEqual from 'lodash/isEqual';
import findIndex from 'lodash/findIndex';
import { usePrevious, useBoolean } from '@umijs/hooks';
import PortletsList from './PortletsList';
import SlickTable from '@/components/SlickTable';
import styles from '../less/styles.less';
import AddReleMenu from './AddReleMenu';

let dragingIndex = -1;

const namespace = 'appTempletManage';
// 判断空对象
const judgeObj = obj => {
  if (Object.keys(obj).length == 0) return true;
  return false;
};

function BodyRow(props) {
  const { isOver, connectDragSource, connectDropTarget, moveRow, ...restProps } = props;
  const style = { ...restProps.style, cursor: 'move' };

  let { className } = restProps;
  if (isOver) {
    if (restProps.index > dragingIndex) {
      className += ' drop-over-downward';
    }
    if (restProps.index < dragingIndex) {
      className += ' drop-over-upward';
    }
  }

  return connectDragSource(
    connectDropTarget(<tr {...restProps} className={className} style={style} />)
  );
}


const rowTarget = {
  drop(props, monitor) {
    const dragIndex = monitor.getItem().index;
    const hoverIndex = props.index;

    // Don't replace items with themselves
    if (dragIndex === hoverIndex) {
      return;
    }

    // Time to actually perform the action
    props.moveRow(dragIndex, hoverIndex);

    // Note: we're mutating the monitor item here!
    // Generally it's better to avoid mutations,
    // but it's good here for the sake of performance
    // to avoid expensive index searches.
    monitor.getItem().index = hoverIndex;
  },
};

const rowSource = {
  beginDrag(props) {
    dragingIndex = props.index;
    return {
      index: props.index,
    };
  },
};

const DragableBodyRow = DropTarget('row', rowTarget, (connect, monitor) => ({
  connectDropTarget: connect.dropTarget(),
  isOver: monitor.isOver(),
}))(
  DragSource('row', rowSource, connect => ({
    connectDragSource: connect.dragSource(),
  }))(BodyRow)
);

const components = {
  body: {
    row: DragableBodyRow,
  },
};


function DndTable({
  dispatch,
  templetName,
  templetDesc,
  loading,
  form: { getFieldDecorator, validateFields, setFieldsValue },
  size: { height },
  editable,
  current,
  all,
  allFunc,
  onConfirm,
  onBack,
  currentMenu,
  props,
}) {
  const {
    state: portletsListVisible,
    setTrue: showPortletsList,
    setFalse: hidePortletsList,
  } = useBoolean(false);
  const {
    state: menusListVisible,
    setTrue: showMenusList,
    setFalse: hideMenusList,
  } = useBoolean(false);

  // const [currentMenu,setCurrentMenu]=useState([]);
  const [portlets, setPortlets] = useState(() =>
    current.sort((a, b) => parseInt(a.y, 10) - parseInt(b.y, 10))
  ); // 当前所有的模块

  const [menus, setMenus] = useState(() =>
    currentMenu?.sort((a, b) => parseInt(a.y, 10) - parseInt(b.y, 10))
  ); // 当前所有的模块菜单
  const tableEl = useRef(null);
  const detailEl = useRef(null);
  const systemEl = useRef();
  const [selectRows, setSelectRows] = useState([]);
  const prevPortlets = usePrevious(portlets);
  const prevMenus = usePrevious(menus);
  const [detailMenuList, setDetailMenuList] = useState([]);
  const [objId, setObjId] = useState('');
  const [selectRowId, setSelectRowId] = useState('');
  const [dataSource, setDataSource] = useState(portlets);
  const [menuDataSource, setMenuDataSource] = useState([]);


  // 点击行颜色变化
  const setRowClassName = record => record.menuId === selectRowId ? `${styles.clickRowStyle}` : '';

  const rowSelect = record => {
      if (undefined != record.module) {
        // setMenus(record.module.details);
        setMenus(record.module.details);
        setDetailMenuList(record.module.details);
      } else {
        setDetailMenuList([]);
        setMenus([]);
      }
      setObjId(record.moduleId);
      setSelectRowId(record.menuId);
  };


  function filterDataSource(value) {
    if (value === '') {
      setDataSource(portlets);
    } else {
      setDataSource(
        dataSource.filter(item =>
          JSON.stringify(item)
            .toLowerCase()
            .includes(value.toLowerCase())
        )
      );
    }
  }

  function filterMenuDataSource(value) {
    if (value === '') {
      setMenuDataSource(menus);
    } else {
      setMenuDataSource(
        menuDataSource.filter(item =>
          JSON.stringify(item)
            .toLowerCase()
            .includes(value.toLowerCase())
        )
      );
    }
  }

  function onAddItem(arr) {
    setPortlets([...portlets, ...arr]);
  }

  function onAddMenusItem(arr) {
    setMenus([...menus, ...arr]);
  }

  useEffect(() => {
    // setPortlets(current);
    if (current.length > 0 && current[0].module != null) {
      setDetailMenuList(current[0].module.details);
      setMenus(current[0].module.details);
      setMenuDataSource(current[0].module.details);
    }
  }, [current, currentMenu]);


  useEffect(() => {
    if (Array.isArray(portlets) && !isEqual(portlets, prevPortlets)) {
      if (portlets.length > 0) {
        portlets.forEach((item, index) => {
          item.moduleIndex = index + 1;
        });
      }

      setPortlets(portlets);
      setDataSource(portlets);
    }
  }, [portlets, prevPortlets, setPortlets]);

  useEffect(() => {
    if (Array.isArray(menus) && prevMenus != undefined && !isEqual(menus, prevMenus)) {
      if (menus.length > 0) {
        menus.forEach((menu, index) => {
          menu.moduleIndex = index + 1;
        });
      }
      // 遍历模板，把最新的关联模块放入对应模板的module里
      portlets.forEach(item => {
        if (item.module == undefined && objId === item.moduleId && item.menuId == selectRowId) {
          setMenus(menus);
          // 按格式拼module并赋值给当前模板
          setDetailMenuList(menus);
          setMenuDataSource(menus);
          const module = {};
          let details = [];
          details = menus;
          module.details = details;
          module.moduleId = item.moduleId;
          item.module = module;
        } else if (item.module != undefined && objId === item.module.moduleId && item.menuId == selectRowId) {
            setMenus(menus);
            // 变化后的数组放入列表
            setDetailMenuList(menus);
            item.module.details = menus;
            setMenuDataSource(menus);
        }
      });
    }
  }, [menus, prevMenus, setMenus]);

  useEffect(() => {
    if (editable && templetName) {
      setFieldsValue({ templetName });
    }
  }, [templetName, setFieldsValue, editable]);

  useEffect(() => {
    if (editable && templetDesc) {
      setFieldsValue({ templetDesc });
    }
  }, [templetDesc, setFieldsValue, editable]);

  function addMenus() {
    if (objId != '') {
      showMenusList();
    } else {
      message.warn('请先选中一个模版');
    }
  }
  return (
    <>
      <Card
        title="手机端模板配置"
        extra={(
          <div>
            <Input.Search
              placeholder="模板名称、编码"
              style={{ width: 150 }}
              onSearch={val => {
                    filterDataSource(val);
                  }}
            />
            {
                  editable ? <Button className="margin-left" type="primary" onClick={showPortletsList}>新增</Button> : null
                }
          </div>
          )}
      >

        <DndProvider backend={HTML5Backend}>
          <SlickTable
            ref={tableEl}
            className="move"
            rowKey={record => record.menuId}
            dataSource={dataSource}
            pagination={{
            pageSize: 100,
          }}
            components={components}
            pick="checkbox"
            columns={[
            {
              dataIndex: 'moduleIndex',
              title: '排序',
              align: 'center',
              width: 50,
              render: (text, record, index) => index + 1,
            },
            { dataIndex: 'menuName', title: '模板名称' },
            { dataIndex: 'menuCode', title: '模板编码' },
            { dataIndex: 'menuDesc', title: '模板描述' },
            { dataIndex: 'menuModuleTypeName', title: '模板类型' },
          ]}

            extra={selectedRowKeys =>
            selectedRowKeys.length > 0 ? (
              <Button
                type="primary"
                ghost
                onClick={() => {
                  setPortlets(
                    portlets.filter(
                      item =>
                        findIndex(tableEl.current.selectedRows, { menuId: item.menuId }) === -1

                    )
                  );
                  // 判断要删除的模板是否有模块，有则删除
                  if (tableEl.current.selectedRows.length > 0) {
                    tableEl.current.selectedRows.forEach(item => {
                        if (isEqual(objId, item.moduleId)) {
                          setMenus([]);
                          setMenuDataSource([]);
                        }
                    });
                  }
                  tableEl.current.cleanSelectedKeys([], []);
                }}
              >
                删除
              </Button>
            ) : null
          }
            onSelectRow={selectedRows => {
            tableEl.current.selectedRows = selectedRows;
          }}
            onRow={(record, index) => ({
            index,
            className: 'move',
            moveRow: (dragIndex, hoverIndex) => {
              const dragRow = portlets[dragIndex];
              setPortlets(
                update(portlets, {
                  $splice: [
                    [dragIndex, 1],
                    [hoverIndex, 0, dragRow],
                  ],
                })
              );
            },
            onClick: () => {
              rowSelect(record);
            },
          })}

            rowClassName={setRowClassName}
          />
        </DndProvider>
      </Card>
      <Card
        title="关联模块"
        extra={(
          <div>
            <Input.Search
              placeholder="模板名称、编码"
              style={{ width: 150 }}
              onSearch={val => {
                  filterMenuDataSource(val);
                }}
            />
            {
                editable ? <Button className="margin-left" type="primary" onClick={() => { addMenus({}); }}>新增</Button> : null
              }
          </div>
        )}
      >
        <DndProvider backend={HTML5Backend}>
          <SlickTable
            ref={detailEl}
            className="move"
            rowKey={record => record.menuId}
            pagination={{
            pageSize: 100,
          }}
            columns={[
            {
              dataIndex: 'moduleIndex',
              title: '排序',
              align: 'center',
              width: 50,
              render: (text, record, index) => index + 1,
            },
          {
            title: '模块名称',
            dataIndex: 'menuName',
          },
          {
            title: '模块编码',
            dataIndex: 'menuCode',
          },
          {
            title: '模块描述',
            dataIndex: 'menuDesc',
          }]}
            pick="checkbox"
            components={components}
            dataSource={menuDataSource}
            extra={selectedRowKeys =>
            selectedRowKeys.length > 0 ? (
              <Button
                type="primary"
                ghost
                onClick={() => {
                  setMenus(
                    menus.filter(
                      item =>
                        findIndex(detailEl.current.selectedRows, { menuId: item.menuId }) === -1
                    )
                  );
                  detailEl.current.cleanSelectedKeys([], []);
                }}
              >
                删除
              </Button>
            ) : null
          }
            onSelectRow={selectedRows => {
            detailEl.current.selectedRows = selectedRows;
          }}
            onRow={(record, index) => ({
            index,
            className: 'move',
            moveRow: (dragIndex, hoverIndex) => {
              const dragRow = menus[dragIndex];
              setMenus(
                update(menus, {
                  $splice: [
                    [dragIndex, 1],
                    [hoverIndex, 0, dragRow],
                  ],
                })
              );
            },
          })}
          />
        </DndProvider>
      </Card>

      <Tooltip placement="left" title="返回列表">
        <Button
          shape="circle"
          icon="rollback"
          size="large"
          className={classNames(styles.back, { [styles.bottom]: !editable })}
          onClick={() => {
            onBack();
          }}
          type="primary"
          ghost={editable}
        />
      </Tooltip>
      {/* 编辑模式可见 */}
      {editable && (
        <>
          <Popover
            overlayStyle={{ zIndex: 1000 }} // 防止被notice盖住
            placement="left"
            title="填写模板信息"
            content={(
              <Form
                onSubmit={e => {
                  e.preventDefault();

                  validateFields((err, values) => {
                    if (err) {
                      return;
                    }
                    onConfirm({
                      ...values,
                      portlets: portlets.map((item, index) => ({
                          ...item,
                          x: 0,
                          y: index + 1,
                        }),
                      ),
                    });
                  });
                }}
              >
                <Row gutter={16}>
                  <Col span={24}>
                    <Form.Item>
                      {getFieldDecorator('templetName', {
                        rules: [
                          {
                            required: true,
                            message: '请输入模板名称',
                          },
                          {
                            max: 15,
                            message: '长度不要超过15个字符!',
                          },
                        ],
                      })(<Input allowClear placeholder="模板名称" />)}
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item>
                      {getFieldDecorator('templetDesc', {
                        rules: [{ max: 128, message: '长度不要超过128个字符!' }],
                      })(<Input.TextArea allowClear rows={3} placeholder="模板描述" />)}
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item>
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={loading}
                        style={{ width: 100 }}
                      >
                        提交
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            )}
            trigger="click"
          >
            <Tooltip placement="left" title="保存模板">
              <Button
                shape="circle"
                icon="form"
                size="large"
                className={styles.save}
                type="primary"
                ghost
              />
            </Tooltip>
          </Popover>

          {/* <Tooltip placement="left" title="添加模块">
            <Button
              shape="circle"
              icon="plus"
              size="large"
              className={styles.add}
              type="primary"
              onClick={showPortletsList}
            />
          </Tooltip> */}
        </>
      )}
      {/* 防止初始化时加载 */}
      {portletsListVisible && (
        <PortletsList
          visible={portletsListVisible}
          close={hidePortletsList}
          all={all.filter(item => findIndex(portlets, { menuId: item.menuId }) === -1)}

          onOk={onAddItem}
        />
      )}

      {menusListVisible && (
        <AddReleMenu
          visible={menusListVisible}
          close={hideMenusList}
          allFunc={allFunc.filter(item => findIndex(menus, { menuId: item.menuId }) === -1)}
          objId={objId}
          onOk={onAddMenusItem}
        />
      )}

    </>

  );
}

DndTable.defaultProps = {
  templetName: '',
  templetDesc: '',
  loading: false,
  editable: false,
  onConfirm: () => {},
  onBack: () => {},
  all: [],
  allFunc: [],
};

DndTable.propTypes = {
  templetName: PropTypes.string,
  templetDesc: PropTypes.string,
  loading: PropTypes.bool,
  editable: PropTypes.bool,
  onConfirm: PropTypes.func,
  onBack: PropTypes.func,
  current: PropTypes.arrayOf(PropTypes.object).isRequired,
  currentMenu: PropTypes.arrayOf(PropTypes.object).isRequired,
  all: PropTypes.arrayOf(PropTypes.object),
  allFunc: PropTypes.arrayOf(PropTypes.object),
};
export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(DndTable));
