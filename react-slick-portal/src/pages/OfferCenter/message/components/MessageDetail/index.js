import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';
import React, { useEffect, useState } from 'react';
import { connect } from 'dva';
import { modifyMessage, qryConMessageUsersById } from '@/services/message';
import stringToHtml from '@/utils/string';
import { getValuesList } from '@/services/offer';
import { queryByObjId } from '@/services/bulletin';
import style from './MessageDetail.module.less';

const MessageDetail = props => {
  const {
    detail, onClose, dispatch, currentIndex, length, onPrev, onNext, setDetailLoading, detailLoading,
  } = props;
  const [conMessage, setConMessage] = useState({});
  const [messageTypeName, setMessageTypeName] = useState([]);
  const [messageImg, setMessageImg] = useState([]);

  const getMessageDetail = async (messageUsersId) => {
    const res = await qryConMessageUsersById(messageUsersId);
    setConMessage(res.resultObject.curConMeaasageUsers.conMessage);
  };

  const readMessageHandle = async (conMeaasageUsers) => {
    if (conMeaasageUsers.statusCd === '1000') {
      await modifyMessage('read', [{messageUsersId: conMeaasageUsers.messageUsersId}]);
      dispatch({
        type: 'message/getMessageType',
      });
    }
  };

  useEffect(() => {
    setDetailLoading(false);
  }, [conMessage])

  useEffect(() => {
    setMessageImg([]);
    if (Object.keys(detail).length>0) {
      getMessageDetail(detail.messageUsersId);
      readMessageHandle(detail);
    }
    queryByObjId({ objId: detail.messageId, objType: '3000' }).then(res => {
      setMessageImg(res);
    })
  }, [detail]);

  useEffect(() => {
    const getValuesType = async () => {
      const res = await getValuesList({ busiNbr: 'ConMessage', propertyName: 'messageType' });
      setMessageTypeName(res);
    };
    getValuesType();
  }, []);

  const getMessageTypeName = () => {
    return (messageTypeName.find(el => el.value === conMessage.messageType))?.name;
  };

  return (
    <Drawer
      visible={props.visible}
      onClose={() => {
        props.onClose();
      }}
      width={1000}
      className={style.drawerCon}
      getContainer={false}
    >
      <div className={style.wrap}>
        <div className={style.container}>
          <div className={style.top}>
            <span className={style.title}>公告详情</span>
            <div className={style.typeContainer}>
              <ul className={style.navList}>
                <li><Button type="default" onClick={onClose} size="small">返回</Button></li>
                <li>
                  <Button
                    disabled={currentIndex === 0}
                    type="default"
                    size="small"
                    onClick={onPrev}
                  >
                    上一条
                  </Button>
                </li>
                <li>
                  <Button
                    disabled={currentIndex === length - 1}
                    type="default"
                    size="small"
                    onClick={onNext}
                  >
                    下一条
                  </Button>
                </li>
              </ul>
            </div>
          </div>
          <Spin spinning={detailLoading}>
            <div className={style.container}>
              <span className={style.messageTitle}>{conMessage.messageTitle}</span>
              <span className={style.messageSubTitle}>{conMessage.messageSubTitle}</span>
              <span className={style.messageTime}>
                <span style={{marginRight: '56px'}}>消息类型：{getMessageTypeName()}</span>
                <span>发布时间：{conMessage.createDate}</span>
              </span>
              {
              messageImg.length > 0 &&
              <img src={`portal/FileStoreController/download.do?docNbr=${messageImg[0]?.docNbr}`} alt="" />
            }
              <div
                className={style.content}
                dangerouslySetInnerHTML={{ __html: stringToHtml(conMessage.messageContent) }}
              />
            </div>
          </Spin>
        </div>
      </div>
    </Drawer>
  );
};

export default connect()(MessageDetail);
