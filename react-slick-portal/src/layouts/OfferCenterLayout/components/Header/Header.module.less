@import '../../less/variable.less';

@navHeight: 64px;

.wrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-width: @default-width;
  height: @navHeight;
  color: @color-text-light;
  font-weight: @font-light;
  font-size: @font-size-base;
  background-color: @bg-nav;

  &.fixed {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
  }

  .left {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: @navHeight;
  }

  .logo {
    margin-left: 50px;
  }

  .subTitle {
    margin-right: @padding-l;
    margin-left: @padding-l;
    font-size: @font-size-heading;
    cursor: pointer;
  }
}

.navList {
  height: @navHeight;
  margin-top: 21px;
  font-weight: @font-light;
  font-size: 14px;
  .flexCenterStart(row);

  li {
    height: @navHeight;
    margin-right: @padding * 8;
    cursor: pointer;
    .flexCenter(row);
  }

  @media screen and (max-width: 1300px) {
    li {
      margin-right: @padding * 4;
    }
  }

  li:hover {
    color: @primary-color;
  }
}

.userInfo {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  height: @navHeight;
}

.cover {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: 0;
}

.popover {
  top: 52px !important;
  :global(.ant-popover-arrow) {
    border-width: 15px;
    box-shadow: 0 0 0 !important;
  }
}
