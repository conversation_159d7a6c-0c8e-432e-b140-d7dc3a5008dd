import React, { useState } from 'react';
import { Card, Row, Col, message, Input, Form, Button } from 'antd';
import LocationCascader from './index';

/**
 * LocationCascader示例组件
 * 展示如何使用LocationCascader的多级地址选择功能，包括：
 * 1. 基础用法 - 展示基本的地址级联选择器功能
 * 2. 省市区街道四级联动示例
 */
const LocationCascaderExample = props => {
  const { getFieldDecorator } = props.form;
  // 级联选择器的值
  const [value1, setValue1] = useState(['henan', 'zhengzhou', 'jinshui', 'weilai']);
  const [value2, setValue2] = useState(['beijing', 'beijing_city', 'chaoyang']);

  // 处理级联选择器值变化
  const handleChange1 = (val, selectedOptions) => {
    console.log('级联选择器1值变化:', val, selectedOptions);
    setValue1(val);
    message.info(`已选择: ${selectedOptions.map(opt => opt.label).join(' > ')}`);
  };

  const handleChange2 = (val, selectedOptions) => {
    console.log('级联选择器2值变化:', val, selectedOptions);
    setValue2(val);
    message.info(`已选择: ${selectedOptions.map(opt => opt.label).join(' > ')}`);
  };

  // 省市区街道数据示例
  const options = [
    {
      value: 'henan',
      label: '河南',
      children: [
        {
          value: 'zhengzhou',
          label: '郑州市',
          children: [
            {
              value: 'jinshui',
              label: '金水区',
              children: [
                {
                  value: 'weilai',
                  label: '未来路街道',
                  children: [
                    {
                      value: 'build',
                      label: '详细地址',
                      // 这里的 render 现在支持接收 option, level, customRender
                      render: (option, level, customRender) => {
                        // 直接调用 customRender，保证赋值逻辑和组件内部一致
                        return customRender(option, level);
                      },
                    },
                  ],
                },
                {
                  value: 'other',
                  label: '其他街道',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      value: 'beijing',
      label: '北京',
      children: [
        {
          value: 'beijing_city',
          label: '北京市',
          children: [
            {
              value: 'haidian',
              label: '海淀区',
              children: [
                {
                  value: 'zhongguancun',
                  label: '中关村街道',
                },
              ],
            },
            {
              value: 'chaoyang',
              label: '朝阳区',
              children: [
                {
                  value: 'guomao',
                  label: '国贸街道',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      value: 'shanghai',
      label: '上海',
      children: [
        {
          value: 'shanghai_city',
          label: '上海市',
          children: [
            {
              value: 'pudong',
              label: '浦东新区',
            },
          ],
        },
      ],
    },
  ];
  // 添加一个辅助函数来获取选中的选项
  const getSelectedOptions = (values, options) => {
    if (!values || !values.length) return [];

    const selectedOptions = [];
    let currentOptions = options;

    values.forEach(value => {
      const option = currentOptions.find(opt => opt.value === value);
      if (option) {
        selectedOptions.push(option);
        currentOptions = option.children || [];
      }
    });

    return selectedOptions;
  };

  // 模拟异步加载数据
  const loadData = async selectedOptions => {
    const targetOption = selectedOptions[selectedOptions.length - 1];

    // 模拟API请求延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 根据当前选中的值动态加载子选项
    if (targetOption.value === 'henan') {
      targetOption.children = [
        {
          value: 'new_city',
          label: '新增城市',
          isLeaf: false,
        },
      ];
    } else if (targetOption.value === 'new_city') {
      targetOption.children = [
        {
          value: 'new_district',
          label: '新增区域',
          isLeaf: false,
        },
      ];
    } else if (targetOption.value === 'new_district') {
      targetOption.children = [
        {
          value: 'new_street',
          label: '新增街道',
          isLeaf: true,
        },
      ];
    }
  };

  // 动态加载示例
  const DynamicLoadExample = () => {
    const [dynamicValue, setDynamicValue] = useState([]);

    const handleDynamicChange = (value, selectedOptions) => {
      console.log('动态加载值变化:', value, selectedOptions);
      setDynamicValue(value);
    };

    return (
      <Col span={24}>
        <h3>动态加载数据</h3>
        <LocationCascader options={options} value={dynamicValue} onChange={handleDynamicChange} loadData={loadData} placeholder="请选择地址" />
      </Col>
    );
  };
  const handleSubmit = () => {
    props.form.validateFields((err, values) => {
      if (!err) {
        message.warn(JSON.stringify(values));
      }
    });
  };
  // 自定义悬停内容函数示例
  const customHoverContent = selectedValue => {
    if (!selectedValue || selectedValue.length === 0) {
      return '请选择地址';
    }

    // 根据选中的值查找对应的选项
    let currentOptions = options;
    const labels = [];

    selectedValue.forEach(val => {
      const option = currentOptions.find(opt => opt.value === val);
      if (option) {
        labels.push(option.label);
        currentOptions = option.children || [];
      }
    });

    return (
      <div>
        <p>
          <strong>当前选择:</strong>
        </p>
        <p>{labels.join(' > ')}</p>
      </div>
    );
  };

  return (
    <Card title="LocationCascader 地址级联选择器示例">
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <h3>基础用法</h3>
          <LocationCascader options={options} value={value1} onChange={handleChange1} placeholder="请选择地址" />
        </Col>

        <Col span={24}>
          <h3>带提示的地址选择器</h3>
          <LocationCascader
            options={options}
            value={value2}
            onChange={handleChange2}
            hoverContent={customHoverContent}
            title="地址信息"
            placeholder="请选择地址"
          />
        </Col>
        <Col span={24}>
          <h3>表单组件</h3>
          <Form onSubmit={handleSubmit}>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Form.Item label="地址选择" required>
                  {getFieldDecorator('address', {
                    rules: [{ required: true, message: '请选择地址' }],
                  })(<LocationCascader options={options} hoverContent={customHoverContent} title="地址信息" placeholder="请选择地址" />)}
                </Form.Item>
              </Col>
              <Col span={24}>
                <Button type="primary" htmlType="submit">
                  提交
                </Button>
              </Col>
            </Row>
          </Form>
        </Col>
      </Row>
    </Card>
  );
};

export default Form.create()(LocationCascaderExample);
