import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Form, Row, Col, Select, Checkbox, message, Button, Modal } from 'antd';
import { getItem, isEmptyArray, toArray } from '@/utils/utils';
import { changeSubUser } from './service';
import { DEFAULT_FLAG, NO_DEFAULT_FLAG } from './common';

const Index = props => {
  const { form, editable = true, dispatch, subSystemList } = props ?? {};

  const { getFieldDecorator } = form;

  const user = getItem('user');
  const { userId: sysUserId } = user.userInfo;

  // 保存最终要提交的数据
  const [finalAccountList, setFinalAccountList] = useState([]);
  const [loading, setLoading] = useState(false);

  const close = () => {
    dispatch({ type: 'fourAAccount/close' });
  };

  useEffect(() => {
    setFinalAccountList(subSystemList);
  }, [subSystemList]);

  const getUserCode = (systemInfoId, userId) =>
    subSystemList.find(item => item.systemInfoId === systemInfoId).accountList.find(account => account.externalUserId === userId).externalUserCode;

  const isDefault = (systemInfoId, userId) => subSystemList.find(item => item.systemInfoId === systemInfoId).defaultUserId === userId;

  const handleAccountSelect = (systemInfo, externalUserId) => {
    const { systemInfoId } = systemInfo;

    // 选择项变更后，根据新项是否是默认账号高亮勾选框
    form.setFieldsValue({
      [`${systemInfoId}ToDefault`]: isDefault(systemInfoId, externalUserId),
    });

    const newValue = {
      ...systemInfo,
      currentUserId: externalUserId,
      currentUserCode: getUserCode(systemInfoId, externalUserId),
    };

    const targetIndex = finalAccountList.findIndex(item => item.systemInfoId === systemInfoId);

    setFinalAccountList([...finalAccountList.slice(0, targetIndex), newValue, ...finalAccountList.slice(targetIndex + 1)]);
  };

  const getDefaultUserId = systemInfo =>
    form.getFieldValue(`${systemInfo?.systemInfoId}ToDefault`) ? systemInfo.currentUserId : systemInfo.defaultUserId;

  // 对比finalAccountList和subSystemList获取当前账号有变化的系统
  const getChangedSystem = () => {
    const res = [];
    finalAccountList.forEach(item => {
      const _isDefault = form.getFieldValue(`${item?.systemInfoId}ToDefault`);

      const defaultSystemInfo = subSystemList.find(system => system.systemInfoId === item.systemInfoId);
      // 检测账号是否有变化，有两种情况
      // 1、账号变化
      if (item.currentUserId !== defaultSystemInfo.currentUserId) {
        res.push({
          ...item,
          defaultUserId: getDefaultUserId(item),
        });
        // 2、账号不变，但勾上了是否设置为默认账号（仅记录从无到有，不记录从有到无）
      } else if (_isDefault && defaultSystemInfo.currentUserId !== defaultSystemInfo.defaultUserId) {
        res.push({
          ...item,
          defaultUserId: item.currentUserId,
        });
      }
    });

    return res;
  };

  const getDefaultFlag = systemInfo => (systemInfo.currentUserId === systemInfo.defaultUserId ? DEFAULT_FLAG : NO_DEFAULT_FLAG);

  const formatSystemList = list =>
    list.map(item => ({
      externalUserId: item?.currentUserId,
      externalUserCode: item?.currentUserCode,
      systemInfoId: item?.systemInfoId,
      defaultFlag: getDefaultFlag(item),
      sysUserId,
    }));

  const handleOk = async () => {
    const changeList = getChangedSystem();
    const formatList = formatSystemList(changeList);

    if (isEmptyArray(formatList)) {
      close();
      return;
    }
    setLoading(true);
    const res = await changeSubUser(formatList);

    if (res) {
      setLoading(false);
      dispatch({
        type: 'fourAAccount/update',
        payload: {
          someList: changeList,
        },
      });

      dispatch({
        type: 'login/changeAccount',
        payload: {
          response: res,
        },
      });
    } else {
      message.error('系统异常');
    }
  };

  const renderFormItem = () =>
    subSystemList.map((elem = {}) => (
      <Row key={elem.systemInfoId}>
        <Col span={editable ? 16 : 24}>
          <Form.Item label={elem.systemName}>
            {getFieldDecorator(elem.systemNbr, {
              rules: [
                {
                  required: true,
                  message: `请选择${elem.systemName}账号`,
                },
              ],
              initialValue: elem.currentUserId,
            })(
              <Select
                style={{ width: '100%' }}
                placeholder="请选择"
                onChange={value => {
                  handleAccountSelect(elem, value);
                }}
              >
                {toArray(elem.accountList).map(item => (
                  <Select.Option key={item.externalUserId} value={item.externalUserId}>
                    {item.externalUserCode}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item wrapperCol={{ span: 16 }}>
            {getFieldDecorator(`${elem.systemInfoId}ToDefault`, {
              valuePropName: 'checked',
              initialValue: elem.currentUserId === elem.defaultUserId,
            })(<Checkbox disabled={form.getFieldValue(`${elem.systemInfoId}ToDefault`)}>是否作为默认账号</Checkbox>)}
          </Form.Item>
        </Col>
      </Row>
    ));

  return (
    <Modal
      title="切换业务系统登陆账号"
      width={700}
      visible
      onCancel={close}
      footer={(
        <div style={{ textAlign: 'right' }}>
          <Button type="default" onClick={close}>
            取消
          </Button>
          <Button type="primary" onClick={handleOk} loading={loading}>
            确认
          </Button>
        </div>
      )}
      destroyOnClose
    >
      <Form labelCol={{ span: 8 }} wrapperCol={{ span: 14 }}>
        {renderFormItem()}
      </Form>
    </Modal>
  );
};

export default connect(({ fourAAccount }) => ({
  subSystemList: fourAAccount.subSystemList,
}))(Form.create()(Index));
