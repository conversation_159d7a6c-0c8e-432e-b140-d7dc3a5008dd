import React, { useState } from 'react';
import { Tabs } from 'antd';
import { connect } from 'dva';
import { tabs } from '@/pages/GroupCust/GroupCustView/components/BaseInfo/const';

const { TabPane } = Tabs;
const Index = ({ loading }) => {
  const [activeTab, setActiveTab] = useState('BaseInfo');
  const handleSelectTabset = k => {
    if (loading) return;
    setActiveTab(k);
  };
  return (
    <div>
      <div>
        {/* 应用自定义样式 */}
        <Tabs activeKey={activeTab} onChange={k => handleSelectTabset(k)} className="tabsCustom">
          {tabs.map(({ name, key, component: Component }) => (
            <TabPane
              tab={name}
              key={key}
            >
              <Component />
            </TabPane>
          ))}
        </Tabs>
      </div>
    </div>
  );
};
export default connect(({ groupCustView, loading }) => ({
  loading: loading.effects['groupCustView/getCustomerInfo'],
  ...groupCustView,
}))(Index);
