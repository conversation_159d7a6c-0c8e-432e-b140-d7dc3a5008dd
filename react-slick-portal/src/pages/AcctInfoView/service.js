import request from '@/utils/request';

// 通过接入号查询账户
export function qryAccount(data) {
  return request('portal/GroupAccountController/qryAccountByQueryType.do', {
    method: 'POST',
    data: {
      ...data,
    },
  });
}
// 账户付费用户查询
export function getAcctPaySubscriberAndAccNum(data) {
  return request('portal/GroupAccountController/acctPaySubscriberAndAccNum.do', {
    method: 'POST',
    data: {
      operCode: '1',
      ...data,
    },
  });
}
// 根据custId获取custName
export function getGroupNameByCustId(data) {
  return request('portal/GroupAccountController/acctPaySubscriberAndAccNum.do', {
    method: 'POST',
    data: {
      operCode: '1',
      ...data,
    },
  });
}
