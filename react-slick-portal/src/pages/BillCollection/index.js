import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'dva';
import { Button } from 'antd';
import StepWizard from '@/components/StepWizard';
import SelectCustomer from './components/SelectCustomer/index';
import styles from './index.less';
import PaymentInfo from '@/pages/BillCollection/components/PaymentInfo';
import Complete from '@/pages/BillCollection/components/Complete';
import { Portal } from '@/utils/utils';

function BillCollection() {
  const [currentStep, setCurrentStep] = useState(1);
  const selectRef = useRef(null);
  const payRef = useRef(null);
  const completeRef = useRef(null);
  const swEl = useState(null);
  // 步骤条配置
  const steps = [
    {
      title: '选择客户/账户',
      content: '选择客户并填写相关信息',
    },
    {
      title: '填写缴费信息',
      content: '进行话费缴纳操作',
    },
    {
      title: '缴费完成',
      content: '缴费完成',
    },
  ];

  useEffect(() => {
    console.log(currentStep);
  });

  /* 下一步 */
  const goNext = () => {
    if (currentStep === 1) {
      selectRef.current?.next();
    } else if (currentStep === 2) {
      payRef.current?.next();
    }
  };

  const goPrev = () => {
    if (currentStep === 2) {
      payRef.current?.prev();
    }
    if (currentStep === 3) {
      completeRef.current?.prev();
    }
  };

  const handleClose = () => {
    Portal.close('/BillCollection');
  };

  return (
    <div className={styles.container}>
      {/* 导航栏 */}
      <div className={styles.stepsContainer}>
        {steps.map((item, index) => (
          <div
            key={item.title}
            className={`${styles.stepItem} ${index + 1 === currentStep ? styles.active : ''} ${index + 1 < currentStep ? styles.finished : ''}`}
          >
            {item.title}
          </div>
        ))}
      </div>
      {/* 内容区 */}
      <StepWizard
        initialStep={1}
        instance={sw => {
          swEl.current = sw;
        }}
        onStepChange={({ activeStep }) => {
          setCurrentStep(activeStep);
        }}
      >
        <SelectCustomer cRef={selectRef} />
        <PaymentInfo cRef={payRef} destroy />
        <Complete cRef={completeRef} destroy />
      </StepWizard>
      {/* 动作 */}
      <div className={styles.footer}>
        {currentStep === 1 && (
          <Button type="primary" onClick={goNext}>
            下一步
          </Button>
        )}
        {currentStep === 2 && (
          <>
            <Button type="primary" onClick={goNext} className="margin-right">
              下一步
            </Button>
            <Button onClick={goPrev}>上一步</Button>
          </>
        )}
        {currentStep === 3 && (
          <>
            <Button type="primary" onClick={handleClose}>
              关闭
            </Button>
          </>
        )}
      </div>
    </div>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(BillCollection);
