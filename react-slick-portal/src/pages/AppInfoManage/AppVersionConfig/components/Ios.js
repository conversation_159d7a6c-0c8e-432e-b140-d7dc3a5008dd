import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Select,
  Icon,
  Input,
  Button,
  Tag,
  Modal,
  Divider,
  message,
} from 'antd';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
import request from '@/utils/request';

const { Search } = Input;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};

function ios(props) {
  const {
    form: { getFieldDecorator },
  } = props;
  const [iosLoading, setIosLoading] = useState(false);

  const iosKeep = () => {
    props.form.validateFields((err, fieldsValue) => {
      if (err) return;
      setIosLoading(true);
      request('portal/DataDictController/update.do', {
        data: {
          id: 51222,
          paramValue: fieldsValue.Ios,
        },
        method: 'PUT',
      }).then(res => {
        setIosLoading(false);
        if (res) {
          message.success('保存成功！');
        } else {
          message.error('保存失败！');
        }
      });
    });
  };

  return (
    <Form {...formItemLayout}>
      <Form.Item label="IOS最低版本号">
        <Row gutter={8}>
          <Col span={16}>
            {getFieldDecorator('Ios', {
              rules: [
                {
                  required: true,
                  message: '版本号不能为空',
                },
                {
                  required: false,
                  pattern: new RegExp(/^$|^\d+(\.\d+){2}_\d+$/, 'g'),
                  message: '请输入正确版本格式',
                },
              ],
              initialValue: props.iosValue.paramValue,
            })(<Input placeholder="请输入IOS版本号" />)}
          </Col>
          <Col span={8}>
            <Button type="primary" onClick={iosKeep} loading={iosLoading}>
              保存
            </Button>
          </Col>
        </Row>
      </Form.Item>
    </Form>
  );
}

export default connect(({}) => ({}))(Form.create()(ios));
