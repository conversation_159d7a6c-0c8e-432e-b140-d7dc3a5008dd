import React from 'react';
import { Button, Space, message } from 'antd';
import { 
  AddIcon, 
  DeleteIcon, 
  EditIcon, 
  SearchIcon, 
  SuccessIcon, 
  ErrorIcon, 
  WarningIcon,
  HomeIcon,
  UserIcon,
  SettingIcon,
  ICON_SIZES,
  ICON_COLORS 
} from './index';

/**
 * 图标使用示例
 * 展示如何在实际项目中使用新的图标系统
 */
const IconExample = () => {
  const handleClick = (action) => {
    message.info(`点击了${action}按钮`);
  };

  return (
    <div style={{ padding: 24 }}>
      <h2>图标使用示例</h2>
      
      {/* 基础使用 */}
      <div style={{ marginBottom: 32 }}>
        <h3>1. 基础使用</h3>
        <Space size="large">
          <AddIcon size={24} color={ICON_COLORS.PRIMARY} />
          <DeleteIcon size={24} color={ICON_COLORS.ERROR} />
          <EditIcon size={24} color={ICON_COLORS.WARNING} />
          <SearchIcon size={24} color={ICON_COLORS.SUCCESS} />
        </Space>
      </div>

      {/* 按钮中使用 */}
      <div style={{ marginBottom: 32 }}>
        <h3>2. 在按钮中使用</h3>
        <Space>
          <Button type="primary" onClick={() => handleClick('添加')}>
            <AddIcon size={16} style={{ marginRight: 4 }} />
            添加
          </Button>
          <Button danger onClick={() => handleClick('删除')}>
            <DeleteIcon size={16} style={{ marginRight: 4 }} />
            删除
          </Button>
          <Button onClick={() => handleClick('编辑')}>
            <EditIcon size={16} style={{ marginRight: 4 }} />
            编辑
          </Button>
          <Button onClick={() => handleClick('搜索')}>
            <SearchIcon size={16} style={{ marginRight: 4 }} />
            搜索
          </Button>
        </Space>
      </div>

      {/* 状态图标 */}
      <div style={{ marginBottom: 32 }}>
        <h3>3. 状态图标</h3>
        <Space size="large">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <SuccessIcon size={20} color={ICON_COLORS.SUCCESS} style={{ marginRight: 8 }} />
            <span>操作成功</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <ErrorIcon size={20} color={ICON_COLORS.ERROR} style={{ marginRight: 8 }} />
            <span>操作失败</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <WarningIcon size={20} color={ICON_COLORS.WARNING} style={{ marginRight: 8 }} />
            <span>警告信息</span>
          </div>
        </Space>
      </div>

      {/* 不同尺寸 */}
      <div style={{ marginBottom: 32 }}>
        <h3>4. 不同尺寸</h3>
        <Space size="large" align="center">
          <HomeIcon size={ICON_SIZES.SMALL} />
          <HomeIcon size={ICON_SIZES.DEFAULT} />
          <HomeIcon size={ICON_SIZES.MEDIUM} />
          <HomeIcon size={ICON_SIZES.LARGE} />
          <HomeIcon size={ICON_SIZES.XLARGE} />
        </Space>
        <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
          12px / 16px / 20px / 24px / 32px
        </div>
      </div>

      {/* 可点击图标 */}
      <div style={{ marginBottom: 32 }}>
        <h3>5. 可点击图标</h3>
        <Space size="large">
          <UserIcon 
            size={24} 
            color={ICON_COLORS.PRIMARY}
            onClick={() => handleClick('用户')}
            style={{ cursor: 'pointer' }}
            title="用户信息"
          />
          <SettingIcon 
            size={24} 
            color={ICON_COLORS.DEFAULT}
            onClick={() => handleClick('设置')}
            style={{ cursor: 'pointer' }}
            title="系统设置"
          />
        </Space>
      </div>

      {/* 自定义颜色 */}
      <div style={{ marginBottom: 32 }}>
        <h3>6. 自定义颜色</h3>
        <Space size="large">
          <AddIcon size={24} color="#ff6b6b" />
          <AddIcon size={24} color="#4ecdc4" />
          <AddIcon size={24} color="#45b7d1" />
          <AddIcon size={24} color="#96ceb4" />
          <AddIcon size={24} color="#feca57" />
        </Space>
      </div>

      {/* 继承父元素颜色 */}
      <div style={{ marginBottom: 32 }}>
        <h3>7. 继承父元素颜色</h3>
        <div style={{ color: '#1890ff' }}>
          <AddIcon size={20} style={{ marginRight: 8 }} />
          这个图标会继承父元素的蓝色
        </div>
        <div style={{ color: '#52c41a' }}>
          <SuccessIcon size={20} style={{ marginRight: 8 }} />
          这个图标会继承父元素的绿色
        </div>
      </div>
    </div>
  );
};

export default IconExample;
