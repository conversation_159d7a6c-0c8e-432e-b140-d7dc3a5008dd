import React, { useState, useEffect, useLayoutEffect } from 'react';
import { Icon, Tabs, message } from 'antd';
import { connect } from 'dva';
import router from 'umi/router';
import { Helmet } from 'react-helmet';
import classNames from 'classnames';
import { formatMessage } from 'umi-plugin-react/locale';
import find from 'lodash/find';
import findIndex from 'lodash/findIndex';
import isObject from 'lodash/isObject';
import delay from 'lodash/delay';
import moment from 'moment';
import { useDebounceFn } from '@umijs/hooks';
import Link from 'umi/link';
import Exception from '@/components/Exception';
import Authorized from '@/utils/Authorized';
import traceLogAction from '@/services/traceLogAction';
import request from '@/utils/request';
import { checkStatus } from '@/services/login';
import { IEVersion, mouseWheel, getViewPort, isJSON, getFinalUrl } from '@/utils/utils';
import Dashboard from '@/pages/Dashboard';
import Header from './components/Header';
import MenuDrawer from './components/MenuDrawer';
import SettingDrawer from './components/SettingDrawer';
import './less/index.less';
import leftStyle from './less/jumpTabs.less';

const wheel = mouseWheel();
const { TabPane } = Tabs;

// 把对象的key序列化成小写
function serialize(data) {
  const result = {};
  Object.keys(data).forEach(val => {
    result[`${val.trim().toLocaleLowerCase()}`] = data[`${val}`];
  });
  return result;
}

function triggerResizeEvent() {
  const event = document.createEvent('HTMLEvents');
  event.initEvent('resize', true, false);
  window.dispatchEvent(event);
}

function hasScrollbar() {
  return (
    document.body.scrollHeight - 45 > (window.innerHeight || document.documentElement.clientHeight)
  );
}

// eslint-disable-next-line no-unused-vars
function getScrollTop() {
  return document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
}

function BasicLayout(props) {
  const {
    dispatch,
    currMenu,
    location: { pathname },
    all,
    setting: { theme, settingDrawerActive, menuDrawerActive, viewMode, size, showLeftMenu, gutter },
    children,
    loginoutResSerList,
  } = props;

  const home = {
    key: '/',
    url: '/',
    closable: false,
    title: formatMessage({ id: 'menu.home' }),
    content: <Dashboard />,
  };

  // eslint-disable-next-line no-unused-vars
  const [activeKey, setActiveKey] = useState('');
  const [title, setTitle] = useState('');
  const [tabPanes, setTabPanes] = useState([home]);
  const [wheelDirection, setWheelDirection] = useState(null);
  const [leftTabs, setLeftTabs] = useState([]);
  // 防抖，计算当前画布大小
  const { run: setSize } = useDebounceFn(() => {
    const { width, height } = getViewPort(false);
    const $header = document.getElementById('layout-header');
    const $tabsNav = document.querySelector('.layout-tabs-nav-blue');
    // const $pane = document.querySelector('.ant-tabs-tabpane-active');
    const $menuDrawer = document.getElementById('menu-drawer');
    const contentHeight =
      height -
      ($header === null ? 0 : $header.offsetHeight) -
      ($tabsNav === null ? 0 : $tabsNav.offsetHeight) -
      // ($pane === null
      //   ? 0
      //   : parseInt(getComputedStyle($pane).paddingTop, 10) +
      //     parseInt(getComputedStyle($pane).paddingBottom, 10)) -
      (viewMode === 'inner' ? 0 : parseInt(gutter, 10) * 2);
    const contentWidth = width - ($menuDrawer === null ? 0 : $menuDrawer.offsetWidth);
    dispatch({
      type: 'setting/saveSize',
      payload: { width: contentWidth, height: contentHeight, fullHeight: height, fullWidth: width },
    });
  }, 50);

  /**
   * 改变tab时的回调,实时更新url
   */
  /* eslint-disable */
  function onTabChange(activeKey, tabPanes) {
    for (let item of tabPanes) {
      if (item.key === `${activeKey}`) {
        if (item.openUrl) {
          request(item.openUrl, {
            method: 'GET',
            showError: false,
          });
        }
      }
    }

    /**
     * IMPROVE: 比如下面场景：
     * 首先，用Portal.open('/iframe/xxx?abc=123')打开一个iframe页面('?abc=123'部分表示往iframe页面传参)
     * 接着，在未关闭该iframe页面时，又Portal.open('/iframe/xxx?abc=456')  参数部分变了
     * 结果，iframe中的props.location.search未更新，导致传参错误
     * 处理方式：当同一个页面，search变化时，需要更新tabPanes中对应项的url以及children，更新children才能保证他上面挂载的props.location.search被更新
     */
    router.push(find(tabPanes, { key: activeKey }).url);
  }
  /* eslint-enable */

  /**
   * 关闭tab时的回调
   */
  function onTabRemove(targetKey, currentTabKey, tabPanes) {
    // 如果关闭的是当前tab, 要激活哪个tab?
    // 首先尝试激活左边的, 再尝试激活右边的
    let nextTabKey = currentTabKey;
    if (nextTabKey === targetKey) {
      let currentTabIndex = -1;
      tabPanes.forEach((pane, i) => {
        if (pane.key === targetKey) {
          currentTabIndex = i;
        }
      });

      // 如果当前tab左边还有tab, 就激活左边的
      if (currentTabIndex > 0) {
        nextTabKey = tabPanes[currentTabIndex - 1].key;
      }
      // 否则就激活右边的tab
      else if (currentTabIndex === 0 && tabPanes.length > 1) {
        nextTabKey = tabPanes[currentTabIndex + 1].key;
      }
    }

    // 过滤panes
    const newTabPanes = tabPanes.filter(pane => pane.key !== targetKey);
    setTabPanes(newTabPanes);
    // setCurrentTabKey(nextTabKey);
    // 关闭时候实时更新url
    router.replace(find(tabPanes, { key: nextTabKey }).url);
  }

  // 鼠标移入左侧标签时
  const onMouseEnterTabs = (item, index) => {
    const newTabs = leftTabs.slice(0);
    newTabs[index].showClose = true;
    let ellipsisTitle = item.title;
    if (ellipsisTitle.length > 8) {
      ellipsisTitle = ellipsisTitle.slice(0, 7);
      ellipsisTitle = ellipsisTitle.concat('...');
    }
    newTabs[index].showTitle = ellipsisTitle;
    setLeftTabs(newTabs);
  };

  // 鼠标移出左侧标签时
  const onMouseOutTabs = (item, index) => {
    const newTabs = leftTabs.slice(0);
    newTabs[index].showClose = false;
    newTabs[index].showTitle = item.ellipsisTitle;
    setLeftTabs(newTabs);
  };

  // 点击关闭左侧标签时
  const deleteTabs = index => {
    const newTabs = leftTabs.slice(0);
    newTabs.splice(index, 1);
    setLeftTabs(newTabs);

    const newTabPanes = tabPanes.slice(0);
    newTabPanes.splice(index + 1, 1);
    setTabPanes(newTabPanes);
  };

  /**
   *
   * @param {string} pathname  当前页面路径
   * @param {object[]} tabPanes
   * @param {object} currMenu  当前菜单信息，必须包含3个字段：{menuId,urlAddr,menuName,menuCode}
   * @param {string} viewMode 视图模式,可选值 'normal | inner'
   */
  function renderBody(pathname, tabPanes, currMenu, viewMode, size) {
    const activeKey = pathname;
    const renderMain = () => (
      <>
        <span className={leftStyle.wrap}>
          {pathname === '/' &&
            leftTabs.map((item, index) => (
              <div
                key={item.title}
                className={leftStyle.jumpLabel}
                onMouseEnter={() => { onMouseEnterTabs(item, index); }}
                onMouseLeave={() => { onMouseOutTabs(item, index); }}
              >
                <Link
                  to={getFinalUrl(item, 'url')}
                  title={item.title}
                  className={leftStyle.text}
                  onClick={() => { onMouseOutTabs(item, index); }}
                >
                  {item.showTitle}
                </Link>
                <a className={leftStyle.close} onClick={() => { deleteTabs(index); }}>{item.showClose && '×'}</a>
              </div>
            ))
          }
        </span>
        <Tabs
          activeKey={activeKey}
          renderTabBar={(props, DefaultTabBar) => (
            <DefaultTabBar {...props} className={`${pathname === '/' ? 'layout-tabs-nav-blue homeTabs' : 'layout-tabs-nav-blue'}`} />
          )}
          type="editable-card"
          onEdit={targetKey => onTabRemove(targetKey, activeKey, tabPanes)}
          onChange={activeKey => onTabChange(activeKey, tabPanes)}
          hideAdd
          animated={IEVersion() !== 9}
          className="layout-body-main"
        >
          {/**
         * 100% 是size.height的初始值，表示setSize未计算结束
         * 必须保证加载children前size计算结束，因为children依赖size的
         * 必须返回undefined。返回null 会出现warning
         */}
          {size.height === '100%'
            ? undefined
            : tabPanes.map(pane => (
              // eslint-disable-next-line react/jsx-indent
              <TabPane
                tab={pane.key === '/' ? <Icon type="home" theme="filled" /> : pane.title}
                key={pane.key}
                closable={pane.key !== '/'}
                style={{ padding: `${pathname === '/' ? 0 : gutter}px` }}
              >
                {pane.content}
              </TabPane>
            ))}
        </Tabs>
        {showLeftMenu && <MenuDrawer />}
      </>
    );
    return (
      <Authorized
        authority={currMenu === undefined || currMenu === null ? undefined : currMenu.authority}
        noMatch={<Exception type="403" />}
      >
        <div id={pathname !== '/' && 'layout-body'}>
          {viewMode === 'inner' ? (
            tabPanes[findIndex(tabPanes, { key: activeKey })] &&
            tabPanes[findIndex(tabPanes, { key: activeKey })].content
          ) : renderMain()}
        </div>
      </Authorized>
    );
  }

  // 设置左侧跳转页签
  useEffect(() => {
    // 获取除了首页之外的标签
    const [, ...tabs] = tabPanes;
    tabs.forEach(element => {
      let ellipsisTitle = element.title;
      if (ellipsisTitle.length > 4) {
        ellipsisTitle = ellipsisTitle.slice(0, 3);
        ellipsisTitle = ellipsisTitle.concat('...');
      }
      element.ellipsisTitle = ellipsisTitle;
      element.showTitle = ellipsisTitle;
    });
    setLeftTabs(tabs);
  }, [tabPanes]);

  /**
   * 监听postMessage指令
   * 指令格式：{to:"portal",action:"close|open",pathname:"/notice"}
   */
  useEffect(() => {
    // 监听来自低代码平台的提示消息
    function handleLowCode(event) {
      if (!event.data || !event.data.u__$Data) {
        return;
      }
      const data = event.data.u__$Data;
      const { to, action, type, content } = serialize(data);
      if (to !== 'portal' || action !== 'tip') {
        return;
      }
      if (type === 'success') {
        message.success(content);
      } else if (type === 'error') {
        message.error(content);
      } else {
        message.warning(content);
      }
    }

    function handleActions(event) {
      let data;
      // 判断是否是来自低代码平台的消息（u__$Data）
      if (event.data && event.data.u__$Data) {
        data = event.data.u__$Data;
      } else {
        if (!isJSON(event.data)) {
          return;
        }
        data = JSON.parse(event.data);
        if (!isObject(data)) {
          return;
        }
      }
      // 把对象中的key全部变小写且去头尾空格
      const { to, action, pathname, targetmenu } = serialize(data);

      // 子系统主动发送校验登录
      if (to.toLocaleLowerCase() === 'portal' && action.toLocaleLowerCase() === 'check') {
        dispatch({
          type: 'login/logout',
        });
        return;
      }

      // 不是有效指令，
      if (
        !to ||
        !action ||
        !pathname ||
        to.trim().toLocaleLowerCase() !== 'portal' ||
        !['close', 'open'].includes(action.toLocaleLowerCase())
      ) {
        // message.error('无效指令，正确格式：{to:"portal",action:"close|open",pathname:""}');
        return;
      }
      // 格式化pathname,防止不以斜杆开头，或以斜杆结尾
      let formatPathname = pathname;
      if (!/^\/\w+/i.test(pathname)) {
        formatPathname = `/${pathname}`;
      }
      if (pathname.endsWith('/')) {
        formatPathname = formatPathname.slice(0, formatPathname.length - 1);
      }
      if (action.toLocaleLowerCase() === 'close') {
        const newTabPanes = [...tabPanes.filter(item => item.key !== formatPathname)];
        router.replace(targetmenu ? decodeURIComponent(targetmenu) : newTabPanes[newTabPanes.length - 1].key);
        setTabPanes(newTabPanes);
      }

      if (action.toLocaleLowerCase() === 'open') {
        router.replace(formatPathname);
      }
    }

    // closeAll 关闭所有标签
    function closeAll(event) {
      if (!isJSON(event.data)) {
        return;
      }
      const data = JSON.parse(event.data);
      if (!isObject(data)) {
        return;
      }
      // 把对象中的key全部变小写且去头尾空格
      const { to, action, pathname } = serialize(data);

      if (action.toLocaleLowerCase() === 'closeall') {
        const newTabPanes = [...tabPanes.filter(item => item.key === '/')];
        setTabPanes(newTabPanes);
      }
    }

    // window.self === window.top 返回false 说明页面嵌套在iframe中 不需监听当前的postMessage 主系统已经监听
    if (window.self === window.top) {
      window.addEventListener('message', handleActions);
      window.addEventListener('message', handleLowCode);
    }

    window.addEventListener('message', closeAll);

    return () => {
      window.removeEventListener('message', handleActions);
      window.removeEventListener('message', handleLowCode);
    };
  }, [tabPanes]);

  // 监听画布size
  useLayoutEffect(() => {
    setSize();
    window.addEventListener('resize', setSize);
    return () => {
      window.removeEventListener('resize', setSize);
    };
  }, [setSize]);

  // 修复'react-grid-layout'组件在宽度变化时不能适配的bug
  useLayoutEffect(() => {
    if (pathname === '/' || pathname === '/customize/user' || pathname === '/customize/role') {
      delay(triggerResizeEvent, 100);
    }
  }, [pathname]);

  // 只在初始化阶段，鼠标滚轮事件监听
  useEffect(() => {
    wheel.config({
      up: () => {
        if (hasScrollbar() && !document.querySelector('body').classList.contains('noScroll')) {
          setWheelDirection('up');
        }
      },
      down: () => {
        if (hasScrollbar() && !document.querySelector('body').classList.contains('noScroll')) {
          setWheelDirection('down');
        }
      },
    });
  }, []);

  // 负责更新TabPanes
  // 根据pathname和all判断当前页面是否存在于菜单中
  useEffect(() => {
    if (pathname.toLocaleLowerCase() !== '/') {
      const newMenu = currMenu;
      if (newMenu !== undefined) {
        setTitle(newMenu.menuName);
        // 如果key不存在就要新增一个tabPane
        if (find(tabPanes, { key: pathname }) === undefined) {
          setTabPanes([
            ...tabPanes,
            {
              key: pathname,
              url: pathname + props.location.search || '',
              title: newMenu.menuName,
              content: children,
              openUrl: currMenu.ifAsync && currMenu.ifAsync === '1000' ? currMenu.openUrl : null,
            },
          ]);
          setActiveKey(pathname);
          // 记录打开菜单
          traceLogAction.traceOpenMenuLog({
            menuId: currMenu.menuId,
            menuName: currMenu.menuName,
          });

          // 新增打开标签发起请求
          if (currMenu.ifAsync && currMenu.ifAsync === '1000') {
            // request()
            if (currMenu.openUrl) {
              request(currMenu.openUrl, {
                method: 'GET',
                showError: false,
              });
            }
          }

          // 更新历史记录菜单
          dispatch({
            type: 'menu/updateRecentMenu',
            payload: currMenu.menuId,
          });
        }
      }
    } else {
      // 更新系统链接
      dispatch({
        type: 'dashboard/getSystemLink',
      });
      // 更新轮播图
      dispatch({
        type: 'banner/getBannerList',
      });
      // 更新待办事项
      dispatch({
        type: 'undo/qryTasksNumbers',
      });
      // 更新未读消息
      checkStatus(dispatch, moment().format('YYYY-MM-DD HH:mm:ss'), loginoutResSerList);
      // 防止antd抽屉卡住首页
      const body = document.getElementsByTagName('body');
      if (body?.[0]?.classList) {
        body[0].classList.remove('ant-scrolling-effect');
      }
      setTitle(home.title);
      setActiveKey('/');
    }
  }, [pathname, all, currMenu, tabPanes, props.location.search, children, dispatch, home.title, loginoutResSerList]);

  // 切换Tab时，判断是否Iframe内嵌的第三方页面，需要控制body的滚动条，避免出现2个
  // useEffect(() => {
  //   if (pathToRegexp('/iframe/:id').test(pathname)) {
  //     document.querySelectorAll('body')[0].style.overflow = 'hidden';
  //   } else {
  //     document.querySelector('body').style.overflow = 'hidden auto';
  //   }
  // }, [pathname]);

  return (
    <>
      <Helmet>
        <title>{`${title} - ${formatMessage({ id: 'app.title' })}`}</title>
      </Helmet>
      <div
        id="layout-container"
        {...wheel.events}
        className={classNames(`view-${viewMode}`, `theme-${theme}`, {
          'menu-drawer-active': menuDrawerActive,
          'setting-drawer-active': settingDrawerActive,
          'tabs-nav-fixed-blue': (wheelDirection === 'down' && pathname !== '/'),
        })}
      >
        {viewMode === 'normal' ? <Header /> : null}
        {renderBody(pathname, tabPanes, currMenu, viewMode, size)}
        {viewMode === 'normal' ? (
          <>
            <SettingDrawer />
            <div
              className={classNames('setting-drawer-mask')}
              onClick={() => {
                dispatch({
                  type: 'setting/toggleSettingDrawer',
                  payload: false,
                });
                // 修复'react-grid-layout'组件在宽度变化时不能适配的bug
                delay(triggerResizeEvent, 100);
              }}
            />
          </>
        ) : null}

        {/* {status === mapping.LOCKED && <Locked />} */}
      </div>
    </>
  );
}

export default connect(({ setting, menu, login }) => ({
  setting,
  all: menu.all,
  status: login.status,
  loginoutResSerList: login.loginoutResSerList,
}))(BasicLayout);
