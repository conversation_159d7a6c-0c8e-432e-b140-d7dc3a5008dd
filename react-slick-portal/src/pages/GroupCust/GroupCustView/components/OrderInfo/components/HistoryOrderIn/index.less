
.queryCard {
  margin-bottom: 16px;

  :global {
    .ant-card-head {
      background-color: #f5f7fa;
      border-bottom: 1px solid #e8e8e8;
    }

    .ant-card-head-title {
      padding: 12px 0;
    }

    .ant-form-item-label {
      text-align: right;
      width: 100px;
    }
  }
}

.cardTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.collapseButton {
  color: #1890ff;
  padding: 0;
  height: auto;
}

.resetButton {
  margin-left: 8px;
}

:global {
  .text-right {
    text-align: right;
  }

  .margin-left {
    margin-left: 8px;
  }

  .ant-table-thead > tr > th {
    background-color: #f5f7fa;
    color: #333;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #e6f7ff;
  }
}
