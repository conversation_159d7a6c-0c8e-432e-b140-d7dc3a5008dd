.siderLayout{

  .menuContainer{
    padding: 16px;

    .menuItem{
      display: flex;
      align-items: center;
      height: 42px;
      line-height: 42px;
    }
  }
  :global{
    .ant-layout{
      background: transparent;
      height: 100%;
    }
    .ant-layout.ant-layout-has-sider > .ant-layout {
      overflow-x: hidden;
    }
    .ant-layout-sider-light{
      border-radius: 4px;
    }
    .ant-layout-sider-trigger{
      bottom: 8px;
      text-align: right;
      border-radius: 0 0 4px 4px;
    }
    .ant-menu-inline{
      border: none;
      .ant-menu-item{
        &::after{
          display: none;
        }
      }
    }
  }
}

.collapsedView{
  .menuContainer{
    .menuItem{
      padding: 0 !important;
      height: 28px;
      line-height: 28px;
      img{
        margin-left: 4px;
      }
    }
  }
  :global{
    .ant-layout-sider-light{
      margin: -8px;
    }
    .ant-menu-inline-collapsed{
      width: 28px;
      border: none;
    }
    .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected{
      background: #F5F5F5;
      border-radius: 2px;
    }

  }
}
:global {
  .ant-tooltip-arrow::before{
    background-color: #FFF;
  }
  .ant-tooltip-inner{
    display: flex;
    align-items: center;
    color: #333;
    background: #FFF;
  }
}
