import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Card } from 'antd';
import dynamic from 'umi/dynamic';
import PageLoading from '@/components/PageLoading';

const CustomerGrowthRate = dynamic({
  loader: () => import('./CustomerGrowthRate'),
  loading: PageLoading,
});

const LeadGrowthRate = dynamic({
  loader: () => import('./LeadGrowthRate'),
  loading: PageLoading,
});

const OppGrowthRate = dynamic({
  loader: () => import('./OppGrowthRate'),
  loading: PageLoading,
});

const ConstractGrowthRate = dynamic({
  loader: () => import('./ConstractGrowthRate'),
  loading: PageLoading,
});

const OrderGrowthRate = dynamic({
  loader: () => import('./OrderGrowthRate'),
  loading: PageLoading,
});

const map = {
  customer: <CustomerGrowthRate />,
  lead: <LeadGrowthRate />,
  opp: <OppGrowthRate />,
  constract: <ConstractGrowthRate />,
  order: <OrderGrowthRate />,
};

function GrowthRate() {
  const [activeKey, setActiveKey] = useState('customer');
  const [contentList, setContenetList] = useState({ customer: <CustomerGrowthRate /> });

  useEffect(() => {
    if (!Object.hasOwnProperty.call(contentList, activeKey)) {
      contentList[activeKey] = map[activeKey];
      const content = { ...contentList, [activeKey]: map[activeKey] };
      setContenetList(content);
    }
  }, [activeKey, contentList]);

  return (
    <Card
      tabList={[
        {
          key: 'customer',
          tab: '客户',
        },
        {
          key: 'lead',
          tab: '线索',
        },
        {
          key: 'opp',
          tab: '商机',
        },
        {
          key: 'constract',
          tab: '合同',
        },
        {
          key: 'order',
          tab: '订单',
        },
      ]}
      activeTabKey={activeKey}
      bodyStyle={{ height: 326 }}
      onTabChange={key => {
        setActiveKey(key);
      }}
    >
      {contentList[activeKey]}
    </Card>
  );
}

export default GrowthRate;
