import { message } from 'antd';
import { omit } from 'lodash';
import { nanoid } from 'nanoid';
import {
  pageContactUsers,
  pageChatUsers,
  pageChatContent,
} from '@/services/instantMessage';
import { scrollToBottom } from '@/utils/scroller';
import { wsCreate } from '@/utils/websocket';
import { getItem } from '@/utils/utils';

const initState = {
  loginId: '',
  loginName: '',
  visible: false, // 聊天窗口打开状态
  curUser: {}, // 当前聊天用户
  inputValue: '', // 发送消息内容
  users: [], // 用户列表
  messageList: [], // 消息列表
  // sendStatus: 1, // 消息发送状态，0：等待、1：成功、2：失败
  searchUsers: [], // 搜索用户结果
  pageNum: 1,
  hasMoreMessage: true,
  messageLoading: false,
  websocket: null, // WebSocket实例
  historyVisible: false, // 聊天记录打开状态
  historyList: [], // 聊天历史记录
  historyPageNum: 1,
  hasMoreHistory: true,
  historyLoading: false,
  searchObj: {}, // 聊天记录搜索条件
};

export default {
  namespace: 'message',

  state: initState,

  effects: {
    *init({ payload }, { put }) {
      const { websocket } = payload;
      const { userId, userName } = getItem('user')?.userInfo;
      yield put({
        type: 'saveLogin',
        payload: { loginId: userId, loginName: userName },
      });
      yield put({
        type: 'getUserList',
      });
      yield put({
        type: 'saveSocket',
        payload: { websocket },
      });
    },
    // 控制即时通讯窗口显示
    *changeVisible({ payload }, { put, select }) {
      const { websocket } = yield select(state => state.message);
      yield put({
        type: 'saveVisible',
        payload,
      });
      if (payload && !websocket) {
        // 打开弹窗时如果连接已断开，重新调用连接方法，进行用户和消息初始化
        wsCreate();
      }
    },
    *changeCurUser({ payload }, { put }) {
      yield put({
        type: 'saveCurUser',
        payload,
      });
    },
    // 获取最近联系用户列表
    *getUserList(_, { call, put, select }) {
      const { users } = yield select(state => state.message);
      let newUser = [];
      const usersResp = yield call(pageChatUsers, {
        pageSize: 100,
        pageNum: 1,
      });
      if (Array.isArray(usersResp.list)) {
        if (users.length) {
          const arrs = [...users, ...usersResp.list];
          newUser = arrs.filter((list, index) => arrs.findIndex(item => item.objId === list.objId) === index);
        } else {
          // const arrsRecent = usersResp.list.filter((list, index) => usersResp.list.findIndex(item => item.objId === list.objId) === index);
          newUser = usersResp.list;
        }
        yield put({
          type: 'saveUsers',
          payload: newUser,
        });
        if (newUser.length > 0) {
          const userId = newUser[0].objId;
          yield put({
            type: 'changeCurUser',
            payload: newUser[0],
          });
          yield put({
            type: 'getMessageList',
            payload: { chatId: userId, pageNum: 1 },
          });
          setTimeout(() => {
            scrollToBottom('bottomElement', 'chatItems');
          }, 500);
        }
      }
    },
    // 切换当前聊天对象
    *changeUsers({ payload }, { select, put }) {
      const { users, curUser } = yield select(state => state.message);
      const newUsers = users.map(item => {
        if (item.objId === curUser.objId) {
          return { ...item, ...payload };
        }
        return item;
      });
      yield put({
        type: 'updateUsers',
        payload: newUsers,
      });
    },
    // 获取搜索用户数据
    *getSearchUser({ payload }, { call, put }) {
      if (payload) {
        const result = yield call(pageContactUsers, {
          pageSize: 100,
          pageNum: 1,
          anyProperties: {
            userName: payload,
          },
        });
        if (result) {
          yield put({
            type: 'saveSearchUsers',
            payload: result?.list,
          });
        }
      } else {
        yield put({
          type: 'saveSearchUsers',
          payload: [],
        });
      }
    },
    // 选择搜索用户至最近联系用户列表中
    *addSearchUser({ payload }, { put, select, take }) {
      const users = yield select(state => state.message.users);
      yield put({
        type: 'addUser',
        payload: [payload, ...users],
      });
      yield take('addUser/@@end');
      yield put({
        type: 'getMessageList',
        payload,
      });
    },
    // 查询发送者为当前登录用户，接收者为入参ID的消息记录
    *getMessageList({ payload }, { call, put, select }) {
      const { chatId = '', pageNum } = payload;
      const { messageList, curUser } = yield select(state => state.message);
      try {
        yield put({ type: 'changeLoading', payload: { messageLoading: true } });
        const result = yield call(pageChatContent, {
          pageSize: 10,
          pageNum,
          anyProperties: {
            partnerId: Number(chatId || curUser.objId),
          },
        });
        if (Array.isArray(result.list)) {
          let noRepeatList = [];
          // lists.reverse();
          if (pageNum !== 1) {
            const newsList = result.list.reverse().concat(messageList);
            noRepeatList = newsList.filter((list, index) =>
              newsList.findIndex(item => item.instantMessageId === list.instantMessageId) === index);
          } else {
            noRepeatList = result.list.reverse();
          }
          yield put({
            type: 'updateMessageList',
            payload: { messageList: noRepeatList, pageNum, hasMoreMessage: result.list.length > 0 },
          });
        }
        yield put({ type: 'changeLoading', payload: { messageLoading: false } });
      } catch (error) {
        message.error(error);
      }
    },
    *sendMessage({ payload }, { put, select }) {
      yield put({ type: 'changeInputValue', payload: '' });
        const { users, messageList, loginId, loginName, websocket } = yield select(state => state.message);
        const sendMsg = {
          ...payload,
          uuid: nanoid(),
          sendId: loginId,
          sendName: loginName,
          messageStatus: '1000',
          sendStatus: 0,
        };
        if (!websocket) {
          wsCreate(sendMsg);
        } else {
          websocket.send(JSON.stringify(sendMsg));
          console.log('websocket消息发送：', JSON.stringify(sendMsg));
        }

        // 判断当前聊天用户是否在最上面，不是的话移上去
        const receiverIndex = users.findIndex(item => item.objId === payload.receiverId);
        if (receiverIndex > 0) {
          const newUsers = [...users];
          newUsers.splice(receiverIndex, 1);
          yield put({
            type: 'saveUsers',
            payload: [users[receiverIndex], ...newUsers],
          });
        }

        // 更新消息列表
        yield put({
          type: 'refreshMessageList',
          payload: [...messageList, sendMsg],
        });
    },
    *receiveMessage({ payload }, { put, select }) {
      const { newMsg } = payload;
      const { messageList, loginId } = yield select(state => state.message);
      if (newMsg && `${newMsg.receiverId}` === `${loginId}` && newMsg.messageType !== 'read') {
        yield put({
          type: 'refreshMessageList',
          payload: [...messageList, newMsg],
        });
        const { messageText, createDate } = newMsg;
        yield put({ type: 'changeUsers', payload: { messageText, createDate } });

        // 判断当前聊天界面是否置底，是的话加入新消息后再调用一次置底逻辑
        const chatItems = document.getElementById('chatItems');
        if (chatItems && (chatItems.scrollTop + chatItems.clientHeight + 80) >= chatItems.scrollHeight) {
          scrollToBottom('bottomElement', 'chatItems');
        }
      }
    },
    // 查询聊天历史记录
    *getHistoryList({ payload }, { call, put, select }) {
      const { pageNum } = payload;
      const { historyList, curUser, searchObj } = yield select(state => state.message);
      try {
        yield put({ type: 'changeLoading', payload: { historyLoading: true } });
        const filterObj = omit(searchObj, 'filterVal');
        const result = yield call(pageChatContent, {
          pageSize: 10,
          pageNum,
          filterVal: searchObj.filterVal || undefined,
          anyProperties: {
            partnerId: Number(curUser.objId),
            ...filterObj,
          },
        });
        if (Array.isArray(result.list)) {
          let noRepeatList = [];
          if (pageNum !== 1) {
            const newsList = historyList.concat(result.list);
            noRepeatList = newsList.filter((list, index) =>
              newsList.findIndex(item => item.instantMessageId === list.instantMessageId) === index);
          } else {
            noRepeatList = result.list;
          }
          yield put({
            type: 'updateHistoryList',
            payload: { historyList: noRepeatList, historyPageNum: pageNum, hasMoreHistory: result.list.length > 0 },
          });
        }
        yield put({ type: 'changeLoading', payload: { historyLoading: false } });
      } catch (error) {
        message.error(error);
      }
    },
    // 查询聊天历史记录
    *changeSearch({ payload }, { put }) {
      yield put({
        type: 'updateSearchObj',
        payload,
      });
      yield put({
        type: 'getHistoryList',
        payload: { pageNum: 1 },
      });
    },
  },

  reducers: {
    resetState() {
      return initState;
    },
    saveLogin(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    saveSocket(state, { payload: { websocket } }) {
      return {
        ...state,
        websocket,
      };
    },
    changeLoading(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    saveUsers(state, { payload }) {
      return {
        ...state,
        users: payload,
      };
    },
    saveSearchUsers(state, { payload }) {
      return {
        ...state,
        searchUsers: payload,
      };
    },
    updateUsers(state, { payload }) {
      return {
        ...state,
        users: payload,
      };
    },
    changeInputValue(state, { payload }) {
      return {
        ...state,
        inputValue: payload,
      };
    },
    // 更新当前聊天列表
    updateMessageList(state, { payload: { messageList = [], pageNum, hasMoreMessage } }) {
      return {
        ...state,
        messageList,
        pageNum,
        hasMoreMessage,
      };
    },
    // 刷新聊天列表
    refreshMessageList(state, { payload }) {
      return { ...state, messageList: payload };
    },
    saveVisible(state, { payload }) {
      return {
        ...state,
        visible: payload,
      };
    },
    saveCurUser(state, { payload }) {
      return {
        ...state,
        curUser: payload,
      };
    },
    changeSendStatus(state, { payload }) {
      return {
        ...state,
        sendStatus: payload,
      };
    },
    addUser(state, { payload }) {
      return {
        ...state,
        users: payload,
      };
    },
    saveRecordsVisible(state, { payload }) {
      return {
        ...state,
        historyVisible: payload,
      };
    },
    // 更新当前聊天历史记录列表
    updateHistoryList(state, { payload: { historyList = [], historyPageNum, hasMoreHistory } }) {
      return {
        ...state,
        historyList,
        historyPageNum,
        hasMoreHistory,
      };
    },
    updateSearchObj(state, { payload }) {
      return {
        ...state,
        searchObj: payload,
      };
    },
  },
};
