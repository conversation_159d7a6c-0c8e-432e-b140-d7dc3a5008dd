@import './variables.less';

:global {
  .setting-drawer {
    position: relative;
    width: @setting-drawer-width;
    overflow-y: auto;
    background-color: #fff;
    border-left: 1px solid #e8e8e8;

    .clearfixAfter {
      display: block;
      clear: both;
      height: 0;
      visibility: hidden;
      content: '.';
    }
    .clearfix {
      display: block;
      zoom: 1;
    }

    .ant-list-item {
      span {
        flex: 1;
      }
    }
  }
  .setting-drawer-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    display: block;
    width: 56px;
    height: 56px;
    padding: 0;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 700;
    font-size: 16px;
    font-style: normal;
    line-height: 56px;
    text-align: center;
    text-transform: none;
    text-decoration: none;
    background: transparent;
    border: 0;
    outline: 0;
    cursor: pointer;
    transition: color 0.3s;
    text-rendering: auto;
    &:hover {
      color: rgba(0, 0, 0, 0.75);
    }
  }
}

.content {
  position: relative;
  padding: @slick-space-base * 2;
}

.productionHint {
  margin-bottom: 24px;
  :global(.ant-alert-message) {
    color: @text-color-secondary;
  }
}

.circle {
  font-size: 16px !important;
  line-height: 22px;
  border-color: transparent;
}
