/* eslint-disable no-console */
import React from 'react';
import { notification } from 'antd';
import { scrollToBottom } from '@/utils/scroller';
import { getItem } from '@/utils/utils';
import { openPrivateChat, createNewGroupChat, checkUseOSSPage } from '@/services/instantMessage';

/**
 * 打开聊天窗口，传入聊天对象userObj，没传则默认聊天对象为用户列表第一个
 * @param {*} chatObj chatObj中type为公共属性，区分是私聊还是群聊；
 * 私聊属性值userId、userCode、userName为必须值，群组时属性值groupId、groupName、members
 */
export async function openWebSocket(chatObj) {
  const { type, groupId } = chatObj;
  const { userCode: curUserCode } = getItem('user')?.userInfo;
  const { message, login } = window.g_app._store.getState();
  const { dispatch } = window.g_app._store;

  let result;
  if (!login.isOSSPage) {
    const { userId, userName } = chatObj;
    let chatId = '';
    const userIndex = message?.users.findIndex(user => `${user.objId}` === `${userId}`);
    if (userIndex !== -1) {
      chatId = message.users[userIndex].objId;
      dispatch({
        type: 'message/changeCurUser',
        payload: message.users[userIndex],
      });
    } else {
      chatId = userId;
      const searchUser = {
        objId: userId,
        objName: userName,
      };
      dispatch({
        type: 'message/changeCurUser',
        payload: searchUser,
      });
      dispatch({
        type: 'message/addSearchUser',
        payload: searchUser,
      });
    }
    // 更新聊天框消息
    dispatch({
      type: 'message/getMessageList',
      payload: { chatId, pageNum: 1 },
    });

    // 打开聊天窗口
    dispatch({ type: 'message/saveVisible', payload: true });
    scrollToBottom('bottomElement', 'chatItems');
  } else if (type === 'privateChat') {
    const { userCode } = chatObj;
    result = await openPrivateChat({
      friendName: userCode,
      mineName: curUserCode,
    });

    // 打开聊天窗口
    if (`${result}` === '1') {
      dispatch({ type: 'message/saveVisible', payload: true });
    }
  } else if (type === 'groupChat') {
    if (groupId) {
      result = groupId;
    } else {
      const { groupName, members } = chatObj;
      result = await createNewGroupChat({
        username: curUserCode,
        isPermitOut: 'Y',
        members,
        groupName,
      });
    }

    if (`${result}` !== '0') {
      // 打开聊天窗口
      dispatch({ type: 'message/saveVisible', payload: true });

      // 群组定位跳转
      const imchatIframe = document.getElementById('iframe_imchat').contentWindow;
      imchatIframe.postMessage({
        groupId: result,
      }, '*');
    }
  }
}

// 重新连接websocker(WebSocket连接地址)
export function wsRecontent() {
  // 延迟避免请求过多
  setTimeout(() => {
    // eslint-disable-next-line no-use-before-define
    wsCreate();
  }, 2000);
}

// WebSocket心跳检测
// const wsHeartCheck = {
//   timeout: 5000, // 5秒一次心跳
//   timeoutObj: null, // 执行心跳的定时器
//   serverTimeoutObj: null, // 服务器超时定时器
//   reset() { // 重置方法
//       clearTimeout(this.timeoutObj);
//       clearTimeout(this.serverTimeoutObj);
//       return this;
//   },
//   start(ws) { // 启动方法
//       const self = this;
//       const { userId, userName } = getItem('user')?.userInfo;
//       this.timeoutObj = setTimeout(() => {
//       // 这里发送一个心跳信息，后端收到后，返回一个消息，在onmessage拿到返回的心跳（信息）就说明连接正常
//       ws.send({
//         sendId: userId,
//         sendName: userName,
//         messageText: 'check',
//       });
//       // 如果超过一定时间还没重置，说明后端主动断开了
//       self.serverTimeoutObj = setTimeout(() => {
//         // 如果onclose会执行reconnect，我们执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
//         wsRecontent();
//       }, self.timeout);
//     }, this.timeout);
//   },
// };

// WebSocket 事件创建
function wsEvent(ws, url, msg) {
  ws.onopen = () => {
    // 心跳检测重置
    // wsHeartCheck.reset().start(ws);
    console.log('WebSocket已连接');
    // 保存websocket实例
    window.g_app._store.dispatch({
      type: 'message/init',
      payload: { websocket: ws },
    });
    // 重新连接后，补发消息
    if (msg) {
      ws.send(JSON.stringify(msg));
      console.log('websocket消息发送：', JSON.stringify(msg));
    }
  };

  ws.onclose = event => {
    // 重新连接WebSocket
    // ws_recontent(url);
    console.log('WebSocket连接已关闭', event);
    // 登出重置即时通讯数据
    window.g_app._store.dispatch({
      type: 'message/resetState',
    });
  };

  ws.onerror = event => {
    // 重新连接WebSocket
    wsRecontent(url);
    console.log('WebSocket错误：', event);
  };

  ws.onmessage = event => {
    // 只要有数据，那就说明连接正常
    // wsHeartCheck.reset().start(ws);

    // 处理数据，只处理非心跳检测的数据!== 'check'
    if (event.data) {
      const result = JSON.parse(event.data);
      const { message } = window.g_app._store.getState();
      const { dispatch } = window.g_app._store;
      if (result.success) {
        const data = result.resultObject;
        if (data.uuid) {
          const newList = message?.messageList || [];
          const msgIndex = newList.findIndex(item => item.uuid === data.uuid);
          newList.splice(msgIndex, 1, { ...data, sendStatus: 1 });

          console.log('websocket接收消息发送结果：', event);

          // 更新消息列表
          dispatch({
            type: 'message/refreshMessageList',
            payload: newList,
          });
          const { messageText, createDate } = data;
          // 更新用户列表消息
          dispatch({
            type: 'message/changeUsers',
            payload: { messageText, createDate },
          });
        } else {
          console.log('websocket新消息接收：', event);

          // 将新消息插入消息列表
          dispatch({
            type: 'message/receiveMessage',
            payload: { newMsg: { ...data, sendStatus: 1 } },
          });
          // 聊天窗口没有打开的情况下，接收到新消息在右下角弹窗
          if (!message?.visible) {
            const { instantMessageId, sendId, sendName, messageText } = data;
            notification.open({
              key: instantMessageId,
              message: `${sendName}发来一条新消息`,
              placement: 'bottomRight',
              description: (
                <div>
                  <div>{messageText}</div>
                  <div style={{ marginTop: 4 }}>
                    <div style={{ float: 'right' }}>
                      <a
                        style={{ marginRight: 8 }}
                        onClick={() => {
                          // 打开聊天窗口
                          openWebSocket({
                            userId: sendId,
                            userName: sendName,
                          });
                          notification.close(instantMessageId);
                        }}
                      >
                        查看详情
                      </a>
                    </div>
                  </div>
                </div>
              ),
            });
          }
        }
      } else {
        console.log('websocket接收失败结果：', event);
      }
    }
  };
}
// 创建WebSocket
export async function wsCreate(msg) {
  const isOSSPage = await checkUseOSSPage();
  if (isOSSPage !== '1') {
    // WebSocket连接地址
    const serverIP = window.location.hostname;
    const serverPort = window.location.port;
    const wsUrl = `ws:${serverIP}:${serverPort}/portal-react/websocket/server`;
    let websocket = null;
    try {
        // 判断是否支持 WebSocket
        if (typeof (WebSocket) === 'undefined') {
          console.log('您的浏览器不支持WebSocket');
        } else {
          console.log('您的浏览器支持WebSocket');
          // 连接WebSocket
          websocket = new WebSocket(wsUrl);
        }
        wsEvent(websocket, wsUrl, msg);
    } catch (e) {
      // 重新连接WebSocket
      wsRecontent();
      console.log(e);
    }
  }
}

// 关闭连接
export function wsClose(ws) {
  if (ws) {
    ws.close();
  }
}
