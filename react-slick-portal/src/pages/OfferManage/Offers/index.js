import React from 'react';
import dynamic from 'umi/dynamic';
import StepWizard from '@/components/StepWizard';
import PageLoading from '@/components/PageLoading';


const Offers = () => {

  const List = dynamic({
    loader: () => import(`./components/List`),
    loading: PageLoading,
  });

  const Detail = dynamic({
    loader: () => import(`./components/Detail`),
    loading: PageLoading,
  });

  const Edit = dynamic({
    loader: () => import(`./components/Edit`),
    loading: PageLoading,
  });

  return (
    <StepWizard isLazyMount initialStep={1}>
      <List />
      <Detail destroy />
      <Edit destroy />
    </StepWizard>
  )
}

export default Offers;
