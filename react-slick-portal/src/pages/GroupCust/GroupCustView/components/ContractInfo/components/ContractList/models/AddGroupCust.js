import { produce } from 'immer';

export default {
  namespace: 'AddGroupCust',

  state: {
    groupCertList: [],
    groupCustTypeList: [],
    methodRegistry: {}, // 方法注册表结构：{ componentName: { methodName: Function } ,args:{} }
    currentForm: {
      SERV_LEVEL: '4',
      GROUP_BUSI_LICENCE_TYPE: '200010',
      GROUP_HALF_LEVEL: '9',
      AGENT_LICENCE_TYPE: '100001',
      OSS_PROVINCE_NAME: '内蒙古',
    },
    currentFormFieldState: {
      buIndustrySub: { disabled: true },
      REGMANADEPARTMENT: { disabled: true },
    }, // {fieldName: String, disabled: Boolean, visible: Boolean}
  },

  effects: {
    *syncFormField({ payload }, { put }) {
      const { fieldName, value } = payload;
      const [prefix, actualFieldName] = fieldName.split('.');

      yield put({
        type: 'callComponentMethod',
        payload: {
          componentName: prefix,
          methodName: 'updateFormField',
          args: {
            fieldName: actualFieldName,
            value,
          },
        },

      });
    },
    // 调用组件方法
    *callComponentMethod({ payload }, { select }) {
      const { componentName, methodName, args } = payload;
      const { methodRegistry } = yield select(state => state.AddGroupCust);

      const method = methodRegistry[componentName]?.[methodName];
      if (typeof method === 'function') {
        return yield method(args); // 支持异步方法
      }
      // eslint-disable-next-line no-console
      console.warn(`未找到注册的方法: ${componentName}.${methodName}`);
      return null;
    },

    /**
     * 统一更新表单字段
     * @param {Object} payload - 参数对象
     * @param {string|Array} payload.fieldName - 字段名称，支持点号分隔的字符串(如'person.name')或数组路径(如['person','name'])
     *                                          当需要更新数组中的元素时，可以使用数字索引，如'contacts.0.name'或['contacts',0,'name']
     * @param {any} payload.value - 字段值
     * @example
     * // 使用点号表示法更新嵌套字段
     * dispatch({
     *   type: 'AddGroupCust/updateField',
     *   payload: { fieldName: 'person.name', value: '张三' }
     * });
     *
     */
    *updateField({ payload }, { put }) {
      const { fieldName, value, syncFlag } = payload;

      // 处理前缀
      let actualPath;

      if (typeof fieldName === 'string') {
        const parts = fieldName.split('.');
        if (parts.length > 1) {
          // 提取前缀和实际路径
          actualPath = parts.slice(1).map(segment => (/^\d+$/.test(segment) ? parseInt(segment, 10) : segment));
        } else {
          actualPath = [fieldName];
        }
      } else if (Array.isArray(fieldName) && fieldName.length > 1) {
        // 数组形式的路径，假设第一个元素是前缀
        actualPath = fieldName.slice(1).map(segment => (typeof segment === 'string' && /^\d+$/.test(segment) ? parseInt(segment, 10) : segment));
      } else {
        actualPath = fieldName;
      }

      yield put({
        type: 'deepUpdate',
        payload: {
          path: ['currentForm', ...actualPath],
          value,
        },
      });
      if (syncFlag) {
        // 发布字段更新事件
        yield put({
          type: 'syncFormField',
          payload: { fieldName, value },
        });
      }
    },

    /**
     * 批量更新多个表单字段
     * @param {Object} payload - 参数对象
     * @param {Array} payload.fields - 字段数组，每个元素包含fieldName和value
     * @param {string|Array} payload.fields[].fieldName - 字段名称，支持点号分隔的字符串或数组路径
     *                                                   当需要更新数组中的元素时，可以使用数字索引
     * @param {any} payload.fields[].value - 字段值
     * @example
     * // 批量更新多个字段
     * dispatch({
     *   type: 'AddGroupCust/updateFields',
     *   payload: {
     *     fields: [
     *       { fieldName: 'person.name', value: '张三' },
     *       { fieldName: 'person.age', value: 30 },
     *       { fieldName: 'contacts.0.phone', value: '13800138000' },
     *       { fieldName: ['contacts', 1, 'phone'], value: '13900139000' }
     *     ]
     *   }
     * });
     */
    *updateFields({ payload }, { put }) {
      const { fields } = payload;

      // 批量更新字段值
      // eslint-disable-next-line no-unused-vars,no-restricted-syntax
      for (const field of fields) {
        const { fieldName, value, syncFlag } = field;

        // 处理前缀
        let actualPath;

        if (typeof fieldName === 'string') {
          const parts = fieldName.split('.');
          if (parts.length > 1) {
            // 提取前缀和实际路径
            // eslint-disable-next-line prefer-destructuring
            actualPath = parts.slice(1).map(segment => (/^\d+$/.test(segment) ? parseInt(segment, 10) : segment));
          } else {
            actualPath = [fieldName];
          }
        } else if (Array.isArray(fieldName) && fieldName.length > 1) {
          // 数组形式的路径，假设第一个元素是前缀
          actualPath = fieldName.slice(1).map(segment => (typeof segment === 'string' && /^\d+$/.test(segment) ? parseInt(segment, 10) : segment));
        } else {
          actualPath = fieldName;
        }

        yield put({
          type: 'deepUpdate',
          payload: {
            path: ['currentForm', ...actualPath],
            value,
          },
        });

        if (syncFlag) {
          // 发布字段更新事件
          yield put({
            type: 'syncFormField',
            payload: { fieldName, value },
          });
        }
      }
    },

    /**
     * 设置表单字段状态
     * @param {Object} payload - 参数对象
     * @param {string|Array} payload.fieldName - 字段名称，支持点号分隔的字符串或数组路径
     * @param {Object} payload.state - 字段状态对象
     * @param {boolean} [payload.state.disabled] - 是否禁用
     * @param {boolean} [payload.state.visible] - 是否可见
     * @example
     * // 禁用字段
     * dispatch({
     *   type: 'AddGroupCust/setFieldState',
     *   payload: {
     *     fieldName: 'person.name',
     *     state: { disabled: true }
     *   }
     * });
     *
     * // 隐藏字段
     * dispatch({
     *   type: 'AddGroupCust/setFieldState',
     *   payload: {
     *     fieldName: 'contacts.0.phone',
     *     state: { visible: false }
     *   }
     * });
     */
    *setFieldState({ payload }, { put }) {
      const { fieldName, state } = payload;
      // 将点号分隔的字段名转换为路径数组
      const path =
        typeof fieldName === 'string' ? fieldName.split('.').map(segment => (/^\d+$/.test(segment) ? parseInt(segment, 10) : segment)) : fieldName;

      yield put({
        type: 'updateFieldState',
        payload: {
          fieldName: path.join('.'),
          state,
        },
      });
    },

    /**
     * 批量设置多个表单字段状态
     * @param {Object} payload - 参数对象
     * @param {Array} payload.fields - 字段数组，每个元素包含fieldName和state
     * @param {string|Array} payload.fields[].fieldName - 字段名称
     * @param {Object} payload.fields[].state - 字段状态对象
     * @example
     * // 批量设置多个字段状态
     * dispatch({
     *   type: 'AddGroupCust/setFieldsState',
     *   payload: {
     *     fields: [
     *       { fieldName: 'person.name', state: { disabled: true } },
     *       { fieldName: 'person.age', state: { visible: false } },
     *       { fieldName: 'contacts.0.phone', state: { disabled: true, visible: true } }
     *     ]
     *   }
     * });
     */
    *setFieldsState({ payload }, { put }) {
      const { fields } = payload;

      // eslint-disable-next-line no-restricted-syntax,no-unused-vars
      for (const field of fields) {
        const { fieldName, state } = field;
        // 将点号分隔的字段名转换为路径数组
        const path =
          typeof fieldName === 'string' ? fieldName.split('.').map(segment => (/^\d+$/.test(segment) ? parseInt(segment, 10) : segment)) : fieldName;

        yield put({
          type: 'updateFieldState',
          payload: {
            fieldName: path.join('.'),
            state,
          },
        });
      }
    },
  },

  reducers: {
    // 深度更新（使用immer优化）
    deepUpdate: produce((draft, { payload }) => {
      const { path, value } = payload;
      let current = draft;
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < path.length; i++) {
        if (i === path.length - 1) {
          current[path[i]] = value;
        } else {
          if (!current[path[i]]) {
            current[path[i]] = typeof path[i + 1] === 'number' ? [] : {};
          }
          current = current[path[i]];
        }
      }
    }),
    // 更新字段状态
    updateFieldState: produce((draft, { payload }) => {
      const { fieldName, state } = payload;
      if (!draft.currentFormFieldState[fieldName]) {
        draft.currentFormFieldState[fieldName] = {};
      }

      // 更新字段状态
      Object.assign(draft.currentFormFieldState[fieldName], state);
    }),
    // 注册方法
    registerMethods: produce((draft, { payload }) => {
      const { componentName, methods } = payload;
      draft.methodRegistry[componentName] = {
        ...draft.methodRegistry[componentName],
        ...methods,
      };
    }),

    // 注销方法
    unregisterMethods: produce((draft, { payload }) => {
      const { componentName } = payload;
      delete draft.methodRegistry[componentName];
    }),

    // 字段验证状态更新
    validateField: produce((draft, { payload }) => {
      const { path, status, message } = payload;
      const fieldPath = path.join('.');
      draft.validateStatus[fieldPath] = { status, message };
    }),
  },
};
