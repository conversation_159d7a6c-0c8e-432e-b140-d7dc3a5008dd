import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Form, Row, Select, message, Input, Button, Modal } from 'antd';
import { useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import { buildColumns } from '@/utils/bestSolutionUtils';
import { toArray } from '@/utils/utils';
import { queryOutsideSystemRole, addOutsideSystemRole, deleteOutsideSystemRole, getSubSystemInfoList, getUnBindRoles } from '../service';
import styles from '../styles.less';

const { Search } = Input;

const Index = props => {
  const { form, tableData: roleValue } = props ?? {};
  const { getFieldDecorator } = form;
  const [visible, setVisible] = useState(false);
  const [filterVal, setFilterVal] = useState();
  const [subSystemInfoList, setSubSystemInfoList] = useState([]);
  const [subRoleInfoList, setSubRoleInfoList] = useState([]);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [loading, setLoading] = useState(false);


  const { tableProps, search: { submit } } = useAntdTable(
    params => queryOutsideSystemRole({
      ...params,
      parRoleId: roleValue?.sysRoleId,
      filterVal,
    }),
    {
      form,
    }
  );
  const { pagination, ...restTableProps } = tableProps;

  const handleAdd = () => {
    setVisible(true);
  };

  const handleDelete = async record => {
    setLoading(true);
    const res = await deleteOutsideSystemRole({
      parRoleId: record?.parRoleId,
      subRoleId: record?.subRoleId,
      sysRoleSubId: record?.sysRoleSubId,
    });

    if (res === true) {
      message.success('子系统角色删除成功');
      submit();
    } else {
      message.error('子系统角色删除失败');
    }
    setLoading(false);
  };

  const handleModalCancel = () => {
    setSubRoleInfoList([]);
    setVisible(false);
  };

  const handleModalOk = () => {
    if (!roleValue?.sysRoleId) {
      message.error('角色信息错误');
      return;
    }
    form.validateFields(async (err, fieldsValue) => {
      if (err) {
        return;
      }

      setConfirmLoading(true);
      const res = await addOutsideSystemRole({
        parRoleId: roleValue?.sysRoleId,
        subRoleId: fieldsValue?.subRoleId,
        systemInfoId: fieldsValue?.systemInfoId,
        remark: fieldsValue?.remark,
      });
      setConfirmLoading(false);

      if (res) {
        message.success('子系统角色增加成功');
        setVisible(false);
        submit();
      }
    });
  };

  const resetSubRoleInfoList = async value => {
    form.setFieldsValue({
      subRoleId: undefined,
    });

    if (!roleValue?.sysRoleId) {
      message.error('角色信息错误');
      return;
    }
    const list = await getUnBindRoles({
      parRoleId: roleValue?.sysRoleId,
      systemInfoId: value,
    });

    setSubRoleInfoList(list);
  };

  const columns = buildColumns([
    {
      title: '编号',
      dataIndex: 'id',
    },
    {
      title: '系统名称',
      dataIndex: 'sysCodeName',
    },
    {
      title: '系统编码',
      dataIndex: 'sysCode',
    },
    {
      title: '角色编码',
      dataIndex: 'sysRoleCode',
    },
    {
      title: '角色名称',
      dataIndex: 'sysRoleName',
    },
    {
      title: '是否有效',
      dataIndex: 'statusCd',
      textFormat: text => {
        if (text === '1000') {
          return '有效';
        }
        if (text === '1100') {
          return '无效';
        }
        return '';
      },
    },
    {
      title: '操作',
      width: 100,
      fixed: 'right',
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => {
            Modal.confirm({
              title: '是否确定删除该角色信息？',
              onOk: () => {
                handleDelete(record);
              },
            });
          }}
        >
          删除
        </Button>
      ),
    },
  ]);

  const initData = async () => {
    const list = await getSubSystemInfoList();

    setSubSystemInfoList(list);
  };

  useEffect(() => {
    initData();
  }, []);


  useEffect(() => {
    setFilterVal();
    setTimeout(() => {
      submit();
    });
  }, [roleValue]);


  return (
    <div>
      <div className={styles.systemExtra}>
        <div>
          <Search
            style={{ width: '200px' }}
            placeholder="请输入角色编码\角色名称"
            onSearch={submit}
            onChange={e => {
              setFilterVal(e.target.value);
            }}
          />
        </div>
        <div>
          <Button type="primary" onClick={handleAdd}>
            新增
          </Button>
        </div>
      </div>
      <SlickTable
        rowKey={record => record.id}
        columns={columns}
        pick="checkbox"
        {...restTableProps}
        loading={loading || restTableProps?.loading}
        data={{ pagination }}
        scroll={{ x: 'max-content' }}
      />
      {
        visible && (
          <Modal
            title="新增子系统角色"
            visible={visible}
            onOk={handleModalOk}
            onCancel={handleModalCancel}
            width="500px"
            destroyOnClose
            confirmLoading={confirmLoading}
          >
            <Form labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <Row>
                <Form.Item label="系统名称">
                  {getFieldDecorator('systemInfoId', {
                    rules: [
                      {
                        required: true,
                        message: '请选择系统名称',
                      },
                    ],
                  })(
                    <Select
                      style={{ width: '100%' }}
                      allowClear
                      placeholder="请选择"
                      onChange={resetSubRoleInfoList}
                    >
                      {toArray(subSystemInfoList).map(item => (
                        <Select.Option key={item.systemInfoId} value={item.systemInfoId}>
                          {item.systemName}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Row>
              <Row>
                <Form.Item label="角色">
                  {getFieldDecorator('subRoleId', {
                    rules: [
                      {
                        required: true,
                        message: '请选择角色',
                      },
                    ],
                  })(
                    <Select
                      style={{ width: '100%' }}
                      allowClear
                      placeholder="请选择"
                    >
                      {toArray(subRoleInfoList).map(item => (
                        <Select.Option key={item.sysRoleId} value={item.sysRoleId}>
                          {`${item.sysRoleName}(${item.sysRoleCode})`}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Row>
              <Row>
                <Form.Item label="备注">
                  {getFieldDecorator('remark')(
                    <Input.TextArea allowClear placeholder="请输入" />
                  )}
                </Form.Item>
              </Row>
            </Form>
          </Modal>
        )
      }
    </div>
  );
};

export default connect(({ roleManage }) => ({
  tableData: roleManage.tableData,
}))(Form.create()(Index));
