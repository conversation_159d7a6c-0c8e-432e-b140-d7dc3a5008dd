.btn {
  /*position: fixed;
  right: 0;
  bottom: 0;
  z-index: 9;*/
  width: 100%;
  padding: 8px;
  line-height: 100%;
  background: rgb(255, 255, 255);
  border-top: 1px solid rgb(232, 232, 232);
  box-shadow: rgba(0,0,0,0.03) 0 -1px 2px;

  .right {
    float: right;
  }
}
.content {
  position: relative;
}
.warp {
  :global {
    .ant-form-item-control {
      line-height: 24px !important;
    }
  }
}
.textAreaFormItem {
  :global {
    .ant-form-item-control {
      .ant-form-item-children{
        height: 100% !important;
      }
      textarea {
        height: auto;
      }
    }
  }
}
.form-drawer-bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 4px 4px;
}

.rowFlex {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.inputStyle{
  width: 80% !important;
}

