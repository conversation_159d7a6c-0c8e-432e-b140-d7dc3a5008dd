// utils/PubSubHelper.js
import PubSub from 'pubsub-js';

class Publisher {
  constructor(options = {}) {
    this.debounceTime = options.debounce || 0;
    this.debounceTimer = null;
    this.pendingUpdates = [];
  }

  batchUpdate(updates, eventName) {
    if (this.debounceTime > 0) {
      this.pendingUpdates.push(...updates.map(u => ({ ...u, _event: eventName })));
      this._debouncedPublish();
    } else {
      updates.forEach(update => {
        this._publishSingle({
          ...update,
          _event: eventName,
        });
      });
    }
  }

  setValue(field, value) {
    this._publishSingle({
      _event: 'SET_VALUE',
      field,
      value,
    });
  }

  setProps(field, props) {
    this._publishSingle({
      _event: 'SET_PROPS',
      field,
      props,
    });
  }

  publish(eventName, payload) {
    this._publishSingle({
      ...payload,
      _event: eventName,
    });
  }

  destroy() {
    clearTimeout(this.debounceTimer);
    this.pendingUpdates = [];
  }

  // 私有方法：通过 this 访问实例配置
  _debouncedPublish() {
    clearTimeout(this.debounceTimer);
    this.debounceTimer = setTimeout(() => {
      this.pendingUpdates.forEach(update => {
        this._publishSingle(update);
      });
      this.pendingUpdates = [];
    }, this.debounceTime);
  }

  // 私有方法：通过 this 访问日志配置（示例）
  _publishSingle(data) {
    try {
      const { _event, ...publishData } = data;
      PubSub.publish(_event, publishData);
    } catch (error) {
      console.error('PubSub publish failed:', error);
    }
  }
}

export default Publisher;
