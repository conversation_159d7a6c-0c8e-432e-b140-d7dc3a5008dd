/* 在途预受理订单 */
import React from 'react';
import { Button, DatePicker, Form, Input, message, Spin } from 'antd';
import { connect } from 'dva';
import { useAntdTable } from '@umijs/hooks';
import moment from 'moment';
import { getUnfinishPreOrderList } from '../services';
import style from './index.less';
import SlickTable from '@/components/SlickTable';
import { Portal } from '@/utils/utils';

const { RangePicker } = DatePicker;

const Index = ({ form, ...otherProps }) => {
  const { currentCust } = otherProps;
  const { getFieldDecorator, validateFields } = form;

  // 定义获取表格数据的函数
  const getTableData = ({ current, pageSize, filters, sorter, ...rest }) => {
    const params = {
      pageType: 'view', // 360视图固定传
      orderStatus: 'unfiled', // 未归档unfiled，已归档用archived
      pageSize,
      pageNum: current,
      custName: currentCust?.GROUP_NAME || '',
      groupId: currentCust?.GROUP_ID,
      // 默认查询最近7天的数据
      // 合并其他查询参数
      ...rest,
    };
    if (rest.date && rest.date.length === 2) {
      params.createStart = rest.date[0].format('YYYY-MM-DD');
      params.createEnd = rest.date[1].format('YYYY-MM-DD');
      delete params.date; // 删除date字段，避免传递给后端
    } else {
      // 如果没有选择日期范围，则默认查询最近7天的数据
      const today = moment();
      params.createStart = today.subtract(7, 'days').format('YYYY-MM-DD');
      params.createEnd = today.format('YYYY-MM-DD');
    }
    return getUnfinishPreOrderList({
      ...params,
    }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE') {
        return {
          total: resultObject?.total || 0,
          data: resultObject?.records || [],
        };
      }
      message.error(resultMsg);
      return {
        total: 0,
        data: [],
      };
    });
  };

  const { tableProps, search: { submit } } = useAntdTable(
    params =>
      getTableData({
        ...params,
        custId: currentCust?.CUST_ID,
      }),
    [currentCust?.GROUP_ID], // 添加queryParams作为依赖项，确保查询条件变化时刷新
    {
      defaultPageSize: 10,
      form,
    }
  );

  const { pagination, loading, ...restTableProps } = tableProps;

  // 跳转订单透明化
  const gotoProcess = row => {
    Portal.open(`/iframe/DISPATCH_MENU_FLOW_DETAIL?oneId=${row.extValues?.oneId || ''}&extOrderNbr=${row.orderNbr}&sourceFrom=2`);
  };


  const columns = [
    {
      title: '订单号',
      dataIndex: 'orderNbr',
      key: 'orderNbr',
      width: 180,
      render: (text, record) => (
        <a href="#" onClick={() => gotoProcess(record)}>
          {text}
        </a>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createDate',
      key: 'createDate',
      width: 160,
    },
    {
      title: '状态',
      dataIndex: 'orderStatusName',
      key: 'orderStatusName',
      width: 120,
    },
    {
      title: '业务名称',
      dataIndex: 'orderBusiTypeName',
      key: 'orderBusiTypeName',
      width: 150,
    },
    {
      title: '商品名称',
      dataIndex: 'prodName',
      key: 'prodName',
      width: 150,
    },
    {
      title: '当前环节',
      dataIndex: 'flowNodeName',
      key: 'flowNodeName',
      width: 150,
    },
    {
      title: '手机号码',
      dataIndex: 'custManagerTel',
      key: 'custManagerTel',
      width: 120,
    },
    {
      title: '操作员',
      dataIndex: 'handleStaffName',
      key: 'handleStaffName',
      width: 120,
    },
    {
      title: '异常信息',
      dataIndex: 'errorMessage',
      key: 'errorMessage',
      width: 200,
      render: (text, record) => record.errorMessage || record.errMessage || '-',
    },
  ];
  // 处理查询
  const handleQuery = () => {
    validateFields(err => {
      if (!err) {
        submit();
      }
    });
  };
  return (
    <>
      <div className="cute">
        <Form style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', gap: '10px' }}>
          <Form.Item style={{ marginBottom: 0 }}>
            {getFieldDecorator('orderNbr', {
              initialValue: '',
            })(<Input placeholder="订单号" style={{ width: 150 }} />)}
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            {getFieldDecorator('date', {
              initialValue: [moment().subtract(7, 'days'), moment()],
            })(<RangePicker placeholder={['开始日期', '结束日期']} style={{ width: 230 }} format="YYYY-MM-DD" allowClear={false} />)}
          </Form.Item>
          <Button type="primary" onClick={handleQuery}>
            查询
          </Button>
        </Form>
        <div className={style.orderListContainer}>
          <Spin spinning={loading}>
            <SlickTable
              rowKey="ORDER_ID"
              {...restTableProps}
              columns={columns}
              pagination={{
                ...pagination,
                showQuickJumper: true,
                showSizeChanger: true,
                showTotal: total => `共 ${total} 条记录`,
              }}
              scroll={{ x: 1500 }}
            />
          </Spin>
        </div>
      </div>
    </>
  );
};

export default connect(({ setting, groupCustView }) => ({
  size: setting.size,
  currentCust: groupCustView.currentCust,
}))(Form.create()(Index));
