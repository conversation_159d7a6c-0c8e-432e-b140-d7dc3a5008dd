import React from 'react';
import BraftEditor from 'braft-editor';
import uploadFn from '@/utils/uploadFn';
import 'braft-editor/dist/index.css';

export default class _BraftEditor extends React.Component {
  static createEditorState = BraftEditor.createEditorState;

  static use = BraftEditor.use;

  render() {
    return (
      <BraftEditor
        {...this.props}
        media={{
          uploadFn,
        }}
      />
    );
  }
}
