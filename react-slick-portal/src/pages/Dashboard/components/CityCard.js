import React, { useEffect } from 'react';
import { connect } from 'dva';
import { Card, message } from 'antd';
// import imgs from '@/components/NavigateImgs';
import imgs from '@/layouts/YunNanLayout/components/SysImg';
import icon1 from '../img/cityIcon/icon1.png';
// import CommonCard from './CommonCard';
import styles from './cityCard.less';
import request from '@/utils/request';
import { getItem, operActionLog } from '@/utils/utils';

const domId = 'portlet-cityCard';

const Index = ({ systemLinks, dispatch }) => {
  const { sessionId, userInfo } = getItem('user');

  const handleClick = () => {
    // message.info('系统对接中，敬请期待');
  };

  const unit = (img, text, infos) => (
    <div className={styles.unit} style={{ width: 110 }}>
      <div className={styles.icon} onClick={handleClick}>
        <img src={imgs[infos?.iconUrl ?? 'icon1'] ?? imgs.icon1} alt="" />
      </div>
      <div className={styles.text} onClick={handleClick} style={{ color: 'gray' }}>
        {text}
      </div>
    </div>
  );

  useEffect(() => {
    dispatch({
      type: 'dashboard/getSystemLink',
    });
  }, []);

  async function openMenu(sysInfo) {
    operActionLog({
      actionType: '1000',
      actionCode: 'TYMH_MENU_CityCard',
      action: '1000',
      handResult: '',
      actionMsg: '',
      systemInfoId: sysInfo.systemInfoId,
      systemInfoName: sysInfo.systemName,
    });

    let _linkUrl = sysInfo.linkUrl;

    if (_linkUrl === undefined) {
      message.warning('未配置链接');
      return;
    }

    if (sysInfo.systemNbr && sysInfo.systemNbr === 'JHXT') {
      // 稽核系统菜单编码为RWGL_MENU_TASK090，点击稽核系统单点登录要进行字符串拼接处理http://10.174.25.75:8080/cloudaudit/baseserver?eventId=12&auditMenu=unauditedTask&sysUserId=TESTKM06&signTimestamp=20200610144120&signString=a74a52c1b77e440f138eb3623fb935b7
      // 地址写死【eventId：单点登录调用方区分标识】 【auditMenu=unauditedTask】  前端获取【sysUserId:CRM工号】   后端返回【signTimestamp：时间戳   signString：加密字符串】
      if (_linkUrl.indexOf('eventId') === -1) {
        message.warning('链接未配置参数区分标识eventId');
        return;
      }
      if (!userInfo || !userInfo.externalUserInfos) {
        message.warning('无CRM用户信息');
        return;
      }
      let externalUserInfo = userInfo.externalUserInfos.find(item => item.systemNbr === 'CRM' && item.currentFlag === '1000');
      if (!externalUserInfo) {
        externalUserInfo = userInfo.externalUserInfos.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000');
      }
      if (!externalUserInfo) {
        message.warning('无可用CRM用户信息');
        return;
      }

      const jhxtParams = {
        sysUserId: externalUserInfo.externalUserCode,
        eventId: '12',
        datePattern: 'yyyyMMddHHmmss',
        auditMenu: 'unauditedTask',
      };
      const jhxtResponse = await request('orgauth/SystemInfoController/md5Sign.do', {
        method: 'POST',
        data: jhxtParams,
      });
      if (jhxtResponse && jhxtResponse.resultCode === '0') {
        _linkUrl = `${_linkUrl}&sysUserId=${externalUserInfo.externalUserCode}&signTimestamp=${jhxtResponse.signTimestamp}&signString=${jhxtResponse.signString}`;
        window.open(_linkUrl);
        return;
      }
      message.warning('鉴权信息获取失败，跳转失败');
      return;
    }

    // 判断所属系统是否需要单点登录，如需要，则添加单点信息
    const { ssoSystem = '' } = getItem('systemInfo');
    if (ssoSystem) {
      if (ssoSystem.toUpperCase().includes(sysInfo.systemNbr.toUpperCase())) {
        const response = await request('portal/SsoLoginStrategyController/ssoLogin.do', {
          method: 'POST',
          data: {
            systemInfo: sysInfo.systemNbr,
            menuCode: '',
            menuUrl: _linkUrl,
          },
        });
        if (response && response.resultCode === 'TRUE') {
          _linkUrl = response.resultObject;
        }
      }
    }

    // 如果配置的url不带域名，则直接取当前域名
    if (!_linkUrl.startsWith('http')) {
      // 去掉address头部的/
      _linkUrl = _linkUrl.startsWith('/') ? _linkUrl.slice(1) : _linkUrl;

      const { origin } = window.location;
      _linkUrl = `${origin}/${_linkUrl}`;
    }

    if (_linkUrl.indexOf('{SYS_USER_ID}') !== -1) {
      _linkUrl = _linkUrl.replace('{SYS_USER_ID}', userInfo.userCode);
    }
    if (_linkUrl.indexOf('{SYS_ORG_ID}') !== -1) {
      _linkUrl = _linkUrl.replace('{SYS_ORG_ID}', userInfo.userOrgId);
    }

    if (_linkUrl.indexOf('ssoType=1') !== -1) {
      const urlParams = {
        bssSessionId: sessionId,
      };
      // 菜单地址SSO加密，拼接返回的signString和singTimestamp
      const response = await request(`orgauth/SystemInfoController/calcSign.do?systemInfoId=${sysInfo.systemInfoId}`, {
        method: 'POST',
        data: urlParams,
      });

      if (response && response.resultCode === '0') {
        _linkUrl = `${_linkUrl}&signString=${response.signString}&signTimestamp=${response.signTimestamp}&bss3SessionId=${sessionId}`;
      }
    } else {
      const connectFlag = _linkUrl.indexOf('?') > -1 ? '&' : '?';
      _linkUrl = `${_linkUrl}${connectFlag}bss3SessionId=${sessionId}`;
    }

    window.open(_linkUrl);
  }

  return (
    <Card title="业务系统专区" bordered={false}>
      <div id={domId} className={styles.row}>
        {Array.isArray(systemLinks) &&
          systemLinks.map(sysInfo => (
            <a onClick={() => openMenu(sysInfo)} key={sysInfo.systemInfoId} className={styles.quickLink}>
              {unit(icon1, sysInfo.systemName, sysInfo)}
            </a>
          ))}
      </div>
    </Card>
  );
};

export default connect(({ dashboard }) => ({
  systemLinks: dashboard.systemLinks,
}))(Index);
