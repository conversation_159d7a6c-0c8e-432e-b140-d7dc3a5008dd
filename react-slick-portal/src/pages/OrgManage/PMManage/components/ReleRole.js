/* eslint-disable no-plusplus */
/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'dva';
import { Button, Modal, message } from 'antd';
import SlickTable from '@/components/SlickTable';
import useSlickTable from '@/hooks/UseSlickTable';
import request from '@/utils/request';
import AddReleRole from './AddReleRole';
import BatchAssociation from './BatchAssociation';
import styles from '../styles.less';

// 空对象校验
const judgeObj = obj => {
  if (Object.keys(obj).length === 0) return true;
  return false;
};

function ReleRole(props) {
  const { tableData: { sysUserId = '', sysUserCode = '', userName = '', statusCd } } = props;
  const systemEl = useRef();
  const [visible, setVisible] = useState(false);
  const [params, setParams] = useState({
    sysUserId,
    roleSysCode: '',
    filterVal: '',
  });
  const [selectRows, setSelectRows] = useState([]);
  // 初始化变量
  const [variable, setVariable] = useState(false);
  const [associationVisiable, setAssociationVisiable] = useState(false);

  const { tableProps } = useSlickTable(
    { pageSize: 5 },
    'portal/UserAndRoleAndLabelController/queryUserRoleList',
    params
  );

  useEffect(() => {
    if (!judgeObj(props.tableData) && variable) {
      setParams({
        ...params,
        sysUserId,
        gb_reset: true,
      });
      setSelectRows([]);
    }
  }, [props.tableData]);

  useEffect(() => {
    setVariable(true);
  }, []);

  // const onChange = value => {
  //   setParams({
  //     ...params,
  //     roleSysCode: value || '',
  //     gb_reset: true,
  //   });
  // };

  const handleSelectRows = value => {
    setSelectRows(value);
  };
  const deleteSelectRows = param => {
    request('portal/UserAndRoleAndLabelController/delUserAndRole', {
      data: param,
      method: 'POST',
    }).then(res => {
      if (res.resultCode === '200') {
        message.success('删除成功！');
        setParams({
          ...params,
        });
        setSelectRows([]);
      } else {
        message.error(res.result || '删除失败！');
      }
    });
  };

  // 列表删除
  const handleDelete = () => {
    Modal.confirm({
      content: '确认进行删除？',
      onOk() {
        const param = {
          sysUserId,
          sysRoleId: selectRows[0].sysRoleId,
        };
        deleteSelectRows(param);
      },
    });
  };

  const handleAdd = () => {
    if (statusCd === '1100') {
      message.error('用户已失效不能进行相关操作！');
    } else {
      setVisible(true);
    }
  };

  const handleOk = () => {
    systemEl.current.handleAdd().then(res => {
      if (res === true) {
        setVisible(false);
        setParams({
          ...params,
          gb_reset: true,
        });
      }
    });
  };

  const handleCancel = () => {
    setVisible(false);
  };

  // const importIdContent = (
  //   <div style={{ width: 400 }}>
  //     <SlickUpload action="orgauth/SystemUserRoleController/batchConfigByFile.do" />
  //   </div>
  // );

  const columns = [
    {
      title: '角色',
      dataIndex: 'systemRoles.sysRoleName',
    },
    {
      title: '角色编码',
      dataIndex: 'systemRoles.sysRoleCode',
    },
    {
      title: '角色类型',
      dataIndex: 'systemRoles.sysRoleTypeName',
    },
    {
      title: '归属系统',
      dataIndex: 'systemRoles.sysCodeName',
    },
    {
      title: '授权管理分类',
      dataIndex: 'manageClassName',
    },
    {
      title: '授权操作类型',
      dataIndex: 'operTypeName',
      width: '100px',
    },
    {
      title: '到期时间',
      dataIndex: 'expDate',
      render: text => text.substring(0, 10) >= '2120-01-01' ? '永久' : text.substring(0, 10),
    },
    {
      title: '状态',
      dataIndex: 'statusCdName',
      width: '100px',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
  ];

  return (
    <div>
      <div className={styles.releRoleExtra}>
        <div>
          <Button type="primary" onClick={handleAdd} className="margin-left">
            新增
          </Button>
        </div>
      </div>
      <SlickTable
        rowKey={record => record.id}
        columns={columns}
        pick="radio"
        {...tableProps}
        extra={
          selectRows.length > 0 ? (
            <div>
              <Button type="danger" ghost onClick={handleDelete}>
                删除
              </Button>
            </div>
          ) : null
        }
        onSelectRow={handleSelectRows}
        selectedRows={selectRows}
      />
      <Modal
        title="新增关联角色"
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width="700px"
        destroyOnClose
      >
        <AddReleRole ref={systemEl} cRef={systemEl} sysUserId={sysUserId} />
      </Modal>
      <Modal
        title={`${userName}（${sysUserCode}）批量关联角色`}
        visible={associationVisiable}
        width={props.size.width * 0.5}
        bodyStyle={{ padding: 0 }}
        onCancel={() => setAssociationVisiable(false)}
        footer={null}
        maskClosable={false}
        destroyOnClose
      >
        <BatchAssociation setAssociationVisiable={setAssociationVisiable} params={params} setParams={setParams} curUser={props.tableData} />
      </Modal>
    </div>
  );
}

export default connect(({ accountManage, setting }) => ({
  tableData: accountManage.tableData,
  size: setting.size,
}))(ReleRole);
