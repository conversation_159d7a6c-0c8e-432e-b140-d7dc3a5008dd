@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

  // .universalContainer {
  //   display: flex;
  //   margin: 8px 0;
  //   cursor: default;

    .groups {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      transition: all 0.8s ease-in-out;
      padding: 12px 8px 2px 20px;
      height: 200px;
      overflow: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;
      &::-webkit-scrollbar {
        display: none;
      }

      .groupItem {
        opacity: 0;
        transform: translateY(-50px);
        animation: fadeIn 0.7s ease-in normal forwards; // 应用动画
        padding: 12px 16px;
        background: #E6F3FF;
        border-radius: 4px;
        flex: 1 1 auto;
        margin-right: 12px;
        margin-bottom: 12px;

        .groupName {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          font-weight: 500;
          font-size: 14px;
          color: #333;
          img{
            width: 24px;
            height: 24px;
            margin-right: 8px;
          }
        }

        .menuList{
          display: flex;
          align-items: center;
          .menuItem {
            padding: 2px 12px;
            margin-right: 8px;
            font-weight: 400;
            font-size: 13px;
            color: #0067AB;
            line-height: 22px;
            background: #FFFFFF;
            border-radius: 99px;
            // width: 90%;
            // overflow: hidden;
            // white-space: nowrap;
            // text-overflow: ellipsis;
            cursor: pointer;
            &:last-child{
              margin-right: 0;
            }
          }
        }
      }

      .triangle {
        align-self: center;
        width: 0;
        height: 0;
        border-top: 12px solid transparent;
        border-right: 1px solid transparent;
        border-left: 8px solid orangered;
        border-bottom: 12px solid transparent
      }
    }

    .pageBtns {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;

      .btn {
        width: 20px;
        height: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #F5F8FE;
      }
    }
  // }

  .sceneContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 12px 12px;
    background: #F9FAFB;

    .sceneTitle {
      margin-right: 20px;
      font-weight: 500;
      font-size: 16px;
      color: #4D44C0;
      line-height: 24px;
      width: 40px;
    }

    .sceneItem {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 8px;
      font-weight: 400;
      font-size: 14px;
      color: #333;
      line-height: 22px;
      cursor: pointer;

      img {
        margin-bottom: 4px;
      }

      &:last-child {
        margin-right: 0;
      }

      .menuName {
        width: 90%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 8px;
      }
    }
  }

.iconImg {
  pointer-events: none;
}

.row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
}

.unit {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
  cursor: pointer;

  .icon {
    margin-bottom: 4px;

    &,
    img {
      width: 32px;
      height: 32px;
    }
  }

  .text {
    width: calc(100% - 8px);
    margin-left: 4px;
    overflow: hidden;
    font-weight: 400;
    font-size: 14px;
    color: #333;
    line-height: 22px;
    white-space: nowrap;
    text-align: center;
    text-overflow: ellipsis;
  }
}

.listMore>* {
  transform: rotate(90deg);
  cursor: pointer;
}
