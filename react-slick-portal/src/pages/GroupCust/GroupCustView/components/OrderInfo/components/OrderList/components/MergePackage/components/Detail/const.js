import dynamic from 'umi/dynamic';
import PageLoading from '@/components/PageLoading';

export const tabs = [
  {
    name: '基本信息',
    key: 'BaseIno',
    component: dynamic({
      loader: () => import('./components/BaseInfo'),
      loading: PageLoading,
    }),
  },
  {
    name: '账户信息',
    key: 'AccountInfo',
    component: dynamic({
      loader: () => import('./components/AccountInfo'),
      loading: PageLoading,
    }),
  },
  {
    name: '资费信息',
    key: 'TariffInfo',
    component: dynamic({
      loader: () => import('./components/TariffInfo'),
      loading: PageLoading,
    }),
  },
  {
    name: '合同信息',
    key: 'Contract',
    component: dynamic({
      loader: () => import('./components/Contract'),
      loading: PageLoading,
    }),
  },

  /* {
    name: '工单流转轨迹',
    key: 'OrderFlow',
    component: dynamic({
      loader: () => import('./components/BaseInfo'),
      loading: PageLoading,
    }),
  },
  {
    name: '产品成员信息',
    key: 'ProductMember',
    component: dynamic({
      loader: () => import('./components/BaseInfo'),
      loading: PageLoading,
    }),
  },
  {
    name: '云MAS外省成员',
    key: 'YunMasInfo',
    component: dynamic({
      loader: () => import('./components/BaseInfo'),
      loading: PageLoading,
    }),
  }, */
];
