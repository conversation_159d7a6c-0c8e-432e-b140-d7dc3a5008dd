import { qrySysBannerImgs } from '@/services/banner';

// 轮播图
export default {
  namespace: 'banner',

  state: {
    bannerList: [],
  },

  effects: {
    *getBannerList(_, { call, put }) {
      const res = yield call(qrySysBannerImgs, 1000);
      if(res && Array.isArray(res.resultObject)) {
        yield put({
          type: 'save',
          payload: { bannerList: res.resultObject },
        });
      }
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },

}
