import { message } from 'antd';
import request from '@/utils/request';

// 获取表格数据
export async function getMessageList(params) {
  return request('portal/ConMessageController/qryConMessageGridData.do', {
    data: params,
  })
};

// 获取获类型下拉框
export async function queryGoodsType(params) {
  return request('portal/DomainDataController/getValuesList.do', {
    data: params,
  })
};

// 详情
export async function getMessageDetails(messageId) {
  return request(`portal/ConMessageController/qryConMessageById.do`, {
    method: 'get',
    data: messageId,
  });
}

// 编辑
export async function updateMessage(params) {
  return request('portal/ConMessageController/updateConMessage.do', {
    data: params,
  })
}

// 新增
export async function addMessage(params) {
  return request('portal/ConMessageController/createConMessage.do', {
    data: params,
  })
}

// 发布
export async function publishMessage(params) {
  return request('portal/ConMessageController/releaseConMessage.do', {
    data: params,
  })
}

// 下线
export async function depublishMessage(params) {
  return request('portal/ConMessageController/offlineConMessage.do', {
    data: params,
  })
}

// 置顶和取消置顶
export async function toppingMessage(params) {
  return request('portal/ConMessageController/updateConMessage.do', {
    data: params,
  })
}

// 删除
export async function deleteMessage(params) {
  return request('portal/ConMessageController/deleteConMessage.do', {
    data: params,
  })
}
/**
 * 操作全部消息
 * @param {操作类型} type read 全部已读  del全部删除
 */
export async function modifyAllMessage(type) {
  let res = {};
  if (type === 'read') {
    res = await request('portal/ConMessageUsersController/makeAsReadAll.do', { method: 'get' });
  } else {
    res = await request('portal/ConMessageUsersController/deleteAllConMessageUsers.do', { method: 'get' });
  }
  if (!res.error) {
    message.success('操作成功');
    return true;
  }
  return false;
}

/**
 * 消息分类统计接口
 * @returns
 */
export async function countMessageType() {
  return request('portal/ConMessageUsersController/countMessageType.do', {
    method: 'get',
  });
}

/**
 * 消息列表分页查询接口
 * @param {*} params
 * @returns
 */
export async function qryConMessageUsersGridData(params) {
  return request('portal/ConMessageUsersController/qryConMessageUsersGridData.do', {
    method: 'post',
    data: params,
  });
}

/**
 * 消息操作
 * @param {操作类型} type read 进行已读操作  del 删除
 * @param {*} messageIds 消息id数组
 */
export async function modifyMessage(type = 'read', messageIds = []) {
  if (Array.isArray(messageIds) && messageIds.length > 0) {
    let res = {};
    if (type === 'read') {
      // 已读操作
      res = await request('portal/ConMessageUsersController/makeAsRead.do', { method: 'post', data: messageIds });
    } else {
      res = await request('portal/ConMessageUsersController/deleteConMessageUsers.do', { method: 'post', data: messageIds });
    }
    if (res.success) {
      message.success('操作成功');
      return true;
    }
  } else {
    message.error('请选择操作的信息');
  }
  return false;
}

/**
 * 消息详情接口
 * @param {*} messageId 消息id
 * @returns
 */
export async function qryConMessageUsersById(messageId) {
  return request('portal/ConMessageUsersController/qryConMessageUsersById.do', {
    method: 'get',
    data: {
      messageId,
    },
  })
}


export const ImageUploadAddress = 'portal/FileStoreController/upload.do';
