import request from '@/utils/request';
import { getItem } from '@/utils/utils';

export async function getCurrentProvince() {
  return request('portal/DataDictController/getValueByCode.do', {
    method: 'get',
    data: {
      groupCode: 'AppGlobal',
      paramCode: 'currentProvince',
      defValue: 'SC',
    },
  });
}

export async function qryTasksNumbers() {
  const { sessionId } = getItem('user');
  return request('orgauth/WorkbenchController/qryTasksNumbers.do', {
    method: 'get',
    data: { tokenId: sessionId },
  });
}

/**
 * 获取自动刷新时间
 * 返回一个字符串数字【单位秒】，如："120"
 * @param {object} defValue 置默认值 单位毫秒
 */
export async function getTodoRefreshTime(defValue) {
  return request('portal/DataDictController/getValueByCode.do', {
    method: 'get',
    data: { groupCode: 'REFRESH_TIME', paramCode: 'TODO_REFRESH_TIME', ...defValue },
  });
}

/**
 * 获取每个小模块的id
 */
export async function getDataDictByGroup() {
  return request('portal/DataDictController/getDataDictByGroup.do', {
    method: 'get',
    data: { groupCode: 'MENU_GROUP_CODE' },
  });
}

// 获取待办任务列表
export async function qryWaitTasks(menuCodeList) {
  return request('portal/LocalWorkbenchController/qryTasksNumbers.do', {
    method: 'post',
    data: {
      menuCodeList,
    },
  }).then(res => res?.resultObject?.[0]?.taskInfos);
}
