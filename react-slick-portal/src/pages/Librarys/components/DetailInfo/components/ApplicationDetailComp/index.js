import React, { useEffect, useState } from 'react';
import { Button, Modal, Table, Form, Switch, Input } from 'antd';
import style from './ApplicationDetailComp.module.less';
import { ImageUploader } from '@/components/Uploader';

/**
 * 应用场景组件
 * @param {props} props
 */
const ApplicationDetailComp = props => {
  const { knowledgeDetailInfo } = props;
  const [showModal, setShowModal] = useState(false);

  const [applicationList, setApplicationList] = useState([]);

  const [title, setTitle] = useState('');
  const [desc, setDesc] = useState('');

  const [isChecked, setIsChecked] = useState(false);
  const [imgList, setImgList] = useState([]);

  useEffect(() => {
    const rootStyle = document.getElementById('root').style;
    if (showModal) {
      rootStyle.overflow = 'hidden';
      rootStyle.position = 'fixed';
    } else {
      rootStyle.overflowY = 'auto';
      rootStyle.position = '';
    }
  }, [showModal]);

  const columns = [
    {
      title: '标题',
      width: '40%',
      dataIndex: 'title',
      ellipsis: true,
    },
    {
      title: '描述',
      width: '50%',
      dataIndex: 'desc',
      ellipsis: true,
    },
    {
      title: '操作',
      width: '10%',
      render(v, r) {
        const arr = [];
        arr.push(
          <Button
            type="link"
            style={{ padding: '0' }}
            onClick={() => {
              setTitle(r.title);
              setDesc(r.desc);
              setImgList(r.imgList);
              setShowModal(true);
            }}
          >
            详情
          </Button>
        );
        return arr;
      },
    },
  ];

  useEffect(() => {
    const sceneDisplay = knowledgeDetailInfo?.sceneGroupRel?.sceneDisplayRecordVos || [];
    const _showFlag = knowledgeDetailInfo?.sceneGroupRel?.showFlag;
    setIsChecked(_showFlag === '1');
    setApplicationList(
      sceneDisplay.map(item => ({
        id: new Date(),
        title: item.title,
        desc: item.desc,
        imgList: item.docFile
          ? [item.docFile].map(_item => ({
            ..._item,
            name: _item.docName,
            uid: _item.id,
            url: _item.docLink,
            type: 'image/png',
            response: [
              {
                docNbr: _item.docNbr,
                docId: _item.docId,
              },
            ],
          }))
          : [],
      }))
    );
  }, [knowledgeDetailInfo]);

  return (
    <>
      <div>
        <span style={{ fontSize: '13px', marginRight: '8px' }}>应用场景</span>
        <Switch checked={isChecked} disabled />
      </div>
      <Table bordered dataSource={applicationList} rowKey={(r, idx) => `${idx}`} columns={columns} pagination={false} />
      <Modal
        title="应用场景详情"
        visible={showModal}
        destroyOnClose
        onCancel={() => {
          setShowModal(false);
          setTitle('');
          setDesc('');
          setImgList([]);
        }}
        width={800}
        footer={null}
        className={style.modalWrap}
        onOk={() => {
          setShowModal(false);
        }}
      >
        <Form labelCol={{ span: 2 }} wrapperCol={{ span: 22 }}>
          <Form.Item label="标题" required>
            <Input value={title} disabled onChange={e => setTitle(e.target.value)} />
          </Form.Item>
          <Form.Item label="图片">
            <ImageUploader
              uploadUrl="portal/FileStoreController/uploadFile.do"
              title="上传图片"
              maximum={1}
              uploadedFileList={imgList}
              disabled
              readonly
              // dimension={[1920, 535]}
              accept={['jpg', 'png']}
            />
          </Form.Item>
          <Form.Item label="描述" required>
            <Input.TextArea value={desc} disabled onChange={e => setDesc(e.target.value)} rows={4} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ApplicationDetailComp;
