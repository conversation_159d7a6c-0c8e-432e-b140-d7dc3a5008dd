import React, { useState, useEffect } from 'react';
import { Descriptions, Tooltip } from 'antd';
import { connect } from 'dva';
import { renderDescItem } from '../const';
import WangEditor from '@/components/WangEditor';
import useAttrs from '@/pages/Librarys/useAttrs';
import { defaultToolbarConfig, defaultEditorConfig } from '@/pages/Librarys/const';
import styles from '../DetailInfo.module.less';

const fields = [
  {
    label: '经验标题',
    key: 'knowledgeName',
    span: 2,
  },
  {
    label: '经验编码',
    key: 'knowledgeNbr',
    optionKey: '',
  },
  {
    label: '归属地市',
    key: 'regionName',
  },
  {
    label: '归属区县',
    key: 'areaName',
  },
  {
    label: '经验分类',
    key: 'knowledgeTypeName',
  },
  {
    label: '发布渠道',
    key: 'releaseChannelName',
  },
  {
    label: '经验维护人姓名',
    key: 'createStaffName',
  },
  {
    label: '经验维护人电话',
    key: 'defandTel',
  },
  {
    label: '经验维护人邮箱',
    key: 'defandMail',
  },
  {
    label: '生效时间(上架)',
    key: 'shelfTime',
  },
  {
    label: '失效时间(下架)',
    key: 'shelvesTime',
  },
  {
    label: '所属产品',
    key: 'prodTypeName',
    span: 3,
  },
  {
    label: '经验关键词(标签)',
    key: 'knowledgeKeyword',
    span: 3,
  },
  {
    label: '经验描述',
    key: 'knowledgeDesc',
    span: 3,
  },
];

/**
 * 经验库基本信息组件
 * @param {props}} props
 * @returns
 */
const ExperienceLibDetailComp = props => {
  const { detail, colFields } = props;
  const attrs = useAttrs();
  // 富文本编辑
  const [editor, setEditor] = useState(null);

  useEffect(() => {
    if (editor) {
      const text = renderDescItem({ key: 'detail', detail, colFields, attrs });
      editor.setHtml(text);
    }
  }, [detail]);

  useEffect(() => () => {
    if (editor == null) return;
    editor.destroy();
    setEditor(null);
  }, [editor]);

  return (
    <div>
      <Descriptions>
        {fields?.map(row => {
          const text = renderDescItem({ ...row, detail, colFields, attrs });
          return (
            <Descriptions.Item label={row.label} span={row.span} key={row.key}>
              <Tooltip title={text} placement="bottomLeft" mouseEnterDelay={1}>
                <div className={`${row.key === 'knowledgeName' ? styles.description : null}`}>{text}</div>
              </Tooltip>
            </Descriptions.Item>
          );
        })}
        <Descriptions.Item label="经验详情" span={3} key="detail">
          <WangEditor
            editor={editor}
            setEditor={setEditor}
            editorConfig={{
              ...defaultEditorConfig,
              readOnly: 'true',
            }}
            toolbarConfig={defaultToolbarConfig}
            editorStyles={{ overflowY: 'hidden', height: '300px', width: '100%' }}
          />
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default connect(({ librarys }) => ({
  ...librarys,
}))(ExperienceLibDetailComp);
