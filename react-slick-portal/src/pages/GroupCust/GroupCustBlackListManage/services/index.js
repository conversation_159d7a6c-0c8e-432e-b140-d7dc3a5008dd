import request from '@/utils/request';

// 查询黑名单
export function queryBalckListInfo(data) {
  return request('portal/GroupBlackListController/queryBalckListInfo.do', {
    method: 'POST',
    data,
  });
}

// 删除黑名单
export function deleteBlackListInfo(data) {
  return request('portal/GroupBlackListController/deleteBlackListInfo.do', {
    method: 'POST',
    data,
  });
}

/* 新增黑名单 */
export function addBlackListInfo(data) {
  return request('portal/GroupBlackListController/addBlackListInfo.do', {
    method: 'post',
    data,
  });
}

// 查询立户风险详情
export function querySettingUpRiskInfo(data) {
  return request('portal/GroupBlackListController/querySettingUpRiskInfo.do', {
    method: 'POST',
    data,
  });
}
