import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Col, Row, Button, Table, Modal, message, InputNumber, Spin } from 'antd';
import { queryCacheByKeyType, delByKeyType, createByKeyType } from '@/services/cacheConfig';
import DetailModal from './detailModal';

const { Item } = Form;
const { TextArea } = Input;
const dataTypeList = [
  {
    value: 'string',
    name: 'string',
  },
  {
    value: 'hash',
    name: 'hash',
  },
  {
    value: 'list',
    name: 'list',
  },
  {
    value: 'set',
    name: 'set',
  },
  {
    value: 'zset',
    name: 'zset',
  },
];
const editRedis = props => {
  const { visible, record, onCancel } = props;
  const { getFieldDecorator, getFieldValue, validateFieldsAndScroll, resetFields } = props.form;
  const [detail, setDetail] = useState({});
  const [detailValue, setDetailValue] = useState({});
  const [loading, setLoading] = useState(false);

  const init = async () => {
    resetFields();
    try {
      setLoading(true);
      const res = await queryCacheByKeyType({
        keyword: record.redisKey,
        type: record.dataType,
      });
      setDetail(res.resultObject);
    } finally {
      setLoading(false);
    }
  };

  const deleteScene = async r => {
    Modal.confirm({
      title: '确认删除吗',
      onOk: async () => {
        await delByKeyType({
          dataType: detail.dataType,
          redisKey: detail.redisKey,
          redisHashKey: r.hkey,
          redisValue: r.svalue || r.zvalue,
        });
        init();
        message.success('删除成功！');
      },
      onCancel() {},
    });
  };

  const renderColumns = () => {
    if (record.dataType === 'list' || record.dataType === 'set') {
      return [
        {
          title: '序号',
          dataIndex: 'id',
          width: 100,
        },
        {
          title: '值',
          dataIndex: 'svalue',
          ellipsis: true,
        },
        {
          title: '操作',
          render: (v, r) => (
            <div>
              <Button
                type="link"
                onClick={() => {
                  setDetailValue({
                    detail: { ...r, dataType: record.dataType },
                    visible: true,
                  });
                }}
              >
                详情
              </Button>
              <Button type="link" onClick={() => deleteScene(r)}>
                删除
              </Button>
            </div>
          ),
        },
      ];
    }
    if (record.dataType === 'hash') {
      return [
        {
          title: '序号',
          dataIndex: 'id',
          width: 100,
          // render: (v, r, index) => index + 1,
        },
        {
          title: 'Hash键',
          dataIndex: 'hkey',
          width: 100,
          ellipsis: true,
        },
        {
          title: 'Hash值',
          dataIndex: 'hvalue',
          // width: '20%',
          ellipsis: true,
          render: v => JSON.stringify(v),
        },
        {
          title: '操作',
          render: (v, r) => (
            <div>
              <Button
                type="link"
                onClick={() => {
                  setDetailValue({
                    detail: { ...r, dataType: record.dataType },
                    visible: true,
                  });
                }}
              >
                详情
              </Button>
              <Button type="link" onClick={() => deleteScene(r)}>
                删除
              </Button>
            </div>
          ),
        },
      ];
    }
    if (record.dataType === 'zset') {
      return [
        {
          title: '序号',
          dataIndex: 'id',
          width: 100,
        },
        {
          title: '值',
          dataIndex: 'zvalue',
          // width: '20%',
          ellipsis: true,
        },
        {
          title: '分值',
          dataIndex: 'zscore',
        },
        {
          title: '操作',
          render: (v, r) => (
            <div>
              <Button
                type="link"
                onClick={() => {
                  setDetailValue({
                    detail: { ...r, dataType: record.dataType },
                    visible: true,
                  });
                }}
              >
                详情
              </Button>
              <Button type="link" onClick={() => deleteScene(r)}>
                删除
              </Button>
            </div>
          ),
        },
      ];
    }
    return [];
  };

  const renderData = () => detail.values;

  const addValue = () => {
    validateFieldsAndScroll(async err => {
      if (!err) {
        await createByKeyType({
          dataType: detail.dataType,
          redisKey: detail.redisKey,
          expire: detail.expire,
          redisHashKey: getFieldValue('redisHashKey'),
          redisValue: getFieldValue('redisValue'),
          score: getFieldValue('score'),
        });
        message.success('新增成功！');
        init();
      }
    });
  };

  const renderButton = () => (
    <Button
      type="primary"
      onClick={() => {
        addValue();
      }}
    >
      添加值
    </Button>
  );

  useEffect(() => {
    if (record) {
      init();
    }
  }, [record]);
  return (
    <Modal
      title="编辑"
      width="80%"
      visible={visible}
      footer={null}
      onCancel={onCancel}
    >
      <Spin spinning={loading}>
        <div>
          <Form labelCol={{ span: 3 }} wrapperCol={{ span: 20 }}>
            <Item label="数据类型">
              {getFieldDecorator('dataType', {
                initialValue: detail.dataType,
              })(
                <Select disabled placeholder="请选择">
                  {dataTypeList.map(item => (
                    <Select.Option key={item.value} value={item.value}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Item>
            <Item label="键">
              {getFieldDecorator('redisKey', {
                initialValue: detail.redisKey,
                rules: [{ required: true, message: 'key不能为空' }],
              })(<Input disabled placeholder="请输入key" />)}
            </Item>
            <Item label="过期时间">{detail.expireStr || ''}</Item>
            {getFieldValue('dataType') === 'hash' && (
              <Item label="Hash键">
                {getFieldDecorator('redisHashKey', {
                  initialValue: detail?.redisHashKey,
                  rules: [{ required: true, message: 'Hash键不能为空' }],
                })(<Input placeholder="请输入Hash键" />)}
              </Item>
            )}
            {getFieldValue('dataType') === 'zset' && (
              <Item label="分值">
                {getFieldDecorator('score', {
                  initialValue: detail?.score,
                  rules: [{ required: true, message: '分值不能为空' }],
                })(<InputNumber max={9999999999} min={-9999999999} style={{ width: '100%', padding: 0 }} placeholder="请输入分值" />)}
              </Item>
            )}
            <Item label="值" required>
              <Row>
                <Col span={20}>
                  <Item>
                    {getFieldDecorator('redisValue', {
                      initialValue: detail?.redisValue,
                      rules: [{ required: true, message: '值不能为空' }],
                    })(<TextArea disabled={detail.dataType === 'string'} autoSize={{ minRows: 3 }} placeholder="请输入值" />)}
                  </Item>
                </Col>
                {detail.dataType !== 'string' && <Col span={3}>{renderButton()}</Col>}
              </Row>
            </Item>
            {detail.dataType !== 'string' && (
              <Item label="值列表">
                <Table bordered rowKey="id" pagination={{ pageSize: 5 }} columns={renderColumns()} dataSource={renderData()} size="middle" />
              </Item>
            )}
          </Form>
        </div>
      </Spin>
      {detailValue.visible && (
        <DetailModal
          onCancel={() => {
            setDetailValue({
              visible: false,
              detail: {},
            });
          }}
          visible={detailValue.visible}
          detail={detailValue.detail}
        />
      )}
    </Modal>
  );
};

export default Form.create()(editRedis);
