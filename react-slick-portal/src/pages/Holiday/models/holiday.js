import { message } from 'antd';
import moment from 'moment';
import { queryHolidays, addHoliday, updateHoliday, deleteHoliday, batchAddHolidays } from '../service';

export default {
  namespace: 'holiday',

  state: {
    holidays: [],
    loading: false,
    currentDate: moment(),
  },

  effects: {
    *fetchHolidays({ payload }, { call, put }) {
      yield put({
        type: 'changeLoading',
        payload: true,
      });

      try {
        const { startDate, endDate } = payload;
        const response = yield call(queryHolidays, startDate, endDate);
        if (response && response.success) {
          yield put({
            type: 'saveHolidays',
            payload: response.resultObject || [],
          });
        } else {
          message.error('获取节假日数据失败');
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('获取节假日数据出错:', error);
        message.error('获取节假日数据出错');
      } finally {
        yield put({
          type: 'changeLoading',
          payload: false,
        });
      }
    },

    *addHoliday({ payload, callback }, { call }) {
      try {
        const response = yield call(addHoliday, payload);
        if (response && response.success) {
          message.success('新增节假日成功');
          if (callback) callback();
        } else {
          message.error('新增节假日失败');
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('新增节假日出错:', error);
        message.error('新增节假日出错');
      }
    },

    *batchAddHolidays({ payload, callback }, { call }) {
      try {
        const response = yield call(batchAddHolidays, payload);
        if (response && response.success) {
          message.success('批量添加节假日成功');
          if (callback) callback();
        } else {
          message.error('批量添加节假日失败');
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('批量添加节假日出错:', error);
        message.error('批量添加节假日出错');
      }
    },

    *updateHoliday({ payload, callback }, { call }) {
      try {
        const response = yield call(updateHoliday, payload);
        if (response && response.success) {
          message.success('更新节假日成功');
          if (callback) callback();
        } else {
          message.error('更新节假日失败');
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('更新节假日出错:', error);
        message.error('更新节假日出错');
      }
    },

    *deleteHoliday({ payload, callback }, { call }) {
      try {
        const response = yield call(deleteHoliday, payload);
        if (response && response.success) {
          message.success('删除节假日成功');
          if (callback) callback();
        } else {
          message.error('删除节假日失败');
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('删除节假日出错:', error);
        message.error('删除节假日出错');
      }
    },
  },

  reducers: {
    saveHolidays(state, { payload }) {
      return {
        ...state,
        holidays: payload,
      };
    },

    changeLoading(state, { payload }) {
      return {
        ...state,
        loading: payload,
      };
    },

    setCurrentDate(state, { payload }) {
      return {
        ...state,
        currentDate: payload,
      };
    },
  },
};
