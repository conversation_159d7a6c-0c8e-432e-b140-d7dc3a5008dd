@import '~antd/lib/style/themes/default.less';

.floatNav {
  position: fixed;
  z-index: 100;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s;
  font-size: 14px;

  &.left {
    left: 0;
  }

  &.right {
    right: 0;
  }

  &.hidden {
    opacity: 0;
    pointer-events: none;
  }

  .content {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 12px 0;
    transition: all 0.3s;
    width: 68px; // 修改展开时宽度为68px

    :global {
      .ant-anchor {
        padding: 0;
      }

      .ant-anchor-wrapper {
        background: none !important;
      }

      .ant-anchor-ink {
        display: none;
      }

      .ant-anchor-link {
        padding: 0;

        .ant-anchor-link-title {
          color: rgba(0, 0, 0, 0.65);

          &:hover {
            color: @primary-color;
          }
        }

        // 添加选中状态样式
        &.ant-anchor-link-active {
          border-left: 2px solid #0085D0;
          background: #E6FAFF;
          color: #0085D0;
          font-size: 14px;
          border-radius: 0;
        }
      }
    }

    .item {
      padding: 10px 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      max-height: 48px;
      white-space: normal;
      // 未选中状态样式
      background: #FFFFFF;
      border-radius: 0;
      font-size: 12px;
      color: #333333;
      line-height: 14px;
      text-align: left;
      transition: all 0.3s;
      overflow: hidden;

      &:hover {
        background-color: @primary-1;
      }

      i {
        margin-right: 8px;
        font-size: 16px;
        flex-shrink: 0;
      }

      .text {
        transition: opacity 0.3s;
        word-break: break-word;
      }
    }
  }

  .toggle {
    background-color: #fff;
    width: 68px;
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 4px 6px;
    color: #1890ff;
    position: relative;
    top: 0;
    right: 0px;
    transition: opacity 0.5s ease-in;

    &:hover {
      background-color: @primary-1;
    }

    &:before {
      content: '回到顶部';
      font-size: 12px;
    }
  }

  &.collapsed {
    .content {
      opacity: 0;
      transition: opacity 0.5s ease-out;
    }

    .toggle {
      width: 28px;
      height: 115px;
      border-radius: 14px;
      background: linear-gradient(208deg, #0085D0 0%, #30AEF6 100%);
      box-shadow: 0px 4px 12px 0px rgba(0, 28, 56, 0.16);
      flex-direction: column;
      border: 1px solid #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #FFFFFF;
      padding: 8px 4px; /* 添加适当的内边距 */
      position: absolute;
      top: 0;
      right: 16px;

      &:before {
        content: '页面导航';
        margin-right: 0;
        writing-mode: vertical-lr;
        letter-spacing: 2px;
        margin-bottom: 5px;
      }
    }
  }
}
