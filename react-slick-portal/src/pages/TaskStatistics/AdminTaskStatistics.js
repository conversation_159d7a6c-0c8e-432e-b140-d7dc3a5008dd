import React, { useState, useEffect } from 'react';
import {
  Chart,
  Geom,
  Axis,
  Tooltip,
} from 'bizcharts';
import { Form, Row, Col, Select, Button} from 'antd';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
import request from '@/utils/request';
import styles from './styles.less';
import { getPageSizeByCardHeight,getItem } from '@/utils/utils';
function AdminTaskStatistics(props) {
  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 8 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 16 },
    },
  };
  const { form } = props;
  const { getFieldDecorator } = props.form;

  const columnsWidth = 90;
  const colModel = [
    {
      dataIndex: 'title',
      title: '区域（客户经理）',
      width: columnsWidth,
    },
    {
      dataIndex: 'WARNING',
      title: '预警任务',
      width: columnsWidth,
    },
    {
      dataIndex: 'SERVICE_TASK',
      title: '服务任务',
      width: columnsWidth,
    },
    {
      dataIndex: 'MARKETING',
      title: '营销任务',
      width: columnsWidth,
    },
    {
      dataIndex: 'VISIT_TASK',
      title: '拜访任务',
      width: columnsWidth,
    },
    {
      dataIndex: 'CUST_TYPE',
      title: '客户任务',
      width: columnsWidth,
    },
    {
      dataIndex: 'OPP_TASK',
      title: '商机任务',
      width: columnsWidth,
    },
    {
      dataIndex: 'SURVEY_TYPE',
      title: '查勘任务',
      width: columnsWidth,
    },
    {
      dataIndex: 'ORDER_TYPE',
      title: '订单任务',
      width: columnsWidth,
    },
    {
      dataIndex: 'AGREEMENT_TYPE',
      title: '合同任务',
      width: columnsWidth,
    },
    {
      dataIndex: 'CPC_TYPE',
      title: '产商品任务',
      width: columnsWidth,
    },
  ];
  const compare = function(obj1, obj2) {
    let val1 = parseInt(obj1.totalCount);
    let val2 = parseInt(obj2.totalCount);
    return val2 - val1;
  };
  const [current, setCurrent] = useState(1);
  const [size, setSize] = useState(getPageSizeByCardHeight(props.size.height - 64 - 8));
  const [queryDateMap, setQueryDateMap] = useState([]);
  const [statisticsData, setStatisticsData] = useState([]);
  const [titleInfo, setTitleInfo] = useState('');
  const [showColumns, setShowColumns] = useState(colModel);
  const [tableDataList, setTableDataList] = useState([]);
  const [titleArray, setTitleArray] = useState([]);

  const resetForm = () => {
    form.resetFields();
  };


  useEffect(() => {
    setSize(getPageSizeByCardHeight(props.size.height - 64 - 8 - props.size.height * 0.4));
  }, [props.size.height]);
  useEffect(() => {

    let queryCode = {
      busiNbr: 'TaskView',
      propertyName: 'queryDate',
    };
    request('portal/DomainDataController/getValuesList.do', {
      data: { ...queryCode },
      method: 'POST',
    }).then(result => {
      const {userInfo } = getItem('user');
      let assitTitleArray =[];
      assitTitleArray.push({
      name: userInfo.postRegionName,
      id:userInfo.postRegionId});
      setTitleArray(assitTitleArray);
      setQueryDateMap(result);
      props.form.setFieldsValue({
        dateDimension:'DAY_ADD',
      })
      search(props,{id:userInfo.postRegionId,name:userInfo.postRegionName});
    });
  }, []);

  const cols = {
    y: {
      min: 0,
      alias: "任务总数"
    },
    sales: {
      tickInterval: 20,
    },
  };
  return (
    <div>
      <Form>
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
          <Col span={6}>
            <Form.Item label="统计周期" {...formItemLayout}>
              {getFieldDecorator('dateDimension')(
                <Select
                  allowClear
                  placeholder="请选择"
                  style={{ width: '100%' }}
                  showSearch
                  optionFilterProp="children"
                >
                  {queryDateMap.map(item => (
                    <Select.Option key={item.value}>{item.name}</Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
          <Button
            type="primary"
            onClick={() => {
              search(props,titleArray[titleArray.length -1]);
            }}
          >
            查询
          </Button>
        </Row>
      </Form>
      <div style={{ border: '1px solid #E8E8E8', padding: '10px' }}>
        <div className={styles.leftTitle}>任务完成量统计</div>
          <div className={styles.statisticsTitle}>{
            titleArray.map((item, index)=>{
                 return  (
                   <span key={index} onClick={()=>clickTitle(item)} > {index!=0 ? ">>":""} {item.name} </span>
                 )
            })
          }</div>
        <div >
          <Chart height={props.size.height * 0.35} data={statisticsData} scale={cols} forceFit onPlotClick={(value) =>handleChartClick(value)}>
            <Axis name="x" />
            <Axis name="y" />
            <Tooltip
              crosshairs={{
                type: "y",
                name:'任务总数'
              }}
            />
            <Geom type="interval" position="x*y" />
          </Chart>
        </div>
        <SlickTable
          style={{ marginTop: 0 }}
          rowKey={record => record.title}
          // columns={showColumns}
          // dataSource={tableDataList}
          // data={{
          //   pagination: {
          //     pageNumber: current,
          //     pageSize: size,
          //   },
          // }}

          data={{
            list:tableDataList,
            pagination: {
              current,
              pageSize:size
            },
          }}
          columns={showColumns}
          // dataSource={tableDataList}
          // width={300}
          onChange={handleChange}
          scroll={{ x: 1000 }}
        />
      </div>
    </div>
  );


  function handleChange  (pagination)  {
    const { current } = pagination;
    setCurrent(current);
  };
  function handleChartClick(e) {
    const data = e.data;
    if(data === undefined || data === null){
      return;
    }
    let statistics = data._origin.data;
    // 点击最深层的时候不做响应，或者图像已经有
    if(statistics.level==3 || titleArray[titleArray.length -1].id == statistics.id){
      return;
    }
    search(props,statistics,'chartClick');

  }

  function clickTitle(item){
    let assitTitleArray =[];
    if(titleArray[titleArray.length -1].id == item.id){
      return;
    }
    for (let i = 0; i <titleArray.length;i++) {
        assitTitleArray.push(titleArray[i]);
        if(assitTitleArray[i].id == item.id){
          break;
        }
    }
    setTitleArray(assitTitleArray);
    search(props,item,'titleClick');
  }
function search(props,statistics,clickType) {
    const { userInfo } = getItem('user');
    if(clickType!=null && clickType != 'titleClick'){
      let assitTitleArray =titleArray;
      assitTitleArray.push(statistics);
      setTitleArray(assitTitleArray);
    }
    let regionId = statistics.id;
    let dateDimension = props.form.getFieldValue('dateDimension');
    let queryCode = {
      userId: userInfo.userId,
      dateDimension: (dateDimension == undefined || dateDimension == 'DAY_ADD') ? 'YESTERDAY_ADD' : dateDimension,
      regionId: regionId,
      level: 1,
    };

   request('orgauth/WorkbenchController/queryTaskManageView.do', {
      data: { ...queryCode },
      method: 'POST',
    }).then(result => {
      const list = result.list;
      let datas = [];
      let tableDatas = [];
      let titleInfo = {};
      list.sort(compare);
      for (let i = 0; i < list.length; i++) {
          if(list[i].totalCount>0){
            datas.push({
              x: list[i].name,
              y: list[i].totalCount,
              id: list[i].id,
              data: list[i]
            });
          tableDatas.push(formTableData(list[i].taskInfos));
          }
      }

      setTableDataList(tableDatas);
      setStatisticsData(datas);
      setTitleInfo(titleInfo);
      setCurrent(1);
    });
  }
  function formTableData(datas) {
    let taskType = {
      SERVICE_TASK: 0,
      SERVICE_TASK: 0,
      OPP_TASK: 0,
      VISIT_TASK: 0,
      WARNING: 0,
      MARKETING: 0,
      CUST_TYPE: 0,
      ORDER_TYPE: 0,
      AGREEMENT_TYPE: 0,
      SURVEY_TYPE: 0,
      CPC_TYPE:0,
    };

    for (var i = 0; i < datas.length; i++) {
      let taskData = datas[i];
      switch (taskData.taskType) {
        case 'SERVICE_TASK':
          taskType.SERVICE_TASK = taskData.completeCount;
          break;
        case 'OPP_TASK':
          taskType.OPP_TASK = taskData.completeCount;
          break;
        case 'VISIT_TASK':
          taskType.VISIT_TASK = taskData.completeCount;
          break;
        case 'WARNING':
          taskType.WARNING = taskData.completeCount;
          break;
        case 'MARKETING':
          taskType.MARKETING = taskData.completeCount;
          break;
        case 'CUST_TYPE':
          taskType.CUST_TYPE = taskData.completeCount;
          break;
        case 'ORDER_TYPE':
          taskType.ORDER_TYPE = taskData.completeCount;
          break;
        case 'AGREEMENT_TYPE':
          taskType.AGREEMENT_TYPE = taskData.completeCount;
          break;
        case 'SURVEY_TYPE':
          taskType.SURVEY_TYPE = taskData.completeCount;
          break;
        case 'CPC_TYPE':
          taskType.CPC_TYPE = taskData.completeCount;
          break;
        default:
          break;
      }
      taskType.title = taskData.name;
    }
    return taskType;
  }
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(AdminTaskStatistics));
