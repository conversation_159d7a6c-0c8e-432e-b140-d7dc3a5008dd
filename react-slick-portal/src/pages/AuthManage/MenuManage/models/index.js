export default {
  namespace: 'menuManage',
  state: {
    menuId: '',
    behavior: 'disable',
    isLeaf: false,
    treeData: [],
    menuData: {},
    iconUrl: '',
  },

  effects: {},

  reducers: {
    saveMenuData(state, { payload: params }) {
      return {
        ...state,
        menuData: params,
      };
    },
    saveMenuID(state, { payload: params }) {
      return {
        ...state,
        menuId: params,
      };
    },
    saveBehavior(state, { payload: params }) {
      return {
        ...state,
        behavior: params,
      };
    },
    saveTreeData(state, { payload: params }) {
      return {
        ...state,
        treeData: params,
      };
    },
    saveIsLeaf(state, { payload: params }) {
      return {
        ...state,
        isLeaf: params,
      };
    },
    saveiconUrl(state, { payload: params }) {
      return {
        ...state,
        iconUrl: params,
      };
    },
  },
};
