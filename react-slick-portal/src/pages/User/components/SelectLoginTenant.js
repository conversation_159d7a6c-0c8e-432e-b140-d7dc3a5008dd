import React, { useState } from 'react';
import { Modal, Form, message } from 'antd';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
import { getTerminalType } from '@/utils/utils';
import ScrollBar from '@/components/ScrollBar';

function SelectLoginTenant({
  visible,
  close,
  dispatch,
  multiTenantList,
  userCode,
  password,
  loginWithPwd,
  phoneLoginInfo,
}) {
  const [loading] = useState(false);
  const [selectRow, setSelectRow] = useState([]);

  const handleSubmit = e => {
    e.preventDefault();

    if (selectRow.length === 0) {
      message.warning('请选择租户进行登录');
      return;
    }

    let formValues;
    // 账号密码登录
    if (loginWithPwd === true) {
      formValues = {
        clientType: getTerminalType(),
        loginWay: 'multiTenant',
        tenantId: selectRow[0].tenantId,
        userCode,
        password,
      };
    } else {
      // 手机号登录
      formValues = {
        clientType: getTerminalType(),
        loginWay: 'sms',
        tenantId: selectRow[0].tenantId,
        ...phoneLoginInfo,
      };
    }

    dispatch({
      type: 'login/login',
      payload: formValues,
      callback: () => {},
    });

    close();
  };

  const columns = [
    {
      title: '租户ID',
      dataIndex: 'tenantId',
    },
    {
      title: '租户名称',
      dataIndex: 'tenantName',
    },
    {
      title: '租户编码',
      dataIndex: 'tenantNbr',
    },
  ];

  return (
    <Modal
      title="选择登录租户"
      visible={visible}
      onOk={handleSubmit}
      onCancel={close}
      confirmLoading={loading}
      destroyOnClose
      centered
      okButtonProps={{ htmlType: 'submit' }}
    >
      <div style={{ padding: '8' }}>
        <ScrollBar autoHide autoHeight autoHeightMax={400}>
          <SlickTable
            rowKey={record => record.tenantId}
            columns={columns}
            onSelectRow={selectedRows => {
              setSelectRow(selectedRows);
            }}
            pick="radio"
            dataSource={multiTenantList}
            pagination={false}
          />
        </ScrollBar>
      </div>
    </Modal>
  );
}

export default connect(({ login }) => ({
  login,
}))(Form.create()(SelectLoginTenant));
