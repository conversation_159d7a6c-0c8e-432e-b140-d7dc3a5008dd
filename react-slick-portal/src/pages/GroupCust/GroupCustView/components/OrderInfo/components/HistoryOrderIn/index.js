/* 历史订购 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Form, Row, Col, Input, Button, DatePicker, Icon } from 'antd';
import { connect } from 'dva';
import { useFormTable } from '@umijs/hooks';
import moment from 'moment';
import SlickTable from '@/components/SlickTable';
import { getPageSizeByCardHeight } from '@/utils/utils';

const { RangePicker } = DatePicker;

// 表格单元格样式
const cellStyle = {
  maxWidth: 200,
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  cursor: 'pointer',
};

// 获取表格数据
const getTableData = ({ current, pageSize }, formData) => {
  // 处理日期范围
  const params = { ...formData };
  if (params.protocolTime && params.protocolTime.length === 2) {
    params.startDate = moment(params.protocolTime[0]).format('YYYY-MM-DD');
    params.endDate = moment(params.protocolTime[1]).format('YYYY-MM-DD');
    delete params.protocolTime;
  }

  // 模拟API调用
  return new Promise(resolve => {
    setTimeout(() => {
      // 模拟数据
      const mockData = [
        {
          id: '8917262617217',
          businessType: '集团业务注销',
          operator: '员工1',
          operatorId: '7651711',
          acceptBusiness: '千里眼',
          orderNumber: '2025-02-21 10:30:31',
          groupId: '***********',
          acceptTime: '集团编码',
        },
        {
          id: '8917262617217',
          businessType: '集团业务变更',
          operator: '员工1',
          operatorId: '7651711',
          acceptBusiness: '千里眼',
          orderNumber: '2024-12-21 10:30:31',
          groupId: '***********',
          acceptTime: '集团编码',
        },
        {
          id: '8917262617217',
          businessType: '集团业务受理',
          operator: '员工1',
          operatorId: '7651711',
          acceptBusiness: '千里眼',
          orderNumber: '2024-11-21 10:30:31',
          groupId: '***********',
          acceptTime: '集团编码',
        },
        {
          id: '8917262617213',
          businessType: '集团业务受理',
          operator: '员工1',
          operatorId: '7651711',
          acceptBusiness: '千里眼',
          orderNumber: '2024-11-20 10:30:31',
          groupId: '***********',
          acceptTime: '集团编码',
        },
        {
          id: '8917262617216',
          businessType: '集团业务受理',
          operator: '员工1',
          operatorId: '7651711',
          acceptBusiness: '千里眼',
          orderNumber: '2024-10-21 10:30:31',
          groupId: '***********',
          acceptTime: '集团编码',
        },
      ];

      resolve({
        total: mockData.length,
        list: mockData,
      });
    }, 500);
  });
};

const HistoryOrders = ({ size: { height }, form }) => {
  const tableRef = useRef(null);
  // 设置表格分页大小
  const [size, setSize] = useState(getPageSizeByCardHeight(height));
  const { getFieldDecorator } = form;

  // 使用hooks获取表格数据
  const {
    tableProps,
    search: { submit, reset, type: searchType, changeType },
  } = useFormTable(getTableData, {
    defaultPageSize: size,
    form,
    manual: true,
  });

  const { pagination, ...restTableProps } = tableProps;

  // 根据size.height变化即时更新pageSize
  useEffect(() => {
    setSize(getPageSizeByCardHeight(height));
  }, [height]);

  const renderExtra = useCallback(() => {
    if (searchType === 'simple') {
      return (
        <Form form={form} style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
          <Form.Item>
            {getFieldDecorator('DATA_STATUS', {
              initialValue: [moment().subtract(7, 'days'), moment()],
            })(<RangePicker placeholder={['开始日期', '结束日期']} style={{ width: 200 }} format="YYYY-MM-DD" onChange={submit} />)}
          </Form.Item>
          <Button type="link" onClick={changeType}>
            展开<Icon type="down" />
          </Button>
        </Form>
      );
    }
    return (
      <Button type="link" onClick={changeType}>
        收起<Icon type="up" />
      </Button>
    );
  }, [searchType]);
  return (
    <>
      <Card className="cute" extra={renderExtra()} title="查询" bordered>
        {searchType !== 'simple' && (
          <Form className="flow fix-label" onSubmit={submit}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="协议编码">{getFieldDecorator('protocolId')(<Input placeholder="请输入协议编码" allowClear />)}</Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="协议名称">{getFieldDecorator('protocolName')(<Input placeholder="输入协议名称称模糊查询" allowClear />)}</Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="集团编码">{getFieldDecorator('groupId')(<Input placeholder="请输入集团编码" allowClear />)}</Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="集团名称">{getFieldDecorator('groupName')(<Input placeholder="请输入集团名称" allowClear />)}</Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="协议到期时间">
                  {getFieldDecorator('protocolTime', {
                    initialValue: [moment().subtract(7, 'days'), moment()],
                  })(<RangePicker placeholder={['开始日期', '结束日期']} style={{ width: '100%' }} format="YYYY-MM-DD" />)}
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={24} className="text-right">
                <Button type="primary" htmlType="submit" loading={tableProps.loading}>
                  查询
                </Button>
                <Button className="margin-left" onClick={reset}>
                  重置
                </Button>
              </Col>
            </Row>
          </Form>
        )}

        <SlickTable
          style={{ marginTop: 20 }}
          ref={tableRef}
          rowKey="id"
          {...restTableProps}
          data={{
            pagination: {
              ...pagination,
              pageSize: size,
            },
          }}
          columns={[
            {
              title: '计费号码',
              dataIndex: 'id',
              key: 'id',
              onCell: () => ({
                style: cellStyle,
              }),
            },
            {
              title: '业务类型',
              dataIndex: 'businessType',
              key: 'businessType',
            },
            {
              title: '操作员',
              dataIndex: 'operator',
              key: 'operator',
            },
            {
              title: '操作员工号',
              dataIndex: 'operatorId',
              key: 'operatorId',
            },
            {
              title: '受理业务',
              dataIndex: 'acceptBusiness',
              key: 'acceptBusiness',
            },
            {
              title: '订单号',
              dataIndex: 'orderNumber',
              key: 'orderNumber',
            },
            {
              title: '集团编码',
              dataIndex: 'groupId',
              key: 'groupId',
            },
            {
              title: '受理时间',
              dataIndex: 'acceptTime',
              key: 'acceptTime',
            },
          ]}
        />
      </Card>
    </>
  );
};

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(HistoryOrders));
