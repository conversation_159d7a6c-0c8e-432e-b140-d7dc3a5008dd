import React, { useMemo, useState } from 'react';
import { Button } from 'antd';
import ComboGrid from '@/components/ComboGrid';

const SignDepartComp = props => {
  const { value, onChange, colFieldsChange } = props;
  const [outsideOpen, setOutsideOpen] = useState(true);

  const renderComboGrid = useMemo(
    () => (
      <ComboGrid
        url="orgauth/OrganizationController/qryOrganizationGroupGridData.do"
        popupStyle={{ width: 560 }}
        placeholder="请选择"
        searchPlaceholder="请输入单位名称或编码进行搜索"
        label="org_name"
        params={{ filterCol: 'org_name,org_code' }}
        destroyPopupOnHide
        rowKey="org_code"
        pick="radio"
        outsideOpen={outsideOpen}
        setOutsideOpen={setOutsideOpen}
        value={value}
        onChange={onChange}
        columns={[
          {
            title: '单位名称',
            dataIndex: 'org_name',
            ellipsis: true,
          },
          {
            title: '单位编码',
            dataIndex: 'org_code',
            ellipsis: true,
          },
          {
            title: '单位路径',
            ellipsis: true,
            dataIndex: 'org_path',
          },
        ]}
        extra={(
          <Button
            type="primary"
            onClick={() => {
              setOutsideOpen(false);
            }}
          >
            确定
          </Button>
        )}
      />
    ),
    [outsideOpen, colFieldsChange]
  );

  return renderComboGrid;
};

export default SignDepartComp;
