/**
 * 向父iframe发送'处理人确认'消息
 * @param {object} userInfo
 */
export function sendUserInfoToParent(userInfo, isLowCode) {
  if (isLowCode) {
    console.log('enter sendUserInfoToParent');
    window.parent.postMessage(
      {
        u__$Type: 'editor',
        u__$Data: userInfo,
        u__$Typeof: typeof userInfo,
      },
      '*'
    );
  } else if (window.parent !== window.self) {
    window.parent.postMessage(
      JSON.stringify({
        action: 'bup#dealUserSelect',
        userInfo: userInfo,
      }),
      '*'
    );
  }
}

/**
 * 向父iframe发送'关闭处理人'弹窗消息
 */
export function closeUserSelect(isLowCode) {
  if (isLowCode) {
    console.log('enter closeUserSelect');
    window.parent.postMessage(
      {
        u__$Type: 'editor',
        u__$Data: { type: 'cancel' },
        u__$Typeof: typeof { type: 'cancel' },
      },
      '*'
    );
  } else if (window.parent !== window.self) {
    window.parent.postMessage(
      JSON.stringify({
        action: 'bup#closeUserSelect',
      }),
      '*'
    );
  }
}

/**
 *  根据title过滤树
 * @param {array} treeData 原始树数据
 * @param {string} filter 过滤字符串
 * @param {array} newTreeData 保存新的树数据
 * @param {array} expandedKeys 保存需要展开的树节点
 * @returns 返回父级节点是否需要添加到新的树数据中
 */
export function filterTree(treeData, filter, newTreeData, expandedKeys) {
  if (!treeData || treeData.length === 0) {
    return false;
  }
  expandedKeys = expandedKeys || [];
  let addParent = false; // 判断是否添加父节点
  let levelData = [];
  for (let i = 0; i < treeData.length; i++) {
    let treeItem = { ...treeData[i] };
    let newChildren = [];
    let contains = treeItem.title.includes(filter); // 当前节点名称包含过滤条件
    let addChild = filterTree(treeItem.children, filter, newChildren, expandedKeys); // 子节点列表中有部分子节点包含过滤条件
    if (contains || addChild) {
      // 节点本身名称包含过滤条件 || 子节点列表有部分节点包含过滤条件
      treeItem.children = newChildren;
      levelData.push(treeItem);
      if (treeItem.parentId && treeItem.parentId >= 0) {
        expandedKeys.push(treeItem.parentId + '');
      }
    }
    addParent = addParent || contains || addChild;
  }
  newTreeData.push(...levelData);
  return addParent;
}

/**
 *  根据原始树数据构建树节点
 * @param {array} treeData 原始树数据
 * @param {array} expandedKeys 需要展开的树节点列表
 * @param {string} selectedKey 选中的树节点
 * @param {string} idName 节点标识属性名
 * @param {string} titleName 节点标题属性名
 * @param {string} parentIdName 父级节点标识属性名
 * @returns 父级节点是否需要展开
 */
export function buildTree(treeData, expandedKeys, selectedKey, idName, titleName, parentIdName) {
  treeData = treeData || [];
  let needExpand = false; // 标记父节点是否需要展开
  for (let i = 0; i < treeData.length; i++) {
    treeData[i].key = treeData[i][idName];
    treeData[i].title = treeData[i][titleName];
    treeData[i].parentId = treeData[i][parentIdName];
    if (Number(treeData[i].key) === Number(selectedKey)) {
      needExpand = true;
    }
    if (!treeData[i].children || treeData[i].children.length === 0) {
      treeData[i].isLeaf = true;
    } else {
      let needExpandCurrent = buildTree(treeData[i].children, expandedKeys, selectedKey, idName, titleName, parentIdName);
      if (needExpandCurrent) {
        // 当前节点需要展开
        expandedKeys.push(treeData[i].key + '');
      }
      // 当前节点需要展开，或者是同级节点为被选中节点
      needExpand = needExpand || needExpandCurrent;
    }
  }
  return needExpand;
}
