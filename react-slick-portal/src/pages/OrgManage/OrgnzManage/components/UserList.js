import React, { useState, useEffect, useRef, useLayoutEffect } from 'react';
import { connect } from 'dva';
import { Row, Col, Card, Form, Input, Icon, Button, Tooltip, Modal, Divider, Radio, message } from 'antd';
import styles from '../styles.less';
import SlickTable from '@/components/SlickTable';
import useSlickTable from '@/hooks/UseSlickTable';
import PubSub from 'pubsub-js';
import request from '@/utils/request';
import AddUserList from './AddUserList';
import OrgSelect from './OrgSelect';
import ScrollBar from '@/components/ScrollBar';
import { newGetDictData } from '@/services/common';
const Search = Input.Search;

const namespace = 'orgManage';

// 空对象校验
const judgeObj = obj => {
  if (Object.keys(obj).length == 0) return true;
  return false;
};

function releUser(props) {
  const [allowEdit, setAllowEdit] = useState(false);
  const userEl = useRef(null);
  const orgEl = useRef(null);
  const [params, setParams] = useState({
    filterCol: 'staffName,staffCode',
    filterVal: '',
    orgId: props.passData.orgId,
  });
  // 初始化变量
  const [variable, setVariable] = useState(false);
  const [selectRows, setSelectRows] = useState([]);
  const [visible, setVisible] = useState(false);
  const [orgVisible, setOrgVisible] = useState(false);

  const { tableProps } = useSlickTable(
    { pageSize: 4 },
    //'orgauth/StaffDepRelController/selectDepStaffGridData.do',
    'orgauth/StaffOrgRelController/selectOrgStaffGridData.do',
    params
  );

  useEffect(() => {
    if (!judgeObj(props.passData) && variable) {
      setParams({
        ...params,
        orgId: props.passData.orgId,
        filterVal: '',
        gb_reset: true,
      });
      setSelectRows([]);
    }
  }, [props.passData]);

  useEffect(() => {
    newGetDictData({
      groupCode: 'SYSTEM_VAR',
      paramCode: 'USE_USER_CENTER',
    }).then(res => {
      setAllowEdit(res !== '1'); // 1:不允许编辑
    });

    setVariable(true);
  }, []);

  const onSearch = value => {
    setParams({
      ...params,
      filterVal: value,
      gb_reset: true,
    });
  };

  const handleSelectRows = value => {
    setSelectRows(value);
  };

  const handleFailure = () => {
    invalidateStaffs().then(res => {
      if (res) {
        message.success('失效操作成功！');
        setParams({
          ...params,
        });
        setSelectRows([]);
      }
    });
  };

  const handleAdjust = () => {
    setOrgVisible(true);
  };

  async function invalidateStaffs() {
    let arr = [];
    selectRows.map(item => {
      arr.push(item.staffId);
    });
    return request('orgauth/StaffController/invalidateStaffs.do', {
      data: arr,
      method: 'PUT',
    });
  }

  const addRow = () => {
    setVisible(true);
  };

  const handleOk = () => {
    userEl.current.handleAdd().then(res => {
      if (res === true) {
        setVisible(false);
        setParams({
          ...params,
          gb_reset: true,
        });
      }
    });
  };
  const handleCancel = () => {
    setVisible(false);
  };

  const orgHandleOk = () => {
    orgEl.current.handleAdd().then(res => {
      if (res.length > 0) {
        let arr = [];
        selectRows.map(item => {
          arr.push({
            staffOrgRelId: item.staffOrgRelId,
            staffId: item.staffId,
            orgId: res[0].orgId,
          });
        });
        // console.log(arr);
        request('orgauth/StaffOrgRelController/updateStaffOrgRels.do', {
          data: arr,
        }).then(res => {
          if (res) {
            setOrgVisible(false), message.success('组织调整成功！');
            setParams({
              ...params,
            });
            setSelectRows([]);
          } else {
            message.error('组织调整失败！');
            setOrgVisible(false);
          }
        });
      } else {
        message.info('请选择组织！');
      }
    });
  };

  const orgHandleCancel = () => {
    setOrgVisible(false);
  };

  const columns = [
    {
      title: '员工名称',
      dataIndex: 'staff.staffName',
    },
    {
      title: '员工类型',
      dataIndex: 'staff.staffTypeName',
    },
    {
      title: '员工编码',
      dataIndex: 'staff.staffCode',
    },
    {
      title: '账号',
      dataIndex: 'staff.staffAccount',
    },
  ];
  // scrollBar 72 : card-head + padding   ;  318 : 下级组织列表高度 310+ 两模块 8 的间距
  return (
    <div style={{ padding: '16 0px' }}>
      <ScrollBar autoHide autoHeight autoHeightMax={props.size.height - 72 - 318}>
        <div style={{ margin: '0 16px' }}>
          <div className={styles.systemExtra}>
            <div>
              <Search placeholder="请选择" style={{ width: '200px' }} placeholder="用户名称，账号搜索" onSearch={onSearch} />
            </div>
            {allowEdit && (
              <div>
                <Button type="primary" onClick={addRow}>
                  新增
                </Button>
              </div>
            )}
          </div>

          <SlickTable
            rowKey={record => record.id}
            columns={columns}
            pick="checkbox"
            {...tableProps}
            extra={
              selectRows.length > 0 && allowEdit ? (
                <span>
                  <Button type="danger" ghost onClick={handleFailure} className="margin-right">
                    失效员工
                  </Button>
                  <Button type="primary" ghost onClick={handleAdjust}>
                    调整组织
                  </Button>
                </span>
              ) : null
            }
            onSelectRow={handleSelectRows}
            selectedRows={selectRows}
          />
          <Modal title="新增员工信息" visible={visible} onOk={handleOk} onCancel={handleCancel} width="500px" destroyOnClose>
            <AddUserList ref={userEl} cRef={userEl} orgId={props.passData.id} />
          </Modal>
          <Modal title="组织选择" visible={orgVisible} onOk={orgHandleOk} onCancel={orgHandleCancel} width="600px" destroyOnClose>
            <OrgSelect ref={orgEl} cRef={orgEl} />
          </Modal>
        </div>
      </ScrollBar>
    </div>
  );
}

export default connect(({ orgManage, setting }) => ({
  passData: orgManage.passData,
  size: setting.size,
}))(releUser);
