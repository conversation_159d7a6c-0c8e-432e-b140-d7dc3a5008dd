/*集团半年级别 start*/

// 混合用于应用基本样式
.groupLevelMixin(@bgcolor, @txtcolor) {
  background-color: @bgcolor;
  color: @txtcolor;
  padding: 2px 12px;
  border-radius: 3px;
  font-size: 12px;
  height: 22px;
}
// 应用到具体的类
.groupLevel {
  .groupLevelMixin(#FFFBE6, #D48806);
}
/*集团半年级别 end*/

/*客户经理 start*/
.groupStatusMixin(@color) {
  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: @color;
    border-radius: 50%;
    margin-right: 8px;
  }
}
.groupStatus {
  display: inline-flex;
  align-items: center;
  color: #333333;
  line-height: 22px;
  /*未开通*/
  &.status1 { .groupStatusMixin(#333333); }
  /*正使用*/
  &.status2 { .groupStatusMixin(#21BF55); }
  /*其余*/
  &.status { .groupStatusMixin(#E40077); }
}
/*客户经理 end*/

/*名单类型 start*/
.listTypeMixin(@bgcolor, @txtcolor) {
  background-color: @bgcolor;
  color: @txtcolor;
}
.listType {
  line-height: 22px;
  height: 22px;
  border-radius: 3px;
  font-size: 12px;
  padding: 2px 4px;
  /*无*/
  &.default { .listTypeMixin(#E6FAFF,#23A0DE); }
  /*黑名单*/
  &.black { .listTypeMixin(#F0F0F0,#333333); }
  /*红名单*/
  &.red { .listTypeMixin(#FFE6EE,#F02688); }
}
/*客户经理 end*/
/*表格展开行 start*/
.expandedRow >td:first-child {
  border-left: 3px solid #0085D0;
}
/*表格展开行 end*/
/*表格嵌套内容 start*/
.expandedRowRender {
  padding: 20px 40px;
  background: #F2FCFF;
  font-weight: 400;
  margin: -8px;
  border-left: 3px solid #0085D0;

  :global {
    .ant-col {
      &.label {
        color: #333333;
        text-align: right;
        padding-right: 8px; // 给label添加一些右边距，使文本之间更易读
        &::after {
          content: "："; /* 在每个label内容后添加冒号 */
        }
      }

      &.value {
        color: #666666;
        text-align: left;
        flex: 1; // 让value占据剩余的空间，以确保与label在同一行
      }
    }
  }
}
/* 初始状态：隐藏 */
.hidden {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.5s ease-out, visibility 0s linear 0.5s;
}

/* 显示状态 */
.visible {
  opacity: 1;
  visibility: visible;
  transition: opacity 0.5s ease-in;
  margin-top: -2px;
}
/*表格嵌套内容 end*/
