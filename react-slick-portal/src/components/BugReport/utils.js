import React from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'antd';
import PubSub from 'pubsub-js';
import html2canvas from 'html2canvas';

function validateOption(data) {
  if (!['all', 'bomc', 'eoms'].includes(data.reportTo)) {
    throw Error('report()参数reportTo，预期为：`all`、`bomc`、`eoms`');
  }

  if (data.type === undefined || !['system', 'service'].includes(data.type.toLocaleLowerCase())) {
    throw Error('report()参数必须包含type，且预期为：`system`或`service`');
  }

  if (!['serial', 'parallel'].includes(data.ejectMode)) {
    throw Error('report()参数ejectMode，预期为：`serial`、`parallel`');
  }
}
export class ErrorListener {
  constructor({ topic, onChange }) {
    this.topic = topic;
    this.onChange = onChange;
    this.errors = [];
    this.token = PubSub.subscribe(topic, this.errorSubscriber);
  }

  /**
   *
   * @param {object} obj 预期格式{type,code,name,msg}
   * type 可选值 ：'system' | 'service'
   */
  report = obj => {
    PubSub.publish(this.topic, obj);
  };

  errorSubscriber = (topic, data) => {
    let newData = data;

    if (Object.prototype.toString.call(newData) !== '[object Object]') {
      throw Error('report()参数必须是对象');
    }

    // 兼容处理：上一版api不传ejectMode，默认值当成'parallel'

    if (newData.ejectMode === undefined) {
      newData = { ...newData, ejectMode: 'parallel' };
    }
    // 兼容处理：上一版api不传reportTo，默认值当成bomc
    if (newData.reportTo === undefined) {
      newData = { ...newData, reportTo: 'bomc' };
    }

    // 校验参数
    validateOption(newData);

    // ejectMode=parallel表示先收集数据到右下角（点击后以列表形式展示）
    if (newData.ejectMode === 'parallel') {
      const isExisted = !!this.errors.find(i => i.key === JSON.stringify(newData));

      if (!isExisted) {
        this.errors = [{ key: JSON.stringify(newData), ...newData }, ...this.errors];
        this.onChange(this.errors);
      }
    } else {
      this.onChange({ key: JSON.stringify(newData), ...newData });
    }
  };

  unsubscribe = () => {
    PubSub.unsubscribe(this.token);
  };

  clear = () => {
    this.errors = [];
  };
}

export function isJSON(str) {
  if (typeof str === 'string') {
    try {
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  }
  return false;
}

export function isObject(value) {
  return Object.prototype.toString.call(value) === '[object Object]';
}

// 返回base64
export async function screenshot() {
  const canvas = await html2canvas(document.getElementById('root'), {
    allowTaint: true, // 允许截取跨域图片
    logging: true,
    imageTimeout: 3000,
    sacle: 4,
  });

  return canvas.toDataURL();
}

// 参数含义：https://www.yuque.com/ifed/blog/bug-report#HQotR
export function quickReport({
  reportTo = 'all',
  ejectMode = 'parallel',
  msg,
  code,
  type = 'service',
  system = '01',
  name,
  ...rest
}) {
  PubSub.publish('quick_report', {
    reportTo,
    ejectMode,
    msg,
    code,
    type,
    system,
    name,
    ...rest,
  });
}

/**
 * 错误弹窗
 * 用法：Portal.error({name, message:'添加调度人员、维护人员添加调度人员、维护人员',traceId:123456,code:54321});
 * @param {string} name 必填，业务类型
 * @param {string} message 必填，错误信息
 * @param {number | string} 必填，traceId
 * @param {string } code 必填，业务编码
 * @param {string } system 非必填，默认值为'01' ，表示系统类型。可选值 '01' | '02' | '03' 分别表示：门户、订单和受理
 */

export function confirm({ message, traceId, code = '', width = 500 }) {
  const isExisted = document.querySelector('.ifed-error-modal') !== null;

  // 防止重复弹窗
  if (isExisted) {
    return;
  }

  const isString = typeof code === 'string';

  if (!isString || (isString && code.length < 6)) {
    throw new Error('code值错误，预期是string类型，且长度>=6');
  }

  // code第六位,如果是'4'或 '5'映射成业务异常 其他就是系统异常
  const newTitle = ['4', '5'].includes(code.charAt(5)) ? '业务异常' : '系统异常';

  const modal = Modal.warning({
    className: 'ifed-error-modal',
    width,
    title: `${newTitle} - ${code}`,
    getContainer: () => document.getElementById('root'),
    content: (
      <>
        <div className="ifed-error-modal-body">
          {typeof message === 'string'
            ? message.split('\n').map(k => <div key={k}> {k}</div>)
            : null}
          <div className="margin-top-lg text-gray">traceId：{traceId}</div>
        </div>
      </>
    ),
  });
}
