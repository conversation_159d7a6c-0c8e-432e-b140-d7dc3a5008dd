import React, { useState, useEffect } from 'react';
import { Card, Input, Button, Modal, message, Select, Divider, Form, Row, Spin, Col } from 'antd';
import moment from 'moment';
import SlickTable from '@/components/SlickTable';
import useSlickTable from '@/hooks/UseSlickTable';
import styles from './styles.less';
import LabelFormDrawer from './components/LabelFormDrawer';
import { saveLabel } from './services/commonServices';
import request from '@/utils/request';

function LabelsManage(props) {
  const [curRecord, setCurRecord] = useState(null);
  const [formDrawerVisible, setFormDrawerVisible] = useState(false);
  const [categoryOpts, setCategoryOpts] = useState([]);
  const [pageLoading, setPageLoading] = useState(false);

  const { getFieldDecorator } = props.form;

  const { form } = props;

  const [params, setParams] = useState({
    filterCol: '',
    filterVal: '',
    category: '',
    categoryCode: '',
    subcategory: '',
    subcategoryCode: '',
    //  gb-reset （考虑业务场景当前表格是否需要重置第一页）值:false 不重置到第一页， true 重置到第一页
  });

  // 初始化
  useEffect(() => {
    request('orgauth/LabelsController/getCategory.do', {}).then(res => {
      const data = res.resultObject;
      const ops = [];
      for (let i = 0; i < data.length; i++) {
        ops.push({ label: data[i].category, value: data[i].categoryCode });
      }
      setCategoryOpts(ops);
    });
  }, []);

  const { tableProps } = useSlickTable({ pageSize: 10 }, 'orgauth/LabelsController/queryPageLabels.do', params);

  const onSearch = () => {
    const queryParams = form.getFieldsValue();
    setParams({
      ...params,
      // category: queryParams.category,
      categoryCode: queryParams.categoryCode,
      subcategory: queryParams.subcategory,
      subcategoryCode: queryParams.subcategoryCode,
      gb_reset: true,
    });
  };

  const reset = () => {
    form.resetFields();
  };

  const refresh = () => {
    const newParam = { ...params };
    setParams(newParam);
  };

  const handleAdd = () => {
    setCurRecord(null);
    setFormDrawerVisible(true);
  };

  const handleEdit = record => {
    setCurRecord(record);
    setFormDrawerVisible(true);
  };

  const updateStatus = record => {
    const obj = {
      labelId: record.labelId,
    };
    if (record.statusCd === '1000') {
      obj.statusCd = '1100';
    } else {
      obj.statusCd = '1000';
    }
    setPageLoading(true);
    saveLabel(obj, () => setPageLoading(false)).then(res => {
      if (!res.success) {
        message.error(res.resultMsg);
      } else {
        message.success(`${record.statusCd === '1000' ? '失效' : '生效'}标签成功`);
        refresh();
      }
    });
  };

  const onCategoryChange = e => {
    form.setFieldsValue({ categoryCode: e });
  };

  const onLabelFormClose = (type, newLabel) => {
    if (newLabel?.label) {
      const opts = [...categoryOpts];
      opts.push(newLabel);
      setCategoryOpts(opts);
    }
    if (type === 'confirm') {
      refresh();
      message.success(`${newLabel?.labelId ? '更新' : '创建'}标签成功`);
    }
    setFormDrawerVisible(false);
  };

  const columns = [
    {
      title: '标签大类',
      dataIndex: 'category',
      align: 'center',
    },
    {
      title: '大类编码',
      dataIndex: 'categoryCode',
      align: 'center',
    },
    {
      title: '标签小类',
      dataIndex: 'subcategory',
      align: 'center',
    },
    {
      title: '小类编码',
      dataIndex: 'subcategoryCode',
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'createDate',
      align: 'center',
      render: text => text && moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '状态',
      dataIndex: 'statusCd',
      align: 'center',
      render: text => {
        const eff = text === '1000';
        return <span style={{ color: eff ? 'green' : 'red' }}>{eff ? '有效' : '无效'}</span>;
      },
    },
    {
      title: '操作',
      align: 'center',
      render: (_, record) => (
        <div>
          <span>
            <a
              onClick={() => {
                handleEdit(record);
              }}
            >
              编辑
            </a>
            <Divider type="vertical" />
            {record.statusCd === '1000' && (
              <a
                onClick={() => {
                  updateStatus(record);
                }}
              >
                失效
              </a>
            )}
            {record.statusCd === '1100' && (
              <a
                onClick={() => {
                  updateStatus(record);
                }}
              >
                生效
              </a>
            )}
          </span>
        </div>
      ),
    },
  ];

  const formItemLayout = {
    wrapperCol: {
      span: 18,
    },
    labelCol: {
      span: 6,
    },
  };

  return (
    <>
      <Spin spinning={pageLoading}>
        <Card
          title="标签管理"
          className="cute"
          extra={(
            <div className={styles.extra}>
              <Button className="margin-left" type="primary" onClick={handleAdd}>
                新增
              </Button>
            </div>
          )}
        >
          <Form {...formItemLayout}>
            <Row>
              <Col span={6}>
                <Form.Item label="标签大类">
                  {getFieldDecorator(
                    'category',
                    {}
                  )(
                    <Select
                      showSearch
                      filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      allowClear
                      placeholder="请选择"
                      onChange={onCategoryChange}
                    >
                      {categoryOpts.map(e => <Select.Option key={e.value}>{e.label}</Select.Option>)}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="大类编码">{getFieldDecorator('categoryCode', {})(<Input disabled />)}</Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="标签小类">{getFieldDecorator('subcategory', {})(<Input placeholder="请输入" allowClear />)}</Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="小类编码">{getFieldDecorator('subcategoryCode', {})(<Input placeholder="请输入" allowClear />)}</Form.Item>
              </Col>
            </Row>
            <Row>
              <Col offset={18} span={6} style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '8px' }}>
                <Button type="primary" className="margin-right" onClick={onSearch}>
                  查询
                </Button>
                <Button type="default" onClick={reset}>
                  重置
                </Button>
              </Col>
            </Row>
          </Form>
          <SlickTable rowKey={record => record.id} columns={columns} {...tableProps} />
        </Card>
        {
          formDrawerVisible && (
            <LabelFormDrawer
              record={curRecord}
              visible={formDrawerVisible}
              width={520}
              categoryOpts={categoryOpts}
              setPageLoading={setPageLoading}
              onClose={onLabelFormClose}
            />
          )
        }
      </Spin>
    </>
  );
}

export default Form.create()(LabelsManage);
