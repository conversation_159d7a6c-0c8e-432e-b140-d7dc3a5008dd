// 需要关注数据
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Card, Form, Row, Col, Select, Divider, Input, Button, DatePicker, Model } from 'antd';
import { useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import styles from './needFollowData.less';

const Index = props => {
  const { temp } = props ?? {};

  return (
    <div className={styles.card}>
      <Card
        title="需要关注数据"
        className="cute"
      />
    </div>
  );
};

export default Index;
