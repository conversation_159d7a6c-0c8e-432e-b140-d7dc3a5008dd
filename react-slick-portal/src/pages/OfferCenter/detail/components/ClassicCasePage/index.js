import React from 'react';
import {
  Table, Button,
} from 'antd';
import { connect } from 'dva';
import usePageTable from '@/hooks/usePageTable';
import { getMaterialList } from '@/services/detail';
import handlerDownload from '@/utils/file';
import style from './ClassicCasePage.module.less';

const PAGE_SIZE = 10;

const ClassicCasePage = props => {
  const { offerInfo } = props;
  const { offerNbr } = offerInfo;
  const {
    list, pageNo, setPageNo, total, loading,
  } = usePageTable({ pageSize: PAGE_SIZE, pageParamsKey: { index: 'pageNum', size: 'pageSize' }, defaultParams: { offerCode: offerNbr, knowledgeType: '13' }, method: getMaterialList });

  const columns = [
    {
      title: '案例名称',
      dataIndex: 'name',
      width: '20%',
    },
    {
      title: '归属地市',
      dataIndex: 'cityName',
      width: '10%',
    }, {
      title: '所属行业',
      dataIndex: 'industryName',
      ellipsis: true,
      width: '10%',
    }, {
      title: '下载量',
      dataIndex: 'downloadCount',
      width: '10%',
    }, {
      title: '案例描述',
      dataIndex: 'descSimple',
      width: '40%',
    }, {
      title: '操作',
      render: record => (
        <div className={style.actions}>
          <Button
            type="link"
            size="small"
            onClick={() => handlerDownload(record)}
          >
            下载
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className={style.wrap}>
      <div className={style.case}>
        <h2 className={style.title}>典型案例</h2>
        <Table
          rowKey="id"
          bordered
          className={style.table}
          columns={columns}
          dataSource={list}
          loading={loading}
          pagination={{
            current: pageNo,
            pageSize: PAGE_SIZE,
            total,
            showTotal: () => `共${total}条`,
            onChange: setPageNo,
            showQuickJumper: true,
          }}
        />
      </div>
    </div>
  );
};

export default connect(state => ({
    ...state.offerDetail,
  }))(ClassicCasePage);
