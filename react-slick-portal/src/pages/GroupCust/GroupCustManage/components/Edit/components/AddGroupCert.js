import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Col, DatePicker, Drawer, Form, Input, message, Modal, Row, Select, Spin, Switch, Icon, Popover } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import styles from '@/pages/OrgManage/LabelsManage/styles.less';
import {
  GROUP_CUST_TYPE_CODE,
  custTypeAndCertTypeRules,
  IDEN_CARD_TYPE,
  // CM_OPERATOR_MODE,
  // GROUP_CARD_TYPE,
} from '@/utils/consts';
import GroupFileUpload from '@/components/GroupFileUpload';
import TakePhoto from '@/busiComponents/TakePhoto';
import {
  disableEndDate,
  disableStartDate,
  validateHandleIdenNbr,
  // validateEmail,
  // validatePhone,
  validateIdenAddressByIdenType,
  validateIdenNameByIdenType,
  validateIdenNbr,
} from '@/utils/validator';
import Scan from '@/busiComponents/Scan';
import { businessCheck, queryEnterpriseDetail } from '@/pages/GroupCust/GroupCustManage/services';
// import { Publisher } from '@/utils/PubSubHelper';
import { queryDisticts, addFileVerification, queryCertInfo, queryhasPriv } from '../../../services';

const { Item: FormItem } = Form;
// const PubEventName = 'AddGroupCert';

// eslint-disable-next-line no-unused-vars
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const popTip = (
  <>
    <div>营业执照、组织机构代码证、事业单位证书、统一社会代码证、个人有效证件等类型上传，证件附件格式必须为JPG格式</div>
    <div>图片短边像素必须700以上，文件大小在100KB以上3M以下</div>
  </>
);

const AddGroupCert = props => {
  const { visible, onCancel, form, dispatch, onValuesChange, currentFormFieldState } = props;
  const { getFieldDecorator, setFieldsValue, getFieldValue } = form;
  const [groupCerFiltertList, setGroupCertFilterList] = useState([{ code: 200002, name: '18位统一社会信用代码（除营业执照）' },
    { code: 200009, name: '集团单位组织机构代码证' }]);

  const [saveButtonVisible, setSaveButtonVisible] = useState(false);
  const [checkButtonVisible, setCheckButtonVisible] = useState(true);
  const [disInfos, setDisInfos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [drawerHandler, setDrawerHandler] = useState(false);

  /**
   * 修改客户类型级联修改证件类型列表
   * @param custType
   */
  const handleChangeCertType = custType => {
    // 清除当前选择
    setFieldsValue({
      idenTypeCode: undefined,
    });
    // 重置列表
    const rules = custTypeAndCertTypeRules[custType];
    if (rules) {
      setGroupCertFilterList(rules);
    }
  };

  const handleChangeCertEff = val => {
    if (val) {
      form.setFieldsValue({
        idenExpDate: moment('2099-12-31'),
      });
    } else {
      form.setFieldsValue({
        idenExpDate: null,
      });
    }
  };
  // 拍照回调
  // eslint-disable-next-line no-unused-vars
  const handleTakePhoto = () => {
    form.setFieldsValue({});
  };
  // 经办人切换证件类型
  // eslint-disable-next-line no-unused-vars
  const changeOpTypeCode = val => {
    // $('#groupInfo_AGENT_LICENCE_NO').attr('datatype', val === '100001' ? 'pspt' : '');
  };

  // 权限判断
  const hasPriv = () => {
    const idList = [
      {
        moduleName: 'addCert',
        id: '52000016', // 新增 证件拍照信息 能否修改 待确定
      },
      {
        moduleName: 'addHandle',
        id: '56000025', // 新增证件经办人证件号、经办人、联系地址 有权限可以修改，没权限只能扫  待确定
      },
    ];
      // id:52000017 跳过在线比对的权限
    idList.forEach(async e => {
      const resp = await queryhasPriv(e.id);
  
      // 有权限拍照可以不是必填
      if (resp.resultCode === 'TRUE' && resp.resultObject === 'true' && e.id === '52000016') {
        dispatch({
          type: 'AddGroupCust/updataFormStateAll',
          payload: {
            photo: { disabled: false },
          },
        });
      }
  
      // 有权限可以修改 经办人的证件号、经办人、联系地址
      if (resp.resultCode === 'TRUE' && resp.resultObject === 'true' && e.id === '56000025') {
        dispatch({
          type: 'AddGroupCust/updataFormStateAll',
          payload: {
            operatorName: { disabled: false },
            operatorAddress: { disabled: false },
          },
        });
      }
    });
  };

  useEffect(() => {
    queryDisticts().then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.resultInfo) {
        setDisInfos(resultObject?.rspParam?.busiInfo?.resultInfo || []);
      } else {
        message.error(resultMsg);
      }
    });
    hasPriv();
  }, []);

  /**
   * 企业详细信息查询
   */
  const queryEnterprise = grpIdenInfo => {
    const { IDEN_NAME, IDEN_TYPE_CODE, IDEN_NR } = grpIdenInfo ;
    const grpIdenType = IDEN_TYPE_CODE;
    const grpIdenNr = IDEN_NR;
    const grpIdenName = IDEN_NAME;
    // 300000: '军队代码',
    // 300001: '有偿服务许可证',
    // 300009: '单位证明',
    // 300010: '个人有效证件',
    // 关于政企客户行业验真改造需求,BSCNM_FUNC_20221130_0009  以下证件类型不致信
    if (IDEN_TYPE_CODE === 300009 || IDEN_TYPE_CODE === 300001 || IDEN_TYPE_CODE === 300010) {
      // 把输入框可输入
      dispatch({
        type: 'AddGroupCust/updataFormStateAll',
        payload: {
          GROUP_LICENCE_NAME: { disabled: false }, // 集团证件 --证件名称
          GROUP_LICENCE_NO: { disabled: false }, // 集团证件 -- 证件号码
          GROUP_LICENCE_ADDRESS: { disabled: false }, // 集团证件 -- 生产经营具体地址
          GROUP_ORG_CODE: { disabled: false }, // 集团证件 -- 组织机构代码
          GROUP_NAME: { disabled: false }, // 基本信息 -- 集团名称
          JURISTIC_NAME: { disabled: false }, // 基本信息 -- 法人名称
          REG_MONEY: { disabled: false }, // 基本信息 -- 注册资金
          buIndustrySub: { disabled: false }, // 基本信息 -- 政企客户行业类型
          REGMANADEPARTMENT: { disabled: false }, // 基本信息 -- 登记管理部门
        },
      });
      return;
    }

    // 如果为军队代码证件，不致信，行业门类固定国防
    if (IDEN_TYPE_CODE === 300000) {
      const paramsForm = {
        CALLING_AREA: '13100',
      };
      form.setFieldsValue(paramsForm);
      // 更改状态
      dispatch({
        type: 'AddGroupCust/updataFormState',
        payload: {
          formName: 'CALLING_AREA',
          state: { disabled: true, visible: true },
        },
      });
      // todo: changeCALLING_AREA_A(); 草稿？
      return;
    }
    form.setFieldsValue({ CALLING_AREA: undefined });
    // 把输入框禁用
    const formState = {
      GROUP_LICENCE_NAME: { disabled: true }, // 集团证件 --证件名称
      GROUP_LICENCE_NO: { disabled: true }, // 集团证件 -- 证件号码
      GROUP_LICENCE_ADDRESS: { disabled: true }, // 集团证件 -- 生产经营具体地址
      GROUP_ORG_CODE: { disabled: true }, // 集团证件 -- 组织机构代码
      GROUP_NAME: { disabled: true }, // 基本信息 -- 集团名称
      JURISTIC_NAME: { disabled: true }, // 基本信息 -- 法人名称
      REG_MONEY: { disabled: true }, // 基本信息 -- 注册资金
      buIndustrySub: { disabled: true }, // 基本信息 -- 政企客户行业类型
      REGMANADEPARTMENT: { disabled: true }, // 基本信息 -- 登记管理部门
      CALLING_AREA: { disabled: false }, // 分类信息 -- 行业门类
    };
    dispatch({
      type: 'AddGroupCust/updataFormStateAll',
      payload: formState,
    });

    // 查询企业详细信息
    let keyType = '1';
    let searchKey;
    if (['200002', '200010'].includes(grpIdenType)) {
      keyType = '3';
      searchKey = grpIdenNr;
    } else {
      keyType = '1';
      searchKey = grpIdenName;
    }
    const params = {
      keyType,
      searchKey,
    };
    setLoading(true);
    queryEnterpriseDetail(params).then(res => {
      // eslint-disable-next-line no-console
      console.log('新增证件，查询企业信息详情res==', res);
      setLoading(false);

      const { resultCode } = res;
      const { resultObject } = res;
      // resultObject = {
      //   busiInfo: {
      //     ecDetailInfo: {
      //       buIndustry: '互联网-省专单位',
      //       entName: '2025测试集客AA百六十六',
      //       entInCome: '',
      //       opTo: '2053-10-19',
      //       hasDranchoffice: '0',
      //       buIndustrySubID: '151',
      //       industry: 'C',
      //       registeredCountry: '新城区',
      //       type: '1',
      //       actAddr: '',
      //       buIndustrySub: '互联网-省专',
      //       entType: '有限责任公司分公司（非自然人投资或控股的法人独资）',
      //       engName: '',
      //       empNum: '1042',
      //       orgCode: '701486373',
      //       regCap: '',
      //       registeredCity: '呼和浩特市',
      //       tel: '***********',
      //       isAbnormaloperation: '0',
      //       coordinateAddr: '东经：111.675911 北纬：40.815415',
      //       opsScope: '在内蒙古自治区经营基础电信业务（具体经营范围以许可证为准）；经营增值电信业务（具体经营范围以许可证为准）；从事移动通信、ＩＰ电话和因特网等网络设计、投资和建设；移动通信、ＩＰ电话和因特网设施的安装、工程施工和维修；经营与移动通信、ＩＰ电话和因特网业务相关的系统集成、漫游结算清算、技术开发、技术服务、设备的销售，以及其他电信及信息服务；出售、出租移动电话终端设备；ＩＰ电话设备、因特网设备及其配件，并提供售后服务；业务培训、会议服务，租赁业务；设计、制作、发布代理各类广告；互联网零售；经营互联网生活服务平台；计算机软件开发、销售；日用百货、电子产品、智能设备、智能穿戴设备销售；票务代理服务；代收居民水电费及其他费用',
      //       industrySub: '16',
      //       regManaDepartment: '工商',
      //       opFrom: '2004-10-20',
      //       email: '<EMAIL>',
      //       registeredProvince: '内蒙古自治区',
      //       isWrongfulFaith: '0',
      //       address: '内蒙古自治区呼和浩特市新城区呼伦南路5号',
      //       wSite: '',
      //       apprDate: '2025-03-19',
      //       uniscid: '11115252220116699653',
      //       esDate: '2004-10-20',
      //       isDishonesty: '0',
      //       regOrg: '呼和浩特市新城区市场监督管理局',
      //       name: '王大大',
      //       entStatus: '在营（开业）企业',
      //       dataSource: '1',
      //       CALLING_AREA: '11000',
      //       CALLING_AREA_A: '12378',
      //     },
      //     reqSerialNo: '47120250512214410875',
      //     resCode: '00',
      //   },
      // };
      if (resultCode === 'TRUE' && resultObject?.busiInfo?.ecDetailInfo) {
        const { ecDetailInfo, reqSerialNo } = resultObject?.busiInfo;
        const { resCode } = resultObject?.busiInfo;
        const { param } = resultObject?.busiInfo;
        if (param) {
          message.warn(param);
          return;
        }

        if (resCode === '00' && ecDetailInfo) {
          dispatch({
            type: 'AddGroupCust/updataOther',
            payload: { needOaAudit: false },
          });
          const {
            entName,
            orgCode,
            uniscid,
            address,
            entType,
            entInCome,
            tel,
            name,
            regCap,
            registeredCity,
            registeredCountry,
            buIndustry,
            buIndustrySub,
            empNum,
            regManaDepartment,
            CALLING_AREA,
            CALLING_AREA_A,
          } = ecDetailInfo;
          
          if (CALLING_AREA && CALLING_AREA_A) {
            form.setFieldsValue({
              CALLING_AREA_INFO: [CALLING_AREA, CALLING_AREA_A],
              CALLING_AREA_A,
              CALLING_AREA,
            });
            // dispatch({
            //   type: 'AddGroupCust/updataFormStateAll',
            //   payload: { CALLING_AREA_INFO: { disabled: true } },
            // });
          }

          // 企业名称 GROUP_NAME  oldName:GROUP_NAME_OLDS
          if (entName) {
            const paramsFillForm = {
              GROUP_NAME: entName,
            };
            form.setFieldsValue(paramsFillForm);
            dispatch({
              type: 'AddGroupCust/updataOther',
              payload: { GROUP_NAME_OLDS: entName },
            });
            // 『集团名称]
            onValuesChange(
              {
                'BaseInfo.GROUP_NAME': entName,
              },
              true
            );
          } else {
            // 没有值 集团名称允许输入
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { GROUP_NAME: { disabled: false } },
            });
          }
          // [是否分支机构] 回填
          // [企业标准名称] 回填
          if (IDEN_NAME === entName && IDEN_NAME !== '') {
            const paramsEntName = {
              IS_BRANCH: '0',
              ENT_NAME: '',
            };
            form.setFieldsValue(paramsEntName);
            onValuesChange(
              {
                'BaseInfo.IS_BRANCH': '0',
                'BaseInfo.ENT_NAME': '',
              },
              true
            );
          }

          // 组织机构代码
          if (orgCode) {
            const paramsFillForm = {
              GROUP_ORG_CODE: orgCode,
            };
            form.setFieldsValue(paramsFillForm);
            // [组织机构代码] 回填
            onValuesChange(
              {
                'CertInfo.GROUP_ORG_CODE': orgCode,
              },
              true
            );
          } else {
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { GROUP_ORG_CODE: { disabled: false } },
            });
          }

          // 证件号码
          if (uniscid) {
            const paramsFillForm = {
              GROUP_LICENCE_NO: uniscid,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'CertInfo.GROUP_LICENCE_NO': uniscid,
              },
              true
            );
          } else {
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { GROUP_LICENCE_NO: { disabled: false } },
            });
          }

          // 营业场所
          if (address) {
            const paramsFillForm = {
              GROUP_LIGROUP_LICENCE_ADDRESSCENCE_NO: address,
            };
            form.setFieldsValue(paramsFillForm);
            // 营业场所
            onValuesChange(
              {
                'CertInfo.GROUP_LICENCE_ADDRESS': address,
              },
              true
            );
          }

          // 集客企业类型
          if (entType) {
            const paramsFillForm = {
              JK_GROUP_TYPE: entType,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.JK_GROUP_TYPE': entType,
              },
              true
            );
          }

          // 集团年营业额
          if (entInCome) {
            const paramsFillForm = {
              YEAR_GAIN: entInCome,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.YEAR_GAIN': entInCome,
              },
              true
            );
          }

          // 企业联系电话
          if (tel) {
            const paramsFillForm = {
              GROUP_CONTACT_PHONE: tel,
            };
            form.setFieldsValue(paramsFillForm);
            // 企业联系电话
            onValuesChange(
              {
                'BaseInfo.GROUP_CONTACT_PHONE': tel,
              },
              true
            );
          }

          // 法人代表
          if (name) {
            const paramsFillForm = {
              JURISTIC_NAME: name,
            };
            form.setFieldsValue(paramsFillForm);
            // 企业联系电话
            onValuesChange(
              {
                'BaseInfo.JURISTIC_NAME': name,
              },
              true
            );
          } else {
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { JURISTIC_NAME: { disabled: false } },
            });
          }

          // 注册资本
          if (regCap) {
            const paramsFillForm = {
              REG_MONEY: regCap,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.REG_MONEY': regCap,
              },
              true
            );
          } else {
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { REG_MONEY: { disabled: false } },
            });
          }
          if (regCap == null || regCap === '' || typeof regCap === 'undefined') {
            message.warning('提示信息，该企业注册资金为空！');
          }
          // 注册地市
          if (registeredCity) {
            const paramsFillForm = {
              MGMT_DISTRICT: registeredCity,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'RegionInfo.MGMT_DISTRICT': registeredCity,
              },
              true
            );
          }

          // 注册区县
          if (registeredCountry) {
            const paramsFillForm = {
              MGMT_COUNTY: registeredCountry,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'RegionInfo.MGMT_COUNTY': registeredCountry,
              },
              true
            );
          }

          // 政企客户行业门类
          if (buIndustry) {
            const paramsFillForm = {
              buIndustry,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.buIndustry': buIndustry,
              },
              true
            );
          }

          // 政企客户行业类型
          if (buIndustrySub) {
            const paramsFillForm = {
              buIndustrySub,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.buIndustrySub': buIndustrySub,
              },
              true
            );
          }

          // 企业员工数
          if (empNum) {
            const paramsFillForm = {
              ENT_EMPLOYEE_COUNT: empNum,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.ENT_EMPLOYEE_COUNT': empNum,
              },
              true
            );
          }

          // 登记管理部门
          if (regManaDepartment) {
            if (regManaDepartment === '机构编制' || regManaDepartment === '中央军委改革和编制办公室') {
              dispatch({
                type: 'AddGroupCust/updataFormStateAll',
                payload: { buIndustrySub: { disabled: false } },
              });
            }
            const paramsFillForm = {
              REGMANADEPARTMENT: regManaDepartment,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.REGMANADEPARTMENT': regManaDepartment,
              },
              true
            );
          } else {
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { buIndustrySub: { disabled: false }, REGMANADEPARTMENT: { disabled: false } },
            });
          }
        } else if (resCode === '02') {
          message.warning(resultObject.busiInfo.rspMsg);
          dispatch({
            type: 'AddGroupCust/updataFormStateAll',
            payload: {
              CALLING_AREA_A: { disabled: false },
              CALLING_AREA: { disabled: false },
              CALLING_AREA_INFO: { disabled: false }, // 行业可以输入
              buIndustrySub: { disabled: false }, // 登记管理部门
              REGMANADEPARTMENT: { disabled: false }, // 基本信息 -- 登记管理部门
              GROUP_NAME: { disabled: false }, // 集团名称允许输入
              GROUP_ORG_CODE: { disabled: false }, // 组织机构代码
              GROUP_LICENCE_NO: { disabled: false }, // 证件号码
              JURISTIC_NAME: { disabled: false }, // 法人代表
              REG_MONEY: { disabled: false }, // 注册资本
            },
          });
        } else {
          dispatch({
            type: 'AddGroupCust/updataFormStateAll',
            payload: {
              buIndustrySub: { disabled: false },
              REGMANADEPARTMENT: { disabled: false },
              GROUP_NAME: { disabled: false },
              GROUP_ORG_CODE: { disabled: false },
              GROUP_LICENCE_NO: { disabled: false },
              JURISTIC_NAME: { disabled: false },
              REG_MONEY: { disabled: false },
            },
          });
          message.warning(`企业详细信息查询失败，无符合条件记录。继续建档将提交到OA审批！${reqSerialNo ? `请求流水：${reqSerialNo}` : ''}`);
        }
      } else {
        message.warning(resultObject.busiInfo.rspMsg);
        dispatch({
          type: 'AddGroupCust/updataFormStateAll',
          payload: {
            buIndustrySub: { disabled: false },
            REGMANADEPARTMENT: { disabled: false },
            GROUP_NAME: { disabled: false },
            GROUP_ORG_CODE: { disabled: false },
            GROUP_LICENCE_NO: { disabled: false },
            JURISTIC_NAME: { disabled: false },
            REG_MONEY: { disabled: false },
          },
        });
      }
    });
  };

  // 证件信息回填
  const handleCallback = () => {
    const ecInfo = {
      IDEN_NAME: form.getFieldValue('idenName'),
      IDEN_TYPE_CODE: form.getFieldValue('idenTypeCode'),
      IDEN_NR: form.getFieldValue('idenNr'),
    };
    const paramsForm = {
      GROUP_BUSI_LICENCE_TYPE: `${form.getFieldValue('idenTypeCode')}`,
      EcCustLicenceInfo: form.getFieldValue('ecCustLicenceInfoPop'),
      GROUP_LICENCE_NO: form.getFieldValue('idenNr'),
      GROUP_LICENCE_NAME: form.getFieldValue('idenName'),
      GROUP_LICENCE_ADDRESS: form.getFieldValue('idenAddress'),
      JURISTIC_NAME: form.getFieldValue('ADD_JURISTIC_NAME'),
      REG_MONEY: '0',
      AGENT_LICENCE_NAME: form.getFieldValue('operatorName'), // 经办人 名称
      AGENT_LICENCE_NO: form.getFieldValue('operatorIdenNr'), // 经办人 身份证号码
      AGENT_LICENCE_ADDRESS: form.getFieldValue('operatorAddress'), // 经办人 地址
      AGENT_IMAGE: form.getFieldValue('partyId'), // 经办人拍照
    };
    form.setFieldsValue(paramsForm);
    // 企业详细信息查询
    queryEnterprise(ecInfo);
    
    onCancel();
  };

  // 获取证件信息并回填
  const queryIdentInfo = async e => {
    if (!e.fileId) {
      message.error('请上传证件扫描附件');
      return;
    }
    const identType = form.getFieldValue('idenTypeCode');
    const params = {
      FILE_ID: e.fileId,
      IDENT_TYPE: identType,
    };
    if (identType !== 200010 && identType !== 200009 && identType !== 300002 && identType !== 200002 && identType !== 300010) {
      return;
    }
    setLoading(true);
    const resp = await queryCertInfo(params);
    setLoading(false);
    if (resp.resultCode !== 'TRUE') {
      message.error(resp.resultMsg);
      form.setFieldsValue({ ecCustLicenceInfoPop: [] });
      return;
    }
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspCode === '00') {
      const { identInfo } = resp.resultObject;
      if (identType !== 300010) {
        form.setFieldsValue({
          idenNr: identInfo.uniscId,
          idenName: identInfo.name,
          idenAddress: identInfo.address,
          ADD_JURISTIC_NAME: identInfo.legalPersonName,
        });
      } else {
        form.setFieldsValue({
          idenNr: identInfo.idCardNumber,
          idenName: identInfo.name,
          idenAddress: identInfo.address,
          ADD_JURISTIC_NAME: identInfo.legalPersonName,
          idenEffDate: identInfo.validDateBegin,
          idenExpDate: identInfo.validDateEnd,
        });
      }
      dispatch({
        type: 'AddGroupCust/updataOtherData',
        payload: { SCAN_RESULT: '1' }, // 记录扫描结果
      });
    } else if (resp.resultObject.rspCode === '99') {
      dispatch({
        type: 'AddGroupCust/updataOtherData',
        payload: {
          SCAN_DESCRIPTION: '证件查询失败',
          SCAN_RESULT: '0',
        }, // 记录扫描结果
      });
      message.error(`证件查询失败，请继续使用手工录入！请求流水：${resp.reqSerialNo}`);
    } else {
      dispatch({
        type: 'AddGroupCust/updataOtherData',
        payload: {
          SCAN_DESCRIPTION: resp.resultMsg,
          SCAN_RESULT: '0',
        }, // 记录扫描结果
      });
      message.error(`${resp.resultMsg}，请求流水：${resp.reqSerialNo}`);
    }
  };
  
  // 附件返回
  const handleBackFile = async e => {
    const identType = form.getFieldValue('idenTypeCode');
    const params = {
      fileId: e.fileId,
      fileExtension: e.name.substring(e.name.lastIndexOf('.') + 1),
    };
    if (identType === 200006 || identType === 200009 || identType === 300002 || identType === 200002 || identType === 300010) {
      const resp = await addFileVerification(params);
      if (resp.resultObject.RESULT === 'false') {
        message.error('错误信息, 获取证件照片信息像素太低!');
        return;
      } if (resp.resultObject.RESULT === null) {
        message.error('错误信息!上传文件有误！请重新上传。');
        return;
      }
    }
    queryIdentInfo(e);
  };

  const handleOk = () => {
    const fields = ['operatorName', 'regionId', 'operatorIdenType', 'operatorIdenNr', 'partyId', 'operatorAddress', 'remarks'];

    const allEmpty = fields.every(field => !getFieldValue(field)); // 检查是否全部为空
    const someNotEmpty = fields.some(field => getFieldValue(field)); // 检查是否有非空值
    if (someNotEmpty && !allEmpty) {
      // 如果有非空值且不是全部为空
      const anyFieldEmpty = fields.some(field => !getFieldValue(field)); // 检查是否有空值
      if (anyFieldEmpty) {
        message.error('请填写全部经办人信息');
        return;
      }
    }
    form.validateFields(['custType', 'ecCustLicenceInfoPop', 'idenAddress', 'ADD_JURISTIC_NAME', 'idenTypeCode', 'idenName', 'photo'], (err, values) => {
      if (!err) {
        const { entResult } = values;
        if (entResult !== '1') {
          Modal.confirm({
            content: '此证件未通过企业致信核验，继续提交将无法办理普通开户NG版业务，是否继续？',
            onOk() {
              handleCallback();
            },
          });
        } else {
          handleCallback();
        }
      }
    });
  };

  // 企业致信
  const handleCheck = () => {
    form.validateFields(['ADD_JURISTIC_NAME', 'idenNr', 'idenTypeCode', 'idenName'], (err, values) => {
      if (!err) {
        const { ADD_JURISTIC_NAME, idenNr, idenName } = values;
        let { idenTypeCode } = values;
        idenTypeCode = idenTypeCode === 200010 ? 200002 : idenTypeCode;

        setLoading(true);
        const params = {
          uniscId: idenNr,
          legalPersonName: ADD_JURISTIC_NAME,
          entName: idenName,
          idenTypeCode,
        };
        businessCheck(params)
          .then(res => {
            const { resultCode, resultObject, resultMsg } = res;
            if (resultCode === 'TRUE') {
              if (resultObject.veriResut === '0') {
                message.error(`该证件企业致信未通过！流水：${resultObject.reqSerialNo}`);
              } else if (resultObject.veriResut === '1') {
                message.success(`该证件企业致信通过，流水：${resultObject.reqSerialNo}`);
              } else {
                message.success(`该证件类型不支持企业致信，流水：${resultObject.reqSerialNo}`);
              }
            } else {
              message.error(resultMsg);
            }
            form.setFieldsValue({
              entResult: resultObject.veriResut,
            });
          })
          .always(() => {
            setLoading(false);
            setSaveButtonVisible(true);
            setCheckButtonVisible(false);
          });
      }
    });
  };

  // 切换证件类型
  // 300002 事业单位法人证书
  // 200002 18位统一社会信用代码（除营业执照）
  // 200009 集团单位组织机构代码证
  const idenTypeCodeOnChange = val => {
    if (val === 200006 || val === 300002 || val === 200002 || val === 200009) { // 需要企业致信
      setSaveButtonVisible(false);
      setCheckButtonVisible(true);
    } else {
      setSaveButtonVisible(true);
      setCheckButtonVisible(false);
    }
  };
  // useEffect(() => {
  //   // 创建发布者实例
  //   const publisher = new Publisher(PubEventName);

  //   return () => {
  //     publisher.destroy();
  //   };
  // }, []);
  const handleOpenBottom = () => (
    <Button type="primary" onClick={() => setDrawerHandler(!drawerHandler)}>{drawerHandler ? '收起' : '展开'}</Button>
  );
  return (
    visible ? (
      <Drawer
        title="新增集团证件"
        destroyOnClose
        bodyStyle={{
          paddingBottom: '70px',
        }
    }
        width={620}
        visible={visible}
        onClose={onCancel}
      >
        <Spin spinning={loading}>
          <Form layout="vertical">
            <Card className="cute" title="集团证件信息" bordered={false}>
              <Row gutter={16}>
                {/* 客户类型 */}
                <Col span={12}>
                  <FormItem label="客户类型">
                    {getFieldDecorator('custType', {
                      initialValue: '100001',
                      rules: [{ required: true, message: '客户类型不能为空' }],
                    })(
                      <Select
                        allowClear
                        showSearch
                        onChange={handleChangeCertType}
                        filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                        placeholder="请选择"
                      >
                        {Object.keys(GROUP_CUST_TYPE_CODE).map(key => (
                          <Select.Option key={key} value={key}>
                            {GROUP_CUST_TYPE_CODE[key]}
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </FormItem>
                </Col>
                {/* 证件类型 */}
                <Col span={12}>
                  <FormItem label="证件类型">
                    {getFieldDecorator('idenTypeCode', {
                      initialValue: 200009,
                      rules: [{ required: true, message: '证件类型不能为空' }],
                    })(
                      <Select
                        allowClear
                        showSearch
                        onChange={idenTypeCodeOnChange}
                        filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                        placeholder="请选择"
                      >
                        {
                        groupCerFiltertList.map(e => (
                          <Select.Option key={e.code} value={e.code}>
                            {e.name}
                          </Select.Option>
                        ))
                      }
                      </Select>
                    )}
                  </FormItem>
                </Col>
              </Row>
              <Row gutter={16}>
                {/* 证件扫描附件 */}
                <Col span={12}>
                  <FormItem
                    label="证件扫描附件"
                  >
                    {getFieldDecorator('ecCustLicenceInfoPop', {
                      rules: [{ required: true, message: '证件扫描附件不能为空' }],
                    })(
                      <GroupFileUpload
                        accept=".jpg,.JPG"
                        fileType="1"
                        typeFlag="addEcCustLicenceInfo"
                        callBackFile={e => handleBackFile(e)}
                        extraIcon={(
                          <Popover
                            content={popTip}
                            onClick={e => {
                              e.stopPropagation();
                            }}
                            placement="right"
                            trigger="hover"
                          >
                            <Icon
                              type="question-circle"
                              onClick={e => {
                                e.stopPropagation();
                              }}
                              style={{ 'margin-left': '5px' }}
                            />
                          </Popover>
                      )}
                      />

                    )}
                  </FormItem>
                </Col>
                {/* 证件号码 */}
                <Col span={12}>
                  <FormItem label="证件号码">
                    {getFieldDecorator('idenNr', {
                      rules: [
                        { validator: validateIdenNbr(getFieldValue('idenTypeCode')) },
                      ],
                    })(<Input />)}
                  </FormItem>
                </Col>
              </Row>
              <Row gutter={16}>
                {/* 客户名称 */}
                <Col span={12}>
                  <FormItem label="客户名称">
                    {getFieldDecorator('idenName', {
                      rules: [
                        { required: true, message: '客户名称不能为空' },
                        { validator: validateIdenNameByIdenType(getFieldValue('idenTypeCode')) },
                      ],
                    })(<Input />)}
                  </FormItem>
                </Col>
                {/* 证件地址 */}
                <Col span={12}>
                  <FormItem label="证件地址">
                    {getFieldDecorator('idenAddress', {
                      rules: [
                        { required: true, message: '证件地址不能为空' },
                        { validator: validateIdenAddressByIdenType(getFieldValue('idenTypeCode')) },
                      ],
                    })(<Input />)}
                  </FormItem>
                </Col>
              </Row>
              <Row gutter={16}>
                {/* 法人代表姓名 */}
                <Col span={12}>
                  <FormItem label="法人代表姓名">
                    {getFieldDecorator('ADD_JURISTIC_NAME', {
                      rules: [{ required: true, message: '法人代表姓名不能为空' }],
                    })(<Input />)}
                  </FormItem>
                </Col>
                {/* 证件是否长期有效 */}
                <Col span={12}>
                  <FormItem label="证件是否长期有效">
                    {getFieldDecorator('denLongEff', {
                      valuePropName: 'checked',
                    })(<Switch checkedChildren="是" unCheckedChildren="否" onChange={handleChangeCertEff} />)}
                  </FormItem>
                </Col>
              </Row>
              <Row gutter={16}>
                {/* 证件有效日期 */}
                <Col span={12}>
                  <FormItem label="证件生效日期">
                    {getFieldDecorator(
                      'idenEffDate',
                      {}
                    )(<DatePicker format="YYYY-MM-DD" disabledDate={disableStartDate(form.getFieldValue('idenExpDate'))} placeholder="生效时间" style={{ width: '100%' }} />)}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="证件失效日期">
                    {getFieldDecorator(
                      'idenExpDate',
                      {}
                    )(
                      <DatePicker
                        disabled={getFieldValue('denLongEff')}
                        disabledDate={disableEndDate(form.getFieldValue('idenEffDate'))}
                        format="YYYY-MM-DD"
                        placeholder="失效时间"
                        style={{ width: '100%' }}
                      />
                    )}
                  </FormItem>
                </Col>
              </Row>
              <Row gutter={16}>
                {/* 证件拍照 todo: hasPriv('52000016') required */}
                <Col span={12}>
                  <FormItem label="证件照片信息">
                    {getFieldDecorator('photo', { rules: [{ required: currentFormFieldState.photo.disabled, message: '法人代表姓名不能为空' }] })(
                      <TakePhoto
                        fillBack="addZjpz"
                        certType="00"
                        idenNbr={getFieldValue('idenNr')}
                        onCallback={({ FILE_ID }) => { form.setFieldsValue({ photo: FILE_ID }); }}
                      />
                    )}
                  </FormItem>
                </Col>
              </Row>
            </Card>
            <Card className="cute" title="经办人信息" extra={handleOpenBottom()}>
              {
              drawerHandler && (
                <div>
                  <p>&nbsp;如果填写经办人信息，请全部填写</p>
                  <Row gutter={16}>
                    {/* 经办人 */}
                    {/* todo: !hasPriv('56000025') required */}
                    {/* todo: operatorInfo.OPERATOR_NAME */}
                    <Col span={12}>
                      <FormItem label="经办人">
                        {getFieldDecorator('operatorName', {
                        })(<Input disabled={currentFormFieldState.operatorAddress.disabled} />)}
                      </FormItem>
                    </Col>
                    {/* 地市 */}
                    <Col span={12}>
                      <FormItem label="地市">
                        {getFieldDecorator('regionId', {
                        })(
                          <Select
                            allowClear
                            showSearch
                            filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                            placeholder="请选择"
                          >
                            {disInfos.map(({ REGION_ID, DISTRICT_NAME }) => (
                              <Select.Option key={REGION_ID} value={REGION_ID}>
                                {DISTRICT_NAME}
                              </Select.Option>
                            ))}
                          </Select>
                        )}
                      </FormItem>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    {/* 证件类型 */}
                    <Col span={12}>
                      <FormItem label="证件类型">
                        {getFieldDecorator('operatorIdenType', {
                          initialValue: '100001',
                        })(
                          <Select disabled placeholder="请选择" onChange={changeOpTypeCode}>
                            {Object.keys(IDEN_CARD_TYPE).map(key => (
                              <Select.Option key={key} value={key}>
                                {IDEN_CARD_TYPE[key]}
                              </Select.Option>
                            ))}
                          </Select>
                        )}
                      </FormItem>
                    </Col>
                    {/* 证件号码 */}
                    <Col span={12}>
                      {/*! hasPriv('56000025') disabled */}
                      <FormItem label="证件号码">
                        {getFieldDecorator('operatorIdenNr', {
                          rules: [
                            { validator: validateIdenNbr(getFieldValue('operatorIdenType')) },
                            { validator: validateHandleIdenNbr(getFieldValue('idenTypeCode'), getFieldValue('idenNr')) },
                          ],
                        })(
                          <Scan
                            onChange={() => {}}
                            onCallback={({ name, address, idenNr }) =>
                              form.setFieldsValue({
                                operatorName: name,
                                operatorAddress: address,
                                operatorIdenNr: idenNr,
                              })}
                          />
                        )}
                      </FormItem>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    {/* 经办人拍照 */}
                    <Col span={12}>
                      <FormItem label="经办人拍照">
                        {getFieldDecorator('partyId', {
                        })(<TakePhoto
                          fillBack="addJbrpz"
                          certType="04"
                          idenNbr={getFieldValue('operatorIdenNr')}
                          onCallback={({ FILE_ID }) =>
                            form.setFieldsValue({ partyId: FILE_ID })}
                        />)}
                      </FormItem>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    {/* 联系地址 */}
                    {/* hasPriv('56000025') disabled */}
                    <Col span={24}>
                      <FormItem label="联系地址">
                        {getFieldDecorator('operatorAddress', {
                          rules: [{ validator: validateIdenAddressByIdenType(getFieldValue('operatorIdenType')) }],
                        })(<Input disabled={currentFormFieldState.operatorAddress.disabled} />)}
                      </FormItem>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    {/* 联系电话 */}
                    {/* <Col span={12}>
                      <FormItem label="联系电话">
                        {getFieldDecorator('operatorNumber', {
                          rules: [{ validator: validatePhone }],
                        })(<Input />)}
                      </FormItem>
                    </Col> */}
                    {/* 邮箱地址 */}
                    {/* <Col span={12}>
                      <FormItem label="邮箱地址">
                        {getFieldDecorator('operatorEmail', {
                          rules: [{ validator: validateEmail }],
                        })(<Input />)}
                      </FormItem>
                    </Col> */}
                  </Row>
                  <Row gutter={16}>
                    {/* 备注 */}
                    <Col span={24}>
                      <FormItem label="备注">
                        {getFieldDecorator('remarks', {
                        })(<Input.TextArea maxLength={500} />)}
                      </FormItem>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    {/* 经办描述 */}
                    {/* <Col span={24}>
                      <FormItem label="经办描述">
                        {getFieldDecorator('operatorDesc', {
                        })(<Input.TextArea maxLength={500} />)}
                      </FormItem>
                    </Col> */}
                  </Row>
                  <Row gutter={16}>
                    {/* 经办方式 */}
                    {/* <Col span={12}>
                      <FormItem label="经办方式">
                        {getFieldDecorator('operatorMode', {
                        })(
                          <Select
                            allowClear
                            showSearch
                            filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                            placeholder="请选择"
                          >
                            {Object.keys(CM_OPERATOR_MODE).map(key => (
                              <Select.Option key={key} value={key}>
                                {CM_OPERATOR_MODE[key]}
                              </Select.Option>
                            ))}
                          </Select>
                        )}
                      </FormItem>
                    </Col> */}
                  </Row>
                  <Row>
                    <Col hidden>
                      {/* 企业置信结果 */}
                      <FormItem label="企业置信结果">{getFieldDecorator('entResult', {})(<Input disabled />)}</FormItem>
                    </Col>
                  </Row>
                </div>
              )
            }
            </Card>
          </Form>
        </Spin>
        <div className={styles['form-drawer-bottom']}>
          {saveButtonVisible && (
          <Button
            loading={loading}
            style={{
              marginRight: 8,
            }}
            onClick={() => handleOk()}
            type="primary"
          >
            保存
          </Button>
          )}
          {checkButtonVisible && (
          <Button
            // loading={loading}
            style={{
              marginRight: 8,
            }}
            onClick={() => handleCheck()}
            type="primary"
          >
            企业致信
          </Button>
          )}

          <Button onClick={onCancel}>取消</Button>
        </div>
      </Drawer>
    ) : (null)
    
  );
};

export default connect(({ AddGroupCust }) => ({
  // groupCertList: AddGroupCust.groupCertList,
  groupCustTypeList: AddGroupCust.groupCustTypeList,
  currentFormFieldState: AddGroupCust.currentFormFieldState,
}))(Form.create()(AddGroupCert));
