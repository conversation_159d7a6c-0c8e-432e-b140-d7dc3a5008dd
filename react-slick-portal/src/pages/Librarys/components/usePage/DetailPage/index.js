/* eslint-disable global-require */
import React, { useState, useEffect } from 'react';
import { Ta<PERSON>, Tooltip, Divider, Form, Button, Typography } from 'antd';
import { connect } from 'dva';
import { getLibDetail } from '@/pages/Librarys/services';
import { addKlKnowledgeAction } from '@/pages/Librarys/services-use';
import { TYPE_VALUE } from '@/pages/Librarys/const';
import Comment from '../Comment';
import DetailInfo from '@/pages/Librarys/components/DetailInfo';
import styles from './DetailPage.module.less';
import commonStyles from '../common.module.less';

const DetailPage = ({ size, knowledgeInfoId = '', goToStep, fromHome, toTop, type, submit }) => {
  const [sceneGroupRel, setSceneGroupRel] = useState({});
  const [imageHeight, setImageHeight] = useState(300);
  const [knowledgeInfo, setKnowledgeInfo] = useState('');
  const [bannerHeight, setBannerHeight] = useState(300);

  // 设置应用场景的图片容器的高度，以保持图片宽高比例
  const setApplySceneImgScale = () => {
    setTimeout(() => {
      const applyScene = document.getElementById('applyScene');
      if (applyScene?.offsetWidth) {
        const imageWidth = Number(applyScene.offsetWidth) / 2;
        const height = imageWidth / 2;
        setImageHeight(height);
      }
    }, 0);
  };

  // 设置大背景图片容器的高度，以保持图片宽高比例
  const setBannerImgScale = () => {
    setTimeout(() => {
      const banner = document.getElementById('banner');
      if (banner?.offsetWidth) {
        // 1860px : 440px
        const height = Number(banner.offsetWidth) / 4.227272727;
        setBannerHeight(height);
      }
    }, 0);
  };

  useEffect(() => {
    // 表示组件是否卸载
    let unmount = false;
    const initData = async () => {
      const res = await getLibDetail(knowledgeInfoId);
      if (res && res?.resultCode === 'TRUE' && !unmount) {
        setSceneGroupRel(res?.resultObject?.sceneGroupRel);
        setKnowledgeInfo(res?.resultObject);

        setTimeout(() => {
          if (res?.resultObject?.bannerFile?.docNbr && !unmount) {
            const banner = document.getElementById('banner');
            if (banner) {
              banner.style.backgroundImage = `url("portal/FileStoreController/download.do?docNbr=${res?.resultObject?.bannerFile?.docNbr}")`;
            }
          }
        }, 200);
      }
    };

    initData();
    setTimeout(() => {
      toTop();
    }, 0);

    setTimeout(() => {
      if (!unmount) {
        addKlKnowledgeAction({ knowledgeInfoId });
      }
    }, 5000);

    return () => {
      unmount = true;
    };
  }, []);

  useEffect(() => {
    // 每次浏览器宽度变化，设置图片容器的高度，以保持图片宽高比例
    setApplySceneImgScale();
    setBannerImgScale();
  }, [size]);

  return (
    <div className={styles.wrap}>
      <div className={styles.innerWrap}>
        <Button
          className={styles.topButton}
          type="primary"
          onClick={() => {
            if (fromHome) {
              goToStep('1', {
                originalKey: TYPE_VALUE[type],
              });
            } else {
              goToStep('2');
              if (typeof submit === 'function') {
                submit();
              }
            }
          }}
        >
          返回
        </Button>
        <div
          id="banner"
          className={styles.banner}
          style={{
            height: `${bannerHeight}px`,
          }}
        >
          <Typography.Paragraph ellipsis={{ rows: 2 }}>
            <div className={styles.title}>
              <Tooltip placement="bottomLeft" title={knowledgeInfo.knowledgeName} mouseEnterDelay={1}>
                <div>{knowledgeInfo.knowledgeName}</div>
              </Tooltip>
            </div>
          </Typography.Paragraph>

          <Tooltip placement="bottomLeft" title={knowledgeInfo.knowledgeDesc} mouseEnterDelay={1}>
            <div className={styles.desc}>{knowledgeInfo.knowledgeDesc}</div>
          </Tooltip>
        </div>
        <Tabs
          defaultActiveKey="1"
          renderTabBar={(props, DefaultTabBar) => <DefaultTabBar {...props} className={styles.detailTabs} />}
          onTabClick={key => {
            if (key === '2') {
              setApplySceneImgScale();
            }
          }}
        >
          <Tabs.TabPane tab="基本信息" key="1">
            <DetailInfo type={type} knowledgeInfoId={knowledgeInfoId} fromUse visible onCancel={() => {}} />
          </Tabs.TabPane>
          {sceneGroupRel?.showFlag === '1' && (
            <Tabs.TabPane tab="应用场景" key="2">
              <div className={commonStyles.module} style={{ backgroundColor: 'white' }}>
                <div id="applyScene" className={styles.applyScene}>
                  {Array.isArray(sceneGroupRel?.sceneDisplayRecordVos) &&
                    sceneGroupRel?.sceneDisplayRecordVos.map((item, index) => (
                      <div key={item.docId}>
                        <div className={styles.smallCard} style={{ height: `${imageHeight}px` }}>
                          {index % 2 === 1 && (
                            <div
                              className={styles.photo}
                              style={{
                                backgroundImage: `url(${
                                  item.docNbr
                                    ? `portal/FileStoreController/download.do?docNbr=${item.docNbr}`
                                    : require('@/pages/Librarys/img/default-detail-apply.png')
                                })`,
                              }}
                            />
                          )}
                          <div className={styles.text}>
                            <Tooltip title={item.title} placement="bottomLeft" mouseEnterDelay={2}>
                              <div className={styles.title}>{item.title}</div>
                            </Tooltip>

                            <Tooltip title={item.desc} placement="bottomLeft" mouseEnterDelay={1}>
                              <Typography.Paragraph ellipsis={{ rows: 8 }}>{item.desc}</Typography.Paragraph>
                            </Tooltip>
                          </div>
                          {index % 2 === 0 && (
                            <div
                              className={styles.photo}
                              style={{
                                backgroundImage: `url(${
                                  item.docNbr
                                    ? `portal/FileStoreController/download.do?docNbr=${item.docNbr}`
                                    : require('@/pages/Librarys/img/default-detail-apply.png')
                                })`,
                              }}
                            />
                          )}
                        </div>
                        <Divider />
                      </div>
                    ))}
                </div>
              </div>
            </Tabs.TabPane>
          )}
        </Tabs>
        <div className={commonStyles.end} />
        <Comment knowledgeInfoId={knowledgeInfoId} />
        <div className={commonStyles.end} />
      </div>
    </div>
  );
};

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(DetailPage));
