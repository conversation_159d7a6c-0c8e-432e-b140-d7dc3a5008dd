import React, { useState } from 'react';
import { Modal, Form, Row, Col, Input } from 'antd';
import { ImageUploader } from '@/components/Uploader';
import style from './style.less';

const { Item } = Form;
const AddBannerModal = props => {
  const { visible, data = {} } = props;
  const { item = {} } = data;
  const { getFieldDecorator, validateFields } = props.form;
  const [images, setImages] = useState((data.item && data.item.picture) || []);

  const submitData = () => {
    validateFields((err, value) => {
      if (!err) {
        props.onOk(value)
      }
    })
  }

  const handleFileChange = file => {
    setImages(file.fileList);
    props.form.setFieldsValue({
      picture: file.fileList,
    });
  };

  return (
    <div>
      <Modal
        width='50%'
        bodyStyle={{ height: '450px', overflowY: 'auto' }}
        title={data.index || data.index === 0 ? '编辑轮播' : '新增轮播'}
        visible={visible}
        onCancel={() => props.onCancel()}
        onOk={() => { submitData() }}
      >
        <Form>
          <Row gutter={16}>
            <Col span={24}>
              <Item label='图片'>
                {getFieldDecorator('picture', {
                  initialValue: item.picture || '',
                  rules: [
                    { required: true, message: '不能为空' },
                  ],
                })(
                  <ImageUploader
                    title="上传图片"
                    maximum={1}
                    uploadedFileList={images}
                    onFileListChange={handleFileChange}
                    dimension={[1920, 535]}
                    accept={['jpg', 'png']}
                  />
                )}
                <span className={style.tips}>图片大小为1920*535px，不超过200k，格式为.png或.jpg</span>
              </Item>
            </Col>
            <Col span={24}>
              <Item label='主题'>
                {getFieldDecorator('title', {
                  initialValue: item.title || '',
                })(
                  <Input placeholder='请输入轮播主题，不超过10个字' maxLength={10} />
                )}
              </Item>
            </Col>
            <Col span={24}>
              <Item label='描述'>
                {getFieldDecorator('description', {
                  initialValue: item.description || '',
                })(
                  <Input placeholder='请输入' />
                )}
              </Item>
            </Col>
            <Col span={24}>
              <Item label='链接'>
                {getFieldDecorator('url', {
                  initialValue: item.url || '',
                  rules: [{
                    pattern: /^https?:\/\/([a-zA-Z0-9]+\.)+[a-zA-Z0-9]+/,
                    message: '请输入链接',
                  }]
                })(
                  <Input placeholder='请输入' />
                )}
              </Item>
            </Col>
            <Col span={12}>
              <Item label='按钮1'>
                {getFieldDecorator(`buttons[0].text`, {
                  initialValue: (item.buttons && item.buttons[0].text) || '',
                })(
                  <Input placeholder='请输入按钮名称' />
                )}
              </Item>
            </Col>
            <Col span={12}>
              <Item label={<></>} colon={false}>
                {getFieldDecorator('buttons[0].url', {
                  initialValue: (item.buttons && item.buttons[0].url) || '',
                  rules: [{
                    pattern: /^https?:\/\/([a-zA-Z0-9]+\.)+[a-zA-Z0-9]+/,
                    message: '请输入链接',
                  }]
                })(
                  <Input placeholder='请输入按钮链接' />
                )}
              </Item>
            </Col>
            <Col span={12}>
              <Item label='按钮2'>
                {getFieldDecorator('buttons[1].text', {
                  initialValue: (item.buttons && item.buttons[1].text) || '',
                })(
                  <Input placeholder='请输入按钮名称' />
                )}
              </Item>
            </Col>
            <Col span={12}>
              <Item label={<></>} colon={false}>
                {getFieldDecorator('buttons[1].url', {
                  initialValue: (item.buttons && item.buttons[1].url) || '',
                  rules: [{
                    pattern: /^https?:\/\/([a-zA-Z0-9]+\.)+[a-zA-Z0-9]+/,
                    message: '请输入链接',
                  }]
                })(
                  <Input placeholder='请输入按钮链接' />
                )}
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  )
}

export default Form.create()(AddBannerModal)
