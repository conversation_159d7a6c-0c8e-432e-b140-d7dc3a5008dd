import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Select,
  Icon,
  Input,
  Button,
  Divider,
  message,
  Table,
  Popconfirm,
} from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import BraftEditor from 'braft-editor';
import { useBoolean } from '@umijs/hooks';
import { getMessageList, queryGoodsType, getMessageDetails, publishMessage, depublishMessage, toppingMessage, deleteMessage } from '@/services/message';
import MessageAdd from './components/MessageAdd';
import MessageDetails from './components/MessageDetails'
import { MESSAGE_STATUSCD, MESSAGE_STATUSCD_DATA } from './const';
import usePageTable from '@/hooks/usePageTable';
import request from '@/utils/request';
import style from './index.less';
import stringToHtml from '@/utils/string';

const PAGE_SIZE = 10;

function Message({ size: { height }, form }) {
  const { getFieldDecorator } = form;
  const [attrArr, setAttrArr] = useState([]);
  const [braftEditor, setBraftEditor] = useState({
    visible: false,
    data: {
      braftEditor: BraftEditor.createEditorState(stringToHtml('')),
    },
  });
  const [mode, setMode] = useState('view');
  const [detail, setDetail] = useState({});
  const { state: visible, setTrue: show, setFalse: hide } = useBoolean(false);
  const [pictureInformation, setPictureInformation] = useState({});

  const queryByObjId = async (id) => {
    request(`portal/FileStoreController/queryByObjId.do?objId=${id}&objType=3000`, {
      method: 'get'
    }).then((res) => {
      if (res && res[0]) {
        setPictureInformation(res[0]);
      }
    })
  }
  // 编辑
  const setEdit = (e, targetRow) => {
    e.preventDefault();
    queryByObjId(targetRow.messageId);
    setMode('edit');
    setDetail(targetRow);
    show();
  }
  const {
    list, pageNo, setPageNo, total, loading, setLoading, onFormChange, onSearch, getTableData, onClearForm,
  } = usePageTable({ pageType: 1, pageParamsKey: { index: 'pageNum', size: 'pageSize' }, pageSize: PAGE_SIZE, method: getMessageList });
  // 删除
  const onDelete = async messageId => {
    // const data = { messageId: [messageId] };
    const data = [{ messageId }]
    const res = await deleteMessage(data);
    if (res.resultCode === "TRUE") {
      message.success('删除成功');
      getTableData();
    } else {
      message.error(res.resultMsg);
    }
  };

  // 查看详情
  const viewDetails = async (data) => {
    const dataUrl = await request(`portal/FileStoreController/queryByObjId.do?objId=${data.messageId}&objType=3000`, {
      method: 'get'
    })
    getMessageDetails({ messageId: data.messageId }).then(res => {
      if (res.resultObject) {
        setBraftEditor({
          visible: true,
          data: {
            ...res.resultObject,
            photoUrl: dataUrl[0].fileGetUrl,
            braftEditor: BraftEditor.createEditorState(stringToHtml(res.resultObject.messageContent)),
          },
        });
      }
    })
  }

  const columns = [
    {
      title: '置顶状态',
      dataIndex: 'topFlag',
      width: '5%',
      render: value => MESSAGE_STATUSCD_DATA[value]
    },
    {
      title: '类型',
      dataIndex: 'messageType',
      width: '8%',
      render: value => (attrArr[0] && attrArr[0].find(item => item.value === value) || {}).name,
    }, {
      title: '标题',
      dataIndex: 'messageTitle',
      ellipsis: true,
      width: '12%',
    }, {
      title: '副标题',
      dataIndex: 'messageSubTitle',
      ellipsis: true,
      width: '12%',
    }, {
      title: '内容',
      dataIndex: 'messageContent',
      width: '20%',
      ellipsis: true,
      render: value => BraftEditor.createEditorState(stringToHtml(value)).toText(),
    }, {
      title: '状态',
      dataIndex: 'statusCd',
      width: '9%',
      render: (text) => {
        let title;
        let icon;
        if (text === '1100') {
          title = '已发布';
          icon = <Icon type="check-circle" theme="twoTone" twoToneColor="#52c41a" className={style.iconClass} />;
        } else if (text === '1000') {
          title = '等待发布';
          icon = <Icon type="clock-circle" theme="twoTone" twoToneColor="grey" className={style.iconClass} />;
        } else if (text === '1200') {
          title = '已下线';
          icon = <Icon type="info-circle" theme="twoTone" twoToneColor="red" className={style.iconClass} />;
        }
        return (
          <span>
            {icon}
            {title}
          </span>
        );
      },
    }, {
      title: '创建时间',
      dataIndex: 'createDate',
      width: '10%',
      render: value => moment(value).format('YYYY-MM-DD HH:mm:ss'),
    }, {
      title: '发布时间',
      dataIndex: 'statusDate',
      width: '10%',
      render: value => (value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : null),
    }, {
      title: '操作',
      render: (value, record) => (
        <div className={style.actions}>
          <a
            onClick={() => viewDetails(record)}
          >
            查看
          </a>
          {
            record.statusCd !== '1100' &&
            <a
              onClick={e => setEdit(e, record)}
            >
              编辑
            </a>
          }
          {
            record.statusCd !== '1100' &&
            <Popconfirm
              title={`确定删除该${((attrArr[0] && attrArr[0].find(item => item.value === record.messageType)) || {}).name}吗？`}
              onConfirm={() => onDelete(record.messageId)}
            >
              <a>
                删除
              </a>
            </Popconfirm>
          }
          {record.statusCd !== '1100' &&
            <a
              onClick={async () => {
                // 调用发布接口
                setLoading(true);
                publishMessage({ messageId: record.messageId }).then(res => {
                  if (res.resultCode === 'TRUE') {
                    message.success('发布成功');
                    getTableData();
                  } else if(res.alertType == undefined) {
                    message.success(res.resultMsg);
                  }
                }).finally(() => {
                  setLoading(false);
                })
              }}
            >
              发布
            </a>
          }
          {
            !(record.statusCd === '1200' || record.statusCd === '1000') &&
            <a
              disabled={record.statusCd === '1200' || record.statusCd === '1000'}
              onClick={async () => {
                // 调用下线接口
                setLoading(true);
                depublishMessage({ messageId: record.messageId }).then(res => {
                  if (res.success) {
                    message.success('下线成功');
                    getTableData();
                    //alertType统一异常日志特殊处理
                  } else if(res.alertType == undefined){
                    message.error(res.resultMsg);
                  }
                }).finally(() => {
                  setLoading(false);
                })
              }}
            >
              下线
            </a>
          }
          {
            <a
              onClick={async () => {
                // 调用下线接口
                const data = {
                  messageId: record.messageId,
                  topFlag: record.topFlag === "1" ? '0' : '1',
                }
                const res = await toppingMessage(data);
                if (res.resultCode === 'TRUE') {
                  message.success(record.topFlag === "1" ? '取消置顶成功' : '置顶成功');
                  getTableData();
                }
              }}
            >
              {record.topFlag === "1" ? '取消置顶' : "置顶"}
            </a>
          }
        </div>
      ),
    },
  ];

  // 类型和状态
  async function getAttrArr() {
    Promise.all([
      queryGoodsType({ busiNbr: "ConMessage", propertyName: "messageType" }),
      queryGoodsType({ busiNbr: "ConMessage", propertyName: "statusCd" }),
    ]).then(res => {
      setAttrArr(res);
    });
  }

  // 重置查询条件
  const handleReset = () => {
    onClearForm();
    form.resetFields();
  };

  // 新增
  const setAdd = (e) => {
    e.preventDefault();
    setMode('add');
    setDetail({});
    show();
  };
  // 关闭详情弹框
  const handleClose = () => {
    setBraftEditor({
      visible: false,
      data: {},
    });
  };
  // 关闭新增弹窗
  const handleAddClose = () => {
    hide();
    getTableData();
  }
  useEffect(() => {
    getAttrArr();
  }, []);

  useEffect(() => {
    const body = document.getElementsByTagName("body");
    if (braftEditor.visible === false && body?.[0]?.classList) {
      body[0].classList.remove('ant-scrolling-effect');
    } else {
      body[0].classList.add('ant-scrolling-effect');
    }
  }, [braftEditor]);

  return (
    <>
      <Card
        title="公告信息"
        style={{ minHeight: height }}
        className="cute"
      >
        <div className={style.searchWapper}>
          <Form className="fix-label" layout="vertical">
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="类型">
                  {getFieldDecorator('messageType')(
                    <Select placeholder="请选择" allowClear onChange={e => onFormChange('messageType', e)}>
                      {
                        attrArr.length > 0 ?
                          attrArr[0].map(item => (
                            <Select.Option value={item.value} key={item.value}>{item.name}</Select.Option>
                          ))
                          : null
                      }
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="标题">
                  {getFieldDecorator('messageTitle')(
                    <Input allowClear placeholder="请输入" onChange={e => onFormChange('messageTitle', e.target.value)} />
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="副标题">
                  {getFieldDecorator('messageSubTitle')(
                    <Input allowClear placeholder="请输入" onChange={e => onFormChange('messageSubTitle', e.target.value)} />
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="是否置顶">
                  {getFieldDecorator('topFlag')(
                    <Select placeholder="请选择" allowClear onChange={e => onFormChange('topFlag', e)}>
                      {
                        MESSAGE_STATUSCD.map(item => (
                          <Select.Option value={item.attrValue} key={item.attrValue}>{item.ValueName}</Select.Option>
                        ))
                      }
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="内容">
                  {getFieldDecorator('messageContent')(
                    <Input allowClear placeholder="请输入" onChange={e => onFormChange('messageContent', e.target.value)} />
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="状态">
                  {getFieldDecorator('statusCd')(
                    <Select placeholder="请选择" allowClear onChange={e => onFormChange('statusCd', e)}>
                      {
                        attrArr.length > 0 ?
                          attrArr[1].map(item => (
                            <Select.Option value={item.value} key={item.value}>{item.name}</Select.Option>
                          ))
                          : null
                      }
                    </Select>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item>
                  <Button type="primary" onClick={onSearch}>
                    查询
                  </Button>
                  <Button className="margin-left" onClick={handleReset}>
                    重置
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
        <div className="panel">
          <Divider style={{ margin: 0 }} />
          <Row>
            <Col span={24}>
              <Button
                style={{ marginTop: '10px', marginBottom: '10px' }}
                onClick={(e) => setAdd(e)}
              >
                <Icon type="plus-circle" theme="filled" />
                新增
              </Button>
            </Col>
          </Row>
          <Table
            bordered
            rowKey="messageId"
            className={style.table}
            columns={columns}
            dataSource={list}
            loading={loading}
            size="middle"
            pagination={{
              current: pageNo,
              pageSize: PAGE_SIZE,
              total,
              showTotal: () => `共${total}条`,
              onChange: setPageNo,
              showQuickJumper: true,
            }}
          />
        </div>
      </Card>
      <MessageAdd visible={visible} mode={mode} detail={detail} pictureInformation={pictureInformation} onClose={handleAddClose} />
      <MessageDetails braftEditor={braftEditor} attrArr={attrArr[0]} onClose={handleClose} />
    </>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(Message));
