import React, { Component } from 'react';
import { Layout, Icon, Badge, Modal, Divider, Tooltip, Dropdown, Menu } from 'antd';
import { formatMessage } from 'umi/locale';
import classNames from 'classnames';
import { connect } from 'dva';
import PubSub from 'pubsub-js';
import Link from 'umi/link';
import Trigger from 'rc-trigger';
import Debounce from 'lodash-decorators/debounce';
import { getItem, openMenu } from '@/utils/utils';
import SelectLang from '@/components/SelectLang';
import { enableLanguage } from '@/defaultSettings';
import AccountUpdate from '@/pages/FourAAccountManage/AccountUpdate';
import StaffOrgMgmt from '@/pages/StaffOrgMgmt';
import { getRoleHighSearchList } from '@/services/menu';
import { loginoutResService } from '@/services/common';
// import { queryUserAvailabilityState, updateAvailabilityState } from '@/services/login';
import { handleSearchHistory } from './utils';
import { getSearchRange, ssoLoginJk } from '@/pages/Dashboard/components/MultiSearch/services';
import SearchInput from '@/pages/Dashboard/components/MultiSearch/components/SearchInput';
// import RecentMenuDropdown from './RecentMenuDropdown';
// import CollectionMenuDropdown from './CollectionMenuDropdown';
// import HeaderSearchMenu from './HeaderSearchMenu';
// import IntelligentSearch from './IntelligentSearch';
import MenuImg from './MenuImg';
// import SearchIcon from './SearchIcon';
import 'rc-trigger/assets/index.css';
import UserInfo from './UserInfo';
import AllMenu from './AllMenu';
import avatar from '../img/user-head.png';
// import AddressBook from './AddressBook';
// import ReportComplaint from './ReportComplaint';
import styles from '../less/header.less';

// import { position } from 'html2canvas/dist/types/css/property-descriptors/position';

// const showHeaderMenuSearch = true;
const { Header } = Layout;

class HeaderView extends Component {
  constructor(props) {
    super(props);

    this.state = {
      showMenuPanel: false,
      // showSearchMenuPanel: false,
      showUserPanel: false,
      // availabilityState: '1000',
      // availabilityEnable: false,
      // // addressBookVisiable: false,
      // onlineLoading: false,
      // dutyServiceInfo: '',
      // isAuth: false,
      // showService: false,
      searchMaps: [],
    };
  }

  componentDidMount() {
    this.getRecommendList();
    // this.initUserAvailabilityState();
    // this.getDutyServiceInfo();
    this.getSearchTypeList();

    PubSub.subscribe('systemOpt_openYXJ', () => {
      this.props.dispatch({ type: 'message/changeVisible', payload: true });
    });
    PubSub.subscribe('systemOpt_closeYXJ', () => {
      this.props.dispatch({ type: 'message/changeVisible', payload: false });
    });

    // const { allMenu } = this.props;
    // allMenu.forEach(item => {
    //   if (item.menuCode === 'ZCFWZX_MENU_ZTKJGDLL') {
    //     this.setState({ isAuth: true });
    //   }
    // });
  }

  componentWillUnmount() {
    PubSub.unsubscribe('systemOpt_openYXJ');
    PubSub.unsubscribe('systemOpt_closeYXJ');
  }

  getSearchTypeList = async () => {
    const result = await getSearchRange();
    if (Array.isArray(result?.resultObject)) {
      const maps = result.resultObject.map(item => ({
        name: item.menuName,
        code: item.menuCode,
      }));
      this.setState({ searchMaps: maps });
    }
  };

  // getDutyServiceInfo = async () => {
  //   const res = await getDataDictByCode({
  //     groupCode: 'SYSTEM_VAR',
  //     paramCode: 'DUTY_SERVICE_INFOMATION',
  //   });
  //   if (res) {
  //     this.setState({ dutyServiceInfo: res.paramValue });
  //   }
  // }

  // addressBookClick = () => {
  //   this.setState({ addressBookVisiable: true });
  // };

  // initUserAvailabilityState = async () => {
  //   const res = await queryUserAvailabilityState();
  //   if (res && res.success) {
  //     this.setState({ availabilityState: res.resultObject.availability, availabilityEnable: res.resultObject.enablePermission });
  //     this.props.getShowMask(res.resultObject.enablePermission && res.resultObject.availability === '1100');
  //   }
  // }

  // submitOnline = async () => {
  //   this.setState({ onlineLoading: true });
  //   const res = await updateAvailabilityState('1000');
  //   if (res && res.success) {
  //     this.setState({ availabilityState: '1000', onlineLoading: false });
  //     this.props.getShowMask(false);
  //   } else {
  //     message.error(res.resultMsg);
  //   }
  // }

  cancelOnline = () => {
    this.props.getShowMask(false);
  };

  getRecommendList = async () => {
    const query1 = getRoleHighSearchList(getItem('user')?.userInfo?.roleId);
    // const query2 = getUserSearchHistoryList();
    const _heightSearchList = await query1;
    // const _searchHistoryList = await query2;

    const { dispatch } = this.props;
    dispatch({
      type: 'menu/saveRecommendMenuList',
      payload: {
        heightSearchList: _heightSearchList,
        searchHistoryList: handleSearchHistory('getAll'),
      },
    });
  };

  handleLogout = e => {
    e.preventDefault();
    const {
      dispatch,
      login: { loginoutResSerList },
    } = this.props;
    // 手动更新ok按钮loading状态
    // onOk返回promise对象时，只有在resolve()之后才会关闭
    const modal = Modal.confirm({
      title: formatMessage({ id: 'logoutConfirm' }),
      onOk() {
        loginoutResService({ loginoutResSerList });

        modal.update({ okButtonProps: { loading: true } });
        return new Promise(resolve => {
          dispatch({
            type: 'login/logout',
          }).then(() => {
            modal.update({ okButtonProps: { loading: true } });
            resolve();
          });
        });
      },
    });
  };

  handleMessage = e => {
    this.props.dispatch({ type: 'message/changeVisible', payload: true });
    e.stopPropagation();
  };

  toggleSettingDrawer = () => {
    const {
      dispatch,
      setting: { settingDrawerActive },
    } = this.props;

    dispatch({
      type: 'setting/toggleSettingDrawer',
      payload: !settingDrawerActive,
    });
  };

  setRefresh = () => {
    const { dispatch, pathname } = this.props;
    dispatch({
      type: 'setting/setRefreshKey',
      payload: pathname,
    });

    setTimeout(() => {
      dispatch({
        type: 'setting/setRefreshKey',
        payload: false,
      });
    });
  };

  showMessage = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'message/changeVisible',
      payload: true,
    });
  };

  saveCurrentOpenLeftMenu = () => {
    const {
      dispatch,
      setting: { currentOpenLeftMenu },
    } = this.props;
    dispatch({
      type: 'setting/saveCurrentOpenLeftMenu',
      payload: !currentOpenLeftMenu,
    });
    this.triggerResizeEvent();
  };

  hideAllMenu = isVisible => {
    if (isVisible) {
      this.setState({ showMenuPanel: true }, () => {
        document.querySelector('body').classList.add('noScroll');
      });
    } else {
      this.setState({ showMenuPanel: false }, () => {
        document.querySelector('body').classList.remove('noScroll');
      });
    }
  };

  // setShowSearchMenuPanel = isVisible => {
  //   if (isVisible) {
  //     this.setState({ showSearchMenuPanel: true }, () => {
  //       document.querySelector('body').classList.add('noScroll');
  //     });
  //   } else {
  //     this.setState({ showSearchMenuPanel: false }, () => {
  //       document.querySelector('body').classList.remove('noScroll');
  //     });
  //   }
  // };

  showUserInfo = isVisible => {
    if (isVisible) {
      this.setState({ showUserPanel: true });
    } else {
      this.setState({ showUserPanel: false });
    }
  };

  handleLock = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'login/lock',
    });
  };

  // 图片无法加载时，使用默认图片
  imgExists = e => {
    // 默认图片
    const imgUrl = avatar;
    if (imgUrl) {
      e.target.src = imgUrl;
    }
  };

  linkToBill = () => {};

  // handleAvailabilityChange = async checked => {
  //   Modal.confirm({
  //     title: checked ? '是否上线' : '是否离线',
  //     onOk: async () => {
  //       // 用于表述业务的有效性，1000：在线，1100：离线
  //       const res = await updateAvailabilityState(checked ? '1000' : '1100');
  //       if (res && res.success) {
  //         this.setState({ availabilityState: checked ? '1000' : '1100' });
  //         // this.props.getShowMask(!checked);
  //       } else {
  //         message.error(res.resultMsg);
  //       }
  //     },
  //   });
  // };

  handleMenuClick = async e => {
    if (e.key === 'crm') {
      const { dispatch, allMenu } = this.props;
      // 如果是打开/iframe/xxx，则用menucode去打开，xxx就是menucode
      const formatPathname = '/iframe/TYMH_MENU_CRM_SSO?ssoType=4A';
      const endIndex = formatPathname.indexOf('?');
      const menuCode = formatPathname.slice(8, endIndex);
      const urlParams = formatPathname.slice(endIndex);
      openMenu(menuCode, allMenu, dispatch, urlParams);
    } else if (e.key === 'jike') {
      const result = await ssoLoginJk();
      if (result && result.success && result.resultObject) {
        window.open(result.resultObject, '_blank');
      }
    }
  };

  // 300毫秒是侧边栏menu-drawer动画的过渡时间，保证在动画结束后触发
  @Debounce(300)
  // eslint-disable-next-line class-methods-use-this
  triggerResizeEvent() {
    const event = document.createEvent('HTMLEvents');
    event.initEvent('resize', true, false);
    window.dispatchEvent(event);
  }

  render() {
    const {
      login: {
        user: {
          // eslint-disable-next-line no-unused-vars
          userInfo: { userName, userPhoto },
        },
      },
      // setting: { showLeftMenu },
      unRead,
      // unreadChat,
      showSubSystemAccountSelect,
      orgActionTag,
      workTableName,
    } = this.props;

    const menu = (
      <Menu onClick={this.handleMenuClick}>
        <Menu.Item key="crm">
          <Link to="#">CRM</Link>
        </Menu.Item>
        <Menu.Item key="jike">
          <Link to="#">集客大厅</Link>
        </Menu.Item>
      </Menu>
    );

    // const unReadTotal = Number(unRead) + Number(unreadChat);

    // const { isAuth } = this.state;

    return (
      <Header id="layout-header" className={classNames(styles.wrapper)}>
        <div className="media-box" style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', whiteSpace: 'nowrap' }}>
          <div id="headerLeft" className="media-left" style={{ display: 'flex', alignItems: 'center' }}>
            {/* 侧边栏开关 */}
            {/* {showLeftMenu && (
              <span className={styles.action} onClick={this.saveCurrentOpenLeftMenu}>
                <Icon type="align-left" />
              </span>
            )} */}
            {/* logo */}
            <span className={classNames(styles.action)}>
              <MenuImg type="logo" width={103} height={32} />
            </span>
            <Divider type="vertical" />
            <Link to="/" className={classNames(styles.action)} style={{ fontSize: 16 }}>
              {workTableName || '内蒙古移动政企工作台'}
            </Link>
            {/* title */}
            {/* <span className={classNames(styles.title, styles.action)}>{formatMessage({ id: 'app.title' })}</span> */}
            {/* 全部菜单 */}
            <Trigger
              popupPlacement="bottomLeft"
              action={['hover']}
              popupAlign={{
                overflow: {
                  adjustX: 1,
                  adjustY: 1,
                },
              }}
              // getPopupContainer={() => document.getElementById('header_windows')}
              mouseEnterDelay={0.3}
              popupClassName={styles.allMenuDropdown2}
              builtinPlacements={{
                bottomLeft: {
                  points: ['tl', 'bl'],
                },
              }}
              popupVisible={this.state.showMenuPanel}
              // popupTransitionName="slide-up"
              popup={<AllMenu hideAllMenu={this.hideAllMenu} />}
              onPopupVisibleChange={isVisible => this.hideAllMenu(isVisible)}
              onPopupAlign={() => {
                // $(popupDomNode).css({
                //   left: '0',
                //   top: '60px',
                //   right: '0',
                //   position: 'fixed',
                // })
                // 防止被layout-tabs-nav遮挡
                document.querySelector('.rc-trigger-popup').parentNode.parentNode.style.zIndex = 1000;
              }}
            >
              <span
                className={classNames(styles.allMenu, styles.action, {
                  [styles.active]: this.state.showMenuPanel,
                })}
                style={{ position: 'relative' }}
                id="header_windows"
              >
                <MenuImg type="menu" />
              </span>
            </Trigger>

            {/* {!showLeftMenu && (
              <>
                <CollectionMenuDropdown />
                <RecentMenuDropdown />
              </>
            )} */}
            {/* <span id="marquee" className={classNames(styles.marquee, styles.action)} style={{ maxWidth: '30%' }}>
              <p className={document.getElementById('marquee')?.clientWidth > document.getElementById('headerLeft')?.clientWidth * 0.28 && styles.scrollMarquee}>{this.state.dutyServiceInfo}</p>
            </span> */}
          </div>
          {/* 搜索框 */}
          <div className="media-body" style={{ display: 'flex', alignItems: 'center' }}>
            <SearchInput searchRange={this.state.searchMaps} isHeader />
          </div>
          <div className="media-right" style={{ display: 'flex', alignItems: 'center' }}>
            {/* 快速报障 */}
            {/* <span
              onClick={() =>
                PubSub.publish('quick_report', {
                  reportTo: 'all',
                  ejectMode: 'serial',
                  type: 'service',
                  system: '03',
                  natureOfFailure: '2', // 1代表一键报障 2代表快捷报障
                })
              }
              className={classNames(styles.action)}
            >
              <Icon type="bug" />
            </span> */}
            {/* 快捷搜索 */}

            <Dropdown overlay={menu} className={classNames(styles.logout, styles.action)}>
              <a onClick={e => e.preventDefault()}>
                <Icon type="swap" />
              </a>
            </Dropdown>

            {/* <SearchIcon /> */}
            <span onClick={this.setRefresh}>
              <Tooltip title="刷新">
                <span className={classNames(styles.logout, styles.action)} id="header_refresh">
                  <Icon type="reload" />
                </span>
              </Tooltip>
            </span>
            {/* 系统消息 */}
            <Tooltip title="统一消息">
              <Link className={classNames(styles.logout, styles.action)} to="/notice">
                {unRead ? (
                  <Badge count={unRead}>
                    <MenuImg type="sound" />
                  </Badge>
                ) : (
                  <MenuImg type="sound" />
                )}
              </Link>
            </Tooltip>
            {/* <ReportComplaint>
              <Popover
                content={(
                  <div className={styles.billContent}>
                    <Menu>
                       <Menu.Item key="myBill">
                        <Link to="/iframe/ZCFWZX_MENU_WDBZ">我的提单</Link>
                      </Menu.Item>
                      <Menu.Item key="dktd">
                        <Link to="/iframe/ZCFWZX_MENU_ZTKJGDLL">代客提单</Link>
                      </Menu.Item>
                    </Menu>
                  </div>
                )}
                visible={this.state.showService}
                onVisibleChange={visible => this.setState({ showService: visible && isAuth })}
                style={{ zIndex: 100 }}
                getPopupContainer={() => document.getElementById('layout-header')}
              >
                <span className={classNames(styles.action)} style={{ position: 'relative' }} id="header_bug">
                  <Icon type="bug" />
                </span>
              </Popover>
            </ReportComplaint> */}


            {/* <Tooltip title="云小集">
              <div className={classNames(styles.logout, styles.action)} onClick={this.handleMessage}>
                {unreadChat ? (
                  <Badge count={unReadTotal - unRead}>
                    <MenuImg type="robot" />
                  </Badge>
                  ) : (
                    <MenuImg type="robot" />
                  )}
              </div>
            </Tooltip> */}
            {/* <Popover
              content={(
                <div className={styles.billContent}>
                  <Menu>
                    <Menu.Item key="myNotice">
                      <Link to="/notice">系统消息（{unRead}）</Link>
                    </Menu.Item>
                    <Menu.Item key="myMessage">
                      <div onClick={this.handleMessage}>云小集（{unreadChat}）</div>
                    </Menu.Item>
                  </Menu>
                </div>
                )}
              trigger="hover"
            >
              <span className={classNames(styles.logout, styles.action)} id="header_notice">
                {unReadTotal ? (
                  <Badge count={unReadTotal}>
                    <MenuImg type="sound" />
                  </Badge>
                  ) : (
                    <MenuImg type="sound" />
                  )}
              </span>
            </Popover> */}
            {/* {this.state.addressBookVisiable ? (
              <AddressBook>
                <Tooltip title="通讯录">
                  <span className={classNames(styles.logout, styles.action)} id="header_addressBook" onClick={() => this.addressBookClick(true)}>
                    <MenuImg type="addressBook" />
                  </span>
                </Tooltip>
              </AddressBook>
            ) : (
              <Tooltip title="通讯录">
                <span className={classNames(styles.logout, styles.action)} id="header_addressBook" onClick={() => this.addressBookClick(true)}>
                  <MenuImg type="addressBook" />
                </span>
              </Tooltip>
            )} */}

            {/* 退出 */}
            <Tooltip title="注销">
              <span onClick={this.handleLogout} className={classNames(styles.logout, styles.action)} id="header_poweroff">
                <Icon type="poweroff" />
              </span>
            </Tooltip>
            {/* 切换语言 */}
            {enableLanguage ? <SelectLang className={classNames(styles.action)} /> : null}
            {/* 锁屏 */}
            {/* <span onClick={this.handleLock} className={classNames(styles.action)}>
            <Icon type="lock" />
          </span> */}
            {/* 设置面板开关 */}
            {/* { this.state.availabilityEnable && (
                this.props.showMask ? (
                  <Popover
                    visible={this.props.showMask}
                    placement="bottomRight"
                    arrowPointAtCenter
                    content={(
                      <div className={styles.onlineCard}>
                        <div>当前处于离线状态，</div>
                        <div>是否立即上线？</div>
                        <div className={styles.onlineBtn}>
                          <Button type="primary" onClick={this.submitOnline} loading={this.state.onlineLoading}>确认</Button>
                          <Button onClick={this.cancelOnline}>取消</Button>
                        </div>
                      </div>
                    )}
                  >
                    <span className={classNames(styles.logout, styles.action)}>
                      <Switch
                        style={{ backgroundColor: this.state.availabilityState === '1000' ? '#8FC31F' : '#999999' }}
                        checkedChildren="在线"
                        unCheckedChildren="离线"
                        checked={this.state.availabilityState === '1000'}
                        onClick={this.handleAvailabilityChange}
                      />
                    </span>
                  </Popover>
                ) : (
                  <Tooltip title="上下线">
                    <span className={classNames(styles.logout, styles.action)}>
                      <Switch
                        style={{ backgroundColor: this.state.availabilityState === '1000' ? '#8FC31F' : '#999999' }}
                        checkedChildren="在线"
                        unCheckedChildren="离线"
                        checked={this.state.availabilityState === '1000'}
                        onClick={this.handleAvailabilityChange}
                      />
                    </span>
                  </Tooltip>
                )
              )
            } */}
            {/* 用户面板 */}
            <Trigger
              popupPlacement="bottomLeft"
              action={['hover']}
              popupAlign={{
                overflow: {
                  adjustX: 1,
                  adjustY: 1,
                },
              }}
              mouseEnterDelay={0.3}
              popupClassName={styles.allMenuDropdown}
              builtinPlacements={{
                bottomLeft: {
                  points: ['tl', 'bl'],
                },
              }}
              popupTransitionName="slide-up"
              popup={<UserInfo showUserInfo={this.showUserInfo} />}
              popupVisible={this.state.showUserPanel}
              onPopupVisibleChange={isVisible => this.showUserInfo(isVisible)}
            >
              <div
                className={classNames(styles.action, {
                  [styles.active]: this.state.showUserPanel,
                })}
                id="header_user"
              >
                <MenuImg type="user" style={{ marginRight: 8 }} />
                <span style={{ position: 'relative', top: 1.5 }}>
                  {getItem('user')?.staffInfo?.staffName} {getItem('user')?.userInfo?.userCode}
                </span>
              </div>
            </Trigger>
            <Tooltip title="系统设置">
              <span className={styles.action} onClick={this.toggleSettingDrawer} id="header_setting">
                <Icon type="align-right" />
              </span>
            </Tooltip>
          </div>
        </div>
        {showSubSystemAccountSelect === 'edit' && <AccountUpdate />}
        {orgActionTag === 'edit' && <StaffOrgMgmt />}
      </Header>
    );
  }
}

export default connect(({ login, setting, notice, fourAAccount, orgMgmt, menu }) => ({
  login,
  setting,
  unRead: notice.unRead,
  unreadChat: notice.unreadChat,
  showSubSystemAccountSelect: fourAAccount.showSubSystemAccountSelect,
  orgActionTag: orgMgmt.actionTag,
  workTableName: menu.workTableName,
  allMenu: menu.all,
}))(HeaderView);
