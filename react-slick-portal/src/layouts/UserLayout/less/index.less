/* stylelint-disable */
@import '~antd/lib/style/themes/default.less';
@import './banner.less';

@font-size-base: 14px; // 主字号
@font-size-lg : @font-size-base + 2px;

@heading-color: rgba(0, 0, 0, 0.85); // 标题色
@text-color: rgba(0, 0, 0, 0.65); // 主文本色
@text-color-secondary : rgba(0, 0, 0, .45); // 次文本色

// vertical paddings
@padding-lg : 24px; // containers
@padding-md : 16px; // small containers and buttons
@padding-sm : 12px; // Form controls and items
@padding-xs : 8px; // small items

.userlayout {
  position: relative;
}

.logo {
  position: absolute;
  top: 30px;
  left: 50px;
  z-index: 99;
}
.computer{
  position: absolute;
  top: 29%;
  left: 12%;
  z-index: 99;
}

.language {
  position: absolute;
  top: 10px;
  width: auto;
  right: 30px;
  z-index: 99;
  color: #fff;
  font-size: 20px;

  &:hover {
    i {
      color: @blue-3;
    }
  }
}
