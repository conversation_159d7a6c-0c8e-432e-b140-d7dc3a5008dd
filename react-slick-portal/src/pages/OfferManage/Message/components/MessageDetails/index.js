import React from 'react';
import {
  Drawer, Divider
} from 'antd';
import BraftEditor from 'braft-editor';
import moment from 'moment';
import stringToHtml from '@/utils/string';
import style from './Details.module.less';

const MessageDetails = props => {
  const { braftEditor, onClose, attrArr } = props;
  return (
    <Drawer
      title="详情"
      placement="right"
      width="1000px"
      closable={false}
      onClose={() => onClose()}
      visible={braftEditor.visible}
    >
      <div className={style.detailWrap}>
        <h1>{braftEditor.data.messageTitle}</h1>
        <h2>{braftEditor.data.messageSubTitle}</h2>
        <div className={style.typeTime}>
          {
            braftEditor.data.messageType &&
            <p>
              <span>消息类型：</span>
              <span>{(attrArr && attrArr.find(item => item.value === braftEditor.data.messageType) || {}).name}</span>
            </p>
          }
          {braftEditor.data.statusDate ?
            <p>
              <span>发布时间:</span>
              <span>
                {braftEditor.data.statusDate ? moment(braftEditor.data.statusDate).format('YYYY-MM-DD') : null}
              </span>
            </p> : null
          }
        </div>
        <Divider />
        {
          braftEditor.data.photoUrl && <img loading="lazy" src={braftEditor.data.photoUrl} alt="Smiley face" style={{ width: '80%' }} />
        }
        {/* <div
          className={style.htmlContent}
          dangerouslySetInnerHTML={{ __html: braftEditor.data.messageContent }}
        /> */}
        <div className={style.htmlContent} dangerouslySetInnerHTML={{ __html: BraftEditor.createEditorState(stringToHtml(braftEditor.data.messageContent)).toHTML() }} />
      </div>
    </Drawer>
  );
};

export default MessageDetails;
