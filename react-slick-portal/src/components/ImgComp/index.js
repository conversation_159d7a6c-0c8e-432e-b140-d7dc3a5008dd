import React, { useEffect } from 'react'
import request from '@/utils/request';

const { useState } = require("react");

const ImgComp = props => {
  const { id } = props;

  const [url, setUrl] = useState('')

  const loadUrl = () => {
    request(`portal/FileStoreController/queryByObjId.do?objId=${id}&objType=3000`, {
      method: 'get'
    }).then(res => {
      if (Array.isArray(res) && res.length === 1) {
        setUrl(res[0]?.fileGetUrl)
      }
    })
  }

  useEffect(() => {
    if (id) {
      loadUrl()
    }
  }, [id])

  return <img src={url} {...props} alt={props.alt || ''} />
}


export default ImgComp
