import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';

/**
 * 基础图标组件
 * 提供统一的图标渲染逻辑和属性支持
 */
const IconBase = ({
  children,
  size = 16,
  color = 'currentColor',
  className,
  style,
  onClick,
  title,
  viewBox = '0 0 24 24',
  ...rest
}) => {
  const iconStyle = {
    width: typeof size === 'number' ? `${size}px` : size,
    height: typeof size === 'number' ? `${size}px` : size,
    fill: color,
    display: 'inline-block',
    verticalAlign: 'middle',
    ...style,
  };

  const iconClass = classNames('icon-base', className);

  return (
    <svg
      className={iconClass}
      style={iconStyle}
      viewBox={viewBox}
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
      role={onClick ? 'button' : 'img'}
      aria-label={title}
      {...rest}
    >
      {title && <title>{title}</title>}
      {children}
    </svg>
  );
};

IconBase.propTypes = {
  children: PropTypes.node.isRequired,
  size: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  color: PropTypes.string,
  className: PropTypes.string,
  style: PropTypes.object,
  onClick: PropTypes.func,
  title: PropTypes.string,
  viewBox: PropTypes.string,
};

export default IconBase;
