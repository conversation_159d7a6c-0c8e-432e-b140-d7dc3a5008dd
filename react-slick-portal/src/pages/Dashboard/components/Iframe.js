import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { getItem } from '@/utils/utils';
import request from '@/utils/request';

function Iframe({ iframeUrl: address, menuId, allMenu }) {
  const [iframeUrl, setIframeUrl] = useState('');

  useEffect(() => {
    const { sessionId } = getItem('user');
    let newAddress = address;

    // 根据原连接是否带?, 决定用什么符号追加 bss3SessionId
    if (address.indexOf('?') === -1) {
      newAddress = `${newAddress}?bss3SessionId=${sessionId}`;
    } else {
      newAddress = `${newAddress}&bss3SessionId=${sessionId}`;
    }
    setIframeUrl(newAddress);

    // 判断链接是否包含ssoType=1，采用SSO单点校验方式进行菜单打开
    if (address.indexOf('ssoType=1') !== -1) {
      const urlParams = {
        bssSessionId: sessionId,
      };

      // 获取菜单编码
      const menuInfo = allMenu.find(item => item.menuId === menuId);

      // 菜单地址SSO加密，拼接返回的signString和singTimestamp
      request(`orgauth/SystemInfoController/calcSign.do?systemInfoId=${menuInfo?.systemCode}`, {
        method: 'POST',
        data: urlParams,
      }).then(response => {
        if (response && response.resultCode === '0') {
          newAddress = `${newAddress}&signString=${response.signString}&signTimestamp=${response.signTimestamp}`;
          setIframeUrl(newAddress);
        }
      });
    }
  }, [address]);

  return (
    <>
      <iframe title="iframe" src={iframeUrl} frameBorder="0" height="100%" width="100%" style={{ backgroundColor: '#fff' }} />
    </>
  );
}

// export default Iframe;
export default connect(({ menu }) => ({
  allMenu: menu.all,
}))(Iframe);
