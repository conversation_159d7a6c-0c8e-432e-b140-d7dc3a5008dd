.ant-collapse.cute {
  .ant-collapse-item {
    /*border-bottom: 0;*/
  }
  .ant-collapse-content {
    border-top: 0;
    background: #ffffff;
  }
  .ant-collapse-header {
    height: 46px;
    padding: 0 12px 0 38px !important; /* 调整内边距以适应图标 */
    font-weight: 600;
    min-height: 32px;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 2px 2px 0 0;
    zoom: 1;
    line-height: 46px; /* 设置行高与高度相同以实现垂直居中 */
    position: relative;
    font-size: 14px;
    display: flex; /* 使用flex布局 */
    align-items: center; /* 垂直居中 */
    background: #ffffff;
    &::before {
      position: absolute;
      top: 50%;
      left: 16px;
      display: inline-block;
      width: 6px;
      height: 14px;
      margin-top: -7px;
      background-color: @blue-6;
      border-radius: 2px;
      content: '';
    }
  }
}
