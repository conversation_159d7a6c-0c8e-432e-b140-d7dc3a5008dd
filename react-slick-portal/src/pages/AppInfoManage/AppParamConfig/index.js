import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Select,
  Icon,
  Input,
  Button,
  Tag,
  Modal,
  Divider,
  message,
  Drawer,
} from 'antd';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
// import useSlickTable from '@/hooks/UseSlickTable';
import { useAntdTable } from '@umijs/hooks';
import { getPageSizeByCardHeight } from '@/utils/utils';
import request from '@/utils/request';
import Edit from './components/Edit';

function appParamConfig({ size: { height }, form, dispatch }) {
  const [size, setSize] = useState(getPageSizeByCardHeight(height - 32 - 8));
  const { getFieldDecorator } = form;

  const [paramsObj, setParamsObj] = useState({});
  const [visible, setVisible] = useState(false);
  const [passData, setPassData] = useState({});
  const [behavior, setBehavior] = useState('disabled');
  const [reashLoading, setReashLoading] = useState(false);

  const getTableData = ({ current, pageSize, paramsObj }) => {
    return request('portal/DataDictController/selectGridData.do', {
      data: {
        pageNum: current,
        pageSize: pageSize,
        sortName: '',
        sortOrder: 'asc',
        filterCol: 'paramCode,paramName,paramValue,groupCode,groupName',
        groupCode: 'APP_PARAMS',
        ...paramsObj,
      },
    }).then(res => {
      if (Array.isArray(res.list)) {
        return {
          total: res.total,
          data: res.list.map(val => {
            const { children, ...rest } = val;
            return rest;
          }),
        };
      }
      return {
        total: 0,
        data: [],
      };
    });
  };

  const { tableProps, refresh } = useAntdTable(
    params => {
      return getTableData({ ...params, pageSize: size, paramsObj });
    },
    [paramsObj, height],
    {
      defaultPageSize: size,
      form,
    }
  );
  const { pagination, ...restTableProps } = tableProps;

  useEffect(() => {
    setSize(getPageSizeByCardHeight(height - 32 - 8));
  }, [height]);

  const submit = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      let obj = {};
      for (let item in fieldsValue) {
        if (fieldsValue[item] != undefined && fieldsValue[item] != '') {
          obj[item] = `%${fieldsValue[item]}%`;
        }
      }
      setParamsObj(obj);
    });
  };

  const handleEdit = record => {
    setVisible(true);
    setPassData(record);
    setBehavior('edit');
  };

  const onClose = () => {
    setVisible(false);
  };

  const handleReash = () => {
    // console.log("刷新")
    Modal.confirm({
      content: '确定要刷新缓存吗?',
      onOk() {
        setReashLoading(true);
        request('portal/DataDictController/reload.do', { method: 'GET' }).then(res => {
          setReashLoading(false);
          res === true ? message.success('缓存刷新成功！') : message.error('缓存刷新失败！');
        });
      },
    });
  };

  const handleReset = () => {
    form.resetFields();
  };

  const reload = () => {
    // request('portal/DataDictController/reload.do', {
    //   method: 'GET',
    // }).then(res => {
    //   res ? message.success('缓存刷新成功！') : message.error('缓存刷新失败');
    // });
  };

  const columns = [
    {
      title: '参数编码',
      dataIndex: 'paramCode',
      ellipsis: true,
    },
    {
      title: '参数名称',
      dataIndex: 'paramName',
      ellipsis: true,
    },
    {
      title: '参数值',
      dataIndex: 'paramValue',
      ellipsis: true,
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return (
          <span>
            <a
              onClick={() => {
                handleEdit(record);
              }}
            >
              编辑
            </a>
          </span>
        );
      },
    },
  ];

  return (
    <>
      <Card
        title="APP参数配置"
        style={{ minHeight: height }}
        className="cute"
        extra={
          <Button
            type="primary"
            onClick={() => {
              handleReash();
            }}
            loading={reashLoading}
          >
            刷新缓存
          </Button>
        }
      >
        <Form className="flow fix-label">
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label="参数名称">
                {getFieldDecorator('paramName')(<Input allowClear placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="参数编码">
                {getFieldDecorator('paramCode')(<Input allowClear placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="参数值">
                {getFieldDecorator('paramValue')(<Input allowClear placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6} className="text-right">
              <Button type="primary" onClick={submit}>
                查询
              </Button>
              <Button className="margin-left" onClick={handleReset}>
                重置
              </Button>
            </Col>
          </Row>
        </Form>
        <SlickTable
          style={{ marginTop: 8 }}
          rowKey={record => record.id}
          columns={columns}
          {...restTableProps}
          data={{
            pagination: {
              ...pagination,
              pageSize: size,
            },
          }}
        />
      </Card>
      <Drawer
        title="参数配置详情"
        destroyOnClose
        width={620}
        onClose={onClose}
        visible={visible}
        bodyStyle={{ paddingBottom: 80 }}
      >
        <Edit close={onClose} refresh={refresh} passData={passData} behavior={behavior} />
      </Drawer>
    </>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(appParamConfig));
