import React, { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Icon, Card, Empty, Tag, Modal, Divider } from 'antd';
import Link from 'umi/link';
import PubSub from 'pubsub-js';
import moment from 'moment';
import SlickUpload from '@/components/SlickUpload';
import useInterval from '@/hooks/useInterval';
import { makeAsRead as makeAsReadService, queryByObjId } from '@/services/bulletin';
import style from './notification.less';

function Notification({ dispatch, list, popupInterval, bulletinInterval, initLoading }) {
  const [visible, setVisible] = useState(false);
  const [detail, setDetail] = useState({});
  const [showPreFlag, setShowPreFlag] = useState('none');
  const [preDetail, setPreDetail] = useState({});

  useEffect(() => {
    dispatch({
      type: 'bulletin/getInitData',
      payload: {
        defaultPopupInterval: 60 * 1000,
        defaultPopupCount: 5,
        defaultBulletinInterval: 60 * 1000,
        defaultBulletinShowCount: 5,
      },
    });
  }, [dispatch]);

  // 轮询，定时更新公告列表。undefined表示bulletinInterval在model中未被初始化过
  useInterval(() => {
    if (bulletinInterval !== undefined) {
      dispatch({
        type: 'bulletin/pollBulletin',
      });
    }
  }, bulletinInterval);

  // 轮询，若有重要信息则右下角弹窗。undefined表示popupInterval在model中未被初始化过
  useInterval(() => {
    if (popupInterval !== undefined) {
      dispatch({
        type: 'bulletin/pollImportant',
      });
    }
  }, popupInterval);

  // 点击事件控制旧公告内容是否展示
  function showPreInfo() {
    if (showPreFlag === 'none') {
      setShowPreFlag('');
    } else {
      setShowPreFlag('none');
    }
  }

  useEffect(() => {
    // const rootStyle = document.getElementById('root').style;
    // if (visible) {
    // rootStyle.overflow = 'hidden' ;
    // rootStyle.position = 'fixed' ;
    // } else {
    //   rootStyle.overflow = 'initial' ;
    //   rootStyle.position = '' ;
    // }
  }, [visible]);

  /**
   * 设置已读
   * @param {object[]} target 被标记行数据
   * @param {object[]} tableData 当前table数据（dataSource）
   * @param {object[]} selectedRows 当前选中的所有的行数据
   */
  function makeAsRead(target) {
    const params = [];

    // 提取id，并序列化
    target.forEach(item => {
      const { bulletin, statusCdName, rcvTypeName, ...rest } = item;
      const { createStaff } = bulletin;
      // 只要这些字段
      // {
      //   bulletinId: 344512,
      //   bulletinRcvObjRelId: 5188744,
      //   createDate: "2020-01-08 10:59:29",
      //   createStaff: 1,
      //   id: 5188744,
      //   rcvId: 1,
      //   rcvType: "1200",
      //   statusCd: "1000",
      //   updateDate: "2020-01-08 11:00:00",
      // }
      params.push({ ...rest, createStaff });
    });

    makeAsReadService(params).then(response => {
      // 回参：1 标记成功
      // IMPROVE: /notice 回参0 表示成功 不统一
      if (response === 1) {
        // 更新table视图
        // IMPORVE: table视图之所以会更新是因为数组是引用类型。数组被修改后，在执行setSelectedRows时视图刷新自动同步
        // target.forEach(item => {
        //   const i = findIndex(tableData, { id: item.id });
        //   if (i !== -1) {
        //     const newItem = tableData[i];
        //     newItem.statusCd = '1100';
        //     newItem.statusCdName = '已读';
        //     tableData[i] = newItem;
        //   }
        // });
        // 剔除selectedRows中已记录的。因为存在批量标记和点击单个详情标记两种情况
        // 当checkbox选中若干项时，又点击其中的某个详情。这个时候就需要从选中项剔除
        // selectedRows.forEach(item => {
        //   const i = findIndex(target, { id: item.id });
        //   if (i !== -1) {
        //     selectedRows.splice(i, 1);
        //   }
        // });
        // tableRef.current.handleRowSelectChange(map(selectedRows, 'id'), selectedRows);
        // setSelectedRows(selectedRows);
      }
    });
  }

  function actionHandler(e, mode, record) {
    if (e) {
      e.preventDefault();
    }
    // 详情
    if (mode === 'view') {
      // 标记已读; 1100表示已读
      // IMPORVE: statusCd 含义与消息中心里的回参相反，那边'1000'表示已读
      if (record.statusCd !== '1100') {
        makeAsRead([record]);
      }
      // 查询附件信息
      queryByObjId({ objId: record.bulletinId, objType: '1000' }).then(res => {
        // 存在附件
        if (Array.isArray(res)) {
          setVisible(true);
          setDetail({
            ...record.bulletin,
            fileList: res.map(item => {
              const { docId, docNbr, fileName, fileGetUrl } = item;
              return {
                uid: docId,
                name: fileName,
                status: 'done',
                url: fileGetUrl,
                docId,
                fileName,
                fileGetUrl,
                docNbr,
              };
            }),
          });
        } else {
          setVisible(true);
          setDetail(record.bulletin);
        }
      });

      // 旧公告内容
      if (record.bulletin && record.bulletin.prevBulletinId > 0) {
        // 查询附件信息
        queryByObjId({ objId: record.bulletin.prevBulletinId, objType: '1000' }).then(res => {
          // 存在附件
          if (Array.isArray(res)) {
            setPreDetail({
              fileList: res.map(item => {
                const { docId, docNbr, fileName, fileGetUrl } = item;
                return {
                  uid: docId,
                  name: fileName,
                  status: 'done',
                  url: fileGetUrl,
                  docId,
                  fileName,
                  fileGetUrl,
                  docNbr,
                };
              }),
            });
          }
        });
      }
    }
  }

  useEffect(() => {
    // 订阅"重新获取高频功能"的消息
    PubSub.subscribe('showNotification', (info, value) => {
      if (info === 'showNotification') {
        actionHandler(undefined, 'view', value);
      }
    });

    return () => {
      PubSub.unsubscribe('showNotification');
    };
  }, []);

  return (
    <Card
      className={style.wrap}
      title="系统公告"
      bordered={false}
      extra={(
        <Link to="/bulletin" style={{ color: '#8F9198' }} data-reg-id="notify_more">
          <span style={{ verticalAlign: 'middle' }}>更多</span>
          <Icon style={{ verticalAlign: 'middle' }} type="right" />
        </Link>
      )}
      loading={initLoading}
    >
      {list.length > 0 ? (
        <ul>
          {list.slice(0, 3).map(item => (
            <li key={item.id}>
              <Tag className={style.tag} color={item.bulletin?.bulletinLevel === '1000' && 'red'}>{item.bulletin?.bulletinTypeName ?? ''}</Tag>
              <span
                className={style.title}
                style={{ color: item.bulletin?.bulletinLevel === '1000' && 'red' }}
                href="#"
                onClick={e => actionHandler(e, 'view', item)}
              >
                {item.bulletin?.bulletinTitle ?? ''}
              </span>
              <span
                className={style.date}
                style={{ color: item.bulletin?.bulletinLevel === '1000' && 'red' }}
              >
                {moment(item.bulletin?.createDate).format('YYYY-MM-DD') ?? ''}
              </span>
            </li>
          ))}
        </ul>
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="当前无未读公告" />
      )}
      <Modal
        width={600}
        title={detail.bulletinTitle}
        visible={visible}
        footer={null}
        onCancel={() => {
          setVisible(false);
          setDetail({});
          setShowPreFlag('none');
        }}
        destroyOnClose
        centered
      >
        <p>{detail.bulletinContent}</p>
        <div>
          <SlickUpload multiple disabled fileList={detail.fileList} />
        </div>
        {detail.prevBulletinId && detail.preBulletinTitle ? (
          <div>
            <Divider />
            <p>
              关联公告：{detail.preBulletinTitle}
              <a onClick={showPreInfo}>(点击后查看内容)</a>
            </p>

            <div style={{ display: showPreFlag }}>
              <p className="text-gray" style={{ textDecoration: 'line-through' }}>
                {detail.preBulletinContent}
              </p>
              <div>
                <SlickUpload multiple disabled fileList={preDetail.fileList} />
              </div>
            </div>
          </div>
        ) : null}
        <div className="text-right text-gray">{detail.effDate}</div>
      </Modal>
    </Card>
  );
}

export default connect(({ bulletin, loading }) => ({
  list: bulletin.list,
  popupInterval: bulletin.bulletinInterval,
  bulletinInterval: bulletin.bulletinInterval,
  initLoading: loading.effects['bulletin/getInitData'],
}))(Notification);
