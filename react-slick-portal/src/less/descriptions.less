.ant-descriptions.cute {

  :global {
    .ant-descriptions-row {

      .ant-descriptions-item-label {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }

      .ant-descriptions-item-content {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
      }
      .ant-descriptions-row > th, .ant-descriptions-row > td {
        padding-top: 12px !important;
      }
    }
  }
}

.ant-descriptions.cuteLabelBold {

  :global {
    .ant-descriptions-row {

      .ant-descriptions-item-label {
        font-weight: 600;
        font-size: 14px;
        color: #333333;
      }

      .ant-descriptions-item-content {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
      }
      .ant-descriptions-row > th, .ant-descriptions-row > td {
        padding-top: 12px !important;
      }
    }
  }
}
.ant-descriptions.right {

  :global {
    .ant-descriptions-row {
      display: grid;
      grid-column-gap: 20px;
      grid-row-gap: 12px;
      grid-template-columns: repeat(3, minmax(100px, 1fr));

      td {
        grid-column-start: span 1;
        display: flex;
        flex-flow: row wrap;
      }

      .ant-descriptions-item-label {
        font-weight: 400;
        font-size: 14px !important;
        width: 150px;
        max-width: 150px;
        flex: 0 0 150px;
        text-align: right;
        color: #999;
        vertical-align: middle;
        display: inline-block !important;
        position: relative;
        padding-right: 8px;

        &::after {
          margin-right: 0 !important;
        }
      }

      .ant-descriptions-item-content {
        font-weight: 400;
        font-size: 14px;
        color: #333;
        vertical-align: middle;
        display: inline-block !important;
        width: calc(100% - 150px);
        word-wrap: break-word;
      }

      .ant-descriptions-row > th, .ant-descriptions-row > td {
        padding-top: 12px !important;
        display: table-cell;
      }
    }
  }
}
