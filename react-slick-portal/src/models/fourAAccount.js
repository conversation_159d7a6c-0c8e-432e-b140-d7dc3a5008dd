import { message } from 'antd';
import { isEmptyStr, toArray, isEmptyArray } from '@/utils/utils';
import { initSubUser } from '@/services/login';

// defaultFlag === 1000 表示当前用户是子系统的默认账号
const DEFAULT_FLAG = '1000';
// defaultFlag === 1100 表示当前用户不是子系统的默认账号

// 标识该用户是子系统的活跃账号
const CURRENT_FLAG = '1000';

// 存在未初始化数据
const hasUnInit = subSystemList => subSystemList.find(item => isEmptyStr(item.defaultUserId));

// 判断是否所有未初始化的子系统默认账号都只有一个选项，若是，直接初始化，并返回结果
const everySubSystemOnlyhasOneSelectForDefault = (subSystemList, userInfo) => {
  let checkRes = true;
  subSystemList.forEach(item => {
    if (isEmptyStr(item.defaultUserId) && item.accountList.length !== 1) {
      checkRes = false;
    }
  });
  if (checkRes === false) {
    return [checkRes];
  }
  const targetList = subSystemList.map(item => ({
    ...item,
    defaultUserId: item?.accountList?.[0]?.externalUserId, // 默认选择数组第一项
    currentUserId: item?.accountList?.[0]?.externalUserId,
    currentUserCode: item?.accountList?.[0]?.externalUserCode,
  }));

  const list = targetList.map(item => ({
    externalUserId: item?.currentUserId,
    externalUserCode: item?.currentUserCode,
    systemInfoId: item?.systemInfoId,
    defaultFlag: DEFAULT_FLAG,
    sysUserId: userInfo.userId,
  }));

  initSubUser(list).then(res => {
    if (!res) {
      message.error('系统异常');
    }
  });
  return [checkRes, targetList];
};

const getDefaultUserId = accountList => accountList.find(item => item.defaultFlag === DEFAULT_FLAG)?.externalUserId;
const getDefaultUserCode = accountList => accountList.find(item => item.defaultFlag === DEFAULT_FLAG)?.externalUserCode;

const getCurrentUserId = accountList => accountList.find(item => item.currentFlag === CURRENT_FLAG)?.externalUserId;
const getCurrentUserCode = accountList => accountList.find(item => item.currentFlag === CURRENT_FLAG)?.externalUserCode;

// 无论是更新还是填写默认账号，都可用这个初始化方法，且只执行一次
const initSubSystemList = allAccountInfo => {
  // 获取systemInfoId列表，并去重
  const systemInfoIdSet = new Set(toArray(allAccountInfo).map(item => item?.systemInfoId));
  const systemInfoIdList = [...systemInfoIdSet];

  // 通过systemInfoIdList构建systemInfo列表
  const list = systemInfoIdList.map(systemInfoId => {
    const systemInfo = allAccountInfo.find(info => info.systemInfoId === systemInfoId);
    const accountList = allAccountInfo.filter(info => info.systemInfoId === systemInfoId);

    const defaultUserId = getDefaultUserId(accountList);
    const defaultUserCode = getDefaultUserCode(accountList);
    const currentUserId = getCurrentUserId(accountList);
    const currentUserCode = getCurrentUserCode(accountList);

    return {
      systemInfoId: systemInfo.systemInfoId,
      systemName: systemInfo.systemName,
      systemNbr: systemInfo.systemNbr,
      defaultUserId,
      currentUserId: currentUserId ?? defaultUserId, // 初始化时，活跃账号如果为空，就用默认账号
      currentUserCode: currentUserCode ?? defaultUserCode, // 初始化时，活跃code如果为空，就用默认code
      accountList,
    };
  });

  return list;
};

// 子系统账号管理
export default {
  namespace: 'fourAAccount',

  state: {

    /** 以子系统为存储单元
     * {
     *    defaultUserId // 默认用户id
     *    currentUserId // 子系统的当前活跃用户id
     *    currentUserCode // 子系统的当前活跃用户code
     *    systemInfoId, // 子系统id
     *    systemNbr // 子系统编码
     *    systemName // 子系统名称
     *    accountList // 子系统可选择的账号列表
     * }
     */
    subSystemList: [],
    showSubSystemAccountSelect: false, // false,init,edit
  },

  reducers: {
    init(state, { payload }) {
      const { externalUserInfos, reInit, userInfo } = payload;

      let list = state.subSystemList;
      let showSubSystemAccountSelect = false;

      if (isEmptyArray(list) || reInit) {
        list = initSubSystemList(externalUserInfos);
      }

      // 判断是否需要填写子系统默认账号
      if (hasUnInit(list)) {
        // 判断是否所有未初始化的子系统默认账号都只有一个选项，若是，直接初始化，并返回结果
        const checkRes = everySubSystemOnlyhasOneSelectForDefault(list, userInfo);

        if (checkRes[0]) {
          // eslint-disable-next-line prefer-destructuring
          list = checkRes[1];
        } else {
          showSubSystemAccountSelect = 'init';
        }
      }

      return {
        ...state,
        subSystemList: list,
        showSubSystemAccountSelect,
      };
    },
    // 将部分修改的列表合并到subSystemList中，然后关闭面板
    update(state, { payload }) {
      const { someList } = payload;

      const newList = state.subSystemList.map(item => {
        const newInfo = someList.find(info => info.systemInfoId === item.systemInfoId);
        return newInfo ?? item;
      });

      return {
        ...state,
        subSystemList: newList,
        showSubSystemAccountSelect: false,
      };
    },
    openForEdit(state) {
      return {
        ...state,
        showSubSystemAccountSelect: 'edit',
      };
    },
    close(state) {
      return {
        ...state,
        showSubSystemAccountSelect: false,
      };
    },
  },

};
