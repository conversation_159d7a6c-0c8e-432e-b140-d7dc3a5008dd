import React, { useState } from 'react';
import { Tabs } from 'antd';
import IconFont from '@/components/IconFont';
import { tabs } from '@/pages/GroupCust/GroupCustView/components/ContractInfo/const';

const { TabPane } = Tabs;
const Index = () => {
  const [activeTab, setActiveTab] = useState([tabs[0].key]);
  return (
    <div>
      <div>
        {/* 应用自定义样式 */}
        <Tabs defaultActiveKey="inTransitOrder" onChange={setActiveTab} className="tabsCustom">
          {tabs.map(({ name, icon, key, component: Component }) => (
            <TabPane
              tab={(
                <>
                  <IconFont
                    className="icon"
                    type={`${activeTab === key ? `${icon}-on` : icon}`} // 假设activeTab与key关联来确定激活状态
                  />
                  <span>{name}</span>
                </>
              )}
              key={key}
            >
              <Component />
            </TabPane>
          ))}
        </Tabs>
      </div>
    </div>
  );
};
export default Index;
