@import '~antd/lib/style/themes/default.less';

.avatar {
  margin-right: 16px;
  vertical-align: top;
}
.slogn {
  margin: 4px 0;
  color: @heading-color;
  font-weight: 500;
  font-size: 16px;
  line-height: 28px;
}

.suffix {
  position: relative;
  padding-right: 1em;
  &::after {
    position: absolute;
    top: 50%;
    right: 0;
    height: 34px;
    margin-top: -17px;
    content: '%';
  }
}
.info {
  color: @text-color-secondary;
  font-size: @font-size-base;
}

.right {
  float: right;
  text-align: right;
  > .item:last-child {
    padding-right: 0;
    &::after {
      display: none;
    }
  }
}

.item {
  position: relative;
  display: inline-block;
  padding: 0 24px;

  > div {
    margin: 0;
    color: @heading-color;
    font-size: 20px;
    line-height: 34px;
    &:first-child {
      color: @text-color-secondary;
      font-size: @font-size-base;
      line-height: 22px;
    }
  }
  &::after {
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 40px;
    margin-top: -20px;
    background-color: #e8e8e8;
    content: '';
  }
}

.danger {
  color: @error-color !important;
}
.warning {
  color: @warning-color !important;
}
