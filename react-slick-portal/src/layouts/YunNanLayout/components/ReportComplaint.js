import React, { useReducer, cloneElement } from 'react';
import { Modal, message, Icon, Button } from 'antd';
import isObject from 'lodash/isObject';
import { isJSON, getItem } from '@/utils/utils';
import request from '@/utils/request';
import { screenshot } from '@/components/BugReport/utils';
import styles from '../less/reportComplaint.less';

const initial = {
  visible: false,
  maximized: true,
  isCancel: false,
  dataSource: [],
  loading: false,
};

let targetUrl = '';
// // 获取当前时间年月日时分秒
// function getNowTime() {
//   const currentDate = new Date();
//   const yy = currentDate.getFullYear();
//   const MM = currentDate.getMonth() + 1 < 10 ? `0${currentDate.getMonth() + 1}` : currentDate.getMonth() + 1;
//   const dd = currentDate.getDate() < 10 ? `0${currentDate.getDate()}` : currentDate.getDate();
//   const HH = currentDate.getHours() < 10 ? `0${currentDate.getHours()}` : currentDate.getHours();
//   const mm = currentDate.getMinutes() < 10 ? `0${currentDate.getMinutes()}` : currentDate.getMinutes();
//   const ss = currentDate.getSeconds() < 10 ? `0${currentDate.getSeconds()}` : currentDate.getSeconds();
//   return yy + MM + dd + HH + mm + ss;
// }

const initData = async () => {
  // message.success('截屏中请稍等');
  const { sessionId } = getItem('user');
  // // 随机数获取 时间戳+随机数
  // let random = '';
  // for (let i = 1; i <= 3; i++) {
  //   random = `${random}${Math.floor(Math.random() * 10)}`;
  // }
  // const orderId = getNowTime() + random;

  // // 获取截图
  // const dataUrl = await screenshot();

  // // 请求后端进行截图上传
  // const result1 = await request('portal/BusinessSystemController/reportComplaint.do', {
  //   data: {
  //     uid: orderId,
  //     appendix: dataUrl,
  //   },
  // });
  // if (!(result1.resultCode === 'TRUE' && result1.resultObject.code === '0000')) {
  //   message.error(`当前截屏上传失败:${result1.resultMsg}`);
  // }

  // 获取支撑服务中心url地址
  const result2 = await request('portal/DataDictController/getValidValueByCode.do', {
    data: { groupCode: 'BUSINESS_SYSTEM_URL', paramCode: 'SUPPORT_SERVICE_CENTER', defValue: '' },
    method: 'GET',
  });

  if (result2) {
    const urlAddr = `${result2}/FaultOrderManage/FastFaultOrderAdd?viewMode=inner&ssoType=1&bss3SessionId=${sessionId}`;
    // console.log('reportComplaint_addr：%s', urlAddr);
    targetUrl = urlAddr;
  } else {
    message.error('未配置支撑服务中心URL地址,请配置');
  }
};

function reducer(state, action) {
  if (!targetUrl) {
    return initial;
  }
  const { type } = action;
  if (type === 'toggleModal') {
    return {
      ...state,
      maximized: true,
      isCancel: false,
      visible: !state.visible,
    };
  }
  if (type === 'toggleMaximized') {
    return {
      ...state,
      visible: !state.visible,
      maximized: !state.maximized,
    };
  }

  if (type === 'cancel') {
    return { ...initial, isCancel: true };
  }

  throw Error();
}

function ReportComplaint({ children }) {
  const [state, dispatch] = useReducer(reducer, initial);
  const { visible, maximized, isCancel } = state;

  const cloned = cloneElement(children, {
    onClick: async () => {
      await initData();
      dispatch({ type: 'toggleModal' });
    },
  });

  const handleMaximize = () => {
    dispatch({ type: 'toggleMaximized' });
  };

  // 获取窗口可视区域宽度
  const { innerWidth } = window;
  const { innerHeight } = window;

  // const sendMessage = () => {
  //   console.log('发送消息');
  //   window.postMessage(
  //     JSON.stringify({ action: 'closeReportBugModal' }),
  //     '*'
  //   );
  // };

  const closeModal = event => {
    if (event?.data) {
      // console.log('接收到消息', event);
      if (!isJSON(event.data)) {
        return;
      }
      const data = JSON.parse(event.data);
      if (!isObject(data)) {
        return;
      }
      if (data.action === 'closeReportBugModal') {
        dispatch({ type: 'cancel' });
      }
    }
  };

  window.addEventListener('message', closeModal);

  return (
    <>
      {cloned}
      <Modal
        title="服务提单"
        visible={visible}
        onCancel={() => dispatch({ type: 'cancel' })}
        destroyOnClose={isCancel}
        maskClosable={false}
        footer={null}
        bodyStyle={{ padding: '0', height: '100%' }}
        width={innerWidth * 0.66}
        height={innerHeight * 0.66}
      >
        <div style={{ position: 'absolute', top: '18px', right: '60px', fontSize: '14px' }}>
          <Icon onClick={handleMaximize} type="fullscreen-exit" />
        </div>
        <iframe title="一键报障" src={targetUrl} height={innerHeight * 0.66} width="100%" style={{ border: 0 }} />
        {/* <Button onClick={sendMessage}>发送message</Button> */}
      </Modal>
      {!visible && !maximized && (
        <div className={styles.minContainer}>
          <div className={styles.minContent}>
            <div className={styles.title}>一键报障</div>
            <div>
              <Icon onClick={handleMaximize} type="fullscreen" style={{ marginRight: '12px' }} />
              <Icon onClick={() => dispatch({ type: 'cancel' })} type="close" />
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default ReportComplaint;
