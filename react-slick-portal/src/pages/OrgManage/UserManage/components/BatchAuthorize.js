import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Form, Input, Icon, Cascader, Tag, DatePicker, Button, message, Select, Modal } from 'antd';
import moment from 'moment';
import { omit } from 'lodash';
import { useAntdTable } from '@umijs/hooks';
import request from '@/utils/request';
import { operActionLog } from '@/utils/utils';
import SlickTable from '@/components/SlickTable';
import ComboGrid from '@/components/ComboGrid';
import styles from '../styles.less';

const FormItem = Form.Item;
const { Search } = Input;
const { Option } = Select;

const BatchAuthorize = ({ form, setAuthorizeVisiable }) => {
  const { getFieldDecorator } = form;
  const dateFormat = 'YYYY-MM-DD';

  // 筛选项相关
  const [labelOptions, setLabelOptions] = useState([]);
  const [authorizeTypeList, setAuthorizeTypeList] = useState([]);
  const [systemList, setSystemList] = useState([]);
  const [selectLabelItems, setSelectLabelItems] = useState([]);

  // 列表相关
  const [selectUserRowsKeys, setSelectUserRowsKeys] = useState([]);
  const [allSelectUserRows, setAllSelectUserRows] = useState([]);
  const [selectAuthorizeRowsKeys, setSelectAuthorizeRowsKeys] = useState([]);
  const [allSelectAuthorizeRows, setAllSelectAuthorizeRows] = useState([]);
  const [useParams, setUseParams] = useState({});
  const [authorizeParams, setAuthorizeParams] = useState({});

  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [loading, setLoading] = useState(false);

  // 获取标签选择数据
  const getLabelData = async () => {
    const result = await request('orgauth/LabelsController/getLabelTree.do', {});
    if (result.success) {
      if (result.resultObject) {
        const labelData = Object.keys(result.resultObject).map(labelName => ({
          label: labelName,
          value: labelName,
          children: result.resultObject[labelName] || [],
        }));
        setLabelOptions(labelData);
      }
    } else {
      message.error(result.resultMsg);
    }
  };

  // 获取权限类型数据
  const getAuthorizeTypeList = async () => {
    const result = await request('orgauth/PermissionsRelController/selectPermissonAttrValueList.do', {
      method: 'GET',
    });
    if (result.success) {
      if (result.resultObject?.length) {
        setAuthorizeTypeList(result.resultObject);
      }
    } else {
      message.error(result.resultMsg);
    }
  };

  // 获取归属系统数据
  const getSystemList = () => {
    try {
      request('orgauth/SystemInfoController/getSystemInfoList.do', {
        method: 'GET',
      }).then(res => {
        setSystemList(res);
      });
    } catch (error) {
      message.error(error);
    }
  };

  useEffect(() => {
    getLabelData();
    getAuthorizeTypeList();
    getSystemList();
  }, []);

  // 获取用户列表数据
  const getUserTableData = params => {
    const { current, pageSize = 10, ...restParams } = params;
    return request('portal/UserAuthorizationController/getComplexConditionsUsers.do', {
      data: {
        page: current,
        pageNum: current,
        pageSize,
        ...useParams,
        ...restParams,
      },
    }).then(res => {
      if (Array.isArray(res?.resultObject?.list)) {
        return {
          total: res.resultObject.total,
          data: res.resultObject.list,
        };
      }
      return {
        total: 0,
        data: [],
      };
    });
  };

  // 用户列表数据查询
  const { tableProps } = useAntdTable(
    params => getUserTableData({ ...params }),
    [useParams],
    { form }
  );
  const { pagination, ...userTableProps } = tableProps;

  const userColumns = [
    {
      title: '账号',
      dataIndex: 'sysUserCode',
      ellipsis: true,
    },
    {
      title: '用户姓名',
      dataIndex: 'userName',
      ellipsis: true,
    },
    {
      title: '手机号码',
      dataIndex: 'mobilePhone',
      ellipsis: true,
    },
    {
      title: '归属组织',
      dataIndex: 'userOrgName',
      ellipsis: true,
    },
  ];

  // 用户列表筛选条件查询
  const handleUserChange = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      const newFormValues = omit(fieldsValue, ['privType', 'systemInfoId', 'authFilterVal']);
      newFormValues.orgId = newFormValues.orgId?.length > 0 ? newFormValues.orgId[0].id : undefined;
      newFormValues.regionId = newFormValues.regionId?.length > 0 ? newFormValues.regionId[0].id : undefined;
      newFormValues.sysRoleIds = newFormValues.sysRoleIds?.length > 0 ? newFormValues.sysRoleIds.map(item => item.id) : undefined;
      newFormValues.subcategoryCode = selectLabelItems.length > 0 ? selectLabelItems.map(item => item.value) : undefined;
      setUseParams(newFormValues);
    });
  };

  // 重置用户列表筛条件
  const handleUserReset = () => {
    form.resetFields(['orgId', 'regionId', 'sysRoleIds', 'subcategoryCode', 'filterVal']);
    setSelectLabelItems([]);
    setUseParams({});
  };

  // 标签改变
  const changeLabel = (value, selectedOptions) => {
    const curValue = selectedOptions[1];
    if (!selectLabelItems.includes(curValue)) {
      setSelectLabelItems([...selectLabelItems, curValue]);
    }
  };

  // 删除标签
  const deleteLabel = index => {
    const labels = [...selectLabelItems];
    labels.splice(index, 1);
    setSelectLabelItems(labels);
  };

  // 用户选择区域
  const userSelect = () => (
    <div>
      <Form>
        <Row>
          {/* <Col span={4}>
            <FormItem>
              {getFieldDecorator('orgId')(
                <ComboGrid
                  url="orgauth/OrganizationController/qryManageOrgGridData.do"
                  popupStyle={{ width: 560 }}
                  placeholder="组织选择"
                  searchPlaceholder="请输入组织名称进行搜索"
                  label="orgName"
                  rowKey="orgId"
                  pick="radio"
                  params={{ createType: '1100' }}
                  suffix={<Icon type="down" />}
                  columns={[
                  {
                    title: '组织标识',
                    dataIndex: 'orgId',
                    ellipsis: true,
                  },
                  {
                    title: '组织名称',
                    dataIndex: 'orgName',
                    ellipsis: true,
                  },
                  {
                    title: '组织路径',
                    dataIndex: 'pathName',
                    ellipsis: true,
                  },
                  {
                    title: '组织层级',
                    dataIndex: 'orgLevel',
                    ellipsis: true,
                  },
                ]}
                />
            )}
            </FormItem>
          </Col> */}
          <Col span={5}>
            <FormItem>
              {getFieldDecorator('regionId')(
                <ComboGrid
                  url="orgauth/CommonRegionController/selectRegionGridDataById.do"
                  popupStyle={{ width: 560 }}
                  placeholder="区域选择"
                  searchPlaceholder="请输入区域名称进行搜索"
                  label="regionName"
                  params={{ filterCol: 'regionName,regionNbr', regionId: 10008 }}
                  // destroyPopupOnHide
                  rowKey="commonRegionId"
                  pick="radio"
                  suffix={<Icon type="down" />}
                  columns={[
                      {
                        title: '区域名称',
                        dataIndex: 'regionName',
                        ellipsis: true,
                      },
                      {
                        title: '区域编码',
                        dataIndex: 'regionNbr',
                        ellipsis: true,
                      },
                      {
                        title: '区域路径',
                        ellipsis: true,
                        dataIndex: 'pathName',
                      },
                    ]}
                />
            )}
            </FormItem>
          </Col>
          <Col span={5}>
            <FormItem>
              {getFieldDecorator('sysRoleIds')(
                <ComboGrid
                  url="orgauth/SystemRolesController/selectSystemRolesGridDataAll.do"
                  popupStyle={{ width: 560 }}
                  placeholder="角色选择"
                  searchPlaceholder="请输入角色名称进行搜索"
                  label="sysRoleName"
                  rowKey="id"
                  pick="checkbox"
                  multipleFlag
                  params={{ filterCol: 'sysRoleName,sysRoleCode', sysCode: '727001' }}
                  suffix={<Icon type="down" />}
                  columns={[
                    {
                      title: '角色名称',
                      dataIndex: 'sysRoleName',
                      ellipsis: true,
                    },
                    {
                      title: '角色编码',
                      dataIndex: 'sysRoleCode',
                      ellipsis: true,
                    },
                    {
                      title: '角色类型',
                      ellipsis: true,
                      dataIndex: 'sysRoleTypeName',
                    },
                    {
                      title: '归属系统',
                      ellipsis: true,
                      dataIndex: 'sysCodeName',
                    },
                  ]}
                />
            )}
            </FormItem>
          </Col>
          <Col span={6} className={styles.formItem}>
            <FormItem>
              <Cascader
                onChange={changeLabel}
                options={labelOptions}
              >
                <div
                  style={{
                    padding: '0 6px 0 2px',
                    minHeight: '22px',
                    height: '32px',
                    maxHeight: '50px',
                    overflowY: 'auto',
                    overflowX: 'hidden',
                    border: '1px solid #d9d9d9',
                    borderRadius: '2px',
                  }}
                >
                  {
                    selectLabelItems.length ? selectLabelItems.map((item, index) => (
                      <Tag
                        closable
                        key={item.value}
                        // eslint-disable-next-line no-unused-vars
                        onClose={e => {
                          // 删除对应索引元素
                          deleteLabel(index);
                        }}
                      >
                        {item.label}
                      </Tag>
                    )) : (
                      <span
                        style={{
                          color: '#bfbfbf',
                          fontSize: '13px',
                          marginLeft: '11px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          height: '22px',
                        }}
                      >
                        选择标签
                      </span>
                    )
                  }
                </div>
              </Cascader>
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem>
              {getFieldDecorator('filterVal')(
                <Search
                  placeholder="账号、姓名、手机号搜索"
                  // onSearch={handleUserChange}
                />
              )}
            </FormItem>
          </Col>
        </Row>
      </Form>
      <SlickTable
        rowKey={record => record.id}
        columns={userColumns}
        {...userTableProps}
        loading={userTableProps?.loading}
        data={{ pagination }}
        selectedRowKeys={selectUserRowsKeys}
        allSelectedRows={allSelectUserRows}
        onSelectRow={rows => {
          setSelectUserRowsKeys(rows.map(item => item.id));
          setAllSelectUserRows(rows);
        }}
        pick="checkbox"
      />
    </div>
  );

  // 权限列表数据查询
  const getAuthorizeTableData = params => {
    const { current, pageSize = 10, ...restParams } = params;
    return request('portal/UserAuthorizationController/getComplexConditionsPrivs.do', {
      data: {
        pageNum: current,
        pageSize,
        ...authorizeParams,
        ...restParams,
      },
    }).then(res => {
      if (Array.isArray(res?.resultObject?.list)) {
        return {
          total: res.resultObject.total,
          data: res.resultObject.list,
        };
      }
      return {
        total: 0,
        data: [],
      };
    });
  };

  // 权限列表数据查询
  const { tableProps: tableProps1 } = useAntdTable(
    params => getAuthorizeTableData({ ...params }),
    [authorizeParams],
    // { form }
  );
  const { pagination: authorizePagination, ...authorizeTableProps } = tableProps1;

  const authorizeColumns = [
    {
      title: '权限名称',
      dataIndex: 'privName',
      ellipsis: true,
      width: '25%',
    },
    {
      title: '权限编码',
      dataIndex: 'privCode',
      ellipsis: true,
      width: '35%',
    },
    {
      title: '归属系统',
      dataIndex: 'sysCodeName',
      ellipsis: true,
      width: '25%',
    },
    {
      title: '权限类型',
      dataIndex: 'privTypeName',
      ellipsis: true,
      width: '15%',
    },
  ];

  // 权限列表筛选条件查询
  const handleAuthorizeChange = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      const { privType, systemInfoId, authFilterVal } = fieldsValue;
      setAuthorizeParams({ privType, systemInfoId, filterVal: authFilterVal });
    });
  };

  // 重置权限列表筛选条件
  const handleAuthorizeReset = () => {
    form.resetFields(['privType', 'systemInfoId', 'authFilterVal']);
    setAuthorizeParams({});
  };

  // 权限选择区域
  const authorizeSelect = () => (
    <div>
      <Form>
        <Row>
          <Col span={6}>
            <FormItem>
              {getFieldDecorator('privType')(
                <Select
                  placeholder="权限类型"
                  style={{ width: '100%' }}
                  allowClear
                >
                  {
                    authorizeTypeList.map(item => (
                      <Option value={item.attrValue} key={item.attrValue}>
                        {item.attrValueName}
                      </Option>
                    )) || null
                  }
                </Select>
            )}
            </FormItem>
          </Col>
          <Col span={9}>
            <FormItem>
              {getFieldDecorator('systemInfoId')(
                <Select
                  placeholder="归属系统"
                  allowClear
                  showSearch
                  style={{ width: '100%' }}
                  optionFilterProp="children"
                >
                  {
                    systemList.map(item => (
                      <Option value={item.systemInfoId} key={item.systemInfoId}>
                        {item.systemName}
                      </Option>
                    )) || []
                  }
                </Select>
            )}
            </FormItem>
          </Col>
          <Col span={9}>
            <FormItem>
              {getFieldDecorator('authFilterVal')(
                <Search style={{ width: '100%' }} placeholder="名称、编码" />
              )}
            </FormItem>
          </Col>
        </Row>
      </Form>
      <SlickTable
        rowKey={record => record.id}
        columns={authorizeColumns}
        data={{ pagination: authorizePagination }}
        loading={authorizeTableProps?.loading}
        {...authorizeTableProps}
        pick="checkbox"
        selectedRowKeys={selectAuthorizeRowsKeys}
        allSelectedRows={allSelectAuthorizeRows}
        onSelectRow={rows => {
          setSelectAuthorizeRowsKeys(rows.map(item => item.id));
          setAllSelectAuthorizeRows(rows);
        }}
      />
    </div>
  );

  const changeDate = (date, type) => {
    if (type === 'start') {
      if (endDate && moment(date).format(dateFormat) > moment(endDate).format(dateFormat)) {
        message.error('生效时间不能大于失效时间，请重新选择！');
        return;
      }
      setStartDate(date);
    } else {
      if (startDate && moment(date).format(dateFormat) < moment(startDate).format(dateFormat)) {
        message.error('失效时间不能小于生效时间，请重新选择！');
        return;
      }
      setEndDate(date);
    }
  };

  // 提交授权
  const authorizeSubmit = () => {
    if (selectUserRowsKeys.length === 0) {
      message.error('请选择需授权用户！');
    } else if (selectAuthorizeRowsKeys.length === 0) {
      message.error('请选择需授权权限！');
    } else if (selectUserRowsKeys.length > 1000) {
      message.error('当次允许选中最大用户数为1000，请调整后进行提交。');
    } else if (!startDate || !endDate) {
      message.error(`请选择${!startDate ? '生效' : '失效'}日期！`);
    } else {
      Modal.confirm({
        title: `确认给选中的${selectUserRowsKeys.length}个用户授权选中的${selectAuthorizeRowsKeys.length}个权限？`,
        onOk() {
      setLoading(true);
      try {
        request('portal/UserAuthorizationController/volumeAuthorization.do', {
          data: {
            sysUserIds: selectUserRowsKeys,
            privIds: selectAuthorizeRowsKeys,
            effDate: `${moment(startDate).format(dateFormat)} 00:00:00`,
            expDate: `${moment(endDate).format(dateFormat)} 23:59:59`,
          },
        }).then(res => {
          setLoading(false);
          if (res?.success) {
            setAuthorizeVisiable(false);
            // 记录系统操作日志
            operActionLog({ actionType: '1100', actionModule: '3290500', action: '2100', handResult: 'success', actionMsg: '' });
            message.success('授权成功！');
          } else {
            // 记录系统操作日志
            operActionLog({ actionType: '1100', actionModule: '3290500', action: '2100', handResult: res?.success, actionMsg: res?.resultMsg });
            message.success(res?.resultMsg);
          }
        }).catch(() => {
          setLoading(false);
          message.error('调用接口失败');
        });
      } catch (error) {
        setLoading(false);
        message.error(error);
      }
        },
      });
    }
  };

  return (
    <div style={{ position: 'relative' }}>
      <Row gutter={16}>
        <Col span={12}>
          <Card
            title="用户选择"
            extra={(
              <div className={styles.extra}>
                <Button className="margin-left" type="primary" onClick={handleUserChange}>
                  查询
                </Button>
                <Button className="margin-left" onClick={handleUserReset}>
                  重置
                </Button>
              </div>
            )}
          >
            {
              userSelect()
            }
          </Card>
        </Col>
        <Col span={12}>
          <Card
            title="权限选择"
            extra={(
              <div className={styles.extra}>
                <Button className="margin-left" type="primary" onClick={handleAuthorizeChange}>
                  查询
                </Button>
                <Button className="margin-left" onClick={handleAuthorizeReset}>
                  重置
                </Button>
              </div>
            )}
          >
            {
              authorizeSelect()
            }
          </Card>
        </Col>
      </Row>
      <Row gutter={32} type="flex" justify="center" style={{ marginTop: '24px' }}>
        <Col>
          <span className={styles.dateRequired}>生效日期：<DatePicker value={startDate} onChange={date => changeDate(date, 'start')} /></span>
        </Col>
        <Col>
          <span className={styles.dateRequired}>失效日期：<DatePicker value={endDate} onChange={date => changeDate(date, 'end')} /></span>
        </Col>
      </Row>
      <div style={{ position: 'absolute', right: '0px', bottom: '0px' }}>
        <Button onClick={() => setAuthorizeVisiable(false)} style={{ marginRight: '8px' }}>取消</Button>
        <Button type="primary" onClick={authorizeSubmit} loading={loading}>确定</Button>
      </div>
    </div>
  );
};
export default Form.create()(BatchAuthorize);
