import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'dva';
import { Card, Icon, Tooltip, Modal } from 'antd';
import PubSub from 'pubsub-js';
import styles from './styles.less';
import SystemMenu from './components/SystemMenu';
import MenuInfo from './components/MenuInfo';
import AddMainMenu from './components/AddMainMenu';
import request from '@/utils/request';

const namespace = 'operationManage';

function Menu(props) {
  const { dispatch } = props;
  // 获取菜单管理的容器高度 计算出功能组件需沾满的高度
  const menuEl = useRef();
  const systemEl = useRef();
  // 满屏高度
  const [height, setHeight] = useState('');
  const [visible, setVisible] = useState(false);

  const handleOk = () => {
    systemEl.current.handleAdd().then(res => {
      if (res === true) {
        setVisible(false);
        PubSub.publish('operationManage_systemMenu.change', {});
      }
    });
  };

  const handleCancel = () => {
    setVisible(false);
  };

  const addModal = () => {
    setVisible(true);
  };

  // effect 监听窗口变化
  useEffect(() => {
    setHeight(props.size.height);
  }, [props.size.height]);

  const queryMenuTreeData = () => {
    request('orgauth/FuncMenuController/selectAllMenu.do', {
      method: 'GET',
    }).then(res => {
      if (res.length > 0) {
        let node = {};
        const arr = [];
        res.forEach(item => {
          node = item;
          // if (item.menuType === '1000') {
          //   node.isLeaf = false;
          // } else {
          //   node.isLeaf = true;
          // }
          arr.push(node);
        });
        dispatch({
          type: `${namespace}/saveMenuTreeData`,
          payload: arr,
        });
      }
    });
  };

  useEffect(() => {
    queryMenuTreeData(); // 关联菜单下拉框
  }, []);

  return (
    <div className={styles.menuManage}>
      <div className={styles.menuManageLeft}>
        <Card
          className="cute"
          title="帮助手册"
          style={{ height }}
          extra={(
            <Tooltip title="新增主目录">
              <Icon type="plus-circle" className={styles.icon_hover} onClick={addModal} />
            </Tooltip>
          )}
        >
          <SystemMenu height={height} />
        </Card>

        <Modal
          title="新增主目录"
          visible={visible}
          onOk={handleOk}
          onCancel={handleCancel}
          width="600px"
          destroyOnClose
        >
          <AddMainMenu ref={systemEl} cRef={systemEl} />
        </Modal>
      </div>

      <div className={styles.menuManageRight}>
        <div ref={menuEl} style={{ margin: '0 0 8px 8px' }}>
          <Card className="cute" title="帮助手册配置">
            <MenuInfo height={height} />
          </Card>
        </div>
      </div>
    </div>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Menu);
