import React from 'react';
import { connect } from 'dva';

function SystemTaskDetail(props) {
  const { iframeUrl, title } = props?.location?.query || {};
  return (
    <div style={{ height: window?.innerHeight - 100 }}>
      {iframeUrl && (
        <iframe
          title={title}
          src={iframeUrl}
          frameBorder="0"
          width="100%"
          height="100%"
          style={{ backgroundColor: '#fff' }}
        />
      )}
    </div>
  );
}

export default connect(({ setting, menu }) => ({
  size: setting.size,
  all: menu.all,
}))(SystemTaskDetail);
