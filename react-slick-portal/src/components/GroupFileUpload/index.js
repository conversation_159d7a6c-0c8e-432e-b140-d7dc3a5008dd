import React, { useState } from 'react';
import { Upload, message, Icon, Button } from 'antd';
import request from '@/utils/request';
import { downloadFile } from '@/utils/utils';


const GroupFileUpload = React.memo(props => {
  const { value = [], onChange, accept, fileType, fileLimit = 1, detailState, extraIcon, callBackFile, typeFlag } = props;

  const [loading, setLoading] = useState(false);
  const uploadButton = (
    <>
      <Button disabled={loading || value?.length >= fileLimit || detailState}>
        <Icon type={loading ? 'loading' : 'plus'} /> 上传
      </Button>
      {extraIcon}
    </>
  );
  const uploadFile = async options => {
    const { onSuccess, onError, file } = options;
    const formData = new FormData();

    formData.append('file', file); // 后台要的参数
    formData.append('fileType', fileType); // 后台要的参数
    setLoading(true);
    return request('portal/GroupFileController/fileUpload.do', {
      method: 'post',
      data: formData,
      processData: false,
      contentType: false,
    })
      .then(async res => {
        const { resultCode, resultObject, resultMsg } = res;
        if (resultCode !== 'TRUE') {
          onError(resultMsg, res);
          return;
        }
        const fileId = resultObject?.rspParam?.busiInfo?.fileId;
        file.key = fileId;
        file.fileId = fileId;
        onSuccess(file);

        // 条件：新增集团证件弹窗 返回附件id 需要拿id校验
        if (typeFlag === 'addEcCustLicenceInfo') {
          callBackFile(file);
        }
      })
      .catch(error => {
        // 清除 fileList
        if (onChange) {
          onChange(value.filter(item => item !== file));
        }
        onError(error);
      })
      .always(() => {
        setLoading(false);
      });
  };

  const onChangeHandle = info => {
    const { fileList, file } = info;
    if (file.status === 'done') {
      message.success(`${file.name} ，文件上传成功`);
    } else if (file.status === 'error') {
      message.error(`${file.name} ，上传失败`);
    }
    if (onChange) {
      onChange(
        fileList.map(item => ({
          ...item,
          fileId: item?.response?.fileId,
        }))
      );
    }
  };

  const onRemove = file => {
    file.status = 'removed';
    if (onChange) {
      onChange([file]);
    }
  };
  const beforeUpload = file => {
    const isLt30M = file.size / 1024 / 1024 < 30;
    if (!isLt30M) {
      message.error('文件大小不能超过30MB!');
      return false;
    }
    return true;
  };
  const handlerDownloadFile = file => {
    const url = 'portal/GroupFileController/fileDownload.do?fileId=';
    downloadFile(file.name, `${url}${file.fileId}`);
  };
  return (
    <>
      <Upload
        disabled={detailState}
        accept={accept}
        fileList={value}
        customRequest={uploadFile}
        onChange={onChangeHandle}
        onRemove={onRemove}
        beforeUpload={beforeUpload}
        onPreview={handlerDownloadFile}
        multiple={fileLimit > 1} // 根据fileLimit判断是否支持多文件上传
      >
        {uploadButton}
      </Upload>
    </>
  );
});

export default GroupFileUpload;
