/* eslint-disable camelcase */
import React, { useState, useEffect } from 'react';
import { Card, Descriptions, message, Skeleton } from 'antd';
import { connect } from 'dva';
import moment from 'moment/moment';
import styles from './index.less';
import {
  getAcctPaySubscriberAndAccNum,
  qryAccount,
} from '@/pages/GroupCust/GroupCustView/components/OrderInfo/components/OrderList/components/OrderInstance/services';
import { CUST_CREDIT_LEVEL } from '@/utils/consts';
import SlickTable from '@/components/SlickTable';


const AccountInfo = props => {
  const { offerInst } = props;
  const [accountInfo, setAccountInfo] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [serviceListLoading, setServiceListLoading] = useState(false);

  /* 查询账户信息 */
  const queryAcctInfo = () => {
    setLoading(true);
    qryAccount({
      subscriberInsId: offerInst.SUBSCRIBER_INS_ID,
      data: {
        accessNum: offerInst.ACCESS_NUM,
        queryType: 'accessNum',
      },
    })
      .then(res => {
        const { resultCode, resultObject } = res;
        // eslint-disable-next-line camelcase
        if (resultCode === 'TRUE' && resultObject?.acctList?.length) {
          // eslint-disable-next-line camelcase
          const info = resultObject?.acctList[0] || {};
          // eslint-disable-next-line camelcase
          setAccountInfo(info);
          if (info.acctId) {
            // eslint-disable-next-line no-use-before-define
            queryPayAccList(info?.acctId);
          }
        }
      })
      .always(() => {
        setLoading(false);
      });
  };

  const queryPayAccList = acctId => {
    setServiceListLoading(true);
    getAcctPaySubscriberAndAccNum({
      acctId,
    }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        const info = resultObject?.rspParam?.busiInfo?.outData || [];
        setDataSource(info);
      } else {
        message.error(resultMsg);
      }
      setServiceListLoading(false);
    }).catch(() => {
      setServiceListLoading(false);
    });
  };
  useEffect(() => {
    queryAcctInfo();
  }, []);

  // 变更账户
  // const openChangeAcctPagee = () => {};
  // 账户基本信息
  const renderAccountInfo = () => (
    <Card className={styles.accountCard}>
      <Skeleton active paragraph={{ rows: 10 }} loading={loading}>
        <div className={styles.accountHeader}>
          <div className={styles.accountTitle}>
            <span className={styles.accountName}>
              {accountInfo?.acctName} ({accountInfo?.acctId})
            </span>
            {accountInfo?.isDefault && (
              <span className={`${styles.accountType} ${accountInfo?.isDefault === '默认账户' ? styles.default : styles.black}`}>
                {accountInfo?.isDefault}
              </span>
            )}
          </div>
          <div className={styles.accountAction}>
            {/* <Button onClick={openChangeAcctPagee} type="primary">
              变更账户
            </Button> */}
          </div>
        </div>

        <Descriptions column={3} className={styles.accountDescriptions}>
          <Descriptions.Item label="账户类型">{accountInfo?.acctTypeName}</Descriptions.Item>
          <Descriptions.Item label="消费限额">{accountInfo?.limitFee || '-'}元</Descriptions.Item>
          <Descriptions.Item label="账户状态">{accountInfo?.acctStatusName}</Descriptions.Item>
          <Descriptions.Item label="付费方式">{accountInfo?.payTypeName}</Descriptions.Item>
          <Descriptions.Item label="信用等级">{CUST_CREDIT_LEVEL[accountInfo?.creditLevel]}</Descriptions.Item>
          <Descriptions.Item label="余额">{accountInfo?.balance}元</Descriptions.Item>
          <Descriptions.Item label="信誉度级别">{CUST_CREDIT_LEVEL[accountInfo?.creditLevel]}</Descriptions.Item>
          <Descriptions.Item label="信用额度">{accountInfo?.creditScore}元</Descriptions.Item>
          <Descriptions.Item label="话费">{accountInfo?.real_fee}元</Descriptions.Item>
          <Descriptions.Item label="开户日期">{accountInfo?.createDate}</Descriptions.Item>
        </Descriptions>
      </Skeleton>
    </Card>
  );

  // 付费账户列表
  const renderServiceList = () => (
    <Card className={styles.serviceCard}>
      {/* <div className={styles.searchBar}>
        <Search placeholder="服务号码/用户编码" onSearch={value => console.log(value)} style={{ width: 300 }} enterButton />
      </div> */}

      <SlickTable
        className={styles.serviceTable}
        columns={[
          {
            title: '服务号码',
            dataIndex: 'ACCESS_NUM',
            key: 'accessNum',
          },
          {
            title: '用户实例编码',
            dataIndex: 'SUBSCRIBER_INS_ID',
            key: 'subscriberInsId',
          },
          {
            title: '产品线',
            dataIndex: 'PROD_LINE_NAME',
            key: 'prodLineName',
          },
          {
            title: '生效时间',
            dataIndex: 'VALID_DATE',
            key: 'validDate',
            render: val => moment(val).format('YYYY-MM-DD HH:mm:ss'),
          },
          {
            title: '失效时间',
            dataIndex: 'EXPIRE_DATE',
            key: 'validDate',
            render: val => moment(val).format('YYYY-MM-DD HH:mm:ss'),
          },
        ]}
        dataSource={dataSource}
        loading={serviceListLoading}
        pagination={{
          total: dataSource.length,
          showTotal: total => `共 ${total} 条`,
          pageSize: 10,
        }}
        size="middle"
        bordered
      />
    </Card>
  );

  return (
    <div className={styles.accountInfoContainer}>
      {accountInfo.length !== 0 && renderAccountInfo()}
      {renderServiceList()}
    </div>
  );
};

export default connect()(AccountInfo);
