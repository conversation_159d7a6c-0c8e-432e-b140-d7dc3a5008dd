import { useRef, useEffect } from 'react';
import isEqual from 'lodash/isEqual';

const depsEqual = (aDeps = [], bDeps = []) => {
  return isEqual(aDeps, bDeps);
};

export const createDeepCompareEffect = hook => (effect, deps) => {
  const ref = useRef();
  const signalRef = useRef(0);

  if (deps === undefined || !depsEqual(deps, ref.current)) {
    ref.current = deps;
    signalRef.current += 1;
  }

  hook(effect, [signalRef.current]);
};

export default createDeepCompareEffect(useEffect);
