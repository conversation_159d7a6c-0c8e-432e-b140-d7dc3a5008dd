/* 集团客户视图 */
import { connect } from 'dva';
import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Card } from 'antd';
import style from './index.less';
import Right from '@/pages/GroupCust/GroupCustView/components/Right';
import LeftMenu from '@/pages/GroupCust/GroupCustView/components/LeftMenu';

const Index = props => {
  const [height, setHeight] = useState('');
  const [searchModalVisible, setSearchModalVisible] = useState(false);

  const location = useLocation();
  const { groupId } = location?.query;

  // 路由获取集团客户编码
  useEffect(() => {
    props.dispatch({
      type: 'groupCustView/getCustomerInfo',
      payload: atob(groupId),
    });
  }, [groupId]);

  // effect 监听窗口变化
  useEffect(() => {
    setHeight(props.size.height);
  }, [props.size.height]);

  // 添加键盘快捷键监听
  useEffect(() => {
    const handleKeyDown = e => {
      // 检测 Ctrl+K 组合键
      if ((e.ctrlKey || e.metaKey) && (e.key === 'k' || e.key === 'K')) {
        e.preventDefault();
        setSearchModalVisible(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <div className={style.groupCustView}>
      {/* 左侧 */}
      <div className={style.groupCustViewLeft}>
        <Card
          style={{ height, border: 'none', paddingTop: '8px' }}
        >
          <LeftMenu
            searchModalVisible={searchModalVisible}
            setSearchModalVisible={setSearchModalVisible}
          />
        </Card>
      </div>

      {/* 右侧 */}
      <div className={style.groupCustViewRight}>
        <Right />
      </div>
    </div>
  );
};
export default connect(({ setting }) => ({
  size: setting.size,
}))(Index);
