import React, { useEffect } from 'react';
import { connect } from 'dva';
import { Card, Badge, Empty } from 'antd';
import classNames from 'classnames';
import Link from 'umi/link';
import moment from 'moment';
import random from 'lodash/random';
import useInterval from '@/hooks/useInterval';
import styles from './newBulletin.less';

const points = ['processing', 'success', 'default', 'error', 'warning'];

/**
 * 1. 首页公告模块
 * - 获取公告数据、允许展示的条数和轮询间隔，并开启轮询定时更新列表
 * - 获取重要信息的轮询间隔和允许弹窗的数量，然后开始轮询重要公告，若存在则右下角弹出提示
 *
 */
function NewBulletin({ dispatch, list, popupInterval, bulletinInterval, initLoading }) {
  useEffect(() => {
    dispatch({
      type: 'newBulletin/getInitData',
      payload: {
        defaultPopupInterval: 60 * 1000,
        defaultPopupCount: 5,
        defaultBulletinInterval: 60 * 1000,
        defaultBulletinShowCount: 5,
      },
    });
  }, [dispatch]);

  // 轮询，定时更新公告列表。undefined表示bulletinInterval在model中未被初始化过
  useInterval(() => {
    if (bulletinInterval !== undefined) {
      dispatch({
        type: 'newBulletin/pollBulletin',
      });
    }
  }, bulletinInterval);

  // 轮询，若有重要信息则右下角弹窗。undefined表示popupInterval在model中未被初始化过
  useInterval(() => {
    if (popupInterval !== undefined) {
      dispatch({
        type: 'newBulletin/pollImportant',
      });
    }
  }, popupInterval);

  return (
    <Card
      className={classNames(styles.projectList)}
      title="系统公告"
      bordered
      extra={<Link to="/bulletin">更多</Link>}
      bodyStyle={{ height: '98px' }}
      loading={initLoading}
    >
      {list.length > 0 ? (
        list.map(item => (
          <Card.Grid className={styles.projectGrid} key={item.id} hoverable={false}>
            <Card bodyStyle={{ padding: 0 }} bordered={false}>
              <Card.Meta
                title={(
                  <div className={styles.cardTitle}>
                    <Badge status={points[random(0, 4)]} />
                    {/**
                       * TODO: 跳转到对应的页面
                       */}
                    <Link to="/bulletin">{item.bulletin.bulletinTitle}</Link>
                    {item.createDate && (
                      <div className={styles.datetime} title={item.createDate}>
                        {moment(item.createDate).fromNow()}
                      </div>
                    )}
                  </div>
                )}
                description={item.bulletin.bulletinContent}
              />
            </Card>
          </Card.Grid>
        ))
      ) : (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="岁月静好，无事相告"
          style={{ margin: 0 }}
        />
      )}
    </Card>
  );
}

export default connect(({ newBulletin, loading }) => ({
  list: newBulletin.list,
  popupInterval: newBulletin.bulletinInterval,
  bulletinInterval: newBulletin.bulletinInterval,
  initLoading: loading.effects['bulletin/getInitData'],
}))(NewBulletin);
