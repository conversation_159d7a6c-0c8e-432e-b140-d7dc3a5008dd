@import '~antd/lib/style/themes/default.less';
@import '~@/less/utils.less';
.projectList {
  overflow: hidden;
  :global {
    .ant-card-meta-description {
      height: 44px;
      overflow: hidden;
      color: @text-color-secondary;
      line-height: 22px;
    }
  }
  .cardTitle {
    font-size: 0;
    a {
      display: inline-block;
      height: 24px;
      // margin-left: 12px;
      color: @heading-color;
      font-size: @font-size-base;
      line-height: 24px;
      vertical-align: top;
      &:hover {
        color: @primary-color;
      }
    }
  }
  .projectGrid {
    width: 33.33%;
    border-right: 1px solid #e8e8e8;
    box-shadow: none;
    &:last-child {
      border-right-width: 0;
    }
  }
}

.datetime {
  float: right;
  color: @disabled-color;
  font-size: 12px;
}
