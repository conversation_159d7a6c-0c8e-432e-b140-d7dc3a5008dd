/* eslint-disable global-require */
import { Result } from 'antd';
import { connect } from 'dva';
import React, { useEffect } from 'react';
import OfferList from '../components/OfferList';
import style from './search.less';

const Search = props => {
  const { prodList, dispatch, location, tmp } = props;
  const searchKey = location.query.searchkey;
  const isSearch = true;

  useEffect(() => {
    dispatch({
      type: 'offer/getProdListByKey',
      payload: searchKey,
    });
    dispatch({
      type: 'offer/getSearchHis',
    });
    dispatch({
      type: 'offer/saveCurSearchKey',
      payload: searchKey,
    });
  }, [searchKey])

  return (
    <div className={style.wrap}>
      {
        prodList?.length > 0 && prodList.map((item, key) => (
          <div key={item.catalogId}>
            <div
              className={style.topImage}
              style={{
                backgroundImage: key % 2 === 0 ? `url(${require('./img/1.png')}) ` : `url(${require('./img/3.png')}) `,
              }}
            >
              <span>{item.catalogName}</span>
            </div>
            <OfferList list={item.offerDatas} data={item} isSearch={isSearch} />
          </div>
        ))
      }
      {
        tmp === '2' && (
          <Result
            title="搜索结果为空"
          />
        )
      }
    </div>
  )
}

export default connect(({ offer }) => ({
  prodList: offer.prodList,
  tmp: offer.tmp,
}))(Search);
