/* 客户经理信息 */
import React, { useCallback, useEffect, useState } from 'react';
import { Col, Form, Input } from 'antd';
import { connect } from 'dva';
// import styles from '@/pages/GroupCust/GroupCustManage/components/Edit/index.less';
// import QueryCustManager from '@/pages/GroupCust/GroupCustManage/components/Edit/components/QueryCustManager';
import { validateEmail, validatePhone } from '@/utils/validator';
import styles from '../index.less';
import CustomGrid from '@/components/CustomGrid';

const FormItem = Form.Item;
const formItemLayout = {
  // labelAlign: 'left',
  // labelCol: {
  //   xs: { span: 24 },
  //   // sm: { span: 4 },
  // },
  // wrapperCol: {
  //   xs: { span: 24 },
  //   sm: { span: 16 },
  // },
};
const CustManager = props => {
  const { form, dispatch, onValuesChange, customerInfo, viewMode } = props;
  const { getFieldDecorator, setFieldsValue } = form;
  // const [openCard, setOpenCard] = useState(true); // 非必填字段
  const [selectedManager, setSelectedManager] = useState([]);

  const setAreaInfo = manage => {
    const mgmtDistrict = manage.MGMT_DISTRICT.length === 3 ? manage.MGMT_DISTRICT : manage.MGMT_DISTRICT.substring(1, 4);
    const mgmtCounty = Number.isNaN(manage.MGMT_COUNTY) ? manage.MGMT_COUNTY.substring(1, 5) : manage.MGMT_COUNTY.substring(0, 4);
    onValuesChange(
      {
        'RegionInfo.MGMT_DISTRICT': mgmtDistrict,
        'RegionInfo.MGMT_COUNTY': mgmtCounty,
        'RegionInfo.CHIP_AREA_CODE': mgmtCounty,
      },
      true
    );
    dispatch({
      type: 'AddGroupCust/callComponentMethod',
      payload: { componentName: 'RegionInfo', methodName: 'queryCounty', args: mgmtDistrict },
    });
    dispatch({
      type: 'AddGroupCust/callComponentMethod',
      payload: { componentName: 'RegionInfo', methodName: 'queryRegionDetails', args: mgmtDistrict },
    });
  };

  const handleSelect = selected => {
    if (!selected || selected.length === 0) return;
    const [row] = selected;
    const params = {
      CUST_MGR_INFO: `[${row.CUST_MGR_ID}]${row.STAFF_NAME}`,
      CUST_MGR_ID: row.CUST_MGR_ID,
      CUST_MANAGER_TYPE: row.MANAGER_TYPE,
      MANAGER_PHONE: row.BILL_ID,
      MANAGER_EMAIL: row?.EMAIL,
      MANAGER_OFFICE: row.OFFICE_TEL,
      custMgrInfo: row,
      BelongingAreaInfo: [row.MGMT_DISTRICT, row.MGMT_COUNTY],
    };

    setFieldsValue(params);
    // 保存选中的客户经理信息
    setSelectedManager(selected);
    // 旧的操作员ID
    // const ordCustMsgId = formData.CUST_MGR_ID;
    onValuesChange(
      {
        'CustManager.CUST_MGR_INFO': row,
        'CustManager.CUST_MGR_ID': row?.CUST_MGR_ID,
        'CustManager.MANAGER_PHONE': row?.BILL_ID,
        'CustManager.MANAGER_OFFICE': row?.OFFICE_TEL,
        'CustManager.MANAGER_EMAIL': row?.EMAIL,
        'CustManager.CUST_MANAGER_TYPE': row?.MANAGER_TYPE,
      },
      true
    );
    // 设置区域信息
    setAreaInfo(row);

    // 若CMIOT的按钮是1-开启
    // const isCmiot = formData.IS_CMIOT;
    // if (isCmiot === '1' && ordCustMsgId !== row.CUST_MGR_ID) {
    //   dispatch({
    //     type: 'AddGroupCust/callComponentMethod',
    //     payload: { componentName: 'BaseInfo', methodName: 'changeToCmiot', args: 1 },
    //   });
    // }
  };

  const updateFormField = useCallback(
    data => {
      if (!data.field) return;
      form.setFieldsValue({
        [data.field]: data.value,
      });
    },
    [form]
  );

  // 初始化时，如果有customerInfo，则设置到selectedManager
  useEffect(() => {
    if (customerInfo) {
      // setSelectedManager([customerInfo]);
      setFieldsValue({
        custMgrInfo: [customerInfo],
        CUST_MGR_INFO: `[${customerInfo?.CUST_MGR_ID}]${customerInfo?.STAFF_NAME}` || '',
        CUST_MGR_ID: customerInfo?.CUST_MGR_ID || '',
        CUST_MANAGER_TYPE: customerInfo?.MANAGER_TYPE || '',
        MANAGER_PHONE: customerInfo?.BILL_ID || '',
        MANAGER_EMAIL: customerInfo?.EMAIL || '',
        MANAGER_OFFICE: customerInfo?.OFFICE_TEL || '',
        BelongingAreaInfo: [customerInfo.MGMT_DISTRICT, customerInfo.MGMT_COUNTY],
      });
    }
  }, [customerInfo, setFieldsValue]);

  useEffect(() => {
    dispatch({
      type: 'AddGroupCust/registerMethods',
      payload: {
        componentName: 'CustManager',
        methods: { updateFormField },
      },
    });

    return () => {
      dispatch({
        type: 'AddGroupCust/unregisterMethods',
        payload: { componentName: 'CustManager' },
      });
    };
  }, [dispatch, updateFormField]);

  return (
    <>
      <Col span={8}>
        <FormItem label="客户经理" {...formItemLayout} className={styles.textAreaFormItem}>
          {getFieldDecorator('custMgrInfo', {
            initialValue: selectedManager,
            rules: [{ required: true, message: '客户经理不能为空！' }],
          })(

            /* <QueryCustManager className={styles.inputStyle} handleSelectRow={handleSelect} customerInfo={selectedManager} /> */
            <CustomGrid
              searchFields={[
                {
                  name: 'staffName',
                  label: '姓名',
                  type: 'input',
                  span: 12,
                },
                {
                  name: 'billId',
                  label: '手机号码',
                  type: 'input',
                  span: 12,
                  rules: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }],
                },
              ]}
              onConfirm={e => handleSelect(e)}
              url="portal/GroupEnterpriseController/queryAllCustManagerByPage.do"
              popupStyle={{ width: 560 }}
              label="STAFF_NAME"
              destroyPopupOnHide
              rowKey="CUST_MGR_ID"
              pick="radio"
              disabled={viewMode === 'view'}
              columns={[

                /* {
                        title: '经理编号',
                        dataIndex: 'CUST_MGR_ID',
                        ellipsis: true,
                      }, */
                {
                  title: '姓名',
                  dataIndex: 'STAFF_NAME',
                  ellipsis: true,
                },
                {
                  title: '手机号码',
                  ellipsis: true,
                  dataIndex: 'BILL_ID',
                },
                {
                  title: '所属地市',
                  dataIndex: 'MGMT_DISTRICT',
                  ellipsis: true,
                },
              ]}
            />
          )}
        </FormItem>
      </Col>
      <Col span={8} hidden>
        <FormItem label="客户经理" {...formItemLayout} className={styles.textAreaFormItem}>
          {getFieldDecorator(
            'CUST_MGR_INFO',
            {
              initialValue: `[${customerInfo?.CUST_MGR_ID}]${customerInfo?.STAFF_NAME}` || '',
            },
            { rules: [{ required: true, message: '客户经理不能为空！' }] }
          )(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
        </FormItem>
      </Col>
      <Col span={8} hidden>
        <FormItem label="CUST_MGR_ID" {...formItemLayout} className={styles.textAreaFormItem}>
          {getFieldDecorator('CUST_MGR_ID', {
            initialValue: customerInfo?.CUST_MGR_ID || '',
          })(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
        </FormItem>
      </Col>
      <Col span={8} hidden>
        <FormItem label="CUST_MANAGER_TYPE" {...formItemLayout} className={styles.textAreaFormItem}>
          {getFieldDecorator('CUST_MANAGER_TYPE', {
            initialValue: customerInfo?.MANAGER_TYPE || '',
          })(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
        </FormItem>
      </Col>
      <Col span={8} hidden>
        <FormItem label="手机号码" {...formItemLayout} className={styles.textAreaFormItem}>
          {getFieldDecorator(
            'MANAGER_PHONE',
            {
              initialValue: customerInfo?.BILL_ID || '',
            },
            {
              rules: [{ required: true, message: '手机号不能为空' }, { validator: validatePhone }],
            }
          )(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
        </FormItem>
      </Col>
      <Col span={8} hidden>
        <FormItem label="电子邮件" {...formItemLayout} className={styles.textAreaFormItem}>
          {getFieldDecorator(
            'MANAGER_EMAIL',
            {
              initialValue: customerInfo?.EMAIL || '',
            },
            {
              rules: [{ required: true, message: '电子邮件不能为空' }, { validator: validateEmail }],
            }
          )(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
        </FormItem>
      </Col>
      <Col span={8} hidden>
        <FormItem label="办公电话" {...formItemLayout} className={styles.textAreaFormItem}>
          {getFieldDecorator('MANAGER_OFFICE', {
            initialValue: customerInfo?.OFFICE_TEL || '',
          })(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
        </FormItem>
      </Col>
      <Col />
      <Col />
    </>
  );
};

export default connect(({ AddGroupCust }) => ({
  formData: AddGroupCust.currentForm,
}))(Form.create()(CustManager));
