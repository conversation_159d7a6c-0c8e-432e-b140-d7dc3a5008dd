// utils/deepUpdate.js
export const deepUpdate = (obj, path, value) => {
  // 安全处理路径参数
  const pathArray = typeof path === 'string' ? path.split('.') : path;

  // 递归终止条件
  if (pathArray.length === 0) return value;

  const [currentKey, ...remainingKeys] = pathArray;

  // 处理数组索引的情况（如 'list.0.name'）
  // eslint-disable-next-line no-restricted-globals
  const isArrayIndex = !isNaN(currentKey) && Array.isArray(obj);

  return {
    ...(obj || {}), // 确保原始对象不变
    [currentKey]: remainingKeys.length
      ? deepUpdate(obj?.[currentKey] || {}, remainingKeys, value)
      : value,
  };
};
export default {
  namespace: 'GroupCustManage',

  state: {
    groupCertList: [],
    groupCustTypeList: [],
    formData: {
      add: {
        AE_INFO: {
          GROUP_NAME: '测试集客AA百六十六', // 集团名称
        },
        OPER_TYPE: '1',
        busiCode: 'ESOP_APP_EC_CUST_OPER',
        operCode: '1',
      },
    },
  },

  effects: {

    *initCustTypeList(_, { call, put }) {
      // todo: 补充接口
      // const res = yield call(getCustTypeList);
      const res = {
        resultCode: '0',
        resultData: [
          { code: 100000, name: '军队' },
          { code: 100001, name: '政府机关' },
          { code: 100002, name: '事业单位' },
          { code: 100003, name: '企业' },
          { code: 100004, name: '社会团体' },
          { code: 100005, name: '民办非企业' },
          { code: 100006, name: '基金会' },
          { code: 100007, name: '律师事务所' },
          { code: 100008, name: '外国文化中心' },
          { code: 100009, name: '群团组织' },
          { code: 100010, name: '村委会' },
          { code: 100011, name: '个体工商户及小微企业' },
        ],
      };
      const { resultCode, resultData } = res;

      if (resultCode === '0') {
        yield put({
          type: 'save',
          payload: {
            groupCustTypeList: resultData,
          },
        });
      }
    },
    *initGroupCertList(_, { call, put }) {
      // todo: 补充接口
      // const res = yield call(getCertTypeList);
      const res = {
        resultCode: '0',
        resultData: [
          { idenTypeCode: 200001, idenTypeId: 200000001, name: '税务等级证' },
          { idenTypeCode: 200002, idenTypeId: 200000002, name: '单位公章/营业执照' },
          { idenTypeCode: 200003, idenTypeId: 200000003, name: '事业单位法人证件' },
          { idenTypeCode: 200004, idenTypeId: 200000004, name: '社会团体法人登记证件' },
          { idenTypeCode: 200006, idenTypeId: 200000006, name: '单位证件' },
          { idenTypeCode: 200009, idenTypeId: 200000009, name: '集团单位组织机构代码' },
          { idenTypeCode: 200010, idenTypeId: 200000010, name: '新版营业执照（三证合一）' },
          { idenTypeCode: 200017, idenTypeId: 200000017, name: '未确定' },
          { idenTypeCode: 100019, idenTypeId: 100000019, name: '介绍信' },
          { idenTypeCode: 200018, idenTypeId: 200000018, name: '特殊单位公函' },
          { idenTypeCode: 300000, idenTypeId: 300000000, name: '军队代码' },
          { idenTypeCode: 300001, idenTypeId: 300000001, name: '有偿服务许可证' },
          { idenTypeCode: 300002, idenTypeId: 300000002, name: '事业单位法人证书' },
          { idenTypeCode: 300004, idenTypeId: 300000004, name: '社团法人证书' },
          { idenTypeCode: 300005, idenTypeId: 300000005, name: '宗教活动场所登记证' },
          { idenTypeCode: 300006, idenTypeId: 300000006, name: '民办非企业单位登记证书' },
          { idenTypeCode: 300007, idenTypeId: 300000007, name: '基金会法人登记证书' },
          { idenTypeCode: 300008, idenTypeId: 300000008, name: '律师事务所执业许可证' },
          { idenTypeCode: 300009, idenTypeId: 300000009, name: '单位证明' },
          { idenTypeCode: 300010, idenTypeId: 300000010, name: '个人有效证件+店铺门头照' },
          { idenTypeCode: 100001, idenTypeId: 100000001, name: '身份证' },
          { idenTypeCode: 100002, idenTypeId: 100000002, name: '户口薄' },
          { idenTypeCode: 100003, idenTypeId: 100000003, name: '驾驶证' },
          { idenTypeCode: 100005, idenTypeId: 100000005, name: '护照' },
          { idenTypeCode: 100010, idenTypeId: 100000010, name: '军官证' },
          { idenTypeCode: 100011, idenTypeId: 100000011, name: '警官证' },
          { idenTypeCode: 100014, idenTypeId: 100000014, name: '社会保险号' },
        ],
      };
      const { resultCode, resultData } = res;

      if (resultCode === '0') {
        yield put({
          type: 'save',
          payload: {
            groupCertList: resultData,
          },
        });
      }
    },
    *initStaticData(_, { put }) {
      yield put({
        type: 'initGroupCertList',
      });
      yield put({
        type: 'initCustTypeList',
      });
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    // 更新formData（支持多级路径）
    updateFormDataField(state, { payload }) {
      // mode 区分 add,mode
      const { mode, path, value } = payload;
      return {
        ...state,
        formData: {
          ...state.formData,
          [mode]: deepUpdate(state.formData[mode], path, value),
        },
      };
    },
  },
};
