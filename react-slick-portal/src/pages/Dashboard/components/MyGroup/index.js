import React, { useState } from 'react';
import { Icon } from 'antd';
import { Portal } from '@/utils/utils';
import CommonHeader from '../CommonHeader';
import Profiling from './components/Profiling';
import Development from './components/Development';
import styles from './myGroup.less';

const MyGroup = () => {
  const tabs = [
    { value: 'pro', label: '客户概况', content: <Profiling /> },
    { value: 'dev', label: '客户发展', content: <Development /> },
  ];
  const [activeTab, setActiveTab] = useState('pro');

  const toGroupView = () => {
    const path = activeTab === 'pro' ? '/MyGroupView?menuIndex=0' : '/MyGroupView?menuIndex=1';
    Portal.open(path);
  };

  return (
    <>
      <CommonHeader
        title="我的集团"
        isFlag
        extra={(
          <div style={{ cursor: 'pointer' }} onClick={toGroupView}>
            <span style={{ color: '#999', fontSize: '12px' }}>查看全部</span>
            <Icon type="right" style={{ marginLeft: 8 }} />
          </div>
        )}
      />
      <div className={styles.groupContainer}>
        <div className={styles.tabsBox}>
          {tabs.map(tab => (
            <div
              key={tab.value}
              className={`${styles.tabItem} ${activeTab === tab.value ? styles.tabItemActive : ''}`}
              onClick={() => setActiveTab(tab.value)}
            >
              {tab.label}
            </div>
          ))}
        </div>
        <div className={styles.content}>
          {(() => {
            const ComponentMap = {
              pro: Profiling,
              dev: Development,
            };
            const Component = ComponentMap[activeTab];
            return Component ? <Component /> : null;
          })()}
        </div>
      </div>
    </>
  );
};

export default MyGroup;
