@import '~antd/lib/style/themes/default.less';
@import './variables.less';

.navMenu {
  height: @header-height;
  overflow: hidden;
  :global(.ant-menu) {
    color: @header-blue-text;
  }
  :global(.ant-menu-horizontal) {
    line-height: @header-height !important;
    background: @header-blue-bg;
  }
  :global(.ant-menu-horizontal > li > a) {
    color: @header-blue-text !important;
  }

  :global(.ant-menu-horizontal .ant-menu-submenu-title) {
    color: @header-blue-text !important;
  }
  :global(.ant-menu-item:hover) {
    background: @header-action-active-blue-bg;
  }
  :global(.ant-menu-item-active) {
    background: @header-action-active-blue-bg;
  }
  :global(.ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open) {
    background: @header-action-active-blue-bg;
  }
  :global(.ant-menu-submenu-active) {
    background: @header-action-active-blue-bg;
  }
  :global(.ant-menu-submenu-title:hover) {
    background: @header-action-active-blue-bg;
  }
}
