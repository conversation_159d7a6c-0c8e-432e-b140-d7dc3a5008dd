.row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.unit {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100px;
  margin-bottom: 16px;

  .icon {
    width: 39px;
    height: 38px;
    margin-bottom: 6px;
    cursor: pointer;
  }

  .text {
    width: 100px;
    overflow: hidden;
    color: rgba(102, 102, 102, 1);
    font-size: 14px;
    font-weight: 400;
    // font-size: 12px;
    line-height: 22px;
    white-space: nowrap;
    text-align: center;
    text-overflow: ellipsis;
    cursor: pointer;
  }
}

.wrap {
  .quickLink {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    width: 85px;
    height: 66px;
    margin: 24px 0 0 20px;

    .menuName {
      color: #555864;
      font-size: 14px;
      text-align: center;
    }
    img {
      width: 30px !important;
      height: 30px !important;
      margin-right: 0 !important;
      margin-bottom: 4px !important;
      transition: all 0.2s;

      &:hover {
        transform: translateY(-4px);
      }
    }
  }

  :global(.ant-card-head-wrapper) {
    height: 48px;
  }
  :global(.ant-card-head-title) {
    font-size: 16px;
  }

  @media screen and (min-width: 1446px) and (max-width: 1498px) {
    .quickLink {
      margin-left: 10px;
    }
  }
  @media screen and (min-width: 1496px) and (max-width: 1566px) {
    .quickLink {
      margin-left: 12px;
    }
  }
}
