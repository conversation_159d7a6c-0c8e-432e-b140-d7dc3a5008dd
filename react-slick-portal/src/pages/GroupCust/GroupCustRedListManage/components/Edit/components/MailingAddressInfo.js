import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Form, message } from 'antd';
import { connect } from 'dva';
import { MailingAddressInfoFields } from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/const';
import { queryPartyCmAddressByPartyId } from '@/pages/GroupCust/GroupCustRedListManage/services';

const MailingAddressInfo = props => {
  const { baseInfo } = props;
  const [mailingAddressInfo, setMailingAddressInfo] = useState({});
  const [openCard, setOpenCard] = useState(true); // 是否展开非必填字段

  useEffect(() => {
    if (!baseInfo.ORGA_ENTERPRISE_ID) {
      // eslint-disable-next-line no-console
      console.log('partyId is null!');
      return;
    }
    queryPartyCmAddressByPartyId({ partyId: baseInfo.ORGA_ENTERPRISE_ID }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        const data = Array.isArray(resultObject?.rspParam?.busiInfo?.outData) ? resultObject.rspParam.busiInfo.outData[0] : {};
        setMailingAddressInfo(data);
      } else {
        message.error(resultMsg);
      }
    });
  }, [baseInfo.ORGA_ENTERPRISE_ID]);

  return (
    <Card
      title="邮寄地址"
      id="panel-0"
      className="cute3"
      extra={!openCard ? <a onClick={() => setOpenCard(true)}>隐藏</a> : <a onClick={() => setOpenCard(false)}>展开</a>}
    >
      {openCard ? (
        <Descriptions className="cuteLabelBold" column={{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }}>
          {MailingAddressInfoFields.map(({ name, key, span, options }) => (
            <Descriptions.Item label={name} span={span} key={key}>
              {!options ? mailingAddressInfo?.[key] : options[mailingAddressInfo?.[key]] ?? mailingAddressInfo?.[key]}
            </Descriptions.Item>
          ))}
        </Descriptions>
      ) : (
        <></>
      )}
    </Card>
  );
};

export default connect()(Form.create()(MailingAddressInfo));
