@import '../../../less/variable.less';

.mulitWrap {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  width: 1200px;
  height: 579px;
  overflow: auto;

  .left {
    width: 168px;
    height: 100%;
    border-right: 1px solid #eee;

    ul {
      padding-left: 0;
      li {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        height: 50px;
        padding-right: @padding-xl;
        color: @color-text-base;
        font-weight: 400;
        line-height: 24px;
        cursor: pointer;

        .icon {
          display: none;
        }

        &.check {
          color: @primary-color;
          font-weight: 600;

          .icon {
            display: inline-block;
          }
        }
      }
    }
  }

  .right {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;

    .search {
      box-sizing: border-box;
      width: 100%;
      padding: 0 @padding-l;

      :global(.ant-input) {
        height: 30px;
        color: #999;
        background: #eee;
      }
    }

    .navContainer {
      width: 100%;
      padding: @padding-l;

      .navTitle {
        display: inline-block;
        padding-bottom: 19px;
        color: #000;
        font-weight: 700;
        font-size: 14px;
      }

      &::before {
        display: inline-block;
        width: 3px;
        height: 12px;
        margin-right: @padding-xs;
        background: @primary-color;
        content: '';
      }

      .titleAContainer {
        width: 220px;
        border-bottom: 1px solid #eee;

        .titleA {
          display: inline-block;
          padding-bottom: @padding-m;
          color: #000;
          font-weight: 500;
          font-size: @font-size-lg;
        }
      }

      .titleB {
        display: inline-block;
        padding-bottom: @padding-m;
        padding-left: @padding-m;
        color: @color-text-base;
        font-size: @font-size-base;
      }

      .link {
        font-size: @font-size-sm;
      }

      .container {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: flex-start;
        justify-content: flex-start;
        width: 100%;
        padding-left: 0;

        li {
          box-sizing: border-box;
          min-width: 25%;
          padding-right: @padding-m;
          padding-bottom: @padding-l;
          list-style: none;
        }
      }
    }

    .subMenuContainer {
      display: flex;
      flex-direction: row;
      align-content: flex-start;
      justify-content: flex-start;
      width: 100%;

      > div {
        box-sizing: border-box;
        width: 220px;
        padding-right: @padding-m;
      }

      .linkList {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: flex-start;
        justify-content: flex-start;
        padding-right: 0;

        a {
          width: 33%;
          padding: @padding-s 0;
          padding-right: @padding-s;
          color: @color-text-base;
          &:hover {
            color: @primary-color;
          }
        }
      }
    }

    .offersContainer {
      width: 1000px;

      .linkList {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: flex-start;
        justify-content: flex-start;
        padding-right: 0;

        a {
          box-sizing: content-box;
          width: 7%;
          margin-right: 10px;
          padding: @padding-s 0;
          color: @color-text-base;
          &:hover {
            color: @primary-color;
          }
        }

        a:nth-child(3n) {
          margin-right: 20px;
        }

        a:nth-child(3n + 1):nth-child(-n + 10) {
          position: relative;

          &::before {
            position: absolute;
            top: 0;
            left: 0;
            display: inline-block;
            width: 220px;
            height: 1px;
            background: #eee;
            content: '';
          }
        }
      }
    }

    .thirdContainer {
      width: 100%;
      border-bottom: 1px solid #eee;
    }
  }
}
