import React from 'react';
import { formatMessage } from 'umi/locale';
import { connect } from 'dva';
import { Button } from 'antd';
import Link from 'umi/link';
import Exception from '@/components/Exception';

const Exception500 = ({ viewMode, dispatch }) => (
  <Exception
    type="500"
    desc={formatMessage({ id: 'app.exception.description.500' })}
    actions={
      <Button
        type="primary"
        size="large"
        onClick={() =>
          dispatch({
            type: 'login/clear',
          })
        }
      >
        返回首页
      </Button>
    }
    viewMode={viewMode}
    linkElement={Link}
    backText={formatMessage({ id: 'app.exception.back' })}
  />
);

export default connect(({ setting }) => ({
  viewMode: setting.viewMode,
}))(Exception500);
