/*自定义标签页 start */
.tabsCustom {
  :global {
    .ant-tabs-bar{
      border: none !important;
    }
    .ant-tabs-nav {
      .ant-tabs-tab {
        height: 32px;
        background-color: #F0F0F0;
        border-radius: 4px 4px 4px 4px;
        padding: 7px 14px;
        margin: 0 8px 0 0;
        color: #333;
        font-size: 12px;
        transition: all 0.3s ease;

        &.ant-tabs-tab-active {
          background-color: #E6FAFF;
          border: 1px solid #4BBBEB;
          color: #0067AB;
          border-radius: 4px 4px 4px 4px;
          font-weight: 400;
        }
      }

      .ant-tabs-ink-bar {
        display: none !important;
      }
    }
  }
}
/*自定义标签页 end */
