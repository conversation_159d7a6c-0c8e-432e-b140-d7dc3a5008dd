import { pick } from 'lodash';
import { message } from 'antd';
import cloneDeep from 'lodash/cloneDeep';
import { getCustomConfigData, saveCustomNavigation, getSceneSelectData } from '@/services/customNavigation';

/*
* 导航菜单卡片
*/
export default {
  namespace: 'customNavigation',
  state: {
    configVisible: false,
    universalData: [], // 通用导航实际数据
    sceneData: [], // 场景导航实际数据
    universalTempData: [], // 通用导航操作数据（用于暂存配置）
    sceneTempData: [], // 场景导航操作数据（用于暂存配置）
    sceneSelectMenus: [], // 场景专区可选菜单
  },

  effects: {
    *initCustomConfig(payload, { call, put }) {
      let universalArr = [];
      // const sceneArr = [];

      // 查询通用导航数据
      const result = yield call(getCustomConfigData, {
        belongType: 'TYDH',
      });
      if (result?.resultObject?.object?.length) {
        universalArr = result.resultObject.object;

        // 按组编码中时间戳升序
        universalArr.sort((a, b) => {
          const timeStampA = Number(a.belongCode.split('TYDH')[1]);
          const timeStampB = Number(b.belongCode.split('TYDH')[1]);
          return timeStampA - timeStampB;
        });
      }

      // 查询场景专区数据，这里先去掉
      // const resultScene = yield call(getCustomConfigData, {
      //   belongType: 'CJZQ',
      // });
      // if (resultScene?.resultObject?.object?.length) {
      //   const objectArr = resultScene.resultObject.object;
      //   if (objectArr[0].list?.length) {
      //     sceneArr = objectArr[0].list;
      //   }
      // }

      yield put({
        type: 'saveInitData',
        payload: {
          universalData: universalArr || [],
          // sceneData: sceneArr || [],
        },
      });
      if (payload && payload?.payload?.action === 'saveNullScene') {
        const getList = cloneDeep(universalArr);
        const res = yield call(saveCustomNavigation, {
          navigationConfigList: [[...getList], []],
        });
        if (res?.success) {
          yield put({
            type: 'hotSceneNav/initCustomConfig',
          });
          message.success('保存成功');
        }
      }
    },
    *getSceneSelectMenus(_, { call, put }) {
      const result = yield call(getSceneSelectData);
      if (result?.resultObject?.length) {
        yield put({
          type: 'saveSceneSelectMenus',
          payload: result.resultObject,
        });
      }
    },
    *saveCustomConfig(_, { select, call, put }) {
      const { universalTempData } = yield select(state => state.customNavigation);

      const needParams = ['menuId', 'menuCode', 'menuName', 'menuType', 'urlAddr', 'systemInfoId', 'iconUrl', 'menuOpenMode'];
      const newUniversalData = universalTempData.reduce((preArr, curItem) => {
        const { belongCode, belongName, list } = curItem;
        const groupObj = { belongCode, belongName, belongType: 'TYDH', statusCd: 1000, relType: 1000 };
        const lists = list.map((item, index) => {
          const newItem = pick(item, needParams);
          return { ...newItem, ...groupObj, sort: index };
        });
        return [...preArr, ...lists];
      }, []);
      // const groupObj = { belongCode: 'CJZQ', belongName: '场景专区', belongType: 'CJZQ', statusCd: 1000, relType: 1000 };
      // const newSceneData = sceneTempData.map((item, index) => {
      //   const newItem = pick(item, needParams);
      //   return { ...newItem, ...groupObj, sort: index };
      // });
      const result = yield call(saveCustomNavigation, {
        navigationConfigList: [...newUniversalData],
      });
      if (result?.success) {
        yield put({
          type: 'initCustomConfig',
        });
        message.success('保存成功');
      }
    },
  },

  reducers: {
    saveInitData(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    saveUniversalData(state, { payload }) {
      return {
        ...state,
        universalData: payload,
      };
    },
    saveUniversalTempData(state, { payload }) {
      return {
        ...state,
        universalTempData: payload,
      };
    },
    saveSceneData(state, { payload }) {
      return {
        ...state,
        sceneData: payload,
      };
    },
    saveSceneTempData(state, { payload }) {
      return {
        ...state,
        sceneTempData: payload,
      };
    },
    saveSceneSelectMenus(state, { payload }) {
      return {
        ...state,
        sceneSelectMenus: payload,
      };
    },
    toggleVisible(state, { payload }) {
      return {
        ...state,
        configVisible: payload,
      };
    },
  },
};
