import React, { useState, useEffect } from 'react';
import { Card, Empty } from 'antd';
import numeral from 'numeral';
import request from '@/utils/request';
import styles from './rank.less';

const cache = {};

/**
 * 异步获取数据后，需要缓存起来。避免组件未销毁时重复发起请求
 */
function OppRanking() {
  const [loading, setLoading] = useState(false);
  const [topList, setTopList] = useState([]);

  useEffect(() => {
    async function getInitData() {
      setLoading(true);

      // 查询排行榜数据 bjType :类型 LEAD:线索 OPP：商机
      const response2 = await request('orgauth/WorkbenchController/qryStatisticsOfBest.do', {
        method: 'get',
        data: { objType: 'OPP' },
      });

      if (
        response2 !== null &&
        response2.resultCode === '0' &&
        response2.resultObject.data.resultCode === '0'
      ) {
        const data = [];
        const rstData = response2.resultObject.data.resultObject.topList;
        if (rstData) {
          /**
           *  过滤掉0%，最多取7个
           *  预期取到的rstData格式如下
           *  [
           *    {
           *      createStaff: 2,
           *      createStaffName: '李可鹏',
           *      num: 22,
           *      numTotal: 76,
           *      percentage: '28.95%',
           *      percentageValue: '28.95%',
           *      ranks: 0,
           *      rowId: 1,
           *    }
           *  ];
           */
          rstData
            .filter(item => numeral(item.percentage)._value !== 0)
            .sort((a, b) => numeral(b.percentage)._value - numeral(a.percentage)._value)
            .slice(0, 7)
            .forEach((rankData, i) => {
              rankData.ranks = i;
              data.push(rankData);
            });
          cache.topList = data;
          setTopList(data);
        }
      }

      setLoading(false);
    }
    if (Object.keys(cache).length < 2) {
      getInitData();
    }
  }, []);

  let _topList;

  if (cache.topList === undefined) {
    _topList = topList;
  } else {
    _topList = cache.topList;
  }
  return (
    <Card bordered loading={loading}>
      <div className={styles.salesRank}>
        <h4 className={styles.rankingTitle}>商机转化率排名（本月）</h4>
        <ul className={styles.rankingList}>
          {_topList.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无排名"
              style={{ marginTop: 120 }}
            />
          ) : (
            _topList.map((item, i) => (
              <li key={item.createStaffName}>
                <span className={`${styles.rankingItemNumber} ${i < 3 ? styles.active : ''}`}>
                  {i + 1}
                </span>
                <span className={styles.rankingItemTitle} title={item.createStaffName}>
                  {item.createStaffName}
                </span>
                <span>{item.percentage}</span>
              </li>
            ))
          )}
        </ul>
      </div>
    </Card>
  );
}

export default OppRanking;
