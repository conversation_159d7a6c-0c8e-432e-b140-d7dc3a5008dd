import React from 'react';
import { Card, Descriptions } from 'antd';
import { connect } from 'dva';
import { renderDescItem } from '../const';
import { LIBRARYS_TYPE } from '@/pages/Librarys/const';
import useAttrs from '@/pages/Librarys/useAttrs';

const fields = [
  {
    label: '浏览权限设置',
    key: 'browsePrivTypeName',
  },
  {
    label: '浏览水印设置',
    key: 'browseWmkTypeName',
  },
  {
    label: '下载权限设置',
    key: 'downloadPrivType',
  },
  {
    label: '下载水印设置',
    key: 'downloadWmkTypeName',
  },
  {
    label: '评分',
    key: 'knowledgeSorce',
  },
  {
    label: '浏览量',
    key: 'browseCount',
  },
  {
    label: '下载量',
    key: 'downloadCount',
  },
  {
    label: '上传时间',
    key: 'createDate',
  },
  {
    label: '更新时间',
    key: 'updateDate',
  },
];

/**
 * 方案库基本信息组件
 * @param {props}} props
 * @returns
 */
const PermissionDetailComp = props => {
  const { detail, type } = props;
  const attrs = useAttrs();

  return (
    <Card title="权限和其他信息">
      <Descriptions>
        {([LIBRARYS_TYPE.BID, LIBRARYS_TYPE.CERT, LIBRARYS_TYPE.COOP, LIBRARYS_TYPE.HARDWARE].includes(type)
          ? fields
          : fields.filter(item => item.key !== 'browsePrivTypeName')
        )?.map(row => (
          <Descriptions.Item label={row.label} span={row.span} key={row.key}>
            {renderDescItem({ ...row, detail, attrs })}
          </Descriptions.Item>
        ))}
      </Descriptions>
    </Card>
  );
};

export default connect(({ librarys }) => ({ ...librarys }))(PermissionDetailComp);
