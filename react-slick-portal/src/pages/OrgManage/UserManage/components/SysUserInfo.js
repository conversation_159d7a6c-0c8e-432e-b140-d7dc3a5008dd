import React, { useState, useEffect, createContext } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Card,
  Form,
  Input,
  Icon,
  Button,
  Table,
  Modal,
  message,
  Select,
  Spin,
  DatePicker,
  TimePicker,
} from 'antd';
import moment from 'moment';
import SlickTable from '@/components/SlickTable';
import useSlickTable from '@/hooks/UseSlickTable';
import styles from '../styles.less';
import PubSub from 'pubsub-js';
import omit from 'lodash/omit';
import request from '@/utils/request';
import ComboGrid from '@/components/ComboGrid';
const { Option } = Select;
const FormItem = Form.Item;
const { TextArea } = Input;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const textAreaFormItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

// 判断空对象
const judgeObj = obj => {
  if (Object.keys(obj).length == 0) return true;
  else return false;
};

const namespace = 'userManage';

function SysUserInfo(props) {
  const {
    form: { getFieldDecorator },
  } = props;

  const [valueList, setValueList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dynamicAttrData, setDynamicAttrData] = useState([]);
  const [bossRoleData, setBossRoleData] = useState([]);
  const [privCodeData, setPrivCodeData] = useState([]);
  const [staffRequire, setStaffRequire] = useState(true);

  const saveBehavior = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveBehavior`,
      payload: params,
    });
  };

  useEffect(() => {
    getValuesList();
    getDynamicAttrData();
    getStaffRequire();
  }, []);

  const getStaffRequire = () => {
    request('portal/DataDictController/getValueByCode.do', {
      data: {
        groupCode: 'SYSTEM_VAR',
        paramCode: 'staffRequireFlag',
        defValue: 'n'
      },
      method: 'GET',
    }).then(res => {
      setStaffRequire(res ==='n'? false:true);
    });
  };

  const getValuesList = () => {
    request('portal/DomainDataController/getValuesList.do', {
      data: {
        busiNbr: 'SystemUser',
        propertyName: 'statusCd',
      },
    }).then(res => {
      setValueList(res);
    });
  };

  const getDynamicAttrData = () =>{
    request('portal/DomainDataController/getDanyPropertys.do', {
      data: {
        busiNbr: 'SystemUser'
      },
    }).then(res => {
      setDynamicAttrData(res);
      res.forEach(item => {
        if(item.attrValueDataType ==="1500"){
          //获取boss角色下拉列表值，个性化下拉组件
          getBossRoleInfo();
        }else if(item.id === 800054149){
          //获取权限值下拉列表值，个性化下拉组件
          getPrivCodeList();
        }

      });
    });
  }

  //获取boss系统角色列表信息
  const getBossRoleInfo = () =>{
    request('orgauth/SystemRolesController/qryRolesBySystemId.do', {
      method: 'get',
      data: {
        systemId: 727020
      },
    }).then(res => {
      setBossRoleData(res);
    });
  }

  //获取用户权限枚举值
  const getPrivCodeList = () => {
    request('portal/DomainDataController/getValuesList.do', {
      data: {
        busiNbr: 'SystemUser',
        propertyName: 'privCode',
      },
    }).then(res => {
      setPrivCodeData(res);
    });
  };


  useEffect(() => {
    if (props.behavior === 'add') {
      props.form.resetFields();
      // props.form.setFieldsValue({"800054146":[]});
      // props.form.setFieldsValue({ searchLimit: '9' });
    }
  }, [props.behavior]);


  const handleKeep = () => {
    props.form.validateFields((err, fieldsValue) => {
      if (err) return;

      //获取动态属性 key值
      let dnyAttrKey =  Object.keys(fieldsValue);
      let entityAttrs = [];
      let entifyAttrsForm = {};
      //判断key为数字的就是动态属性值
      dnyAttrKey.map(key =>{
        let numKey = Number(key);
        if (!isNaN(numKey))
          {
             if(fieldsValue[key] != null){
              // userInfo.entityAttrs.push({"attrId": "400", "attrValue": eiacUid, "attrValueId": -1});
              //时间取值特殊处理
              if(key === '800054150' || key === '800054151'){
                entityAttrs.push({
                  attrId:key,
                  attrValue:fieldsValue[key].format("HH:mm:ss"),
                  attrValueId: -1
                })

                entifyAttrsForm[key] = moment(fieldsValue[key], 'HH:mm:ss');

              } else if(key === '800054146'){
                //组织列表取值特殊处理
                entityAttrs.push({
                  attrId:key,
                  attrValue:fieldsValue[key][0].orgId,
                  attrValueId: -1
                })
                entifyAttrsForm[key] = [{
                  orgId:fieldsValue[key][0].orgId,
                  orgName:fieldsValue[key][0].orgName
                }];
              } else if(key === '800054148'){
              //角色多选下拉框列表取值特殊处理
                let bossRoles = '';
                fieldsValue[key].map(item =>{
                  if(bossRoles === ''){
                    bossRoles = item;
                  }else{
                    bossRoles = bossRoles+','+item;
                  }
                })

                entityAttrs.push({
                  attrId:key,
                  attrValue:bossRoles,
                  attrValueId: -1
                })
                entifyAttrsForm[key] = fieldsValue[key];
              } else{
                entityAttrs.push({
                  attrId:key,
                  attrValue:fieldsValue[key],
                  attrValueId: -1
                })

                entifyAttrsForm[key] = fieldsValue[key];
              }

              //删除非实体对象值，以免后台服务校验失败
              delete fieldsValue[key];
             }
          }
      })

      if (props.behavior === 'edit') {
        const editObj = {
          entityAttrs: entityAttrs,
          copyAddLists: null,
          // loginCode: props.tableData.loginCode,
          sysUserId: props.tableData.sysUserId,
          userPathName: props.tableData.userPathName,
          ...fieldsValue,
          staffId: fieldsValue.staffId.length > 0 ? fieldsValue.staffId[0].staffId : '',
          staffName: fieldsValue.staffId.length > 0 ? fieldsValue.staffId[0].staffName : '',
          regionName: fieldsValue.regionName2 ? fieldsValue.regionName2[0].regionName : '',
          regionId: fieldsValue.regionName2 ? fieldsValue.regionName2[0].commonRegionId : '',
        };
        const paramsObj = omit(editObj, ['regionName2']);
        setLoading(true),
          request('orgauth/SystemUserController/updateUser.do', {
            data: paramsObj,
            method: 'PUT',
          }).then(res => {
            if(res.resultCode === 'TRUE'){
                message.success('保存成功！');
                setLoading(false);
                saveBehavior('disabled');

                //编辑框动态属性值变化

                props.form.setFieldsValue({
                  mobilePhone: fieldsValue.mobilePhone,
                  userName: fieldsValue.userName,
                  userOrgName: fieldsValue.regionName2[0].orgName,
                  sysUserDesc: fieldsValue.sysUserDesc,
                  ...entifyAttrsForm
                });
                PubSub.publish(`userManageTable.editUpdate`);
            }else{
                message.error('保存失败！');
            }
          });
      } else {
        const addObj = {
          copyAddLists: null,
          entityAttrs: entityAttrs,
          ...fieldsValue,
          // loginCode: fieldsValue.sysUserCode,
          staffId: fieldsValue.staffId.length > 0 ? fieldsValue.staffId[0].staffId : '',
          staffName: fieldsValue.staffId.length > 0 ? fieldsValue.staffId[0].staffName : '',
          regionName: fieldsValue.regionName2 ? fieldsValue.regionName2[0].regionName : '',
          regionId: fieldsValue.regionName2 ? fieldsValue.regionName2[0].commonRegionId : '',
          sysUserId: '',
          userPathName: '',
        };
        const paramsObj = omit(addObj, ['regionName2']);
        setLoading(true),
          request('orgauth/SystemUserController/addUser.do', { data: paramsObj }).then(res => {
            if (res.resultCode === 'TRUE'){
              message.success('新增成功！');
              setLoading(false);
              saveBehavior('disabled');
              PubSub.publish(`userManageTable.addUpdate`);
            }else{
              //message.error(res.resultMsg);
              setLoading(false);
            }
          });
      }
    });
  };


  const handleCancel = () => {
    saveBehavior('disabled');
  };

  return (
    <Spin spinning={loading}>
      <Form>
        <Row>
          <Col span={6}>
            <FormItem label="账号" {...formItemLayout}>
              {getFieldDecorator('sysUserCode', {
                rules: [
                  {
                    required: true,
                    message: '账号不能为空',
                  },
                ],
              })(
                <Input
                  placeholder="请输入"
                  disabled={
                    props.behavior === 'disabled' || props.behavior === 'edit' ? true : false
                  }
                />
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="手机号码" {...formItemLayout}>
              {getFieldDecorator('mobilePhone', {
                rules: [
                  {
                    required: true,
                    message: '手机号码不能为空',
                  },
                  {
                    required: false,
                    pattern: new RegExp(/^1(3|4|5|6|7|8|9)\d{9}$/, 'g'),
                    message: '请输入正确的手机号',
                  },
                ],
              })(
                <Input
                  placeholder="请输入"
                  disabled={props.behavior === 'disabled' ? true : false}
                />
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="用户名称" {...formItemLayout}>
              {getFieldDecorator('userName', {
                rules: [
                  {
                    required: true,
                    message: '用户名称不能为空',
                  },
                ],
              })(
                <Input
                  placeholder="请输入"
                  disabled={props.behavior === 'disabled' ? true : false}
                />
              )}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="用户状态" {...formItemLayout}>
              {getFieldDecorator('statusCd')(
                <Select placeholder="请选择" disabled={true}>
                  {valueList.length
                    ? valueList.map(item => {
                        return (
                          <Option value={item.value} key={item.value}>
                            {item.name}
                          </Option>
                        );
                      })
                    : null}
                </Select>
              )}
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span={6}>
            <FormItem label="当前登录次数" {...formItemLayout}>
              {getFieldDecorator('loginedNum')(<Input placeholder="请输入" disabled={true} />)}
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="员工" {...formItemLayout}>
              {getFieldDecorator('staffId', {
                  rules: [
                    {
                      required: staffRequire,
                      message: '员工不能为空',
                    },
                  ],
                })(
                <ComboGrid
                  disabled={
                    props.behavior === 'disabled' ? true : false
                    // props.behavior === 'disabled' || props.behavior === 'edit' ? true : false
                  }
                  url="orgauth/StaffController/selectStaffGridData.do"
                  popupStyle={{ width: 560 }}
                  destroyPopupOnHide
                  placeholder="请选择"
                  searchPlaceholder="请输入员工、组织、区域名称进行搜索"
                  label="staffName"
                  rowKey="staffId"
                  pick="radio"
                  //params={{regionId: props.form.getFieldValue("regionName2") && props.form.getFieldValue("regionName2")[0].commonRegionId?props.form.getFieldValue("regionName2")[0].commonRegionId:""}}
                  columns={[
                    {
                      title: '员工名称',
                      dataIndex: 'staffName',
                      ellipsis: true,
                    },
                    {
                      title: '员工编码',
                      dataIndex: 'staffCode',
                      ellipsis: true,
                    },
                    {
                      title: '员工类型',
                      ellipsis: true,
                      dataIndex: 'staffTypeName',
                    },
                    {
                      title: '员工账号',
                      ellipsis: true,
                      dataIndex: 'staffAccount',
                    },
                    {
                      title: '归属组织',
                      ellipsis: true,
                      dataIndex: 'orgName',
                    },
                    {
                      title: '归属区域',
                      ellipsis: true,
                      dataIndex: 'commonRegionName',
                    },
                  ]}
                />
              )}
            </FormItem>
          </Col>
          {props.behavior === 'disabled'? (
            <Col span={6}>
              <FormItem label="归属区域" {...formItemLayout}>
                {getFieldDecorator('regionName')(<Input placeholder="请输入" disabled={true} />)}
              </FormItem>
            </Col>
          ) : (
            <Col span={6} hidden={props.behavior === 'disabled' || staffRequire? true : false}>
              <FormItem label="区域选择" {...formItemLayout}>
                {getFieldDecorator('regionName2', {
                  rules: [
                    {
                      required: staffRequire? false:true,
                      message: '区域不能为空',
                    },
                  ],
                })(
                  <ComboGrid
                    url="orgauth/CommonRegionController/selectRegionGridDataById.do"
                    //url="orgauth/OrganizationController/selectOrgGridDataByOrgId.do"
                    popupStyle={{ width: 560 }}
                    placeholder="请选择"
                    searchPlaceholder="请输入区域名称进行搜索"
                    label="regionName"
                    params={{ filterCol: 'regionName,regionNbr', regionId: 10008 }}
                    destroyPopupOnHide
                    rowKey="commonRegionId"
                    pick="radio"
                    columns={[
                      {
                        title: '区域名称',
                        dataIndex: 'regionName',
                        ellipsis: true,
                      },
                      {
                        title: '区域编码',
                        dataIndex: 'regionNbr',
                        ellipsis: true,
                      },
                      {
                        title: '区域路径',
                        ellipsis: true,
                        dataIndex: 'pathName',
                      },
                    ]}
                  />
                )}
              </FormItem>
            </Col>
          )}
        </Row>
        {props.behavior === 'disabled' ? (
          <Row>
            <Col span={12}>
              <FormItem label="区域路径" {...textAreaFormItemLayout}>
                {getFieldDecorator('userPathName')(
                  <TextArea
                    rows={2}
                    placeholder="请输入"
                    disabled={props.behavior === 'disabled' ? true : false}
                  />
                )}
              </FormItem>
            </Col>
          </Row>
        ) : null}
        <Row>
          <Col span={12}>
            <FormItem label="用户描述" {...textAreaFormItemLayout}>
              {getFieldDecorator('sysUserDesc')(
                <TextArea
                  rows={4}
                  placeholder="请输入"
                  disabled={props.behavior === 'disabled' ? true : false}
                />
              )}
            </FormItem>
          </Col>
        </Row>

        <Row>
        {dynamicAttrData.length > 0 ? (

               dynamicAttrData.map(item => (
                <Col span={6} key={item.attrId}>
                  <FormItem label={item.attrName} {...formItemLayout}>
                  {
                    getFieldDecorator(item.attrId+'',{
                      rules: [
                         {
                             required:item.isNullable === false? true:false,
                             message: item.attrName +'不能为空',
                         },
                       ],
                     })(
                        // attr_value_data_type 1000 文本， 1200 下拉， 1300时间  1400 组织列表获取 1500 定制化多选下拉列表
                        // is_nullable 是否可为空 必填 0 fale：  非必填1 true
                       //attr_format 校验规则
                         item.attrValueDataType==="1200"?(
                             item.id === 800054149?(<Select placeholder="请选择" disabled={props.behavior === 'disabled' ? true : false}>
                               {privCodeData.length
                                   ? privCodeData.map(item => {
                                  return (
                                  <Option value={item.value} key={item.value}>
                                  {item.name}
                                  </Option>
                                 );
                                })
                                 : null}
                              </Select>):(
                                  <Select placeholder="请选择" disabled={props.behavior === 'disabled' ? true : false}>

                                  </Select>
                              )
                          ):(
                            item.attrValueDataType ==="1300"?(
                              <TimePicker style={{ width: '100%' }} format='HH:mm:ss' allowClear disabled={props.behavior === 'disabled' ? true : false}/>
                            ):(
                              item.attrValueDataType ==="1400"?(
                                <ComboGrid disabled={props.behavior === 'disabled' ? true : false}
                                  //url="orgauth/CommonRegionController/selectRegionGridDataById.do"
                                  url="orgauth/OrganizationController/selectOrgGridDataByOrgId.do"
                                  popupStyle={{ width: 560 }}
                                  placeholder="请选择"
                                  searchPlaceholder="请输入组织名称或编码进行搜索"
                                  label="orgName"
                                  params={{ filterCol: 'orgName,orgCode', orgId: 10008 }}
                                  destroyPopupOnHide
                                  rowKey="orgId"
                                  pick="radio"
                                  columns={[
                                    {
                                      title: '组织ID',
                                      dataIndex: 'orgId',
                                      ellipsis: true,
                                     },
                                   {
                                    title: '组织名称',
                                    dataIndex: 'orgName',
                                    ellipsis: true,
                                   },
                                   {
                                    title: '组织编码',
                                    dataIndex: 'orgCode',
                                    ellipsis: true,
                                  },
                                   {
                                     title: '组织路径',
                                     ellipsis: true,
                                     dataIndex: 'pathName',
                                   },
                                  ]}/>
                              ):(
                                item.attrValueDataType ==="1500"?(
                                  <Select disabled={props.behavior === 'disabled' ? true : false}
                                   mode="multiple" placeholder="选择角色">
                                     {bossRoleData.length>0?(
                                       bossRoleData.map(item => (
                                        <Option key={item.sysRoleCode}>{item.sysRoleName}</Option>
                                       ))
                                     ):null}
                                  </Select>

                                ):(
                                  <Input placeholder="请输入" disabled={props.behavior === 'disabled' ? true : false}/>
                                )
                              )

                            )

                    ))
                  }
                </FormItem>
                </Col>
                ))

        ):null}

        </Row>

        {props.behavior !== 'disabled' ? (
          <div style={{ textAlign: 'center' }}>
            <Button type="primary" className="margin-right" onClick={handleKeep}>
              保存
            </Button>
            <Button type="default" onClick={handleCancel}>
              取消
            </Button>
          </div>
        ) : null}
      </Form>
    </Spin>
  );
}

export default connect(({ userManage }) => ({
  behavior: userManage.behavior,
  tableData: userManage.tableData,
}))(
  Form.create({
    mapPropsToFields(props) {
      if (!judgeObj(props.tableData)) {
        if (props.behavior !== 'add') {
          let staffIdArr = [];
          let userOrgNameArr = [];
          props.tableData.staff
            ? staffIdArr.push({
                staffId: props.tableData.staff.staffId,
                staffName: props.tableData.staff.staffName,
              })
            : null;
          userOrgNameArr.push({
            commonRegionId: '',
            regionName: props.tableData.userOrgName,
          });

          let userInfoData = {
            sysUserCode: Form.createFormField({ value: props.tableData.sysUserCode }),
            mobilePhone: Form.createFormField({ value: props.tableData.mobilePhone }),
            userName: Form.createFormField({ value: props.tableData.userName }),
            statusCd: Form.createFormField({ value: props.tableData.statusCd }),
            loginedNum: Form.createFormField({ value: props.tableData.loginedNum }),
            staffId: Form.createFormField({ value: staffIdArr }),
            regionName: Form.createFormField({ value: props.tableData.userOrgName }),
            regionName2: Form.createFormField({ value: userOrgNameArr }),
            userPathName: Form.createFormField({ value: props.tableData.userPathName }),
            sysUserDesc: Form.createFormField({ value: props.tableData.sysUserDesc })
          }

             //构造动态属性值渲染
             if(props.tableData.entityAttrs != null){
              props.tableData.entityAttrs.forEach(item =>{
                let objectInfo = {}

                //时间类型特殊处理
                if(item.attrSpec.attrValueDataType ==="1300"){
                  objectInfo[item.attrId]=Form.createFormField({ value: moment(item.attrValue, 'HH:mm:ss') })
                }else if(item.attrSpec.attrValueDataType ==="1400"){

                  //组织列表数据特殊处理
                 let userOrgNameAr =[{
                    orgId: item.attrValue,
                    orgName: props.tableData.chnOrgName,
                  }];

                  objectInfo[item.attrId]=Form.createFormField({ value: userOrgNameAr })

                }else if(item.attrSpec.attrValueDataType ==="1500"){

                  //角色多选下拉列表数据特殊处理
                  let roles = item.attrValue;
                  let rolesArray = [];
                  if(roles !== ''){
                    rolesArray = roles.split(',');
                  }

                  objectInfo[item.attrId]=Form.createFormField({ value: rolesArray })

                }else{
                  objectInfo[item.attrId]=Form.createFormField({ value: item.attrValue })
                }
                userInfoData = {
                  ...userInfoData,
                  ... objectInfo
                }


              })
            }


          return userInfoData;
        }else{
           //构造动态属性值渲染
          //  let objectInfo = {}
          //  if(props.tableData.entityAttrs != null){
          //   props.tableData.entityAttrs.forEach(item =>{


          //     if(item.attrSpec.attrValueDataType ==="1400"){

          //       //组织列表数据特殊处理
          //      let userOrgNameAr =[{
          //           orgId: '',
          //           orgName: '',
          //       }];

          //       // objectInfo[item.attrId]=Form.createFormField({ value: userOrgNameAr })

          //     }

          //   })
          // }
          // return objectInfo;
        }
      }
    },
  })(SysUserInfo)
);
