import React from 'react';
import { connect } from 'dva';
import { Menu, Icon, Empty, Popover } from 'antd';
import Link from 'umi/link';
import classNames from 'classnames';
import { getFinalUrl } from '@/utils/utils';
import styles from '../less/header.less';

function RecentMenuDropdown({ recent }) {
  const content =
    recent.length === 0 ? (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="暂无数据"
        style={{ padding: '16px', margin: 0 }}
      />
    ) : (
      <Menu className={styles.menu}>
        {/**
         * recent中urlAddr值为[iframe]开头的表示第三方集成的子系统
         * 取menuCode值
         * 第三方集成的页面统一指向@/pages/iframe。会根据传进去的menuCode从all中遍历出对应的菜单地址
         */}
        {recent.map(menu => (
          <Menu.Item key={menu.menuCode}>
            <Link
              className="text-ellipsis"
              title={menu.menuName}
              to={getFinalUrl(menu)}
            >
              {menu.menuName}
            </Link>
          </Menu.Item>
        ))}
      </Menu>
    );

  return (
    <Popover
      placement="bottom"
      content={content}
      trigger="hover"
      getPopupContainer={trigger => trigger.parentNode}
    >
      <span className={classNames(styles.action)}>
        <Icon type="history" />
      </span>
    </Popover>
  );
}

export default connect(({ menu }) => ({
  recent: menu.recent,
}))(RecentMenuDropdown);
