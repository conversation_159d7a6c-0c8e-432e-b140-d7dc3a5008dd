export default {
  namespace: 'notice',

  state: {
    unRead: 0, // 未读系统消息
    unreadChat: 0, // 未读聊天消息
  },

  effects: {},

  reducers: {
    setNumber(state, { payload }) {
      return {
        ...state,
        unRead: parseInt(payload, 10),
      };
    },
    setChatNumber(state, { payload }) {
      return {
        ...state,
        unreadChat: parseInt(payload, 10),
      };
    },
  },
};
