import React, { useState, useEffect, useRef, useImperativeHandle } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Card,
  Form,
  Input,
  Icon,
  Button,
  Select,
  Modal,
  message,
  Spin,
  InputNumber,
  Table
} from 'antd';
import request from '@/utils/request';
import styles from '../styles.less';
import ComboGrid from '@/components/ComboGrid';
import {operActionLog} from '@/utils/utils';
import pick from 'lodash/pick';
import findIndex from 'lodash/findIndex';
import PubSub from "pubsub-js";
const { TextArea } = Input;
const { Option } = Select;
const Search = Input.Search;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const textAreaFormItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

function AddReleMenu(props) {
  const [spinning, setSpinning] = useState(false);
  const { getFieldDecorator } = props.form;
  const [dataSource,setDataSource]=useState([]);
  const [dataAllSource,setDataAllSource]=useState([])
  const [saveValue,setSaveValue]=useState([]);

  const rowSelection={
    onChange: (selectedRowKeys, selectedRows)=>{
      setSaveValue(selectedRows);
    },
    type:'checkbox '
  }

  const filterDataSource=(value)=>{
   if(value==''){
     setDataSource(dataAllSource);
   }else{
     setDataSource(
       dataAllSource.filter(item =>
         JSON.stringify(item)
           .toLowerCase()
           .includes(value.toLowerCase()),
       )
     );
   }
  }

  useEffect(()=>{
    //已关联
    let alreadySource=[]
    request('portal/AppThemeController/selectAppThemeDetailGridList.do',{data:{
        themeCode:props.themeCode,
      }}).then(res=>{
         alreadySource=res;
    })


  /*  'privId','menuId','menuName' ,'menuDesc','menuLevel','menuType','menuTypeName','parMenuId','menuIndex','regionId',
      'urlAddr','systemCode','statusCd','firstLetter','iconUrl','menuCode','ifAsync','openUrl','showFlag'*/
   request('orgauth/FuncMenuController/selectMenuFromSession.do',{method:'get'}).then((res)=>{
      let source=[];
      //过滤app菜单
      res.forEach(item=>{
        if(item.systemCode=='727004'||item.menuCode.indexOf('TYMH_MENU_CARD_')!=-1){
          source.push(
            pick(
              item,
              'menuId',
              'parMenuId',
              'parMenuName',
              'urlAddr',
              'regionId',
              'systemCode',
              'menuName',
              'menuDesc',
              'menuLevel',
              'iconUrl',
              'menuCode'
            )
          );
        }
      });

      //过滤已关联菜单
     source=source.filter(item=>findIndex(alreadySource,{menuId:item.menuId})===-1)
      if(source.length>0) {
        setDataSource(source);
        setDataAllSource(source);
      }else{
        message.error("无可添加菜单,请先添加相关菜单")
      }
   })
  },[])

  useImperativeHandle(props.cRef, () => ({
   handleAdd:()=>{
     return new Promise(function(resolve) {
       props.form.validateFields((err, fieldsValue) => {
         if(err) return
         const params=
           {
             themeCode: props.themeCode,
             menu:saveValue,
           }
         setSpinning(true);
           request('portal/AppThemeAttrController/batchAddThemeAttr.do',{data:params}).then((res)=>{
             if(res.resultCode=='0'){
               message.success('新增成功');
               setSpinning(false)
             }else{
               setSpinning(false);
               message.error(res.resultMsg)
             }
           })

       })})
   },
    //判断是否选择菜单
    getdataSource:()=>{return saveValue}

  }))
  const columns=[
    {
      title:'菜单名称',
      dataIndex:'menuName',
    },
    {
      title:'菜单编码',
      dataIndex:'menuCode',
    },
    {
      title:'菜单描述',
      dataIndex:'menuDesc',
    },
  ]
  return (
    <Spin spinning={spinning}>
      <div>
          <span >
          <Search  placeholder='搜索：菜单名称、编码'  allowClear={true}  onSearch={val => {filterDataSource(val);
          }}/>
          </span>
      </div>
      <Table
        style={{marginTop:10}}
        bordered
        columns={columns}
        dataSource={dataSource}
        rowSelection={rowSelection}
      />
    </Spin>
  );
}

export default connect(({}) => ({}))(Form.create()(AddReleMenu));
