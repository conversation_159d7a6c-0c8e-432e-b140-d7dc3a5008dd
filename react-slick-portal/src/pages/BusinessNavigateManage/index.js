import React, { useState } from 'react';
import { Row, Col, Card, Form, Input, Button, message, Divider, Modal } from 'antd';
import { useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import useContact from '@/hooks/useContact';
import { buildColumns } from '@/utils/bestSolutionUtils';
import Detail from './components/Detail';
import { queryTableData, deleteNavigate, releaseOrNot } from './service';

const Index = props => {
  const { form } = props;

  const { getFieldDecorator } = form;
  const [loading, setLoading] = useState(false);

  const {
    tableProps,
    search: { submit, reset },
  } = useAntdTable(queryTableData, { form });
  const { pagination, ...restTableProps } = tableProps;

  const { openForAdd, openForEdit, openForReadOnly, ...restDetailProps } = useContact({
    closeExtra: refresh => {
      if (refresh) {
        submit();
      }
    },
  });

  const { visible: detailVisible } = restDetailProps;

  const deleteNav = async record => {
    setLoading(true);
    const res = await deleteNavigate(record?.funcNavigationId, record?.menuRelId);
    if (res === true) {
      message.success('删除成功');
      submit();
    } else {
      message.error('删除失败');
    }
    setLoading(false);
  };

  const releaseOrNotNav = async (funcNavigationId, needRelease) => {
    setLoading(true);
    //  statusCd 传1000表示取消发布，传1200表示发布
    const res = await releaseOrNot(funcNavigationId, needRelease ? '1200' : '1000');
    if (res === true) {
      message.success('操作成功');
      submit();
    } else {
      message.error('操作失败');
    }
    setLoading(false);
  };

  const columns = [
    {
      title: '编号',
      dataIndex: 'funcNavigationId',
    },
    {
      title: '导航名称',
      dataIndex: 'navigationName',
    },
    {
      title: '发布至菜单',
      dataIndex: 'statusCdName',
    },
    {
      title: '更新人',
      dataIndex: 'createStaffName',
    },
    {
      title: '更新时间',
      dataIndex: 'updateDate',
    },
    {
      title: '创建时间',
      dataIndex: 'createDate',
    },
    {
      title: '操作',
      width: 200,
      fixed: 'right',
      render: (text, record, index) => (
        <div>
          <a
            type="link"
            onClick={() => {
              if (record?.statusCd === '1000') {
                Modal.confirm({
                  title: '是否进行业务导航发布？',
                  onOk: () => {
                    releaseOrNotNav(record?.funcNavigationId, true);
                  },
                });
              } else {
                Modal.confirm({
                  title: '是否取消业务导航发布？',
                  onOk: () => {
                    releaseOrNotNav(record?.funcNavigationId, false);
                  },
                });
              }
            }}
          >
            {record?.statusCd === '1000' ? '发布' : '取消发布'}
          </a>
          <Divider type="vertical" />
          <a
            type="link"
            onClick={() => {
              openForReadOnly(record, index);
            }}
          >
            详情
          </a>
          <Divider type="vertical" />
          <a
            type="link"
            onClick={() => {
              openForEdit(record, index);
            }}
          >
            编辑
          </a>
          <Divider type="vertical" />
          <a
            type="link"
            onClick={() => {
              Modal.confirm({
                title: '是否确定删除该导航信息？',
                onOk: () => {
                  deleteNav(record);
                },
              });
            }}
          >
            删除
          </a>
        </div>
      ),
    },
  ];

  const finalColumns = buildColumns(columns);

  return (
    <Card
      title="业务导航设置"
      className="cute"
      extra={
        <Button
          type="primary"
          onClick={() => {
            openForAdd();
          }}
        >
          新增
        </Button>
      }
    >
      <Form labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
        <Row>
          <Col span={8}>
            <Form.Item label="导航名称">{getFieldDecorator('navigationName')(<Input allowClear />)}</Form.Item>
          </Col>
          <Col offset={8} span={8}>
            <div style={{ textAlign: 'right' }}>
              <Button type="primary" className="margin-right margin-left" onClick={submit}>
                查询
              </Button>
              <Button type="default" onClick={reset}>
                重置
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
      <SlickTable
        rowKey={record => record.funcNavigationId}
        columns={finalColumns}
        {...restTableProps}
        loading={loading || restTableProps?.loading}
        data={{ pagination }}
        scroll={{ x: 'max-content' }}
      />
      {detailVisible && <Detail {...restDetailProps} />}
    </Card>
  );
};

export default Form.create()(Index);
