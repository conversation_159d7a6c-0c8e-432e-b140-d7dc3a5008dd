import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Select,
  Divider,
  Icon,
  Input,
  Button,
  Drawer,
  message,
  Modal,
} from 'antd';
import classNames from 'classnames';
import { connect } from 'dva';
import { useBoolean, useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import { getPageSizeByCardHeight } from '@/utils/utils';
import {
  deleteBulletin,
  effectiveBulletin,
  invalidateBulletin,
  getValuesList,
  selectBulletinManageGridData,
} from '@/services/bulletin';
import DetailDrawer from './components/DetailDrawer';
import style from './bulletin.less';

const getTableData = ({ current, pageSize, ...rest }) => selectBulletinManageGridData({
  page: current,
  pageNum: current,
  pageSize,
  rowNum: pageSize,
  sortName: '',
  sortOrder: 'asc',
  ...rest,
}).then(res => {
  if (Array.isArray(res.list)) {
    return {
      total: res.total,
      data: res.list,
    };
  }
});

function Manage({ size: { height }, form }) {
  const { state: expand, toggle } = useBoolean(false);
  const [title, setTitle] = useState('');
  const [selectedRows, setSelectedRows] = useState([]);
  const [detail, setDetail] = useState({});
  const [mode, setMode] = useState('view');
  const { state: visible, setTrue: show, setFalse: hide } = useBoolean(false);
  const [bulletinTypeMap, setBulletinTypeMap] = useState([]);
  const [bulletinlevelMap, setBulletinLevelMap] = useState([]);
  const tableRef = useRef(null);
  // card-body除rowHeight
  const [size, setSize] = useState(getPageSizeByCardHeight(height));
  const { getFieldDecorator } = form;
  const {
    tableProps,
    refresh,
    search: { submit, reset },
  } = useAntdTable(
    params => {
      /**
       * params包含 分页和表单值，格式如下：
       * {current: 1,filters: {},pageSize: 2,sorter:{},bulletinTitle:''}
       * expand=true 表示高级搜索；高级搜索无视快捷搜索上的bulletinTitle值；反之一样
       */

      if (expand) {
        return getTableData({ ...params, pageSize: size });
      }
      return getTableData({ ...params, pageSize: size, bulletinTitle: title });
    },
    // 依赖变化会触发请求
    [title, height],
    {
      defaultPageSize: size,
      form,
    }
  );
  const { pagination, ...restTableProps } = tableProps;

  /**
   * 批量删除
   * @param {object[]}
   */
  function handleDelete(selectedRows) {
    // 手动更新ok按钮loading状态
    // onOk返回promise对象时，只有在resolve()之后才会关闭
    const modal = Modal.confirm({
      title: '确定要删除吗',
      onOk() {
        modal.update({ okButtonProps: { loading: true } });
        const result = [];
        selectedRows.forEach(item => {
          const { bulletinLevelName, bulletinTypeName, statusCdName, ...rest } = item;
          // 只要这些字段
          // {
          //   bulletinContent: "222222222",
          //   bulletinId: 344512,
          //   bulletinLevel: "1200",
          //   bulletinTitle: "soon2",
          //   bulletinType: "1000",
          //   createDate: "2020-01-08 10:59:29",
          //   createStaff: 1,
          //   effDate: "2020-01-08 10:58:41",
          //   expDate: "2020-01-08 11:17:41",
          //   id: 344512,
          //   isTop: "1",
          //   launchOrg: 10008,
          //   launchStaff: 1,
          //   launchStaffName: "管思坤",
          //   rcvType: "1200",
          //   realTime: false,
          //   statusCd: "1100",
          //   updateDate: "2020-01-08 11:17:41",
          //   updateStaff: 1,
          // }
          result.push({ ...rest });
        });

        return new Promise(resolve => {
          deleteBulletin(result)
            .then(response => {
              // 成功标识
              if (response && response.resultCode === '0') {
                // 剔除selectedRows中的记录
                setSelectedRows([]);
                refresh();
                // 更新SlickTable内部的状态
                tableRef.current.cleanSelectedKeys([], []);
                message.success('删除成功');
              } else {
                // if (response && response.resultMsg) {
                //   message.error(response.resultMsg);
                // } else {
                //   message.error('删除失败');
                // }
              }
            })
            .finally(() => {
              resolve();
              modal.update({ okButtonProps: { loading: false } });
            });
        }).catch(() => message.error('删除失败'));
      },
    });
  }

  /**
   * 启用公告
   * @param {object} targetRow
   */
  function setActived(e, targetRow) {
    e.preventDefault();
    // 手动更新ok按钮loading状态
    // onOk返回promise对象时，只有在resolve()之后才会关闭
    const modal = Modal.confirm({
      title: '确定要启用该公告吗',
      onOk() {
        modal.update({ okButtonProps: { loading: true } });
        // 剔除冗余字段
        const { bulletinTypeName, bulletinLevelName, statusCdName, ...params } = targetRow;
        return new Promise(resolve => {
          effectiveBulletin(params)
            .then(() => {
              // IMPROVE: 没有成功标记
              refresh();
              message.success('操作成功');
            })
            .finally(() => {
              resolve();
              modal.update({ okButtonProps: { loading: false } });
            });
        }).catch(() => {
          message.error('操作失败');
        });
      },
    });
  }

  /**
   * 新增
   */
  function setAdd(e) {
    e.preventDefault();
    setMode('add');
    setDetail({});
    show();
  }

  /**
   * 编辑
   * @param {object} targetRow
   */
  function setEdit(e, targetRow) {
    e.preventDefault();
    setMode('edit');
    setDetail(targetRow);
    show();
  }

  /**
   * 查看
   * @param {object} targetRow
   */
  function viewDetail(e, targetRow) {
    e.preventDefault();
    setMode('view');
    setDetail(targetRow);
    show();
  }

  /**
   * 设置失效
   * @param {object} targetRow
   */
  function setDisabled(e, targetRow) {
    e.preventDefault();
    // 手动更新ok按钮loading状态
    // onOk返回promise对象时，只有在resolve()之后才会关闭
    const modal = Modal.confirm({
      title: '确定要让该公告失效吗',
      onOk() {
        modal.update({ okButtonProps: { loading: true } });
        const { bulletinTypeName, bulletinLevelName, statusCdName, ...params } = targetRow;
        return new Promise(resolve => {
          invalidateBulletin(params)
            .then(() => {
              // IMPROVE: 没有成功标记
              refresh();
              message.success('操作成功');
            })
            .finally(() => {
              resolve();
              modal.update({ okButtonProps: { loading: false } });
            });
        }).catch(() => {
          message.error('操作失败');
        });
      },
    });
  }

  // 获取码表数据
  useEffect(() => {
    getValuesList({ busiNbr: 'Bulletin', propertyName: 'bulletinType' }).then(res => {
      if (Array.isArray(res)) {
        setBulletinTypeMap(res);
      }
    });
  }, []);

  // 获取码表数据
  useEffect(() => {
    getValuesList({ busiNbr: 'Bulletin', propertyName: 'bulletinLevel' }).then(res => {
      if (Array.isArray(res)) {
        setBulletinLevelMap(res);
      }
    });
  }, []);

  // 根据size.height 变化即时更新pageSize
  useEffect(() => {
    setSize(getPageSizeByCardHeight(height));
  }, [height]);

  return (
    <>
      <Card
        title="公告管理"
        extra={(
          <>
            {expand ? null : (
              <Input.Search
                placeholder="公告标题搜索"
                allowClear
                style={{ width: 'auto' }}
                onSearch={val => {
                  setTitle(val);
                }}
              />
            )}
            <Button type="default" className="inline-block margin-left" onClick={() => toggle()}>
              高级查询
              <Icon className={classNames('animated', { rotate180: expand })} type="down" />
            </Button>
            <Button type="primary" className="margin-left" onClick={e => setAdd(e)}>
              新增
            </Button>
          </>
        )}
        style={{ minHeight: height }}
        bordered
      >
        {expand ? (
          <Form className="flow fix-label" onSubmit={submit}>
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item label="公告标题">
                  {getFieldDecorator('bulletinTitle')(<Input allowClear placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="发布人">
                  {getFieldDecorator('launchStaffName')(<Input allowClear placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="公告等级">
                  {getFieldDecorator('bulletinLevel')(
                    <Select placeholder="请选择">
                      {bulletinlevelMap.map(item => (
                        <Select.Option key={item.value}>{item.name}</Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="公告类型">
                  {getFieldDecorator('bulletinType')(
                    <Select placeholder="请选择" allowClear>
                      {bulletinTypeMap.map(item => (
                        <Select.Option key={item.value}>{item.name}</Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="公告内容">
                  {getFieldDecorator('bulletinContent')(<Input allowClear placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="备注">
                  {getFieldDecorator('remark')(<Input allowClear placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={12} className="text-right">
                <Button type="primary" htmlType="submit" loading={tableProps.loading}>
                  查询
                </Button>
                <Button className="margin-left" onClick={reset}>
                  重置
                </Button>
              </Col>
            </Row>
          </Form>
        ) : null}
        <SlickTable
          ref={tableRef}
          extra={allChecked =>
            allChecked.length > 0 ? (
              <Button type="primary" ghost onClick={() => handleDelete(selectedRows)}>
                删除
              </Button>
            ) : null
          }
          rowKey={record => record.id}
          pick="checkbox"
          rowClassName={record => {
            if (record.bulletinLevel === '1000') {
              return `${style.highLevel}`;
            }
            return null;
          }}
          {...restTableProps}
          data={{
            pagination: {
              ...pagination,
              pageSize: size,
            },
          }}
          columns={[
            {
              title: '公告标题',
              dataIndex: 'bulletinTitle',
              ellipsis: true,
              width: 200,
              render: (text, record) => {
                const newFlag = `【新】${text}`;
                const editFlag = `【变更】${text}`;
                return (
                  <a href="#" onClick={e => viewDetail(e, record)} style={{ color: record.bulletinLevel === '1000' && 'red' }}>
                    {record.prevBulletinId ? editFlag : newFlag}
                  </a>
                );
              },
            },
            {
              title: '公告内容',
              dataIndex: 'bulletinContent',
              ellipsis: true,
            },
            {
              title: '时间',
              align: 'center',
              width: 160,
              dataIndex: 'effDate',
            },
            {
              title: '公告发布人',
              align: 'center',
              width: 140,
              ellipsis: true,
              dataIndex: 'launchStaffName',
            },
            {
              title: '公告等级',
              align: 'center',
              width: 80,
              dataIndex: 'bulletinLevelName',
            },
            {
              title: '公告类型',
              align: 'center',
              width: 120,
              ellipsis: true,
              dataIndex: 'bulletinTypeName',
            },
            {
              title: '状态',
              align: 'center',
              width: 80,
              dataIndex: 'statusCdName',
              render: (text, record) => {
                if (record.statusCd === '1000') {
                  // 有效
                  return <span className="text-success">{text}</span>;
                }
                if (record.statusCd === '1100') {
                  // 失效
                  return <span className="text-danger">{text}</span>;
                }
                return text;
              },
            },
            {
              title: '操作',
              align: 'center',
              key: 'action',
              width: 120,
              render: (text, record) => {
                function render(statusCd) {
                  if (statusCd === '1000') {
                    return (
                      <a href="#" onClick={e => setDisabled(e, record)}>
                        失效
                      </a>
                    );
                  }
                  if (statusCd === '1100') {
                    return (
                      <a href="#" onClick={e => setActived(e, record)}>
                        启用
                      </a>
                    );
                  }
                  return null;
                }
                return (
                  <span>
                    <a href="#" onClick={e => setEdit(e, record)}>
                      编辑
                    </a>
                    <Divider type="vertical" />
                    {render(record.statusCd)}
                  </span>
                );
              },
            },
          ]}
          onSelectRow={rows => {
            setSelectedRows(rows);
          }}
        />
      </Card>
      <Drawer
        title={mode === 'add' ? '新建公告' : detail.bulletinTitle}
        destroyOnClose
        width={720}
        onClose={hide}
        visible={visible}
        bodyStyle={{ paddingBottom: 80 }}
      >
        <DetailDrawer detail={detail} mode={mode} close={hide} refresh={refresh} />
      </Drawer>
    </>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(Manage));
