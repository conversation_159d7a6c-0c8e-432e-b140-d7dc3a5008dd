/* eslint-disable */
import React, { Component } from 'react';
import { Icon, Upload, Modal, message, Button } from 'antd';
import isEqual from 'lodash/isEqual';
import PropTypes from 'prop-types';
import find from 'lodash/find';
import isFunction from 'lodash/isFunction';
import remove from 'lodash/remove';
import findIndex from 'lodash/findIndex';
import request from '@/utils/request';
import './index.less';
import filter from 'lodash/filter';

const uploadUrl = 'portal/FileStoreController/upload.do';
const deleteUrl = 'portal/FileStoreController/delete.do';

function checkUrl(fileList) {
  let result = true;
  fileList.map(item => {
    if (result) {
      if (item.url) {
        result = true;
      } else {
        result = false;
      }
    }
  });
  return result;
}

class PictureUpload extends Component {
  constructor(props) {
    super(props);

    const { fileList = [] } = props;
    this.state = {
      previewVisible: false,
      previewImage: '',
      fileList,
      prevPropsFileList: fileList,
    };
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    // props.fileList 有更新
    const propsFileList = nextProps.fileList ? nextProps.fileList : [];
    if (!isEqual(propsFileList, prevState.prevPropsFileList)) {
      return {
        ...prevState,
        fileList: propsFileList,
        prevPropsFileList: propsFileList,
      };
    }
    // 返回 null 表示不更新，此函数最后一定需要返回值
    return null;
  }

  renderButton = () => {
    return (
      <div>
        <Icon type="plus" />
        <div className="ant-upload-text">上传</div>
      </div>
    );
  };

  render() {
    const { previewVisible, previewImage, fileList } = this.state;
    const { fileList: ab, ...rest } = this.props;
    const { accept, length, size, listType } = rest;
    return (
      <div className="clearfix">
        <Upload
          className="slick-upload"
          fileList={fileList}
          {...rest}
          // 上传的时候做的校验
          beforeUpload={file => {
            const ext = `.${file.name.substr(file.name.lastIndexOf('.') + 1)}`;
            const isValidFormat =
              accept
                .split(',')
                .map(val => val.trim())
                .indexOf(ext) !== -1;
            // IE9 不做size判断
            const isSmall = file.size === undefined ? true : file.size < size;
            const isValidLength = fileList.length + 1 <= length;
            if (!isValidFormat) {
              message.error('不支持的文件格式！');
              return false;
            }

            if (!isSmall) {
              message.error('文件太大！');
              return false;
            }

            if (!isValidLength) {
              message.error('已上传文件数量已超过允许的最大个数！');
              return false;
            }

            return true;
          }}
          // 打开modal 查看原图
          onPreview={file => {
            this.setState({
              previewImage: find(fileList, { uid: file.uid }).thumbUrl,
              previewVisible: true,
            });
          }}
          // 这个再上传的时候/删除的时候触发
          onChange={({ file, fileList }) => {
            const { onChange } = this.props;
            const targetIndex = findIndex(fileList, { uid: file.uid });
            // 下载成功
            if (file.status === 'done' && Array.isArray(file.response)) {
              const { fileGetUrl: url, photoUrl: thumbUrl } = file.response[0];
              const isImage = /^image\//.test(file.type);

              // 附件下载链接
              fileList[targetIndex].url = url;

              // TODO: 暂不支持缩略图预览，因为目前回参中没有返回缩略图。
              // 如果不是图片，且是 'picture-card' 模式，禁止给thumbUrl和url的赋值，这样预览按钮就会自动禁用
              if (isImage) {
                fileList[targetIndex].thumbUrl = thumbUrl || '';
              } else if (listType === 'picture-card') {
                fileList[targetIndex].url = '';
              }

              if (isFunction(onChange)) {
                onChange(
                  fileList.map(val => {
                    if (Array.isArray(val.response)) {
                      // 提取回参中的有用字段，挂载对象的一级属性上
                      const { docId, docNbr, fileName, fileGetUrl, photoUrl } = val.response[0];
                      return {
                        ...val,
                        docId,
                        docNbr: docNbr,
                        fileName,
                        fileGetUrl,
                        thumbUrl: photoUrl,
                      };
                    }
                    return val;
                  })
                );
              }
            }
            // 下载失败
            if (file.status === 'error') {
              remove(fileList, item => item.uid === file.uid);
              message.error('文件上传接口，服务异常！');
            }

            // 删除
            if (file.status === 'removed') {
              if (isFunction(onChange)) {
                onChange(
                  fileList.map(val => {
                    if (Array.isArray(val.response)) {
                      // 提取回参中的有用字段，挂载对象的一级属性上
                      const { docId, docNbr, fileName, fileGetUrl, photoUrl } = val.response[0];
                      return {
                        ...val,
                        docId,
                        docNbr: docNbr,
                        fileName,
                        fileGetUrl,
                        thumbUrl: photoUrl,
                      };
                    }
                    return val;
                  })
                );
              }
            }

            // beforeUpload 返回 false
            if (file.status === undefined) {
              remove(fileList, item => item.uid === file.uid);
            }
            // 接口多了个portal处理化操作
            if (fileList.length) {
              if (checkUrl(fileList)) {
                // fileList.map((item, index) => {
                //   if (item.url.substr(0, 14) === '/portal/portal') {
                //     const oi = item.url.indexOf('/', 1);
                //     fileList[index].url = fileList[index].url.substring(oi);
                //   }
                // });
                this.setState({
                  fileList: fileList,
                });
              } else {
                this.setState({ fileList });
              }
            } else {
              this.setState({ fileList });
            }
          }}
        >
          {fileList.length >= length ? null : this.renderButton()}
        </Upload>

        <Modal
          visible={previewVisible}
          footer={null}
          onCancel={() => this.setState({ previewVisible: false })}
        >
          <img alt="preview" style={{ width: '100%', padding: 16 }} src={previewImage} />
        </Modal>
      </div>
    );
  }
}

PictureUpload.defaultProps = {
  // fileList: [],
  action: uploadUrl,
  onRemove: file => {
    return request(deleteUrl, {
      method: 'get',
      data: {
        docNbr: file.docNbr ? file.docNbr : file.response[0].docNbr,
      },
    }).then(res => {
      if (res.resultCode === '0000') {
        message.success('删除成功');
      } else {
        message.error('删除失败');
      }
    });
  },
  length: 999,
  size: 1024 * 1024 * 1024,
  listType: 'picture-card',
  multiple: false,
  accept:
    '.png,.jpg,.jpeg,.gif,.bmp,.txt,.doc,.docx,.xls,.xlsx,.pdf,.ppt,.ogg,.mp4,.mp4,.mpg,.rm,.rmvb,.wmv,.mov,.mkv,.flv,.avi,.rar,.zip',
};

// TODO: fileList没交代清楚
PictureUpload.propTypes = {
  // fileList: PropTypes.arrayOf(PropTypes.object),
  action: PropTypes.string,
  length: PropTypes.number,
  size: PropTypes.number,
  listType: PropTypes.oneOf(['text', 'picture', 'picture-card']),
  accept: PropTypes.string,
  onRemove: PropTypes.func,
  multiple: PropTypes.bool,
};

export default PictureUpload;
