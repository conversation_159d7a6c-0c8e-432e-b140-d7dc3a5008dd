// 适用于card+table场景，body部分无padding
.ant-card.compact {
  .ant-table-bordered {
    .ant-card-head {
      margin-bottom: -2px;
    }
  }

  .ant-card-body {
    padding: 0;
  }

  .ant-table-pagination.ant-pagination {
    margin-right: @slick-space-base;
  }
}

.ant-card.cute {
  > .ant-card-head {
    > .ant-card-head-wrapper {
      > .ant-card-head-title {
        position: relative;
        padding: 6px 0 6px 16px;

        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          display: inline-block;
          width: 4px;
          height: 16px;
          margin-top: -7px;
          background-color: #0085D0;
          border-radius: 2px;
          content: '';
        }
      }
    }

  }

}

.ant-card.cute2 {
  border: 1px solid #E6E6E6;

  > .ant-card-head {
    height: 40px;
    min-height: 40px;
    line-height: 1.5;
    background: rgba(93, 171, 230, 0.15);
    padding: 0 12px 0 16px;

    > .ant-card-head-wrapper {
      height: 100%;
      display: flex;
      align-items: center;

      .ant-card-head-title {
        padding: 0 0 0 22px;
        position: relative;
        font-weight: 500;
        font-size: 14px;
        color: #333;
        font-style: normal;
        text-transform: none;

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
          width: 20px;
          height: 20px;
          background-image: url('./img/icons-imac_settings.png');
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
        }
      }

      .ant-card-extra {
        padding: 0;
        display: flex;
        align-items: center;
        margin-left: auto;

        .ant-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px 4px 4px 4px;
        }
      }
    }
  }
}

.ant-card.cute3 {
  background: #F9FAFB;
  border-radius: 0px;
  border: none;

  .ant-card-head {
    padding: 16px 16px 0px 16px;
    border-bottom: none;
  }

  .ant-card-extra {
    .ant-btn {
      height: 32px;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      border-radius: 4px;
    }
  }

  .ant-card-body {
    padding-top: 16px !important;
  }

  .ant-card-head-title {
    position: relative;
    padding: 0px 0px 0px 16px !important;
    font-weight: 700;
    font-size: 16px;
    color: #333333;
    border-bottom: 3px solid white;

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      display: inline-block;
      width: 4px;
      height: 16px;
      background: linear-gradient(228deg, #0085D0 0%, #30AEF6 100%);
      margin-top: -7px;
      border-radius: 2px;
      content: '';
    }
  }
}


