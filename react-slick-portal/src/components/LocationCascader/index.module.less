.locationCascader {
  position: relative;
  display: inline-block;
  width: 100%;
}

.selector {
  width: 100%;
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.tabsContainer {
  padding: 8px;
}

.tabs {
  margin-bottom: 0;
}

.tabContent {
  max-height: 300px;
  overflow-y: auto;
}

.option {
  padding: 8px 12px;
  cursor: pointer;
  transition: background 0.3s;
  &:hover {
    background: #f5f5f5;
  }
  &.selected {
    background: #e6f7ff;
  }
}

.checkIcon {
  margin-left: 8px;
  color: #1890ff;
}

.emptyOptions {
  padding: 8px 12px;
  color: #999;
  text-align: center;
}

.customContent {
  padding: 8px 12px;
}

.disabled {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;

  &:hover {
    background-color: #f5f5f5;
  }
}

.disabledAll {
  opacity: 0.65;
  cursor: not-allowed;

  .selector {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
}

.searchContainer {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.searchInput {
  width: 100%;
}

.searchResults {
  max-height: 300px;
  overflow-y: auto;
}

.menu-item-keyword {
  color: #f50;
}

.emptyContent {
  padding: 8px 12px;
  color: #999;
  text-align: center;
}
