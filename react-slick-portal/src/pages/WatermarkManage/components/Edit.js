import React, { useState } from 'react';
import { Form, Row, Col, Select, Input, Button, message, InputNumber } from 'antd';
import { CompactPicker } from 'react-color';
import { connect } from 'dva';
import moment from 'moment';
import { add, edit } from '../service';

const currentTime = () => moment().format('YYYY-MM-DD HH:mm:ss');

function Edit(props) {
  const { getFieldDecorator } = props.form;
  const [loading, setLoading] = useState(false);

  const editWatermark = async data => {
    const res = await edit(data);
    if (res && res.success) {
      props.close();
      props.refresh();
      setLoading(false);
      message.success('保存成功！');
      return;
    }
    message.error(res.resultMsg);
    setLoading(false);
  };

  const handleSubmit = () => {
    props.form.validateFields((err, fieldsValue) => {
      if (err) return;
      setLoading(true);
      const paramsObj = {
        ...fieldsValue,
        fontColor: fieldsValue.fontColor.hex ? fieldsValue.fontColor.hex : props?.data.fontColor,
        id: props.data.id,
      };
      editWatermark(paramsObj);
    });
  };

  const addWatermark = async data => {
    const res = await add(data);
    if (res && res.success) {
      props.close();
      props.refresh();
      setLoading(false);
      message.success('创建成功！');
      return;
    }
    message.error(res.resultMsg);
    setLoading(false);
  };

  const handleCreate = () => {
    props.form.validateFields((err, fieldsValue) => {
      if (err) return;
      setLoading(true);
      const paramsObj = {
        ...fieldsValue,
        fontColor: fieldsValue.fontColor.hex,
      };
      addWatermark(paramsObj);
    });
  };

  return (
    <>
      <Form layout="vertical" onSubmit={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="水印名称">
              {getFieldDecorator('watermarkName', {
                rules: [{ required: true, message: '请输入水印名称' }],
              })(<Input placeholder="请输入" />)}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="水印编码">
              {getFieldDecorator('watermarkCode', {
                rules: [{ required: true, message: '请输入水印编码' }],
              })(<Input placeholder="请输入" />)}
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="水印类型">
              {getFieldDecorator('watermarkType', {
                initialValue: 1000,
                rules: [{ required: true, message: '请选择水印类型' }],
              })(
                <Select placeholder="请选择">
                  <Select.Option value={1000} key="1000">
                    明水印
                  </Select.Option>
                  <Select.Option value={1100} key="1100">
                    暗水印
                  </Select.Option>
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="水印位置" required>
              <Row gutter={16}>
                <Input.Group>
                  <Col span={12}>
                    <Form.Item style={{ marginBottom: 0 }}>
                      {getFieldDecorator('coordinateX', {
                        rules: [{ required: true, message: '请输入X轴起始位置' }],
                      })(<Input placeholder="起始坐标_X" />)}
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item style={{ marginBottom: 0 }}>
                      {getFieldDecorator('coordinateY', {
                        rules: [{ required: true, message: '请输入Y轴起始位置' }],
                      })(<Input placeholder="起始坐标_Y" />)}
                    </Form.Item>
                  </Col>
                </Input.Group>
              </Row>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="状态">
              {getFieldDecorator('statusCd', {
                initialValue: '1000',
                rules: [{ required: true, message: '请选择状态' }],
              })(
                <Select placeholder="请选择">
                  <Select.Option value="1000" key="1000">
                    有效
                  </Select.Option>
                  <Select.Option value="1100" key="1100">
                    无效
                  </Select.Option>
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="水印大小">
              {getFieldDecorator('fontSize', {
                initialValue: 13,
                rules: [{ required: true, message: '请输入水印大小' }],
              })(<InputNumber style={{ width: '100%' }} min={6} max={48} placeholder="请输入 6~48 的整数配置，单位px" />)}
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="水印倾斜度">
              {getFieldDecorator('inclination', {
                initialValue: 15,
                rules: [{ required: true, message: '请输入水印倾斜度' }],
              })(<InputNumber style={{ width: '100%' }} min={1} max={360} placeholder="请输入 1~360 的整数配置" />)}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="水印透明度">
              {getFieldDecorator('transparency', {
                initialValue: 0.06,
                rules: [{ required: true, message: '请输入水印透明度' }],
              })(<InputNumber style={{ width: '100%' }} step={0.001} precision={3} min={0.005} max={1} placeholder="请输入大于0.005的配置" />)}
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="水印颜色">
              {getFieldDecorator('fontColor', {
                rules: [{ required: true, message: '请输入水印颜色' }],
              })(<CompactPicker color={props?.mode === 'edit' ? props?.data.fontColor : '#22194D'} />)}
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="水印内容">
              {getFieldDecorator('watermarkText', {
                rules: [{ required: true, message: '请输入水印内容' }],
              })(<Input.TextArea rows={2} placeholder="请输入" />)}
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="描述">{getFieldDecorator('watermarkDesc')(<Input.TextArea rows={2} placeholder="请输入" />)}</Form.Item>
          </Col>
        </Row>
        {/* 只展示，不提交 */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="创建时间">
              <Input readOnly disabled defaultValue={props?.mode === 'edit' ? props?.data.createDate : currentTime()} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="修改时间">
              <Input readOnly disabled defaultValue={props?.mode === 'edit' ? props?.data.updateDate : currentTime()} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <div
        style={{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
        }}
      >
        {props.mode === 'add' ? (
          <Button type="primary" className="margin-right" onClick={handleCreate} loading={loading}>
            创建
          </Button>
        ) : (
          <Button type="primary" className="margin-right" onClick={handleSubmit} loading={loading}>
            保存
          </Button>
        )}
        <Button type="ghost" onClick={props.close}>
          取消
        </Button>
      </div>
    </>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(
  Form.create({
    mapPropsToFields(props) {
      if (props.mode === 'edit') {
        return {
          watermarkName: Form.createFormField({ value: props.data.watermarkName }),
          watermarkCode: Form.createFormField({ value: props.data.watermarkCode }),
          watermarkType: Form.createFormField({ value: props.data.watermarkType }),
          coordinateX: Form.createFormField({ value: props.data.coordinateX }),
          coordinateY: Form.createFormField({ value: props.data.coordinateY }),
          statusCd: Form.createFormField({ value: props.data.statusCd }),
          fontSize: Form.createFormField({ value: props.data.fontSize }),
          inclination: Form.createFormField({ value: props.data.inclination }),
          transparency: Form.createFormField({ value: props.data.transparency }),
          fontColor: Form.createFormField({ value: props.data.fontColor }),
          watermarkText: Form.createFormField({ value: props.data.watermarkText }),
          watermarkDesc: Form.createFormField({ value: props.data.watermarkDesc }),
        };
      }
      return {};
    },
  })(Edit)
);
