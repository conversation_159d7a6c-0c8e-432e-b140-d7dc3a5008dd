import React, { useEffect } from 'react';
import dynamic from 'umi/dynamic';
import { connect } from 'dva';
import StepWizard from '@/components/StepWizard';
import PageLoading from '@/components/PageLoading';
import { Publisher } from '@/utils/PubSubHelper';

const List = dynamic({
  loader: () => import('./components/List'),
  loading: PageLoading,
});
const Detail = dynamic({
  loader: () => import('./components/Detail'),
  loading: PageLoading,
});

function Index(props) {
  useEffect(() => {
    /* 初始化 */
    props.dispatch({
      type: 'GroupCustManage/initStaticData',
    });
  }, []);
  const publisher = new Publisher();

  const toggleParentTabVisible = val => {
    publisher.publish('toggleTab', {
      visible: val,
    });
  };
  return (
    <div>
      <StepWizard isLazyMount>
        <List toggleParentTab={toggleParentTabVisible} />
        <Detail toggleParentTab={toggleParentTabVisible} destroy />
      </StepWizard>
    </div>
  );
}

export default connect(() => ({}))(Index);
