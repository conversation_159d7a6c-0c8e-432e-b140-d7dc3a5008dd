import isObject from 'lodash/isObject';
import defaultSettings from '@/defaultSettings';
import { getViewMode, setItem, getItem } from '@/utils/utils';

function updateLocalSetting(userId, key, value) {
  let settings = getItem(`user${userId}:settings`, 'localStorage');
  if (isObject(settings)) {
    settings[key] = value;
  } else {
    settings = {
      [key]: value,
    };
  }
  setItem(`user${userId}:settings`, settings, 'localStorage');
}

export default {
  namespace: 'setting',

  state: {
    viewMode: getViewMode(), // 可选值 "normal | inner"; normal表示有header和tab-nav; inner相反
    size: {
      width: '100%',
      height: '100%', // 内容高度(不包括#layout-header、.layout-tabs-nav和.ant-tabs-tabpane的上下内边距)
      fullWidth: '100%',
      fullHeight: '100%', // window高度
    },
    menuDrawerActive: true, // 是否默认展开左侧菜单
    showLeftMenuText: true, // 是否显示左侧边栏文字
    currentOpenLeftMenu: true, // 当前是否展开左侧菜单
    showSecondMenu: true, // 当前是否展开左侧二级菜单
    guideVisible: false, // 是否打开操作指引
    refreshKey: false, // 要刷新页面的key
    YXPluginReady: false, // 副驾是否完成初始化
    ...defaultSettings,
  },

  effects: {
    *showLeftMenu({ payload }, { put, select }) {
      const userId = yield select(state => state.login.user.userInfo.userId);
      // 记录用户的偏好设置

      updateLocalSetting(userId, 'showLeftMenu', payload);
      yield put({
        type: 'updateSettings',
        payload: { showLeftMenu: payload },
      });
    },
    *expandLeftMenu({ payload }, { put, select }) {
      const userId = yield select(state => state.login.user.userInfo.userId);
      // 记录用户的偏好设置
      updateLocalSetting(userId, 'menuDrawerActive', payload);
      yield put({
        type: 'updateSettings',
        payload: { menuDrawerActive: payload },
      });
    },
    *changeShowLeftMenuText({ payload }, { put, select }) {
      const userId = yield select(state => state.login.user.userInfo.userId);
      // 记录用户的偏好设置
      updateLocalSetting(userId, 'showLeftMenuText', payload);
      yield put({
        type: 'updateSettings',
        payload: { showLeftMenuText: payload },
      });
    },
    *gutter({ payload }, { put, select }) {
      const userId = yield select(state => state.login.user.userInfo.userId);
      // 记录用户的偏好设置
      updateLocalSetting(userId, 'gutter', payload);
      yield put({
        type: 'updateSettings',
        payload: { gutter: payload },
      });
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    readUserLocalSettings(state, { payload }) {
      const userSettings = getItem(`user${payload}:settings`, 'localStorage') ?? {};

      return {
        ...state,
        ...defaultSettings,
        ...userSettings,
        // currentOpenLeftMenu不是配置项，但初始化时依赖于配置项，因此在这里设置
        currentOpenLeftMenu: userSettings?.menuDrawerActive ?? defaultSettings.menuDrawerActive,
      };
    },
    toggleSettingDrawer(state, { payload }) {
      if (payload === true) {
        document.querySelector('body').classList.add('noScroll');
      } else {
        document.querySelector('body').classList.remove('noScroll');
      }
      return {
        ...state,
        settingDrawerActive: payload,
      };
    },
    toggleMenuDrawer(state, { payload }) {
      return {
        ...state,
        menuDrawerActive: payload,
      };
    },
    saveCurrentOpenLeftMenu(state, { payload }) {
      return {
        ...state,
        currentOpenLeftMenu: payload,
      };
    },
    saveSize(state, { payload }) {
      return {
        ...state,
        size: payload,
      };
    },
    updateSettings(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    updateViewMode(state, { payload }) {
      // 视图模式变化后，内容高度随之变化，所以需要触发basicLayout中的resize监听，让它重新计算一下高度
      const event = document.createEvent('HTMLEvents');
      event.initEvent('resize', true, false);
      window.dispatchEvent(event);
      return {
        ...state,
        ...payload,
      };
    },
    setRefreshKey(state, { payload }) {
      return {
        ...state,
        refreshKey: payload,
      };
    },
  },
};
