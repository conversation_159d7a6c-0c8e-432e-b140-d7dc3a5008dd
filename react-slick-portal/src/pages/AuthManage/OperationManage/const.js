import { message } from 'antd';

export const LEAF_TYPE = '1100';


// 工具栏配置
export const defaultToolbarConfig = {
  toolbarKeys: [
    'headerSelect', // 正文
    'blockquote', // 引号
    'bold', // 加粗
    'underline', // 下划线
    'italic', // 倾斜
    // 菜单组，包含多个菜单
    {
      key: 'group-more-style',
      title: '更多',
      iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M204.8 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M505.6 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M806.4 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path></svg>',
      menuKeys: [
        'through',
        'sup',
        'sub',
        'clearStyle',
      ],
    },
    'color',
    'bgColor',
    '|',
    'fontSize',
    'fontFamily',
    'lineHeight',
    '|',
    'bulletedList',
    'numberedList',
    {
      key: 'group-justify',
      title: '对齐',
      iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',
      menuKeys: [
        'justifyLeft',
        'justifyRight',
        'justifyCenter',
        'justifyJustify',
      ],
    },
    {
      key: 'group-indent',
      title: '缩进',
      iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v128H0z m384 192h640v128H384z m0 192h640v128H384z m0 192h640v128H384zM0 832h1024v128H0z m0-128V320l256 192z"></path></svg>',
      menuKeys: [
        'indent',
        'delIndent',
      ],
    },
    {
      key: 'group-image',
      title: '图片',
      iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z"></path></svg>',
      menuKeys: [
        'insertImage',
        'uploadImage',
      ],
    },
  ],
};

// 编辑器配置
export const defaultEditorConfig = { // JS 语法
  placeholder: '<p>请输入</p>',
  readOnly: true, // 只读
  MENU_CONF: {
    // 上传图片配置
    uploadImage: {
      // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
      allowedFileTypes: ['image/*'],
      server: 'portal/FileStoreController/upload.do',
      fieldName: 'file',
      headers: {
        Authorization: `Bearer ${sessionStorage.getItem('token')}`,
      },
      // 上传之前触发
      onBeforeUpload(file) { // JS 语法
        // file 选中的文件，格式如 { key: file }
        return file;
      },

      // 上传进度的回调函数
      // onProgress(progress) { // JS 语法
      //   // progress 是 0-100 的数字
      //   // console.log('上传图片进度', progress);
      // },
      // 自定义插入图片
      customInsert(res, insertFn) { // JS 语法
        // console.log('插入图片res', res);
        // res 即服务端的返回结果
        const { photoUrl, fileName, fileGetUrl } = res[0];
        const _fileGetUrl = fileGetUrl.replace('/portal-react/', '');
        // 从 res 中找到 url alt href ，然后插入图片
        insertFn(_fileGetUrl, fileName, photoUrl);
      },
      // 单个文件上传成功之后
      onSuccess(file) { // JS 语法
        message.success(`${file.name}上传成功`);
      },
      // 单个文件上传失败
      onFailed(file) { // JS 语法
        message.error(`${file.name}上传失败`);
      },

      // 上传错误，或者触发 timeout 超时
      onError(file, err) { // JS 语法
        message.error(`上传出错：${err}`);
      },
    },
  },
};
