import { Button, Col, Form, Input, message, Row } from 'antd';
import React, { useRef, useState } from 'react';
import styles from '@/pages/GroupCust/GroupCustView/components/BaseInfo/components/OrgInfo/components/SubGroupInfo/styles.less';
import SubGroupTable from '@/pages/GroupCust/GroupCustView/components/BaseInfo/components/OrgInfo/components/SubGroupInfo/components/SubGroupTable';
import {
  addSubGroupRel,
  qryGroupEnterpriseInfo,
} from '@/pages/GroupCust/GroupCustView/components/BaseInfo/components/OrgInfo/components/SubGroupInfo/service';


const AddGroupForm = props => {
  const { form, onSubmit, onCancel, parentGroupId } = props;
  const [list, setList] = useState([]);
  const childRef = useRef(null);
  const [queryLoading, setQueryLoading] = useState(false);
  const { getFieldDecorator } = form;
  const [addLoading, setAddLoading] = useState(false);
  const handleQuery = () => {
    const { groupName, groupId } = form.getFieldsValue();
    if (!groupName && !groupId) {
      message.warn('请输入查询集团编号或者集团名称');
      return;
    }
    setQueryLoading(true);
    let type = '';
    let keyword = '';
    let value = '';
    if (groupName) {
      type = 'cust_IQueryEnterpriseCSV_queryEnterpriseByGroupName';
      keyword = 'groupName';
      value = groupName;
    }
    if (groupId) {
      type = 'cust_IQueryEnterpriseCSV_queryEnterpriseByGroupId';
      keyword = 'groupId';
      value = groupId;
    }
    qryGroupEnterpriseInfo({
      type,
      data: {
        [keyword]: value,
      },
      pageFlag: 1,
      pageInfo: {
        currentPage: 1,
        pageSize: 10,
      },
    }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        setList(resultObject?.rspParam?.busiInfo?.outData);
        setQueryLoading(false);
      } else {
        setList([]);
        setQueryLoading(false);
        message.error(resultMsg);
      }
    });
  };

  const handleSubmit = () => {
    const subGroupIds = childRef?.current?.getChildData();
    console.log('parentGroupId=', parentGroupId);
    console.log('subGroupId=', subGroupIds[0]);
    if (parentGroupId && subGroupIds[0]) {
      setAddLoading(true);
      addSubGroupRel({
        subGroupId: subGroupIds[0],
        parentGroupId,
      }).then(res => {
        const { resultCode, resultMsg } = res;
        if (resultCode === 'TRUE') {
          setAddLoading(false);
          message.info('子集团绑定成功！');
          onSubmit();
        } else {
          setAddLoading(false);
          message.error(resultMsg || '子集团绑定失败！');
        }
      });
    } else {
      message.error('请选择需要绑定的子集团！');
    }
  };
  return (
    <div style={{ position: 'relative' }}>
      <Form className="flow fix-label">
        <Row gutter={16}>
          {/* 集团编码 */}
          <Col span={12}>
            <Form.Item label="集团编码">{getFieldDecorator('groupId', {})(<Input allowClear />)}</Form.Item>
          </Col>
          {/* 集团名称 */}
          <Col span={12}>
            <Form.Item label="集团名称">{getFieldDecorator('groupName', {})(<Input allowClear />)}</Form.Item>
          </Col>
        </Row>
      </Form>
      <Row>
        <Col className="text-right">
          <Button type="primary" onClick={handleQuery} loading={queryLoading}>
            查询
          </Button>
        </Col>
      </Row>
      <SubGroupTable ref={childRef} list={list} showOper={false} pick="radio" loading={queryLoading} />
      <div className={styles.formModalBottom}>
        <Button type="ghost" onClick={onCancel}>
          取消
        </Button>
        <Button type="primary" className="margin-left" onClick={handleSubmit} loading={addLoading}>
          确定
        </Button>
      </div>
    </div>
  );
};
export default Form.create()(AddGroupForm);
