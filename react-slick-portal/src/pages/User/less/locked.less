@import '~antd/lib/style/themes/default.less';

.container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10000;
  background: #35aef8;
  background: -webkit-linear-gradient(-45deg, #35aef8 0%, #7681ff 76%, #7681ff 76%);
  background: -webkit-linear-gradient(315deg, #35aef8 0%, #7681ff 76%, #7681ff 76%);
  background: linear-gradient(135deg, #35aef8 0%, #7681ff 76%, #7681ff 76%);
}

.main {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 420px;
  height: 134px;
  margin-top: -77px;
  margin-left: -210px;
  padding: 35px 25px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 3px;
}
.logout {
  position: fixed;
  top: 10px;
  right: 10px;
  width: 32px;
  height: 32px;
  color: #fff;
  font-size: 18px;
  line-height: 32px;
  cursor: pointer;
  &:hover {
    color: @text-color;
  }
}
.mediaBox,
.mediaBody {
  overflow: hidden;
  zoom: 1;
}
.mediaLeft,
.mediaRight,
.media-body {
  vertical-align: top;
}
.mediaLeft {
  float: left;
  padding-right: 20px;
}

/* 基于display:table-cell的自适应布局 */
.mediaBody {
  display: table-cell;
  *display: inline-block;
  width: 2000px;
  *width: auto;
}

/* fix：如果cell-item内包含连续英文字符换行 */
.mediaBodyTextHack {
  display: table;
  width: 100%;
  word-wrap: break-word;
  table-layout: fixed;
}

.input {
  input {
    height: 32px;
    line-height: 18px;
  }

  :global(.ant-input-group-addon) {
    padding: 0;
    border: none;
    :global(.ant-btn) {
      width: 36px;
      height: 32px;
      padding: 0;
      line-height: 32px;
    }
  }
}
.bg {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.circle1 {
  transform: translate(-9.85322px, 29.5597px);
}

.circle2 {
  transform: translate(9.85322px, -29.5597px);
}
