/* eslint-disable no-plusplus */
/* eslint-disable no-undef */
/* eslint-disable consistent-return */
/* eslint-disable no-unused-vars */
import React, { useState, useEffect, useRef, useImperativeHandle, useLayoutEffect } from 'react';
import { Icon, Form, Row, Col, Select, DatePicker, Button, message, Modal, Divider, Popover, Checkbox } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import { getPageSizeByCardHeight } from '@/utils/utils';
import request from '@/utils/request';
import AreaSelect from '@/components/AreaSelect';
import ComboGrid from '@/components/ComboGrid';

const { confirm } = Modal;
let checkBoxDatas = [];
function SystemOperLog({ size: { height }, form, cRef }) {
  const [size, setSize] = useState(getPageSizeByCardHeight(height - 64 - 8));
  const [statusMap, setStausMap] = useState([[], []]);
  const [editingKey, setEditingKey] = useState(-1);
  const [isEditing, setIsEditing] = useState(false);
  const [isCRUD, setIsCRUD] = useState(false);
  const { getFieldDecorator } = form;
  const [records, setRecords] = useState([]);
  const [areaValue, setAreaValue] = useState('');

  const [showColumns, setShowColumns] = useState([]);
  const [number, setNumber] = useState(0);
  const [totalCount, setTotalCount] = useState(0);

  const [paramsObj, setParamsObj] = useState({});
  const tableRef = useRef(null);
  const [checkBoxNums, setCheckBoxNums] = useState(0);

  const [actionType, setActionType] = useState([]);
  const [actionList, setActionList] = useState([]);
  const [dynamicColumns, setDynamicColumns] = useState([]);
  const [actionTypeObj, setActionTypeObj] = useState({}); // 当前选中的操作类型对象
  const [actionObjs, setActionObjs] = useState({}); // 操作动作key/value对象

  let handResult = null;
  const columnsWidth = 150;

  const defaultColumns = [
    {
      title: '序号',
      dataIndex: 'systemActionLogId',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '省份',
      dataIndex: 'provinceName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '地市',
      dataIndex: 'cityName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '区县',
      dataIndex: 'countryName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '账号',
      dataIndex: 'sysUserCode',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '用户名',
      dataIndex: 'userName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '归属系统',
      dataIndex: 'systemInfoName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '操作类型',
      dataIndex: 'actionTypeName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '菜单名称',
      dataIndex: 'menuName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '操作动作',
      dataIndex: 'actionName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '操作时间',
      dataIndex: 'createDate',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '客户端ip',
      dataIndex: 'clientIp',
      width: columnsWidth,
    },
  ];

  // 获取列表数据
  // eslint-disable-next-line no-shadow
  const getTableData = async ({ current, pageSize, paramsObj }) => {
    if (paramsObj.startDate && paramsObj.endDate && paramsObj.actionType) {
      const result = await request('orgauth/SystemActionLogController/selectGridDataByInfo.do', {
        data: {
          page: current,
          pageNum: current,
          pageSize,
          rowNum: pageSize,
          sortName: '',
          sortOrder: 'asc',
          filterCol: 'userName,sysUserCode',
          ...paramsObj,
        },
      });
      if (result) {
        if (Array.isArray(result.list)) {
          setTotalCount(result.total);
          return {
            total: result.total,
            data: result.list.map(val => {
              const { children, ...rest } = val;
              return {
                ...rest,
                // actionTypeName: actionType.find(item => item.attrValue === paramsObj.actionType)?.attrValueName || '',
                actionTypeName: actionTypeObj?.attrValueName,
                // actionName: actionList.find(item => item.attrValue === val.action)?.attrValueName || '',
                actionName: actionObjs[val.action],
              };
            }),
          };
        }
        return {
          total: 0,
          data: [],
        };
      }
    }
  };

  const { tableProps } = useAntdTable(params => getTableData({ ...params, pageSize: size, paramsObj }), [paramsObj, height], {
    defaultPageSize: size,
    form,
  });

  // 查询操作动作下拉选项
  const getActionList = async type => {
    const result = await request(`orgauth/AttrSpecController/getAttrByNbr.do?attrNbr=action_${type}`, {
      method: 'GET',
    });
    if (result.resultCode === 'TRUE') {
      const actionData = [];
      const objs = {};
      if (Array.isArray(result.resultObject[0]?.attrValues)) {
        result.resultObject[0]?.attrValues.forEach(item => {
          actionData.push({ ...item, value: item.attrValue, name: item.attrValueName });
          objs[item.attrValue] = item.attrValueName;
        });
      }
      setActionList(actionData);
      setActionObjs(objs);
    } else {
      message.error(result.resultMsg);
    }
  };

  // 查询动态表头
  const getDynamicColumn = async type => {
    const result = await request(`portal/MappingConfigController/getMappingConfigList.do?tableName=system_action_log&sceneType=${type}`, {
      method: 'GET',
    });
    if (result.resultCode === 'TRUE') {
      const columnData = result.resultObject?.map(item => ({ ...item, value: item.attrValue, name: item.attrValueName })) || [];
      if (columnData.length > 0) {
        const newColunm = columnData.map(item => ({
          title: item.codeName,
          dataIndex: item.columnName,
          width: columnsWidth,
          ellipsis: true,
        }));
        setDynamicColumns(newColunm);
        return newColunm;
      }
      setDynamicColumns([]);
      return [];
    }
    message.error(result.resultMsg);
  };

  // 查询操作类型下拉选项
  const getActionType = async (startDate, endDate) => {
    const result = await request('orgauth/AttrSpecController/getAttrByNbr.do?attrNbr=actionTypeLog', {
      method: 'GET',
    });
    if (result.resultCode === 'TRUE') {
      const typeData = result.resultObject[0]?.attrValues.map(item => ({ ...item, value: item.attrValue, name: item.attrValueName })) || [];
      setActionType(typeData);
      if (typeData?.length > 0) {
        setActionTypeObj(typeData[0]);
        await getActionList(typeData[0].attrValue);
        const columns = await getDynamicColumn(typeData[0].attrValue);
        if (columns.length > 0) {
          setShowColumns([...defaultColumns, ...columns]);
        }
        setParamsObj({ startDate, endDate, actionType: typeData[0].attrValue });
        form.setFieldsValue({ actionType: typeData[0].attrValue });
      }
    } else {
      message.error(result.resultMsg);
    }
  };

  function transferDateToStr() {
    const date = new Date();
    const y = date.getFullYear();
    let m = date.getMonth() + 1;
    m = m < 10 ? `0${m}` : m;
    let d = date.getDate();
    d = d < 10 ? `0${d}` : d;
    return `${y}-${m}-${d}`;
  }

  useLayoutEffect(() => {
    const systemLogInfo = {};
    systemLogInfo.startDate = moment(transferDateToStr());
    systemLogInfo.endDate = moment(transferDateToStr());
    form.setFieldsValue(systemLogInfo);
    const startDate = moment(systemLogInfo.startDate).format('YYYY-MM-DD');
    const endDate = moment(systemLogInfo.endDate).format('YYYY-MM-DD');
    getActionType(startDate, endDate);
    request('orgauth/SystemActionLogController/checkLogCUDPriv.do', {
      data: {},
      method: 'GET',
    }).then(result => {
      setIsCRUD(result);
      // setIsCRUD(true);
    });
  }, []);

  // 场景值改变
  const handleTypeSelect = key => {
    setActionTypeObj(actionType.find(item => item.attrValue === key));
    getActionList(key);
    getDynamicColumn(key);
    form.setFieldsValue({ action: [] });
  };

  function formIdsFormRecord(recordArr) {
    const arr = [];
    if (recordArr) {
      if (recordArr != null && recordArr.length !== 0) {
        // eslint-disable-next-line no-plusplus
        for (let i = 0; i < recordArr.length; i++) {
          arr.push(recordArr[i].id);
        }
      }
      return arr.toString();
    }
    return '';
  }
  function exportForm() {
    form.validateFields((err, fieldsValue) => {
      // if (err) return;
      let newFormValues = fieldsValue;

      // 拆分fieldsValue.createDateRange
      // 同时格式化数值，因为默认取到的是这样的格式 ["2019-02-09T15:22:14.109Z", "2019-03-11T15:22:14.109Z"]
      const startDate = moment(fieldsValue.startDate).format('YYYY-MM-DD');
      const endDate = moment(fieldsValue.endDate).format('YYYY-MM-DD');
      const ids = formIdsFormRecord(records);
      newFormValues.regionId = areaValue;
      newFormValues = { ...newFormValues, startDate, endDate, ids };

      // let formData = new FormData();
      // for(let key  in newFormValues){
      //   formData.append(key, newFormValues[key]==undefined?'':newFormValues[key]);
      // }
      if (totalCount > number) {
        message.error('导出数据过大,可通过过滤条件或手动选择来完成导出功能');
        return;
      }
      // 表单提交方式
      const tempForm = document.createElement('form');
      tempForm.action = 'orgauth/SystemActionLogController/expSystemLogGirdDataToExcel.do';
      // 如需打开新窗口，form的target属性要设置为'_blank'
      tempForm.target = '_blank';
      tempForm.method = 'post';
      tempForm.style.display = 'none';
      // 添加参数  //此为提交多个
      Object.keys(newFormValues).forEach(item => {
        const opt = document.createElement('input');
        if (item === 'userName') {
          opt.name = item;
          opt.value = newFormValues[item] && newFormValues[item].length > 0 ? newFormValues[item][0].userName : null;
        } else {
          // 设置 name 参数
          opt.name = item;
          opt.value = newFormValues[item] === undefined ? '' : newFormValues[item];
        }
        tempForm.appendChild(opt);
      });
      // for (const item in newFormValues) {
      //   const opt = document.createElement('input');
      //   if (item === 'userName') {
      //     opt.name = item;
      //     opt.value = newFormValues[item] && newFormValues[item].length > 0 ? newFormValues[item][0].userName : null;
      //   } else {
      //     // 设置 name 参数
      //     opt.name = item;
      //     opt.value = newFormValues[item] == undefined ? '' : newFormValues[item];
      //   }
      //   tempForm.appendChild(opt);
      // }
      // eslint-disable-next-line no-shadow
      const form = $(tempForm);
      $(document.body).append(form);
      // 提交数据
      form.submit();
      tableRef.current.cleanSelectedKeys([], []);
    });
  }

  // 此处注意useImperativeHandle方法的的第一个参数是目标元素的ref引用
  useImperativeHandle(cRef, () => ({
    // changeVal 就是暴露给父组件的方法
    exportForm: () => {
      exportForm();
    },
  }));

  const resetForm = async () => {
    setParamsObj({});
    setAreaValue('');
    form.resetFields();
    if (actionType?.length > 0) {
      setActionTypeObj(actionType[0]);
      await getActionList(actionType[0].attrValue);
      const columns = await getDynamicColumn(actionType[0].attrValue);
      if (columns.length > 0) {
        setShowColumns([...defaultColumns, ...columns]);
      }
    }
  };
  const { pagination, ...restTableProps } = tableProps;

  useEffect(() => {
    setSize(getPageSizeByCardHeight(height - 64 - 8));
  }, [height]);

  useEffect(() => {
    const queryCode = {
      groupCode: 'SYSTEM_EXP_CONF',
      paramCode: 'excelExpCount',
      defValue: 8000,
    };
    request('portal/DataDictController/getValueByCode.do', {
      data: { ...queryCode },
      method: 'GET',
    }).then(result => {
      setNumber(Number(result));
    });
  }, []);

  async function queryValueList() {
    let result;
    await Promise.all([
      request('portal/DomainDataController/getValuesList.do', {
        data: {
          busiNbr: 'SystemActionLog',
          propertyName: 'handResult',
        },
      }),
      request('orgauth/SystemInfoController/getSystemInfoList.do', {
        method: 'GET',
      }),
    ]).then(res => {
      result = res;
    });
    return result;
  }
  useEffect(() => {
    queryValueList().then(res => {
      setStausMap(res);
    });
  }, []);

  const submit = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      let newFormValues = fieldsValue;
      newFormValues.userName = fieldsValue.userName != null && fieldsValue.userName.length >= 1 ? fieldsValue.userName[0].userName : null;
      // 拆分fieldsValue.createDateRange
      // 同时格式化数值，因为默认取到的是这样的格式 ["2019-02-09T15:22:14.109Z", "2019-03-11T15:22:14.109Z"]
      const startDate = moment(fieldsValue.startDate).format('YYYY-MM-DD');
      const endDate = moment(fieldsValue.endDate).format('YYYY-MM-DD');
      newFormValues.regionId = areaValue;
      const systemInfoIdStr = fieldsValue.systemInfoId?.length > 0 ? fieldsValue.systemInfoId.join() : '';
      const actionStr = fieldsValue.action?.length > 0 ? fieldsValue.action.join() : '';
      newFormValues = { ...newFormValues, startDate, endDate, systemInfoId: systemInfoIdStr, action: actionStr };
      setParamsObj(newFormValues);
      setShowColumns([...defaultColumns, ...dynamicColumns]);
    });
  };

  const inputChange = value => {
    handResult = value;
  };

  function handleEdit(record) {
    setEditingKey(record.id);
    setIsEditing(true);
  }

  function handleSave(record) {
    const params = {};
    params.handResult = handResult || record.handResult;
    params.systemActionLogId = record.systemActionLogId;
    if (handResult) {
      params.handResult = handResult;
    }

    request('orgauth/SystemActionLogController/updateSystemActionLog.do', {
      data: params,
    }).then(result => {
      if (result.resultCode === '0000' || result.resultCode === '0') {
        setIsEditing(false);
        setEditingKey(-1);
        handResult = null;
        message.success('修改成功');
        submit();
      } else {
        message.error(result.resultMsg);
      }
    });
  }

  function handResultColumn(record) {
    return (
      <Select
        allowClear
        placeholder="请选择"
        style={{ width: '100%' }}
        showSearch
        optionFilterProp="children"
        onChange={inputChange}
        defaultValue={record.handResult}
      >
        {statusMap[0].length ? statusMap[0].map(item => <Select.Option key={item.value}>{item.name}</Select.Option>) : null}
      </Select>
    );
  }

  // 操作时保存和取消
  function saveAndCancel(record) {
    return (
      <span>
        <a
          onClick={() => {
            handleSave(record);
          }}
        >
          保存
        </a>
        <Divider type="vertical" />
        <a
          onClick={() => {
            setIsEditing(false);
            setEditingKey(-1);
            handResult = null;
          }}
        >
          取消
        </a>
      </span>
    );
  }
  function editAndDelDisabled() {
    return (
      <div>
        <a disabled>编辑</a>
        {/* <Divider type="vertical" /> */}
        {/* <a disabled>删除</a> */}
      </div>
    );
  }
  function editAndDel(record) {
    return (
      <div hidden={!isCRUD}>
        <span>
          <a
            onClick={() => {
              handleEdit(record);
            }}
          >
            编辑
          </a>
        </span>
        {/* <Divider type="vertical" /> */}
        {/* <span> */}
        {/*  <a */}
        {/*    onClick={() => { */}
        {/*      handleDelete(record); */}
        {/*    }} */}
        {/*  > */}
        {/*    删除 */}
        {/*  </a> */}
        {/* </span> */}
      </div>
    );
  }
  function operatorColumn(record) {
    const item = [];
    if (isEditing && editingKey === record.id) {
      item.push(saveAndCancel(record));
    } else if (isEditing && editingKey !== record.id) {
      item.push(editAndDelDisabled());
    } else {
      item.push(editAndDel(record));
    }
    return item;
  }
  const columns = [
    {
      title: '序号',
      dataIndex: 'systemActionLogId',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '省份',
      dataIndex: 'provinceName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '地市',
      dataIndex: 'cityName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '区县',
      dataIndex: 'countryName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '账号',
      dataIndex: 'sysUserCode',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '用户名',
      dataIndex: 'userName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '手机号码',
      dataIndex: 'mobilePhone',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '归属系统',
      dataIndex: 'systemInfoName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '操作类型',
      dataIndex: 'actionTypeName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '菜单名称',
      dataIndex: 'menuName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '操作动作',
      dataIndex: 'actionName',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '操作时间',
      dataIndex: 'createDate',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '对应异常信息',
      dataIndex: 'actionMsg',
      width: columnsWidth,
      ellipsis: true,
    },

    {
      title: '出错类别',
      dataIndex: 'errorType',
      width: columnsWidth,
      ellipsis: true,
      // render: (text, record) => {
      //   let value = '';
      //   if (statusMap[1].length) {
      //     for (let i = 0; i < statusMap[1].length; i++) {
      //       if (statusMap[1][i].value === record.errorType) {
      //         value = statusMap[1][i].name;
      //       }
      //     }
      //   }
      //   return <span>{value}</span>;
      // },
    },
    {
      title: '错误编码',
      dataIndex: 'errorCode',
      width: columnsWidth,
      ellipsis: true,
    },
    {
      title: '客户端ip',
      dataIndex: 'clientIp',
      width: columnsWidth,
    },
    // {
    //   title: '处理结果',
    //   dataIndex: 'handResultName',
    //   width: columnsWidth,
    //   ellipsis: true,
    //   render: (text, record) => {
    //     const item = [];
    //     if (editingKey !== record.id) {
    //       item.push(<span>{text}</span>);
    //     } else {
    //       item.push(handResultColumn(record));
    //     }
    //     return <div>{item}</div>;
    //   },
    // },
    // {
    //   title: '操作',
    //   width: columnsWidth,
    //   dataIndex: 'operator',
    //
    //   render: (text, record) => {
    //     let item = [];
    //     const disabled = isEditing && editingKey !== record.id;
    //     if (!record.handResult) {
    //       item.push(
    //         <span>
    //           <a
    //             disabled={disabled}
    //             onClick={() => {
    //               handleDelete(record);
    //             }}
    //           >
    //             删除
    //           </a>
    //         </span>
    //       );
    //     } else {
    //       item = operatorColumn(record);
    //     }
    //     return <div>{item}</div>;
    //   },
    // },
  ];

  useEffect(() => {
    setCheckBoxNums(columns.length);
    setShowColumns(defaultColumns);
  }, [isCRUD, isEditing, editingKey]);

  const config = {
    rules: [{ required: true, message: '不能为空' }],
  };

  function getIds(datas) {
    const params = [];
    for (let i = 0; i < datas.length; i++) {
      const param = {};
      param.id = datas[i].id;
      param.systemActionLogId = datas[i].systemActionLogId;
      params.push(param);
    }
    return params;
  }
  function delVisitPlanBatch(delType, record) {
    let params = [];
    if (delType === 'batchDel') {
      params = records;
    } else {
      params.push(record);
    }
    if (params.length === 0 && String(delType) === 'batchDel') {
      message.warn('请勾选您要删除的操作日志');
      return;
    }
    confirm({
      title: '确定删除所选操作日志？',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        request('orgauth/SystemActionLogController/deleteSystemActionLogs.do', {
          data: getIds(params),
        }).then(result => {
          if (result.resultCode === '0000') {
            message.success('删除成功');
            setRecords([]);
            tableRef.current.cleanSelectedKeys([], []);
            submit();
          } else {
            message.error('删除失败');
          }
        });
      },
      onCancel() {},
    });
  }

  function handleDelete(record) {
    delVisitPlanBatch('del', record);
  }
  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 8 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 16 },
    },
  };

  const areaChange = record => {
    setAreaValue(record.commonRegionId);
  };

  const checkBoxColumns = [
    {
      title: '序号',
      dataIndex: 'systemActionLogId',
    },
    {
      title: '省份',
      dataIndex: 'provinceName',
    },
    {
      title: '地市',
      dataIndex: 'cityName',
    },
    {
      title: '区县',
      dataIndex: 'countryName',
    },
    {
      title: '账号',
      dataIndex: 'sysUserCode',
    },
    {
      title: '用户名',
      dataIndex: 'userName',
    },
    {
      title: '手机号码',
      dataIndex: 'mobilePhone',
    },
    {
      title: '归属系统',
      dataIndex: 'systemInfoName',
    },
    {
      title: '操作类型',
      dataIndex: 'actionTypeName',
    },
    {
      title: '菜单名称',
      dataIndex: 'menuName',
    },
    {
      title: '操作动作',
      dataIndex: 'actionName',
    },
    {
      title: '操作时间',
      dataIndex: 'createDate',
    },
    {
      title: '对应异常信息',
      dataIndex: 'actionMsg',
    },
    {
      title: '出错类别',
      dataIndex: 'errorType',
    },
    {
      title: '错误编码',
      dataIndex: 'errorCode',
    },
    // {
    //   title: '处理结果',
    //   dataIndex: 'handResultName',
    // },
    {
      title: '客户端ip',
      dataIndex: 'clientIp',
    },
    // {
    //   title: '操作',
    //   dataIndex: 'operator',
    // },
  ];
  const checkBoxColumnsDefault = [
    {
      title: '序号',
      dataIndex: 'systemActionLogId',
    },
    {
      title: '省份',
      dataIndex: 'provinceName',
    },
    {
      title: '地市',
      dataIndex: 'cityName',
    },
    {
      title: '区县',
      dataIndex: 'countryName',
    },
    {
      title: '账号',
      dataIndex: 'sysUserCode',
    },
    {
      title: '用户名',
      dataIndex: 'userName',
    },
    {
      title: '归属系统',
      dataIndex: 'systemInfoName',
    },
    {
      title: '操作类型',
      dataIndex: 'actionTypeName',
    },
    {
      title: '菜单名称',
      dataIndex: 'menuName',
    },
    {
      title: '操作动作',
      dataIndex: 'actionName',
    },
    {
      title: '操作时间',
      dataIndex: 'createDate',
    },
    {
      title: '客户端ip',
      dataIndex: 'clientIp',
    },
  ];

  const checkBoxOnChange = datas => {
    const assitColums = [];
    let width = 0;
    if (datas.length >= 1 && datas.length <= 8) {
      width = parseInt(190 * (8 - datas.length), 10) / datas.length;
    }
    checkBoxDatas = datas;
    columns.forEach(column => {
      datas.forEach(item => {
        if (column.dataIndex === item) {
          assitColums.push(column);
        }
      });
    });
    for (let i = 0; i < assitColums.length; i++) {
      assitColums[i].width += width;
    }
    setCheckBoxNums(datas.length);
    setShowColumns(assitColums);
  };

  function isDisabledCheckbox(value) {
    // 最少需要展示一列
    return checkBoxDatas.length < 2 && checkBoxDatas.includes(value);
  }
  // eslint-disable-next-line no-shadow
  const CheckBoxContent = (checkBoxColumns, checkBoxColumnsDefault) => {
    const defaultValue = [];
    // 默认展示：序号、省份、地市 、区县、账号、用户名、归属系统、菜单名称、操作动作、操作时间、客户端IP
    for (let i = 0; i < checkBoxColumnsDefault.length; i++) {
      defaultValue.push(checkBoxColumnsDefault[i].dataIndex);
    }
    return (
      <div style={{ width: '350px' }}>
        <Checkbox.Group style={{ width: '100%' }} onChange={checkBoxOnChange} defaultValue={defaultValue}>
          <Row>
            {checkBoxColumns.map((item, index) => (
              <Col span={8} key={index}>
                {' '}
                <Checkbox value={item.dataIndex} disabled={isDisabledCheckbox(item.dataIndex)}>
                  {item.title}
                </Checkbox>
              </Col>
            ))}
          </Row>
        </Checkbox.Group>
      </div>
    );
  };

  return (
    <div>
      <Form>
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
          <Col span={6}>
            <Form.Item label="开始时间：" {...formItemLayout}>
              {getFieldDecorator('startDate', {
                ...config,
                initialValue: moment(transferDateToStr()),
              })(<DatePicker style={{ width: '100%' }} allowClear />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="结束时间：" {...formItemLayout}>
              {getFieldDecorator('endDate', {
                ...config,
                initialValue: moment(transferDateToStr()),
              })(<DatePicker defaultValue={moment(transferDateToStr(), 'YYYY-MM-DD')} style={{ width: '100%' }} />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="归属区域:" {...formItemLayout}>
              {getFieldDecorator('regionId')(<AreaSelect placeholder="请选择" onChange={areaChange} />)}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="系统用户：" {...formItemLayout}>
              {getFieldDecorator('userName', {
                rules: [
                  {
                    required: false,
                    message: '用户名称不能为空',
                  },
                ],
              })(
                <ComboGrid
                  url="orgauth/SystemUserController/selectUserGridData.do"
                  popupStyle={{ width: 560 }}
                  placeholder="请选择"
                  searchPlaceholder="请输入名称或账号进行搜索"
                  label="userName"
                  rowKey="id"
                  pick="radio"
                  destroyPopupOnHide
                  params={{ filterCol: 'userName' }}
                  columns={[
                    {
                      title: '账号',
                      dataIndex: 'sysUserCode',
                      ellipsis: true,
                    },
                    {
                      title: '名称',
                      dataIndex: 'userName',
                      ellipsis: true,
                    },
                    {
                      title: '所属区域等级',
                      ellipsis: true,
                      dataIndex: 'userOrgLevel',
                      // eslint-disable-next-line no-nested-ternary
                      render: text => (text === 1 ? '省级' : text === 2 ? '市级' : '区县级'),
                    },
                  ]}
                />
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
          <Col span={6}>
            <Form.Item label="归属系统" {...formItemLayout}>
              {getFieldDecorator('systemInfoId')(
                <Select placeholder="请选择" allowClear style={{ width: '100%' }} showSearch optionFilterProp="children" mode="multiple">
                  {statusMap[1].length
                    ? statusMap[1].map(item => (
                        <Select.Option value={item.id} key={item.id}>
                          {item.systemName}
                        </Select.Option>
                      ))
                    : null}
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="操作类型" {...formItemLayout}>
              {getFieldDecorator('actionType', {
                ...config,
                initialValue: actionType?.length > 0 ? actionType[0].attrValue : '',
              })(
                <Select allowClear placeholder="请选择" style={{ width: '100%' }} showSearch optionFilterProp="children" onSelect={handleTypeSelect}>
                  {actionType.length ? actionType.map(item => <Select.Option key={item.value}>{item.name}</Select.Option>) : null}
                </Select>
              )}
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="操作动作" {...formItemLayout}>
              {getFieldDecorator('action')(
                <Select allowClear placeholder="请选择" style={{ width: '100%' }} showSearch optionFilterProp="children" mode="multiple">
                  {actionList.length ? actionList.map(item => <Select.Option key={item.value}>{item.name}</Select.Option>) : null}
                </Select>
              )}
            </Form.Item>
          </Col>
          {/* <Col span={6}> */}
          {/*  <Form.Item label="处理结果" {...formItemLayout}> */}
          {/*    {getFieldDecorator('handResult')( */}
          {/*      <Select */}
          {/*        allowClear */}
          {/*        placeholder="请选择" */}
          {/*        style={{ width: '100%' }} */}
          {/*        showSearch */}
          {/*        optionFilterProp="children" */}
          {/*      > */}
          {/*        {statusMap[2].length */}
          {/*          ? statusMap[2].map(item => ( */}
          {/*              <Select.Option key={item.value}>{item.name}</Select.Option> */}
          {/*            )) */}
          {/*          : null} */}
          {/*      </Select> */}
          {/*    )} */}
          {/*  </Form.Item> */}
          {/* </Col> */}
        </Row>
        <Row>
          <Col span={4}>
            <Popover placement="bottomRight" content={CheckBoxContent(checkBoxColumns, checkBoxColumnsDefault)} trigger="click">
              <Icon style={{ marginLeft: '5px' }} type="setting" onClick={() => {}} />
              <span style={{ marginLeft: '5px' }}>自定义列</span>
            </Popover>
          </Col>
          <Col span={20} className="text-right">
            <Button type="primary" onClick={submit}>
              查询
            </Button>
            <Button type="ghost" className="margin-left" onClick={resetForm}>
              重置
            </Button>
          </Col>
        </Row>
      </Form>
      <SlickTable
        ref={tableRef}
        style={{ marginTop: 8 }}
        rowKey={record => record.systemActionLogId}
        columns={showColumns}
        {...restTableProps}
        data={{
          pagination: {
            ...pagination,
            pageSize: size,
          },
        }}
        // width={300}
        scroll={{ x: 1000 }}
        onSelectRow={data => {
          setRecords(data);
        }}
      />
    </div>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(SystemOperLog));
