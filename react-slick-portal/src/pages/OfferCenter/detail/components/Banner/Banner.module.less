.wrap {
  position: relative;
  width: 100%;
  height: 340px;

  .banners {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 340px;

    .bg {
      width: 100%;
      height: 340px;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;

      .baseInfo {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        box-sizing: border-box;
        // position: absolute;
        width: 1200px;
        height: 100%;
        margin: 0 auto;
        padding-top: 48px;

        .title {
          color: #fff;
          font-weight: 600;
          font-size: 32px;
        }

        .desc {
          display: inline-block;
          width: 600px;
          padding-top: 12px;
          color: #fff;
          font-size: 12px;
        }

        .amount {
          padding-top: 16px;
          color: #FF4D4F;
          font-weight: bold;
          font-size: 28px;

          &::before {
            font-size: 16px;
            content: '￥';
          }

          &::after {
            font-size: 14px;
            content: '/月';
          }
        }

        .btns {
          display: flex;
          padding-top: 16px;

          button {
            margin-right: 16px;
          }

          .collection {
            display: flex;
            align-items: center;
            color: #a5a8ab;
            font-size: 24px;

            .collectionHandle {
              margin-left: 10px;
              font-size: 16px;
            }

            .avtiveClorl {
              color: #ff4d4f;
            }
          }
        }
      }
    }

    .bgContainer {
      width: 100%;
      height: 340px;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;
    }
  }


}
