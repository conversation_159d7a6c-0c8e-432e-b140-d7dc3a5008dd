/* 基础信息 */
import {
  CDR_TABLE_PROVINCE_CODE,
  CMIOT_ORG_VALUE,
  CMIOT_REGION_VALUE,
  ENTERPRISE_IMPORT_FLAG,
  ENTERPRISE_SIZE_CODE,
  GROUP_TYPE,
  GROUP_TYPE_DETAIL
} from '@/utils/consts';

export const BaseInfoFields = [
  { name: '集团名称', key: 'GROUP_NAME', span: 1 },
  { name: '集团编码', key: 'GROUP_ID', span: 1 },
  { name: '传真', key: 'FAX_NBR', span: 1 },
  { name: '联系电话', key: 'GROUP_CONTACT_PHONE', span: 1 },
  { name: '公司邮编', key: 'POST_CODE', span: 1 },
  { name: '法人代表', key: 'JURISTIC_NAME', span: 1 },
  { name: '集团地址', key: 'GROUP_ADDR', span: 1 },
  { name: '客户服务等级', key: 'SERV_LEVEL', span: 1, options: null, enumeration: 'GROUP_SERV_LEVEL' }, // GROUP_SERV_LEVEL
  { name: '集团规模', key: 'ENTERPRISE_SIZE_CODE', span: 1, options: null, enumeration: 'ENTERPRISE_SIZE_CODE' }, // ENTERPRISE_SIZE_CODE
  { name: '归属区重要集团', key: 'IMPORT_FLAG', span: 1, options: null, enumeration: 'ENTERPRISE_IMPORT_FLAG' }, // ENTERPRISE_IMPORT_FLAG
  { name: '组织机构代码', key: 'GROUP_ORG_CODE', span: 1 },
  { name: '法人证件类型', key: 'JURISTIC_TYPE_CODE', span: 1, options: null, enumeration: 'GROUP_JURISTIC_IDEN_TYPE' }, // GROUP_JURISTIC_IDEN_TYPE
  { name: '法人证件号码', key: 'JURISTIC_IDEN_NR', span: 1 },
  { name: '证件编号', key: 'BUSI_LICENCE_NO', span: 1 },
  { name: '企业网站', key: 'WEBSITE', span: 1 },
  { name: '信用级别', key: 'CREDIT_LEVEL', span: 1, options: null, enumeration: 'GROUP_CREDIT_LEVEL' }, // GROUP_CREDIT_LEVEL
  { name: '证件编号', key: 'BUSI_LICENCE_NO', span: 1 },
  { name: '竞争对手信息', key: 'GROUP_ADVERSARY', span: 1 },
  { name: '集团总部编码', key: 'PNATIONAL_GROUP_ID', span: 1 },
  { name: '客户类型', key: 'GROUP_CUST_TYPE', span: 1, options: null, enumeration: 'GROUP_CUST_TYPE' }, // GROUP_CUST_TYPE
  { name: '客户责任人', key: 'GROUP_DUTY_CUST', span: 1 },
  { name: 'BOSS集团客户号', key: 'EC_CODE', span: 1 },
  { name: '升级为全网集团', key: 'IS_SEND_BBOSS', span: 1, options: null, enumeration: 'ENTERPRISE_IMPORT_FLAG' }, // ENTERPRISE_IMPORT_FLAG
  { name: '是否直管客户', key: 'IS_DIRECT', span: 1, options: null, enumeration: 'ENTERPRISE_IMPORT_FLAG' }, // ENTERPRISE_IMPORT_FLAG
  { name: '直管客户编号', key: 'DIRECT_CUST_ID', span: 1 },
  { name: '组织机构编码', key: 'CUST_ORG_STRUCT_CODE', span: 1 },
  { name: '一经编码', key: 'SEND_HQ_CODE', span: 1 },
  { name: '客户证件扫描附件', key: 'CUST_UN_FILE_FTP', span: 1 },
  { name: '是否升级物联网', key: 'SYNC_WLW_STATUS', span: 1, options: null, enumeration: 'ENTERPRISE_IMPORT_FLAG' }, // ENTERPRISE_IMPORT_FLAG
];

export const IdenInfoFields = [
  { name: '集团证件类型', key: 'IDEN_TYPE_CODE', span: 1, options: null, enumeration: 'GROUP_BUSI_LICENCE_TYPE' }, // GROUP_BUSI_LICENCE_TYPE
  { name: '集团证件号码', key: 'IDEN_NR', span: 1 },
  { name: '集团证件名称', key: 'IDEN_NAME', span: 1 },
  { name: '集团单位证件地址', key: 'IDEN_ADDRESS', span: 1 },
];

export const OrderOperatorInfoFields = [
  { name: '姓名', key: 'OPERATOR_NAME', span: 1 },
  { name: '证件类型', key: 'OPERATOR_IDEN_TYPE', span: 1, options: null, enumeration: 'IDEN_CARD_TYPE' }, // IDEN_CARD_TYPE
  { name: '证件号码', key: 'OPERATOR_IDEN_NR', span: 1 },
  { name: '地址', key: 'OPERATOR_ADDRESS', span: 1 },
];

export const UserOwnerInfoFields = [
  { name: '姓名', key: 'OWNER_MAN', span: 1 },
  { name: '证件类型', key: 'CARD_TYPE_ID', span: 1, options: null, enumeration: 'IDEN_CARD_TYPE' }, // IDEN_CARD_TYPE
  { name: '证件号码', key: 'CARD_NO', span: 1 },
  { name: '地址', key: 'ADDRESS', span: 1 },
];

export const NetworkCoverInfoFields = [
  { name: 'PON网络覆盖(3个工作日开通)', key: 'NET_COVER', span: 1, options: null, enumeration: 'ENTERPRISE_IMPORT_FLAG' }, // ENTERPRISE_IMPORT_FLAG
  { name: 'PTN网络覆盖(3个工作日开通)', key: 'PTN_COVER', span: 1, options: null, enumeration: 'ENTERPRISE_IMPORT_FLAG' }, // ENTERPRISE_IMPORT_FLAG
  { name: 'SDH网络覆盖(3个工作日开通)', key: 'SDH_COVER', span: 1, options: null, enumeration: 'ENTERPRISE_IMPORT_FLAG' }, // ENTERPRISE_IMPORT_FLAG
  { name: '光纤覆盖(7个工作日开通)', key: 'FIBER_COVER', span: 1, options: null, enumeration: 'ENTERPRISE_IMPORT_FLAG' }, // ENTERPRISE_IMPORT_FLAG
  { name: '无覆盖(7个以上工作日开通)', key: 'NON_COVER', span: 1, options: null, enumeration: 'ENTERPRISE_IMPORT_FLAG' }, // ENTERPRISE_IMPORT_FLAG
];

export const ResourceCoverageInfoFields = [
  { name: '所在经度', key: 'LONGITUDE', span: 1 },
  { name: '所在纬度', key: 'LATITUDE', span: 1 },
  { name: 'PON网络是否覆盖', key: 'IS_PONCOVER', span: 1 },
  { name: '传输网络是否覆盖', key: 'IS_TRANSCOVER', span: 1 },
  { name: '光纤资源是否覆盖', key: 'IS_OPTICALCOVER', span: 1 },
  { name: '覆盖信息最后更新时间', key: 'COVER_CHANGE_DATE', span: 1 },
];

export const RegionInfoFields = [
  { name: '地市', key: 'MGMT_DISTRICT', span: 1 },
  { name: '区县', key: 'MGMT_COUNTY', span: 1 },
  { name: '片区', key: 'CHIP_AREA_CODE', span: 1 },
];

export const MailingAddressInfoFields = [
  { name: '省份', key: 'UP_ADDR_ID', span: 1 },
  { name: '地市', key: 'ADDR_CODE', span: 1 },
  { name: '邮编', key: 'EMAIL_POST_CODE', span: 1 },
  { name: '地址', key: 'EMAIL_ADDR', span: 1 },
];

export const EnterpriseManagerInfoFields = [
  { name: '客户经理', key: 'STAFF_NAME', span: 1 },
  { name: '客户经理编码', key: 'CUST_MGR_ID', span: 1 },
  { name: '手机号码', key: 'BILL_ID', span: 1 },
  { name: '办公电话', key: 'OFFICE_TEL', span: 1 },
  { name: '邮件地址', key: 'EMAIL', span: 1 },
];

export const ClassifiedInfoFields = [
  { name: '行业类型', key: 'CALLING_TYPE', span: 1, options: null, enumeration: 'CALLING_AREA_CODE' }, // CALLING_AREA_CODE
  { name: '行业门类', key: 'CALLING_AREA', span: 1, options: null, enumeration: 'GROUP_ARCHIVES@TRADE_TYPE' }, // GROUP_ARCHIVES@TRADE_TYPE
  { name: '行业大类', key: 'CALLING_AREA_A', span: 1 }, // "GROUP_ARCHIVES@BIG_TRADE_TYPE@" + CALLING_AREA
  { name: '行业中类', key: 'CALLING_AREA_B', span: 1 }, // "GROUP_ARCHIVES@MIDDLE_TRADE_TYPE@" + CALLING_AREA + "@" + CALLING_AREA_A
  { name: '集团半年级别', key: 'GROUP_HALF_LEVEL', span: 1, options: null, enumeration: 'GROUP_SERV_LEVEL' },
  { name: '集团所在省份', key: 'PROVINCE_CODE', span: 1, options: CDR_TABLE_PROVINCE_CODE },
  { name: '集团客户级别', key: 'GROUP_LEVEL_NEW', span: 1, options: null, enumeration: 'GROUP_CUST_LEVEL' }, // GROUP_CUST_LEVEL
  { name: '经营区域范围', key: 'ENTERPRISE_SCOPE', span: 1, options: null, enumeration: 'ENTERPRISE_SCOPE' }, // ENTERPRISE_SCOPE
  { name: '支付方式', key: 'GROUP_PAY_MODE', span: 1, options: null, enumeration: 'GROUP_PAY_MODE' }, // GROUP_PAY_MODE
];

export const ExtendedInfoFields = [
  { name: '是否拍照集团', key: 'IS_PHOTO', span: 1, options: null, enumeration: 'ENTERPRISE_IMPORT_FLAG' }, // ENTERPRISE_IMPORT_FLAG
  { name: '是否100强集团', key: 'IS_TOP100', span: 1, options: null, enumeration: 'ENTERPRISE_IMPORT_FLAG' }, // ENTERPRISE_IMPORT_FLAG
  { name: '跨省集团编号', key: 'MP_GROUP_CODE', span: 1 },
  { name: '欠费告警阀值（元）', key: 'ARR_WARN', span: 1 },
  { name: '总机号码', key: 'SWITCH_PHONE', span: 1 },
  { name: '集团类型', key: 'GROUP_TYPE', span: 1, options: null, enumeration: 'GROUP_TYPE' }, // GROUP_TYPE
  { name: '集团详细类型', key: 'GROUP_TYPE_DETAIL', span: 1, options: null, enumeration: 'GROUP_TYPE_DETAIL' }, // GROUP_TYPE_DETAIL
];

export const OtherInfoFields = [
  { name: '总部名称', key: 'PNATIONAL_GROUP_NAME', span: 1 },
  { name: '上级机构集团编码', key: 'SUPER_GROUP_ID', span: 1 },
  { name: '上级机构名称', key: 'SUPER_GROUP_NAME', span: 1 },
  { name: '企业员工数', key: 'ENT_EMPLOYEE_COUNT', span: 1 },
  { name: '集团纪念日', key: 'GROUP_FESTIVAL', span: 1 },
  { name: '通信预算（元）', key: 'COMM_BUDGET', span: 1 },
  { name: '移动手机数', key: 'MOBILE_NUM', span: 1 },
  { name: '联通手机数', key: 'UNION_NUM', span: 1 },
  { name: '小灵通手机数', key: 'XLT_NUM', span: 1 },
  { name: '注册资金（万元）', key: 'REG_MONEY', span: 1 },
  { name: '年营业额（万元）', key: 'YEAR_GAIN', span: 1 },
  { name: '经营范围', key: 'SCOPE', span: 1 },
  { name: '年利润（万元）', key: 'TURNOVER', span: 1 },
  { name: '跨省类型', key: 'MULTI_PROVINCE', span: 1, options: null, enumeration: 'GROUP_SCOPE_TYPE' }, // GROUP_SCOPE_TYPE
  { name: '产品说明', key: 'PROD_DESC', span: 1 },
  { name: '话费报销方式', key: 'PAYFOR_WAY_CODE', span: 1, options: null, enumeration: 'PAYFOR_WAY_CODE' }, // PAYFOR_WAY_CODE
  { name: '人均话费（元）', key: 'EMPLOYEE_ARPU', span: 1 },
  { name: '报销人数', key: 'WRITEFEE_COUNT', span: 1 },
  { name: '最感兴趣的移动业务', key: 'WRITEFEE_COUNT', span: 1 },
  { name: '最希望的优惠方式', key: 'LIKE_DISCNT_MODE', span: 1 },
  { name: '移动信号覆盖情况', key: 'SING_OVER_LAY', span: 1 },
  { name: '其他通信产品使用情况', key: 'OTH_USE_DTL', span: 1 },
  { name: '备注', key: 'REMARKS', span: 1 },
];

export const queryGroupFields = [
  { key: 'groupName', label: '集团名称', type: 'input' },
  { key: 'groupId', label: '集团编码', type: 'input' },
];

export const CmiotFields = [
  {
    name: 'CMIOT归属组织',
    key: 'CMIOT_ORG_ID',
    span: 1,
    options: CMIOT_ORG_VALUE,
  },
  {
    name: 'CMIOT客户经理从账号',
    key: 'CMIOT_STAFF_NUMBER',
    span: 1,
  },
  {
    name: 'CMIOT归属地市',
    key: 'CMIOT_REGION_ID',
    span: 1,
    options: CMIOT_REGION_VALUE,
  },
  {
    name: 'CMIOT手机号',
    key: 'CMIOT_MOBILE_PHONE',
    span: 1,
  },
  {
    name: 'CMIOT归属区县',
    key: 'CMIOT_COUNTRY_ID',
    span: 1,
  },
  {
    name: 'CMIOT业务附件',
    key: 'CMIOT_ATTACHMENTS',
    span: 1,
  },
];

/* 纳税人属性信息 */
export const TaxpayerInfoFields = [
  {
    name: '纳税人资质属性',
    key: 'TAX_QUALIFICATION',
    span: 1,
    options: [
      { label: '小规模纳税人', value: '0' },
      { label: '一般纳税人', value: '1' },
    ],
  },
  {
    name: '发票打印类型',
    key: 'INVOICE_PRINT_TYPE',
    span: 1,
    options: [
      { label: '预存打印发票', value: '0' },
      { label: '月结打印发票', value: '1' },
      { label: '月结通用机打打发票', value: '2' },
      { label: '月结增值税专票', value: '3' },
      { label: '增值税电子普通发票', value: '4' },
    ],
  },
  {
    name: '企业属性',
    key: 'ENTERPRISE_ATTR',
    span: 1,
    options: [
      { label: '企业', value: '0' },
      { label: '个人', value: '1' },
      {
        label: '企业性单位或个体工商户',
        value: '2',
      },
      { label: '非企业性单位', value: '3' },
    ],
  },
  {
    name: '企业名称',
    key: 'ENTERPRISE_NAME',
    span: 1,
  },
  {
    name: '纳税人识别号',
    key: 'TAX_ID',
    span: 1,
  },
  {
    name: '纳税人电话（固话）',
    key: 'TAX_TEL',
    span: 1,
  },
  {
    name: '纳税人开户行',
    key: 'TAX_BANK_ACCT',
    span: 1,
  },
  {
    name: '纳税人账号',
    key: 'TAX_ACCT_NUM',
    span: 1,
  },
  {
    name: '纳税人账户名称',
    key: 'TAX_ACCT_NAME',
    span: 1,
  },
  {
    name: '营业执照开始时间',
    key: 'LICENSE_BEGIN_DATE',
    span: 1,
  },
  {
    name: '营业执照结束时间',
    key: 'LICENSE_END_DATE',
    span: 1,
  },
  {
    name: '是否失效',
    key: 'TAX_STATUS',
    span: 1,
    options: [
      { label: '否', value: '1' },
      { label: '否', value: '2' },
      { label: '是', value: '3' },
    ],
  },
  {
    name: '纳税人地址',
    key: 'TAX_ADDRESS',
    span: 1,
  },
  {
    name: '预打印增值税资质',
    key: 'PRE_PRINT',
    span: 1,
    options: [
      { label: '没有资质', value: '0' },
      { label: '有资质', value: '1' },
    ],
  },
  {
    name: '打印增值税资质',
    key: 'PRINT',
    options: [
      { label: '没有资质', value: '0' },
      { label: '有资质', value: '1' },
    ],
  },
];

/* 附加信息 */
export const ExtraInfoFields = [
  {
    name: '是否拍照集团',
    key: 'IS_PHOTO',
    span: 1,
    options: ENTERPRISE_IMPORT_FLAG,
  },
  {
    name: '是否100强集团',
    key: 'IS_TOP100',
    span: 1,
    options: ENTERPRISE_IMPORT_FLAG,
  },
  {
    name: '跨省集团编号',
    key: 'MP_GROUP_CODE',
    span: 1,
  },
  {
    name: '欠费告警阈值（元）',
    key: 'ARR_WARN',
    span: 1,
  },
  {
    name: '总机号码',
    key: 'SWITCH_PHONE',
    span: 1,
  },
  {
    name: '集团类型',
    key: 'GROUP_TYPE',
    span: 1,
    options: GROUP_TYPE,
  },
  {
    name: '集团详细类型',
    key: 'GROUP_TYPE_DETAIL',
    span: 1,
    options: GROUP_TYPE_DETAIL,
  },
];
