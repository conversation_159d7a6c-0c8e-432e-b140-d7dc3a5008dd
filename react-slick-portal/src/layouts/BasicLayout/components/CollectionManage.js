import React, { useState, useEffect, useRef } from 'react';
import { Modal, Input, Icon, Tooltip, Button, message } from 'antd';
import { connect } from 'dva';
import memoizeOne from 'memoize-one';
import isEqual from 'lodash/isEqual';
import find from 'lodash/find';
import findIndex from 'lodash/findIndex';
import { DndProvider, DragSource, DropTarget } from 'react-dnd';
import HTML5Backend from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import mapping from '@/services/menu';
import SlickTable from '@/components/SlickTable';
import StepWizard from '@/components/StepWizard';

let dragingIndex = -1;

function BodyRow(props) {
  const { isOver, connectDragSource, connectDropTarget, moveRow, ...restProps } = props;
  const style = { ...restProps.style, cursor: 'move' };

  let { className } = restProps;
  if (isOver) {
    if (restProps.index > dragingIndex) {
      className += ' drop-over-downward';
    }
    if (restProps.index < dragingIndex) {
      className += ' drop-over-upward';
    }
  }

  return connectDragSource(
    connectDropTarget(<tr {...restProps} className={className} style={style} />)
  );
}

const rowTarget = {
  drop(props, monitor) {
    const dragIndex = monitor.getItem().index;
    const hoverIndex = props.index;

    // Don't replace items with themselves
    if (dragIndex === hoverIndex) {
      return;
    }

    // Time to actually perform the action
    props.moveRow(dragIndex, hoverIndex);

    // Note: we're mutating the monitor item here!
    // Generally it's better to avoid mutations,
    // but it's good here for the sake of performance
    // to avoid expensive index searches.
    monitor.getItem().index = hoverIndex;
  },
};

const rowSource = {
  beginDrag(props) {
    dragingIndex = props.index;
    return {
      index: props.index,
    };
  },
};

const DragableBodyRow = DropTarget('row', rowTarget, (connect, monitor) => ({
  connectDropTarget: connect.dropTarget(),
  isOver: monitor.isOver(),
}))(
  DragSource('row', rowSource, connect => ({
    connectDragSource: connect.dragSource(),
  }))(BodyRow)
);

const components = {
  body: {
    row: DragableBodyRow,
  },
};

const {
  MENU_TYPE_LEAF,
  MENU_LEVEL_WORK_BENCH,
  MENU_LEVEL_CUSTOMER_VIEW,
  MENU_LEVEL_CUSTOMER_WORK_BENCH,
  COLLECT_MENU_SIZE_DEFAULT,
} = mapping;

// 备选菜单：过滤出有效的叶子菜单，并追加parMenuName和id
function serialize1(all, tableData1) {
  return all
    .filter(menu => {
      const { menuType, menuLevel } = menu;
      if (
        `${menuType}` === `${MENU_TYPE_LEAF}` &&
        menuLevel !== MENU_LEVEL_WORK_BENCH &&
        menuLevel !== MENU_LEVEL_CUSTOMER_VIEW &&
        menuLevel !== MENU_LEVEL_CUSTOMER_WORK_BENCH
      ) {
        return true;
      }
      return false;
    })
    .filter(menu => find(tableData1, { contentId: menu.menuId }) === undefined)
    .map(item => {
      const parentNode = find(all, { menuId: item.parMenuId });
      if (parentNode !== undefined) {
        return { ...item, contentId: item.menuId, parMenuName: parentNode.menuName };
      }
      return { ...item, contentId: item.menuId, parMenuName: '' };
    });
}

// 收藏菜单，追加parMenuName
function serialize2(all, collection) {
  return collection
    .map(item => {
      const parentNode = find(all, { menuId: item.parMenuId });
      if (parentNode !== undefined) {
        return { ...item, parMenuName: parentNode.menuName };
      }
      return { ...item, parMenuName: '' };
    })
    .sort((a, b) => parseInt(a.collectSort, 10) - parseInt(b.collectSort, 10));
}

const _serialize1 = memoizeOne(serialize1, isEqual);
const _serialize2 = memoizeOne(serialize2, isEqual);

function CollectionManage({ loading, all, collection, visible, close, dispatch }) {
  const [currentIndex, setCurrentIndex] = useState(1);
  const [tableData1, setTableData1] = useState([]);
  const [options, setOptions] = useState([]);
  const [selectedRows1, setSelectedRows1] = useState([]);
  const [selectedRows2, setSelectedRows2] = useState([]);
  const swEl = useRef(null);

  useEffect(() => {
    setOptions(_serialize1(all, collection));
    setTableData1(_serialize2(all, collection));
  }, [all, collection, setOptions, setTableData1]);

  useEffect(() => {
    setOptions(_serialize1(all, tableData1));
  }, [tableData1, all]);

  // 窗口关闭时重置数据
  // IMPROVE: 虽然Modal设置了destroyOnClose，但是仍有缓存（可能是由SetpWizard引起）
  useEffect(() => {
    setOptions(_serialize1(all, collection));
    setTableData1(_serialize2(all, collection));
    setCurrentIndex(1);
  }, [visible, setOptions, all, collection, setTableData1]);

  function filterOptions(value) {
    if (value === '') {
      setOptions(_serialize1(all, tableData1));
    } else {
      const result = options.filter(item =>
        JSON.stringify(item.menuName)
          .toLowerCase()
          .includes(value.toLowerCase())
      );
      setOptions(result);
    }
  }

  function handleSubmit(e) {
    e.preventDefault();
    dispatch({
      type: 'menu/updateCollectionMenu',
      payload: {
        close,
        collection: tableData1.map((item, index) => {
          return { ...item, collectSort: index + 1 };
        }),
      },
    });
  }

  function renderTitle(currentIndex) {
    if (currentIndex === 1) {
      return '菜单收藏夹';
    }
    if (currentIndex === 2) {
      return (
        <span
          className="pointer"
          onClick={() => {
            swEl.current.goToStep(1);
          }}
        >
          <Icon className="margin-right-sm" type="left" />
          返回收藏夹
        </span>
      );
    }
    return null;
  }

  function renderBtn(currentIndex) {
    const footer = [
      <Button
        key="cancel"
        onClick={() => {
          close();
        }}
      >
        取消
      </Button>,
    ];
    if (currentIndex === 1) {
      return [
        ...footer,
        <Button key="ok" onClick={handleSubmit} type="primary" loading={loading}>
          提交
        </Button>,
        <Button
          key="add"
          className="pull-left margin-0 padding-0"
          onClick={() => {
            swEl.current.goToStep(2);
          }}
          icon="plus"
          type="link"
        >
          添加菜单
        </Button>,
      ];
    }
    if (currentIndex === 2) {
      return [
        ...footer,
        <Button
          key="back"
          onClick={() => {
            if (selectedRows2.length === 0) {
              message.warn(`没有选中项`);
              return;
            }
            if ([...tableData1, ...selectedRows2].length > COLLECT_MENU_SIZE_DEFAULT) {
              message.warn(`最多允许收藏${COLLECT_MENU_SIZE_DEFAULT}个菜单`);
              return;
            }
            swEl.current.goToStep(1);
            setTableData1(prev => [...prev, ...selectedRows2]);
            setSelectedRows2([]);
          }}
          type="primary"
        >
          添加选中项
        </Button>,
      ];
    }
    return null;
  }

  return (
    <Modal
      title={renderTitle(currentIndex)}
      width={800}
      visible={visible}
      onCancel={() => {
        close();
      }}
      bodyStyle={{ height: '470px' }}
      confirmLoading={loading}
      destroyOnClose
      centered
      footer={renderBtn(currentIndex)}
    >
      <StepWizard
        onStepChange={({ activeStep }) => setCurrentIndex(activeStep)}
        instance={sw => {
          swEl.current = sw;
        }}
      >
        <DndProvider backend={HTML5Backend}>
          <SlickTable
            pick="checkbox"
            className="move"
            rowKey={record => record.contentId}
            selectedRows={selectedRows1}
            columns={[
              {
                dataIndex: 'moduleIndex',
                title: '排序',
                align: 'center',
                width: 50,

                render: (text, record, index) => {
                  return index + 1;
                },
              },
              { dataIndex: 'menuName', title: '菜单名称', ellipsis: true, width: 150 },
              { dataIndex: 'parMenuName', title: '上级菜单', ellipsis: true, width: 150 },
              { dataIndex: 'menuDesc', title: '菜单描述', ellipsis: true },
            ]}
            extra={selectedRows =>
              selectedRows.length > 0 ? (
                <Tooltip placement="right" title="右下角点击[提交]才会生效">
                  <Button
                    type="primary"
                    ghost
                    onClick={() => {
                      setTableData1(
                        tableData1.filter(
                          item => findIndex(selectedRows1, { contentId: item.contentId }) === -1
                        )
                      );
                      setSelectedRows1([]);
                    }}
                  >
                    删除
                  </Button>
                </Tooltip>
              ) : null
            }
            dataSource={tableData1}
            onRow={(record, index) => ({
              index,
              className: 'move',
              moveRow: (dragIndex, hoverIndex) => {
                const dragRow = tableData1[dragIndex];
                setTableData1(
                  update(tableData1, {
                    $splice: [
                      [dragIndex, 1],
                      [hoverIndex, 0, dragRow],
                    ],
                  })
                );
              },
            })}
            onSelectRow={selectedRows => {
              setSelectedRows1(selectedRows);
            }}
            components={components}
          />
        </DndProvider>
        <div>
          <Input.Search
            allowClear
            placeholder="菜单名称"
            className="margin-bottom"
            style={{ width: '240px' }}
            onSearch={val => {
              filterOptions(val);
            }}
          />
          <SlickTable
            pick="checkbox"
            rowKey={record => record.menuId}
            selectedRows={selectedRows2}
            columns={[
              { dataIndex: 'menuName', title: '菜单名称', ellipsis: true, width: 150 },
              { dataIndex: 'parMenuName', title: '上级菜单', ellipsis: true, width: 150 },
              { dataIndex: 'menuDesc', title: '菜单描述', ellipsis: true },
            ]}
            dataSource={options}
            onSelectRow={selectedRows => {
              setSelectedRows2(selectedRows);
            }}
          />
        </div>
      </StepWizard>
    </Modal>
  );
}

export default connect(({ menu, loading }) => ({
  all: menu.all,
  collection: menu.collection,
  loading: loading.effects['menu/updateCollectionMenu'],
}))(CollectionManage);
