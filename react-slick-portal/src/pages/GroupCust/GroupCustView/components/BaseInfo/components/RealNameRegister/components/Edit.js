import React, { useState, useEffect } from 'react';
import { Button, Card, Col, Form, Input, message, Row, Select, Spin, Modal } from 'antd';
import { connect } from 'dva';
import styles from '../index.less';
import { validateEmail, validateHandleIdenNbr, validateIdenAddressByIdenType, validateIdenNbr } from '@/utils/validator';
import { IDEN_CARD_TYPE } from '@/utils/consts';
import GroupFileUpload from '@/components/GroupFileUpload';
import OnlineCompare from '@/pages/GroupCust/components/OnlineCompare';
import InfoCheck from '@/pages/GroupCust/components/InfoCheck';
import { editGroupRealNameInfo } from '@/pages/GroupCust/GroupCustView/components/BaseInfo/components/RealNameRegister/service';
import { qryGroupEnterpriseInfoNoMask } from '@/pages/GroupCust/GroupCustView/service';
import request from '@/utils/request';
import { getDataDictByCode } from '@/services/common';
import { queryCommonRefl } from '@/pages/GroupCust/GroupCustManage/services';

const FormItem = Form.Item;
// 响应式表单布局配置
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 24 },
    md: { span: 8 },
    lg: { span: 8 },
    xl: { span: 8 },
    xxl: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
    md: { span: 16 },
    lg: { span: 16 },
    xl: { span: 16 },
    xxl: { span: 18 },
  },
};

// 文本域响应式布局配置
const textAreaFormItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 24 },
    md: { span: 4 },
    lg: { span: 4 },
    xl: { span: 4 },
    xxl: { span: 3 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
    md: { span: 20 },
    lg: { span: 20 },
    xl: { span: 20 },
    xxl: { span: 21 },
  },
};
const Edit = props => {
  const {
    form,
    size: { height },
    goToStep,
    GROUP_ID,
    user: {
      userInfo: { externalUserInfos },
    },
  } = props;

  const [currentRegionId, setCurrentRegionId] = useState('');
  useEffect(() => {
    const currentUser = externalUserInfos.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000');
    setCurrentRegionId(currentUser?.outRegionId || '');
  }, [externalUserInfos]);

  /* useEffect(() => {
    const OPERATOR_ID = externalUserInfos.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000')?.externalUserId;
    if (!OPERATOR_ID) {
      message.warning('未找到有效的操作员信息');
      return;
    }

    getOrgInfoByOpId({ OPERATOR_ID })
      .then(resp => {
        const { resultCode, resultObject } = resp;
        if (resultCode === 'TRUE' && resultObject) {
          setOrgInfo(resultObject);
        } else {
          message.warning('获取客户经理组织信息失败');
        }
      })
      .catch(error => {
        console.error('获取组织信息失败:', error);
        message.error('获取客户经理组织信息失败');
      });
  }, [externalUserInfos]); */

  // 经办人在线比对 - 使用简化的状态管理
  const [onlineCompare, setOnlineCompare] = useState({
    custName: '',
    custCertNo: '',
    custNation: '',
    busiType: '1',
    onlineCompareResult: null,
  });
  // 责任人信息核验 - 使用简化的状态管理
  const [infoCheck, setInfoCheck] = useState({
    name: '',
    idenNr: '',
    infoCheckResult: null,
  });
  const { getFieldDecorator, getFieldValue } = form;
  const [disInfos, setDisInfos] = useState([]);

  // 数据回显
  const [agentInfo, setAgentInfo] = useState({});
  // 数据回显
  const [ownerInfo, setOwnerInfo] = useState({});
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    queryCommonRefl('TAG_AREA_CODE')
      .then(res => {
        const { resultCode, resultObject, resultMsg } = res;
        if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
          setDisInfos(resultObject?.rspParam?.busiInfo?.outData || []);
        } else {
          message.warning(resultMsg || '获取地区信息失败');
        }
      })
      .catch(error => {
        // eslint-disable-next-line no-console
        console.error('获取地区信息失败:', error);
        message.error('获取地区信息失败');
      });
  }, []);

  const updateOnlineCompare = newState => {
    setOnlineCompare(prev => ({ ...prev, ...newState }));
  };

  const updateInfoCheck = newState => {
    setInfoCheck(prev => ({ ...prev, ...newState }));
  };
  // 在线比对校验
  const beforeOnlineCompare = () => {
    const baseString = form.getFieldValue('AGGENT_BASE_STRING');
    const imageString = form.getFieldValue('AGGENT_IMAGE_STRING');
    const custName = form.getFieldValue('AGENT_NAME');
    const custCertNo = form.getFieldValue('AGENT_ID_NUM');
    const custNation = form.getFieldValue('CUST_NATION');

    // 直接返回在线比对所需的数据对象
    return {
      custName,
      custCertNo,
      custNation,
      busiType: '1',
      baseString,
      imageString,
    };
  };

  /* 初始化责任人信息 */
  const initOwnerInfo = () => {
    setLoading(true);
    qryGroupEnterpriseInfoNoMask(GROUP_ID)
      .then(res => {
        const { resultCode, resultObject, resultMsg } = res;
        if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData?.[0]) {
          const info = resultObject?.rspParam?.busiInfo?.outData?.[0] || {};
          setOwnerInfo(info);
          form.setFieldsValue({
            OWNER_ID_NUM: info.JURISTIC_IDEN_NR,
            OWNER_NAME: info.JURISTIC_NAME,
          });
        } else {
          message.warning(resultMsg || '未查询到当前登陆集团法人信息');
        }
      })
      .catch(error => {
        // eslint-disable-next-line no-console
        console.error('获取集团法人信息失败:', error);
        message.error('获取集团法人信息失败');
      })
      .finally(() => {
        setLoading(false);
      });
  };
  // 在线比对成功的回调
  const handleOnlineCompareSuccess = result => {
    updateOnlineCompare({ onlineCompareResult: result });
    message.success('在线比对通过');
  };
  // 责任人 信息核验
  const beforeInfoCheck = () => {
    const name = form.getFieldValue('OWNER_NAME');
    const idenNr = form.getFieldValue('OWNER_ID_NUM');

    // 验证必要字段
    if (!name || !idenNr) {
      message.warning('请先填写姓名和证件号码');
      return false;
    }
    return {
      name,
      idenNr,
    };
  };
  // 信息核验成功的回调
  const handleInfoCheckSuccess = result => {
    updateInfoCheck({ infoCheckResult: result });
    message.success('信息核验通过');
  };

  /**
   * 从无纸化获取身份证信息
   * @param scanInfo 身份证信息
   */
  const getIdCardImg = (scanInfo, timeStamp) => {
    const agentData = {
      AGENT_NAME: scanInfo.INDIVIDUAL_NAME || scanInfo.name,
      AGENT_MASK_NAME: scanInfo.INDIVIDUAL_NAME || scanInfo.name,
      AGENT_ADDR: scanInfo.IDEN_ADDRESS,
      AGENT_MASK_ADDR: scanInfo.IDEN_ADDRESS || scanInfo.address,
      AGENT_ID_NUM: scanInfo.IDEN_NR || scanInfo.idenNr,
      AGENT_MASK_ID_NUM: scanInfo.IDEN_NR || scanInfo.idenNr,
      AGGENT_BASE_STRING: scanInfo.BASE_STRING || scanInfo.baseStr,
      AGGENT_IMAGE_STRING: scanInfo.IMAGE_STRING || scanInfo.baseStr,
      CUST_NATION: scanInfo.CUST_NATION,
      COMPARE_IMAGE_TIMESTAMP: `A${timeStamp}`,
      IMAGE_TIMESTAMP: timeStamp,
    };
    form.setFieldsValue(agentData);
    setAgentInfo(prev => ({ ...prev, ...agentData }));

    // 如果读取的身份证信息和责任人信息一致，则设置责任人信息
    const juristicName = ownerInfo?.JURISTIC_NAME;
    const juristicIdenNr = ownerInfo?.JURISTIC_IDEN_NR;
    if (scanInfo?.INDIVIDUAL_NAME === juristicName && scanInfo?.IDEN_NR === juristicIdenNr) {
      const ownerData = {
        OWNER_NAME: scanInfo.INDIVIDUAL_NAME,
        OWNER_ID_NUM: scanInfo.IDEN_NR,
        OWNER_ADDR: scanInfo.IDEN_ADDRESS,
      };
      form.setFieldsValue(ownerData);
      message.success('检测到经办人与责任人为同一人，已自动填充责任人信息');
    }
  };
  // 同步经办人信息
  const handleCopyInfo = async () => {
    const agentInfoTemp = form.getFieldsValue([
      'AGENT_NAME',
      'AGENT_ID_NUM',
      'AGENT_MASK_ADDR',
      'AGENT_PHONE',
      'AGENT_EMAIL',
      'AGENT_DISTR',
      'AGENT_REMARK',
      'AGENT_CITY',
    ]);

    // 验证必要字段
    if (!agentInfoTemp.AGENT_NAME || !agentInfoTemp.AGENT_ID_NUM) {
      message.warning('请先完善经办人的姓名和证件号码信息');
      return;
    }

    const ownerData = {
      OWNER_NAME: agentInfoTemp.AGENT_NAME,
      OWNER_ID_NUM: agentInfoTemp.AGENT_ID_NUM,
      OWNER_ADDR: agentInfoTemp.AGENT_MASK_ADDR,
      OWNER_PHONE: agentInfoTemp.AGENT_PHONE,
      OWNER_EMAIL: agentInfoTemp.AGENT_EMAIL,
      OWNER_DISTR: agentInfoTemp.AGENT_DISTR,
      OWNER_REMARK: agentInfoTemp.AGENT_REMARK,
      OWNER_CITY: agentInfoTemp.AGENT_CITY,
    };

    form.setFieldsValue(ownerData);

    // 更新信息核验状态
    updateInfoCheck({
      name: agentInfoTemp.AGENT_NAME,
      idenNr: agentInfoTemp.AGENT_ID_NUM,
      infoCheckResult: null,
    });

    message.success('经办人信息已复制到责任人，请重新进行信息核验');
  };
  const checkIsNeedAuthLetter = () => {
    const juristicName = getFieldValue('OWNER_NAME');
    const juristicIdenNr = getFieldValue('OWNER_ID_NUM');
    const individualName = getFieldValue('AGENT_NAME');
    const idenNr = getFieldValue('AGENT_ID_NUM');
    if (juristicName !== individualName || juristicIdenNr !== idenNr) {
      const val = getFieldValue('AGENT_auth_letter');
      if (!val || val?.length === 0) {
        return false;
      }
    }
    return true;
  };
  const handleSubmit = () => {
    // 检查在线比对是否已完成
    if (onlineCompare.onlineCompareResult === null) {
      message.warning('请操作经办人人像比对');
      return;
    }
    // 检查信息核验是否完成
    if (infoCheck.infoCheckResult === null) {
      message.warning('请操作责任人信息核验');
      return;
    }
    form.validateFields(async (err, value) => {
      if (!err) {
        if (!checkIsNeedAuthLetter()) {
          message.error('经办人与责任人信息不一致，录入经办人信息需要上传授权书！');
          return;
        }

        // 构建提交参数
        const params = {
          groupId: ownerInfo?.GROUP_ID || GROUP_ID,
          groupName: ownerInfo?.GROUP_NAME || '测试集团',
          realNameInfo: {
            AGENT_INFO: {
              DATA_TYPE_DESC: '经办人',
              DATA_TYPE: '1',
              INDIVIDUAL_NAME: value?.AGENT_NAME,
              IDEN_NR: value?.AGENT_ID_NUM,
              REGION_ID: value?.AGENT_CITY,
              IDEN_ADDRESS: value?.AGENT_ADDR,
              BILL_ID: value?.AGENT_PHONE,
              EMAIL: value?.AGENT_EMAIL,
              INPUT_DESC: value?.AGENT_DISTR,
              // eslint-disable-next-line camelcase
              AUTHORIZATION_LETTER: value?.AGENT_auth_letter?.[0]?.fileId,
              REMARKS: value?.AGENT_REMARK,
              CERT_TYPE: value?.ID_TYPE,
              CHANNEL_CODE: '1',
              CUST_NATION: value?.CUST_NATION,
              ONLINE_COMPARE_RESULT: onlineCompare?.onlineCompareResult || '0',
              BASE_STRING: value?.AGGENT_BASE_STRING || '',
              IMAGE_STRING: value?.AGGENT_IMAGE_STRING || '',
              TIME_STAMP: value?.COMPARE_IMAGE_TIMESTAMP || '',
            },
            OWNER_INFO: {
              DATA_TYPE_DESC: '责任人',
              DATA_TYPE: '2',
              INDIVIDUAL_NAME: value?.OWNER_NAME,
              IDEN_ADDRESS: value?.OWNER_ADDR,
              REGION_ID: value?.OWNER_CITY,
              CERT_TYPE: value?.OWNER_ID_TYPE,
              IDEN_NR: value?.OWNER_ID_NUM,
              BILL_ID: value?.OWNER_PHONE,
              EMAIL: value?.OWNER_EMAIL,
              INPUT_DESC: value?.OWNER_DISTR,
              REMARKS: value?.OWNER_REMARK,
              CHANNEL_CODE: '1',
              TIME_STAMP: value?.COMPARE_IMAGE_TIMESTAMP || '',
              CHECK_RESULT: '1',
              ONLINE_COMPARE_RESULT: infoCheck?.infoCheckResult || '0',
            },
          },
        };
        setLoading(true);
        editGroupRealNameInfo(params)
          .then(res => {
            const { resultCode, resultMsg } = res;
            if (resultCode === 'TRUE') {
              message.success('保存成功');
              // 返回列表页并传递刷新标志
              goToStep(1, {
                refreshFlag: new Date().getTime(), // 使用时间戳确保每次都是新值
              });
            } else {
              message.error(resultMsg || '保存失败');
            }
          })
          .catch(error => {
            // eslint-disable-next-line no-console
            console.error('提交失败:', error);
            message.error('提交失败，请检查网络连接');
          })
          .finally(() => {
            setLoading(false);
          });
      }
    });
  };

  /**
   * 返回
   */
  const goBack = () => {
    props.goToStep(1);
  };

  useEffect(() => {
    if (GROUP_ID) {
      initOwnerInfo();
    }
  }, [GROUP_ID]);

  // 身份证扫描相关状态
  const [scanUrl, setScanUrl] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [iframeUrl, setIframeUrl] = useState('');
  const [scanTimeStamp, setScanTimeStamp] = useState('');
  const [scanOpId, setScanOpId] = useState('');

  // 获取扫描地址
  useEffect(() => {
    const getScanUrl = async () => {
      try {
        const res = await getDataDictByCode({
          groupCode: 'BUSINESS_SYSTEM_URL',
          paramCode: 'SCAN_URL',
        });
        if (res) {
          setScanUrl(res.paramValue);
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('获取扫描地址失败:', error);
        message.error('获取扫描地址失败');
      }
    };
    getScanUrl();
  }, []);

  // 点击身份证扫描按钮
  const handleScan = () => {
    const idType = getFieldValue('ID_TYPE');
    const timeStampTemp = new Date().getTime();
    setScanTimeStamp(timeStampTemp);

    // 从全局用户信息中获取操作员ID
    const opIdStr = props.user?.userInfo?.externalUserInfos?.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000')?.externalUserId;

    if (!opIdStr) {
      message.error('未获取到有效的操作员信息');
      return;
    }

    setScanOpId(opIdStr);

    const params = {
      work_no: opIdStr,
      org_info: 710001,
      certType: idType,
      time_stamp: timeStampTemp,
      img_flag: 'img_flag',
      localization: 'Y',
    };

    if (!scanUrl) {
      message.error('证件扫描地址获取异常，请关闭重试');
      return;
    }

    setIframeUrl(`${scanUrl}&${new URLSearchParams(params)}`);
    setModalVisible(true);
  };

  // 获取身份扫描结果
  const handleScanSuccess = () => {
    request('portal/NoPaperInfoController/getImages.do', {
      method: 'POST',
      data: {
        OP_ID: scanOpId,
        TIME_STAMP: scanTimeStamp,
      },
    })
      .then(res => {
        const { resultCode, resultObject, resultMsg } = res;
        if (resultCode === 'TRUE') {
          // 更新表单值
          form.setFieldsValue({
            AGENT_MASK_ID_NUM: resultObject?.IDEN_NR,
          });

          // 调用原有的回调函数
          getIdCardImg(
            {
              INDIVIDUAL_NAME: resultObject?.INDIVIDUAL_NAME,
              IDEN_NR: resultObject?.IDEN_NR,
              IDEN_ADDRESS: resultObject?.IDEN_ADDRESS,
              BASE_STRING: resultObject?.BASE_STRING,
              IMAGE_STRING: resultObject?.IMAGE_STRING,
              CUST_NATION: resultObject?.CUST_NATION,
            },
            scanTimeStamp
          );

          setModalVisible(false);
        } else {
          message.error(resultMsg || '获取扫描结果失败');
        }
      })
      .catch(error => {
        // eslint-disable-next-line no-console
        console.error('获取扫描结果失败:', error);
        message.error('获取扫描结果失败');
      });
  };

  return (
    <Spin spinning={loading}>
      <Card
        title="集团实名登记信息录入"
        className="cute"
        style={{ minHeight: height }}
        extra={(
          <div style={{ display: 'flex', gap: '12px' }}>
            <Button type="primary" icon="check" onClick={handleSubmit}>
              提交保存
            </Button>
            <Button icon="arrow-left" onClick={goBack}>返回</Button>
          </div>
        )}
      >
        <Form className="flow fix-label">
          <div className={styles.content}>
            <div className={styles.twoColumnLayout}>
              {/* 左侧栏 - 经办人信息 */}
              <div className={styles.columnLeft}>
                <Card
                  className={`cute2 ${styles.formCard}`}
                  title={<span style={{ fontWeight: 'bold', color: '#0085D0' }}>经办人信息</span>}
                  extra={(
                    <>
                      <Button type="primary" icon="scan" onClick={handleScan}>
                        身份证扫描
                      </Button>
                      <OnlineCompare onBeforeCompare={beforeOnlineCompare} onSuccess={handleOnlineCompareSuccess} />
                    </>
                  )}
                >
                  <div className={styles.warp}>
                    <Row gutter={[16, 16]}>
                      {/* 隐藏参数 */}
                      <Col span={8} hidden>
                        <FormItem label="证件芯片照">{getFieldDecorator('AGGENT_BASE_STRING')(<Input disabled />)}</FormItem>
                        <FormItem label="现场照">{getFieldDecorator('AGGENT_IMAGE_STRING')(<Input disabled />)}</FormItem>
                        <FormItem label="国籍">{getFieldDecorator('CUST_NATION')(<Input disabled />)}</FormItem>
                        <FormItem label="读卡拍照时间">{getFieldDecorator('COMPARE_IMAGE_TIMESTAMP')(<Input disabled />)}</FormItem>
                        <FormItem label="读卡拍照时间">{getFieldDecorator('TIMESTAMP')(<Input disabled />)}</FormItem>
                      </Col>
                      {/* 证件类型 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        <FormItem label="证件类型" {...formItemLayout}>
                          {getFieldDecorator('ID_TYPE', {
                            initialValue: '100001',
                          })(
                            <Select disabled placeholder="请选择">
                              {Object.keys(IDEN_CARD_TYPE).map(key => (
                                <Select.Option key={key} value={key}>
                                  {IDEN_CARD_TYPE[key]}
                                </Select.Option>
                              ))}
                            </Select>
                          )}
                        </FormItem>
                      </Col>
                      {/* 姓名 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        {/* todo: disabled */}
                        <FormItem label="姓名" {...formItemLayout}>
                          {getFieldDecorator('AGENT_MASK_NAME', {
                            initialValue: agentInfo?.AGENT_MASK_NAME,
                            rules: [{ required: true, message: '经办人姓名不能为空！' }],
                          })(<Input disabled />)}
                        </FormItem>
                      </Col>
                      <Col span={8} hidden>
                        {/* todo: hidden */}
                        <FormItem label="姓名" {...formItemLayout}>
                          {getFieldDecorator('AGENT_NAME', {
                            initialValue: agentInfo?.NAME,
                          })(<Input />)}
                        </FormItem>
                      </Col>
                      {/* 地市 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        <FormItem label="地市" {...formItemLayout}>
                          {getFieldDecorator('AGENT_CITY', {
                            initialValue: agentInfo?.CITY_CODE || currentRegionId,
                            rules: [{ required: true, message: '经办人地市不能为空！' }],
                          })(
                            <Select
                              allowClear
                              showSearch
                              filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                              placeholder="请选择"
                            >
                              {disInfos.map(({ STATIC_KEY, STATIC_VALUE }) => (
                                <Select.Option value={STATIC_KEY} key={STATIC_KEY}>
                                  {STATIC_VALUE}
                                </Select.Option>
                              ))}
                            </Select>
                          )}
                        </FormItem>
                      </Col>
                      {/* 证件号码 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        <FormItem label="证件号码" {...formItemLayout}>
                          {getFieldDecorator('AGENT_MASK_ID_NUM', {
                            initialValue: agentInfo?.MASK_IDEN_NR,
                            rules: [
                              {
                                required: true,
                                message: '证件号码不能为空！',
                              },
                              {
                                validator: (rule, value, callback) => {
                                  const idType = getFieldValue('ID_TYPE');
                                  const idNum = getFieldValue('AGENT_ID_NUM');
                                  if (idNum) {
                                    validateIdenNbr(idType)(rule, idNum, callback);
                                  } else {
                                    callback();
                                  }
                                },
                              },
                            ],
                          })(<Input disabled />)}
                        </FormItem>
                      </Col>
                      <Col span={8} hidden>
                        {/* todo: hidden */}
                        <FormItem label="证件号码" {...formItemLayout}>
                          {getFieldDecorator('AGENT_ID_NUM', {
                            initialValue: agentInfo?.IDEN_NR,
                          })(<Input />)}
                        </FormItem>
                      </Col>
                      {/* 证件地址 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        {/* todo: disabled */}
                        <FormItem label="证件地址" {...formItemLayout}>
                          {getFieldDecorator('AGENT_MASK_ADDR', {
                            initialValue: agentInfo?.MASK_IDEN_ADDRESS,
                            rules: [{ required: true, message: '证件地址不能为空！' }],
                          })(<Input disabled />)}
                        </FormItem>
                      </Col>
                      {/* 证件地址 */}
                      <Col span={8} hidden>
                        {/* todo: hidden */}
                        <FormItem label="证件地址" {...formItemLayout}>
                          {getFieldDecorator('AGENT_ADDR', {
                            initialValue: agentInfo?.IDEN_ADDRESS,
                          })(<Input />)}
                        </FormItem>
                      </Col>
                      {/* 联系电话 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        <FormItem label="联系电话" {...formItemLayout}>
                          {getFieldDecorator('AGENT_PHONE', {
                            initialValue: agentInfo?.CONTACTS_PHONE,
                            rules: [{ required: true, message: '联系电话不能为空！' }],
                          })(<Input placeholder="请输入联系电话" allowClear />)}
                        </FormItem>
                      </Col>
                      {/* 邮箱地址 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        <FormItem label="邮箱地址" {...formItemLayout}>
                          {getFieldDecorator('AGENT_EMAIL', {
                            initialValue: agentInfo?.EMAIL,
                            rules: [{ validator: validateEmail }],
                          })(<Input placeholder="请输入邮箱地址" allowClear />)}
                        </FormItem>
                      </Col>
                      {/* 授权书 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        <FormItem label="授权书" {...formItemLayout}>
                          {getFieldDecorator('AGENT_auth_letter', {})(<GroupFileUpload accept=".doc,.docx,.jpg,.png" fileType="1" />)}
                        </FormItem>
                      </Col>
                    </Row>
                    <Row gutter={[16, 16]}>
                      {/* 经办描述 */}
                      <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
                        <FormItem label="经办描述" className={styles.textAreaFormItem} {...textAreaFormItemLayout}>
                          {getFieldDecorator('AGENT_DISTR', {
                            initialValue: agentInfo?.DISTR,
                          })(<Input.TextArea maxLength={500} allowClear placeholder="请输入经办描述" rows={4} />)}
                        </FormItem>
                      </Col>
                      {/* 备注 */}
                      <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
                        <FormItem label="备注" className={styles.textAreaFormItem} {...textAreaFormItemLayout}>
                          {getFieldDecorator('AGENT_REMARK', {
                            initialValue: agentInfo?.REMARK,
                          })(<Input.TextArea maxLength={500} allowClear placeholder="请输入备注信息" rows={4} />)}
                        </FormItem>
                      </Col>
                    </Row>
                  </div>
                </Card>
              </div>

              {/* 中间复制按钮 */}
              <div className={styles.copyButton} onClick={handleCopyInfo} title="复制经办人信息到责任人">
                <Button type="link" icon="arrow-right" style={{ color: 'white', padding: 0 }} />
              </div>

              {/* 右侧栏 - 责任人信息 */}
              <div className={styles.columnRight}>
                <Card
                  className={`cute2 ${styles.formCard}`}
                  title={<span style={{ fontWeight: 'bold', color: '#0085D0' }}>责任人信息</span>}
                  extra={<InfoCheck onBeforeCompare={beforeInfoCheck} onSuccess={handleInfoCheckSuccess} checkData={infoCheck} />}
                >
                  <div className={styles.warp}>
                    <Row gutter={[16, 16]}>
                      {/* 证件类型 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        <FormItem label="证件类型" {...formItemLayout}>
                          {getFieldDecorator('OWNER_ID_TYPE', {
                            initialValue: '100001',
                          })(
                            <Select disabled placeholder="请选择">
                              {Object.keys(IDEN_CARD_TYPE).map(key => (
                                <Select.Option key={key} value={key}>
                                  {IDEN_CARD_TYPE[key]}
                                </Select.Option>
                              ))}
                            </Select>
                          )}
                        </FormItem>
                      </Col>
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        <FormItem label="姓名" {...formItemLayout}>
                          {getFieldDecorator('OWNER_NAME', {
                            rules: [{ required: true, message: '姓名不能为空！' }],
                          })(<Input disabled />)}
                        </FormItem>
                      </Col>
                      {/* 地市 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        <FormItem label="地市" {...formItemLayout}>
                          {getFieldDecorator('OWNER_CITY', {
                            initialValue: agentInfo?.CITY_CODE || currentRegionId,
                            rules: [{ required: true, message: '地市不能为空！' }],
                          })(
                            <Select
                              allowClear
                              showSearch
                              filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                              placeholder="请选择"
                            >
                              {disInfos.map(({ STATIC_KEY, STATIC_VALUE }) => (
                                <Select.Option value={STATIC_KEY} key={STATIC_KEY}>
                                  {STATIC_VALUE}
                                </Select.Option>
                              ))}
                            </Select>
                          )}
                        </FormItem>
                      </Col>
                      {/* 证件号码 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        {/* todo: disabled */}
                        <FormItem label="证件号码" {...formItemLayout}>
                          {getFieldDecorator('OWNER_ID_NUM', {
                            rules: [
                              { required: true, message: '证件号码不能为空！' },
                              {
                                validator: (rule, value, callback) => {
                                  if (value) {
                                    validateIdenNbr(getFieldValue('OWNER_ID_TYPE'))(rule, value, callback);
                                  } else {
                                    callback();
                                  }
                                },
                              },
                              {
                                validator: (rule, value, callback) => {
                                  if (value) {
                                    validateHandleIdenNbr(getFieldValue('OWNER_ID_TYPE'), value)(rule, value, callback);
                                  } else {
                                    callback();
                                  }
                                },
                              },
                            ],
                          })(<Input disabled />)}
                        </FormItem>
                      </Col>
                      {/* 证件地址 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        {/* todo: disabled */}
                        <FormItem label="证件地址" {...formItemLayout}>
                          {getFieldDecorator('OWNER_ADDR', {
                            rules: [
                              {
                                required: true,
                                message: '证件地址不能为空！',
                              },
                              {
                                validator: (rule, value, callback) => {
                                  if (value) {
                                    validateIdenAddressByIdenType(getFieldValue('OWNER_ID_TYPE'))(rule, value, callback);
                                  } else {
                                    callback();
                                  }
                                },
                              },
                            ],
                          })(<Input disabled />)}
                        </FormItem>
                      </Col>
                      {/* 联系电话 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        <FormItem label="联系电话" {...formItemLayout}>
                          {getFieldDecorator('OWNER_PHONE', {
                            rules: [
                              {
                                required: true,
                                message: '联系电话不能为空！',
                              },
                            ],
                          })(<Input placeholder="请输入联系电话" allowClear />)}
                        </FormItem>
                      </Col>
                      {/* 邮箱地址 */}
                      <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
                        <FormItem label="邮箱地址" {...formItemLayout}>
                          {getFieldDecorator('OWNER_EMAIL', {
                            rules: [{ validator: validateEmail }],
                          })(<Input placeholder="请输入邮箱地址" allowClear />)}
                        </FormItem>
                      </Col>
                    </Row>
                    <Row gutter={[16, 16]}>
                      {/* 经办描述 */}
                      <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
                        <FormItem label="经办描述" className={styles.textAreaFormItem} {...textAreaFormItemLayout}>
                          {getFieldDecorator('OWNER_DISTR', {})(<Input.TextArea maxLength={500} placeholder="请输入经办描述" rows={4} />)}
                        </FormItem>
                      </Col>
                      {/* 备注 */}
                      <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24}>
                        <FormItem label="备注" className={styles.textAreaFormItem} {...textAreaFormItemLayout}>
                          {getFieldDecorator('OWNER_REMARK', {})(<Input.TextArea maxLength={500} placeholder="请输入备注信息" rows={4} />)}
                        </FormItem>
                      </Col>
                    </Row>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </Form>

        {/* 添加扫描弹窗 */}
        <Modal
          title="身份证扫描"
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
          onOk={handleScanSuccess}
          width="80%"
          style={{ height: 'auto' }}
        >
          <iframe src={iframeUrl} width="100%" height="100%" style={{ height: '100vh' }} title="身份证扫描" />
        </Modal>
      </Card>
    </Spin>
  );
};

export default connect(({ setting, login }) => ({
  size: setting.size,
  user: login.user,
}))(Form.create()(Edit));
