import React from 'react';
import { connect } from 'dva';
import { Avatar } from 'antd';
import ChatMessage from './component/ChatMessage';
import MessageInput from './component/MessageInput';
import styles from '../index.less';

const ChatWindow = ({ size: { height }, message }) => {
  const { curUser = {} } = message;

  const handleName = () => {
    const { objName } = curUser;
    if (objName?.length > 2) {
      return objName.substring(objName.length - 2);
    }
      return objName;
  };

  return (
    <div className={styles.chatWindow} style={{ height: height * 0.9 }}>
      <div className={styles.header}>
        {curUser.objName && <Avatar size={36} style={{ color: '#FFF', backgroundColor: '#2a97f7', fontSize: '12px' }}>{handleName()}</Avatar>}
        <div className={styles.userName}>{curUser.objName}</div>
      </div>
      <ChatMessage />
      <MessageInput />
    </div>
  );
};
export default connect(({ setting, message }) => ({
  size: setting.size,
  message,
}))(ChatWindow);
