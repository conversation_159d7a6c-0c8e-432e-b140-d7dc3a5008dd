import React, { useState, useEffect, useImperativeHandle } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Form,
  Input,
  Select,
  message,
  Spin,
  DatePicker,
} from 'antd';
import request from '@/utils/request';

const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Option } = Select;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

function AddLoginInfo(props) {
  const { getFieldDecorator } = props.form;
  const [spinning, setSpinning] = useState(true);

  useEffect(() => {
    setSpinning(false);
  }, []);


  // 新增
  useImperativeHandle(props.cRef, () => ({
    handleAdd: () => new Promise(resolve => {
        props.form.validateFields((err, fieldsValue) => {
          if (err) return;
          // console.log(fieldsValue);
          // console.log(fieldsValue.expDate[0].format('YYYY-MM-DD HH:mm:ss'))
          const obj = {
            effDate: fieldsValue.expDate[0].format('YYYY-MM-DD HH:mm:ss'),
            expDate: fieldsValue.expDate[1].format('YYYY-MM-DD HH:mm:ss'),
            loginLimitIpVal: `${fieldsValue.limitIpBegin}-${fieldsValue.limitIpEnd}`,
            sysUserId: props.sysUserId,
            loginLimitType: fieldsValue.loginLimitType,
            remark: fieldsValue.remark,
          };
          setSpinning(true);
          request('orgauth/SysuserLoginLimitController/createLoginLimit.do', { data: obj }).then(res => {
            if (res) {
              setSpinning(false);
              message.success('新增成功');
              resolve(true);
            } else {
              setSpinning(false);
              message.error('新增失败');
            }
          });
        });
      }),
  }));


  return (
    <Spin spinning={spinning}>
      <Form>
        <Row>
          <Col span={24}>
            <Form.Item label="登录设置类型" {...formItemLayout}>
              {getFieldDecorator('loginLimitTypeName', {
                rules: [
                  {
                    required: true,
                    message: '登录设置类型不能为空',
                  },
                ],
                initialValue: 1000,
              })(
                <Select placeholder="请选择" style={{ width: 350 }}>
                  <Option value={1000} key={1000}>
                    IP地址
                  </Option>
                </Select>
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Form.Item
              label={(
                <span>
                  <span style={{
                    marginRight: '4px',
                    color: '#f5222d',
                    fontSize: '13px',
                    fontFamily: 'SimSun, sans-serif',
                    lineHeight: 1,
                  }}
                  >*
                  </span>
                  <span>起止IP值</span>
                </span>
              )}
              {...formItemLayout}
              style={{ marginBottom: 0 }}
            >
              <Form.Item style={{ display: 'inline-block', width: '163px' }}>
                {getFieldDecorator('limitIpBegin', {
                  rules: [
                    {
                      required: true,
                      message: '起始IP值不能为空',
                    },
                    {
                      required: false,
                      pattern: new RegExp(/^((25[0-5]|2[0-4][0-9]|1?[0-9]?[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1?[0-9]?[0-9])$/, 'g'),
                      message: '请输入正确的IP值',
                    },
                  ],
                })(
                  <Input placeholder="起始IP值" style={{ textAlign: 'center' }} />
                )}
              </Form.Item>
              <span style={{ display: 'inline-block', width: '24px', textAlign: 'center' }}>~</span>
              <Form.Item style={{ display: 'inline-block', width: '163px' }}>
                {getFieldDecorator('limitIpEnd', {
                    rules: [
                      {
                        required: true,
                        message: '终止IP值不能为空',
                      },
                      {
                        required: false,
                        pattern: new RegExp(/^((25[0-5]|2[0-4][0-9]|1?[0-9]?[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1?[0-9]?[0-9])$/, 'g'),
                        message: '请输入正确的IP值',
                      },
                    ],
                  })(
                    <Input placeholder="终止IP值" style={{ textAlign: 'center' }} />
                  )}
              </Form.Item>
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Form.Item label="生失效时间" {...formItemLayout}>
              {getFieldDecorator('expDate', {
                rules: [
                  {
                    required: true,
                    message: '生失效时间不能为空',
                  },
                ],
              })(
                <RangePicker
                  showTime={{ format: 'HH:mm:ss' }}
                  format="YYYY-MM-DD HH:mm:ss"
                  placeholder={['生效时间', '失效时间']}
                />
              )}
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Form.Item label="备注" {...formItemLayout}>
              {getFieldDecorator('remark')(<TextArea rows={4} placeholder="请输入" style={{ width: 350 }} />)}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Spin>
  );
}

export default connect(() => ({}))(Form.create()(AddLoginInfo));
