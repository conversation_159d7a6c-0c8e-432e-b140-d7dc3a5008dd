.wrap {
  min-height: 500px;

  :global(.ant-card-head-title) {
    position: relative;
    padding-left: 14px;
  }

  :global(.ant-card-head-title)::before {
    position: absolute;
    top: 50%;
    left: 0;
    display: inline-block;
    width: 6px;
    height: 14px;
    margin-top: -7px;
    background-color: #1890ff;
    border-radius: 2px;
    content: "";
  }
}
.container {

  .queryForm {
    padding-bottom: 16px;
  }

  :global(.ant-btn-link) {
    padding: 0 4px;
  }

  .btnCon {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
  }

}

.putModal {
  :global {
    .ant-btn {
      height: 30px
    }
  }
}
