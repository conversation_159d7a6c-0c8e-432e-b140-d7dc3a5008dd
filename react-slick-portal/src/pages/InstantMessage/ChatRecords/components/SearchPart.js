import React, { useState } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { DatePicker } from 'antd';
import styles from '../../index.less';

const SearchPart = ({ message: { searchObj }, dispatch, height }) => {
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  const changeStart = date => {
    setStartDate(date);
    dispatch({
      type: 'message/changeSearch',
      payload: { ...searchObj, createDateStart: date ? `${moment(date).format('YYYY-MM-DD')} 00:00:00` : undefined },
    });
  };

  const changeEnd = date => {
    setEndDate(date);
    dispatch({
      type: 'message/changeSearch',
      payload: { ...searchObj, createDateEnd: date ? `${moment(date).format('YYYY-MM-DD')} 23:59:59` : undefined },
    });
  };

  const disabledStartDate = startValue => {
    if (!startValue || !endDate) {
      return false;
    }
    return startValue.valueOf() > endDate.valueOf();
  };

  const disabledEndDate = endValue => {
    if (!endValue || !startDate) {
      return false;
    }
    return endValue.valueOf() <= startDate.valueOf();
  };

  return (
    <div className={styles.searchPart} style={{ height }}>
      <div className={styles.title}>添加筛选条件</div>
      <div>
        <div className={styles.smallTitle}>日期：</div>
        <DatePicker value={startDate} onChange={changeStart} disabledDate={disabledStartDate} style={{ marginBottom: '4px' }} />
        <DatePicker value={endDate} onChange={changeEnd} disabledDate={disabledEndDate} />
      </div>
    </div>
  );
};
export default connect(({ message }) => ({
  message,
}))(SearchPart);
