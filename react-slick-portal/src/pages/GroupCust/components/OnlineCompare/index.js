import React, { useState } from 'react';
import { Button, message } from 'antd';
import request from '@/utils/request';

const OnlineCompare = ({ onBeforeCompare, onSuccess, onFail }) => {
  const [loading, setLoading] = useState(false);

  // 参数
  const [compareData, setCompareData] = useState({
    custName: '',
    custCertNo: '',
    custNation: '',
    busiType: '1',
    baseString: '',
    imageString: '',
    subSourceCode: 'WEB',
    BUSI_CODE: '',
    LOG_VERIFY_TYPE: '3', // 认证类型 默认为3 经办人    认证类型 0 个人 1 使用人 2责任人 3经办人 4 客户,
    SUB_SOURCE_CODE: 'WEB',
    mixFlag: '', // //是否集团业务融合预受理的法人/授权人在线比对
    RISKID: '',
    EXT_COMPARE_IMAGE_TIMESTAMP: '',
    ACCESS_NUM: '',
    businessType: '',
    ONLINE_COMPARE_RESULT: '',
  });

  // 执行在线比对
  const handleCompare = async () => {
    // 执行前置处理
    let updatedData = { ...compareData };
    if (onBeforeCompare && typeof onBeforeCompare === 'function') {
      const result = onBeforeCompare();
      if (result === false) {
        return;
      }

      // 如果 onBeforeCompare 返回了对象，则合并到 compareData
      if (result && typeof result === 'object') {
        updatedData = { ...updatedData, ...result };
        setCompareData(updatedData);
      }
    }

    // 获取比对参数
    const { custName, custCertNo, imageString, baseString } = updatedData;

    // 验证必要字段
    if (!custName || !custCertNo) {
      message.warning('请先填写姓名和证件号码！');
      return;
    }

    if (!imageString) {
      message.warning('请先拍照！');
      return;
    }

    setLoading(true);

    const params = {
      checkType: '1',
      busiData: {
        ...updatedData,
        userType: 1,
        soldierUse: 'false',
        imageString,
        baseString,
        custCertNo,
        custName,
        billId: '',
        busiType: updatedData.businessType,
      },
    };
    delete params.busiData.BUSI_CODE;
    delete params.busiData.LOG_VERIFY_TYPE;
    delete params.busiData.SUB_SOURCE_CODE;
    delete params.busiData.RISKID;
    delete params.busiData.EXT_COMPARE_IMAGE_TIMESTAMP;
    delete params.busiData.ACCESS_NUM;
    delete params.busiData.ONLINE_COMPARE_RESULT;
    delete params.busiData.mixFlag;
    delete params.busiData.custNation;
    delete params.busiData.subSourceCode;
    delete params.busiData.businessType;

    // 发送请求
    request('portal/CrmSystemController/onlineComparison.do', {
      method: 'POST',
      data: params,
    })
      .then(response => {
        const { resultCode, resultObject, resultMsg } = response;
        // 处理响应
        if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.checkData?.CODE === '0000') {
          // 调用成功回调
          if (onSuccess && typeof onSuccess === 'function') {
            onSuccess(resultObject);
          }
        } else {
          message.error(`在线比对不通过，原因：${resultMsg}`);

          // 调用失败回调
          if (onFail && typeof onFail === 'function') {
            onFail(response);
          }
        }
      })
      .catch(() => {
        message.error('比对请求失败，请稍后重试');
      })
      .always(() => {
        setLoading(false);
      });
  };
  // 处理响应

  return (
    <Button type="primary" onClick={handleCompare} loading={loading}>
      在线比对
    </Button>
  );
};

export default OnlineCompare;
