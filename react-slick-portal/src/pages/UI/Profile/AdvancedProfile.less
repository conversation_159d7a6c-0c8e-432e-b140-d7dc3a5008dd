@import '~antd/lib/style/themes/default.less';

.headerList {
  margin-bottom: 4px;
}

.tabsCard {
  :global {
    .ant-card-head {
      padding: 0 16px;
    }
  }
}

.noData {
  color: @disabled-color;
  font-size: 16px;
  line-height: 64px;
  text-align: center;
  i {
    position: relative;
    top: 3px;
    margin-right: 16px;
    font-size: 24px;
  }
}

.heading {
  color: @heading-color;
  font-size: 20px;
}

.stepDescription {
  position: relative;
  left: 38px;
  padding-top: 8px;
  font-size: 14px;
  text-align: left;

  > div {
    margin-top: 8px;
    margin-bottom: 4px;
  }
}

.textSecondary {
  color: @text-color-secondary;
}

@media screen and (max-width: @screen-sm) {
  .stepDescription {
    left: 8px;
  }
}
