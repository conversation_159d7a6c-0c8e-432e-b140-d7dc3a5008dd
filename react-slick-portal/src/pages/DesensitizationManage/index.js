import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Card, Input, Button, Modal, Divider, message } from 'antd';
import SlickTable from '@/components/SlickTable';
import { useAntdTable } from '@umijs/hooks';
import styles from './styles.less';
import SwitchTab from './components/Tab';
import { getSystemList, sceneConfPage, updateSceneState, deleteSceneConfById, refreshCache } from './service';

const namespace = 'desensitizationManage';

function DesensitizationManage(props) {
  const [height, setHeight] = useState('');
  const [filterVal, setFilterVal] = useState(null);
  const [sysCode, setSysCode] = useState([]);
  const [currentRecord, setCurrentRecord] = useState({});

  // 系统信息，可供其他组件使用
  const saveSyscode = sysCodes => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveSyscode`,
      payload: sysCodes,
    });
  };

  // 操作状态 默认详情：disabled, 编辑：edit, 新增：add
  const saveBehavior = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveBehavior`,
      payload: params,
    });
  };

  // 当前操作记录
  const saveTableData = params => {
    setCurrentRecord(params);
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveTableData`,
      payload: params,
    });
  };

  const { tableProps, refresh } = useAntdTable(
    queryParams => sceneConfPage({ pageNum: queryParams.current, pageSize: 5, filterVal }),
    [filterVal],
  );

  const { pagination, ...restTableProps } = tableProps;

  useEffect(() => {
    setHeight(props.size.height);
  }, [props.size.height]);

  useEffect(() => {
    refresh();
  }, [props.refreshTag]);

  const initSystemInfo = async () => {
    const res = await getSystemList();
    if (res && res.length > 0) {
      setSysCode(res);
      saveSyscode(res);
    }
  };

  useEffect(() => {
    initSystemInfo();
  }, []);


  const onSearch = value => {
    if (value) {
      setFilterVal(value);
    } else {
      setFilterVal(null);
    }
  };

  const handleEdit = () => {
    saveBehavior('edit');
  };
  const handleAdd = () => {
    saveBehavior('add');
  };

  const refreshConfigCache = async () => {
    const res = await refreshCache();
    if (res && res.success) {
      message.success('刷新缓存成功');
      return;
    }
     message.error('刷新缓存失败');
  };

  const rowSelect = record => {
    if (props.behavior === 'disabled') {
      saveTableData(record);
    } else {
      Modal.info({ content: '请先保存或取消当前操作' });
    }
  };

  const columns = [
    {
      title: '脱敏场景名称',
      dataIndex: 'sceneName',
      ellipsis: true,
    },
    {
      title: '脱敏场景编码',
      dataIndex: 'sceneCode',
      ellipsis: true,
    },
    {
      title: '归属系统',
      dataIndex: 'systemNbr',
      ellipsis: true,
      render: (text, record) => {
        const matchedValues = sysCode.find(item => item.systemNbr === record.systemNbr);
        return matchedValues?.systemName;
      },
    },
    {
      title: '描述',
      dataIndex: 'sceneDesc',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'statusCd',
      width: '100px',
      render: (text, record) => {
        if (record.statusCd === '1000') {
          // 有效
          return <span className="text-success">启用</span>;
        }
        // 失效
        return <span className="text-danger">禁用</span>;
      },
    },
    {
      title: '操作',
      width: '180px',
      render: (text, record) =>
        (
          <span>
            <a
              onClick={() => {
                handleEdit(record);
              }}
            >
              编辑
            </a>
            <Divider type="vertical" />
            <a
              onClick={() => {
                Modal.confirm({
                  title: '是否确认删除',
                  onOk: async () => {
                    const res = await deleteSceneConfById({ id: record.id });
                    if (res && res.success) {
                      message.success('操作成功', 1, () => {
                        refresh();
                      });
                    }
                  },
                });
              }}
            >
              删除
            </a>
            <Divider type="vertical" />
            <a
              onClick={() => {
                Modal.confirm({
                  title: record.statusCd === '1000' ? '是否禁用' : '是否启用',
                  onOk: async () => {
                    const res = await updateSceneState({ id: record.id, statusCd: record.statusCd === '1000' ? '1100' : '1000' });
                    if (res && res.success) {
                      message.success('操作成功', 1, () => {
                        refresh();
                      });
                    }
                  },
                });
              }}
            >
              {record.statusCd === '1000' ? '禁用' : '启用'}
            </a>
          </span>
        ),
    },
  ];

  const setRowClassName = record => record.id === currentRecord.id ? `${styles.clickRowStyle}` : '';

  return (
    <Card
      title="脱敏配置管理"
      className="cute"
      style={{ minHeight: height }}
      extra={
        (
          <div className={styles.extra}>
            <div>
              <Input.Search placeholder="场景名称、编码搜索" onSearch={onSearch} />
            </div>
            <div>
              <Button className="margin-left" type="primary" onClick={handleAdd}>
                新增
              </Button>
              <Button className="margin-left" type="primary" onClick={refreshConfigCache}>
                刷新
              </Button>
            </div>
          </div>
        )
      }
    >
      <SlickTable
        rowKey={record => record.id}
        columns={columns}
        {...restTableProps}
        data={{
          pagination: {
            ...pagination,
            pageSize: 5,
          },
        }}
        loading={tableProps?.loading}
        onRow={record => ({
          onClick: () => {
            rowSelect(record);
          },
        })}
        rowClassName={setRowClassName}
      />
      <SwitchTab behavior={props.behavior} />
    </Card>
  );
}

export default connect(({ desensitizationManage, setting }) => ({
  behavior: desensitizationManage.behavior,
  refreshTag: desensitizationManage.refreshTag,
  size: setting.size,
}))(DesensitizationManage);
