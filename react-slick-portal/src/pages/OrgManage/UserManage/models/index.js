export default {
  namespace: 'userManage',
  state: {
    behavior: 'disabled',
    tableData: {},
  },

  effects: {},

  reducers: {
    saveBehavior(state, { payload: params }) {
      return {
        ...state,
        behavior: params,
      };
    },
    saveTableData(state, { payload: params }) {
      return {
        ...state,
        tableData: params,
      };
    },

     // 销毁时重置
     reset() {
      return {
        tableData: {},
        passData: {},
        behavior: 'disabled',
      };
    },
  },
};
