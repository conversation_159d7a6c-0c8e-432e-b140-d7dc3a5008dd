// utils/PubSubHelper/subscriber.js
import PubSub from 'pubsub-js';

class Subscriber {
  constructor(options = {}) {
    this.handlers = options.handlers || {};
    this.subscriptions = [];

    // 初始化订阅
    this._initSubscriptions();
  }

  _initSubscriptions() {
    // 为每个处理器创建订阅
    Object.keys(this.handlers).forEach(eventName => {
      const handler = this.handlers[eventName];

      const token = PubSub.subscribe(eventName, (msg, data) => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in handler for event ${eventName}:`, error);
        }
      });

      this.subscriptions.push(token);
    });
  }

  // 添加新的事件处理器
  addHandler(eventName, handler) {
    if (this.handlers[eventName]) {
      console.warn(`Handler for event ${eventName} already exists and will be overwritten.`);
    }

    this.handlers[eventName] = handler;

    // 创建新的订阅
    const fullEventName = this.namespace ? `${this.namespace}.${eventName}` : eventName;
    const token = PubSub.subscribe(fullEventName, (msg, data) => {
      try {
        handler(data);
      } catch (error) {
        console.error(`Error in handler for event ${eventName}:`, error);
      }
    });

    this.subscriptions.push(token);
    return token;
  }

  // 移除特定的事件处理器
  removeHandler(eventName) {
    if (this.handlers[eventName]) {
      delete this.handlers[eventName];
      // 注意：这里不会取消订阅，因为我们没有跟踪每个事件名对应的token
      // 如果需要取消特定事件的订阅，需要额外的数据结构
    }
  }

  // 取消所有订阅
  unsubscribeAll() {
    this.subscriptions.forEach(token => {
      PubSub.unsubscribe(token);
    });
    this.subscriptions = [];
  }

  // 销毁订阅者
  destroy() {
    this.unsubscribeAll();
    this.handlers = {};
  }
}

export default Subscriber;
