.menuManage {
  display: table;
  .menuManageLeft {
    display: table-cell;
    min-width: 250px;
    vertical-align: top;
    :global {
      .ant-card .ant-card-body {
        padding: 0px;
      }
    }
  }
  .menuManageRight {
    width: 100%;
    display: table-cell;
    vertical-align: top;
    .funComponents {
      :global {
        .ant-card .ant-card-body {
          padding: 0px;
        }
      }
      .fun_components {
        padding: 16px 0px;
      }
    }
  }
}

.system_menu {
  .system_menu_search {
    padding: 16px 16px 0px 16px;
  }
  .system_menu_tree {
    margin: 0px 16px;
    .system_menu_tree_edit {
      // display: none;
      padding-left: 8px;
      visibility: hidden;
      .icon {
        padding-right: 8px;
      }
      .icon:hover {
        color: #1890ff;
      }
    }
  }
  :global {
    .ant-tree li .ant-tree-node-content-wrapper:hover .ant-tree-title > span > span:nth-child(2) {
      // display: inline-block;
      visibility: visible;
    }
  }
}

.icon_hover:hover {
  color: #1890ff;
}
