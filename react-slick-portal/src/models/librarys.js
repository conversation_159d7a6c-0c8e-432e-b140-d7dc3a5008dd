import { getAttrSpecsByNbr, getKlKnowledgeBsDataPublicList, getKlKnowledgeBsDataPublic, getIndustryList, queryCommonRegion } from '@/pages/Librarys/services';
import { qryFileTypeList } from '@/pages/Librarys/services-use';
import { BASE_FILE_TYPE } from '@/pages/Librarys/const';
import { toArray } from '@/utils/utils';

const buildTreeSelectOption = (attrs, attrType) => {
  if (attrType === 'companyQualify') {
    return toArray(attrs[attrType]).map(item1 => {
      const children = toArray(item1.bsDataPublicDtoList).map(item2 => ({
        title: item2.paramName,
        label: attrType === 'sceneSub' ? `${item1.paramName}|${item2.paramName}` : item2.paramName,
        value: item2.paramValue,
        key: item2.id,
      }));

      return {
        title: item1.paramName,
        label: item1.paramName,
        value: item1.paramValue,
        key: item1.id,
        children,
      };
    });
  }
  // 第一级：所有
  const allInfo = attrs[attrType][0] || {};

  // 第二级
  const second = toArray(allInfo?.bsDataPublicDtoList).map(item1 => {
    const children = toArray(item1.bsDataPublicDtoList).map(item2 => ({
      title: item2.paramName,
      label: attrType === 'sceneSub' ? `${item1.paramName}|${item2.paramName}` : item2.paramName,
      value: item2.paramValue,
      key: item2.id,
    }));

    return {
      title: item1.paramName,
      label: item1.paramName,
      value: item1.paramValue,
      key: item1.id,
      children,
    };
  });

  return [
    {
      title: allInfo.paramName,
      label: allInfo.paramName,
      value: allInfo.paramValue,
      key: allInfo.paramValue,
      children: second,
    },
  ];
};

// 初始加载的属性值
// const initLoadAttrs = ['knowledgeType', 'knowledgeFileType', 'prodType', 'statusCd', 'schemeType', 'industryType', 'typicalCaseCategory'];

export default {
  namespace: 'librarys',
  state: {
    initLoad: false,
    cityList: [],
    attrs: {
      getTreeSelectOption: () => {},
    },
    instantAddVisible: false, // 打包页面显示
    editor: null,
    packChatData: {
      modalType: '', // 窗体类别， casePack打包案例，serviceList服务单
      initExp: '',
      currentId: '', // 当前群组会话ID
    }, // 聊天窗口打包内容
  },
  effects: {
    *initData(...args) {
      const { select, call, put } = args[1];
      const { initLoad } = yield select(state => state.librarys);
      // 初始化数据获取
      if (!initLoad) {
        // 初始主数据获取
        const attrs = yield call(getAttrSpecsByNbr, 'KlKnowledgeInfo');

        // 数据获取，也算是主数据，只是从不同的接口获取
        const attrs2 = yield call(getKlKnowledgeBsDataPublic);

        // 支撑地域信息
        const supportAreaList = yield call(getKlKnowledgeBsDataPublicList, 'supportArea');

        // 获取行业信息
        const industryType = yield call(getIndustryList);

        // 获取文件类型列表
        const fileTypeList = yield call(qryFileTypeList);

        // 设置基础文件类型
        const baseFileTypeList = [];
        BASE_FILE_TYPE.forEach(type => {
          const value = fileTypeList.find(itme => itme.paramCode === type)?.paramValue;
          if (value) {
            baseFileTypeList.push({
              label: type,
              value,
            });
          }
        });

        const allAttrs = {
          ...attrs,
          ...attrs2,
          industryType,
          fileTypeList,
          baseFileTypeList,
          supportAreaList,
        };

        yield put({
          type: 'onSaveAttrs',
          payload: {
            ...allAttrs,
            getTreeSelectOption: attrType => buildTreeSelectOption(allAttrs, attrType),
          },
        });

        const cityList = yield call(queryCommonRegion);
        yield put({
          type: 'onSaveCityList',
          payload: cityList,
        });
        yield put({
          type: 'onSaveInitLoad',
          payload: true,
        });
      }
    },
    *receivePackMessage({ payload }, { put }) {
      yield put.resolve({
        type: 'savePackChatData',
        payload,
      });
      yield put({
        type: 'toggleVisible',
        payload: true,
      });
    },
    *handleOpenAdd({ payload }, { put, select }) {
      yield put({
        type: 'initData',
      });
      const { editor } = yield select(state => state.librarys);
      const { messageData } = payload;
      if (editor && messageData) {
        editor.setHtml(messageData);
      }
    },
  },
  reducers: {
    onSaveAttrs(state, { payload }) {
      return {
        ...state,
        attrs: payload,
      };
    },
    onSaveCityList(state, { payload }) {
      return {
        ...state,
        cityList: payload,
      };
    },
    onSaveInitLoad(state, { payload }) {
      return {
        ...state,
        initLoad: payload,
      };
    },
    toggleVisible(state, { payload }) {
      return {
        ...state,
        instantAddVisible: payload,
      };
    },
    saveEditor(state, { payload }) {
      return {
        ...state,
        editor: payload,
      };
    },
    savePackChatData(state, { payload }) {
      return {
        ...state,
        packChatData: payload,
      };
    },
  },
};
