/* 在途订单 */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Form, DatePicker, Tabs, Input, Button } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import OrderList from '@/pages/GroupCust/GroupCustView/components/OrderInfo/components/InTransitOrder/components/orderList';
import PreOrderList from '@/pages/GroupCust/GroupCustView/components/OrderInfo/components/InTransitOrder/components/preOrderList';

const { TabPane } = Tabs;

const { RangePicker } = DatePicker;

const Index = ({ form, currentCust }) => {
  const { getFieldDecorator, validateFields } = form;
  const orderRef = useRef(null);
  const preRef = useRef(null);
  // 添加查询条件状态
  const [queryParams, setQueryParams] = useState({});
  // 添加当前激活的标签页状态
  const [activeKey, setActiveKey] = useState('1');

  // 处理查询
  const handleQuery = () => {
    validateFields((err, values) => {
      if (!err) {
        // 处理日期格式
        const params = { ...values };
        if (params.date && params.date.length === 2 && activeKey === '2') {
          params.startDate = params.date[0].format('YYYY-MM-DD');
          params.endDate = params.date[1].format('YYYY-MM-DD');
        }
        if (params.date && params.date.length === 2 && activeKey === '1') {
          params.createStart = params.date[0].format('YYYY-MM-DD');
          params.createEnd = params.date[1].format('YYYY-MM-DD');
        }
        delete params.date;
        // 更新查询条件状态
        setQueryParams(params);
        if (activeKey === '1') {
          preRef.current?.query({
            ...params,
            groupId: currentCust.GROUP_ID,
          });
        } else if (activeKey === '2') {
          orderRef.current?.query({
            ...params,
            groupId: currentCust.GROUP_ID,
          });
        }
      }
    });
  };

  // 处理标签页切换
  const handleTabChange = key => {
    setActiveKey(key);
  };
  // 组件初始化时设置默认查询条件
  useEffect(() => {
    const defaultValues = form.getFieldsValue();
    const defaultParams = { ...defaultValues };

    if (defaultParams.date && defaultParams.date.length === 2) {
      defaultParams.startDate = defaultParams.date[0].format('YYYY-MM-DD');
      defaultParams.endDate = defaultParams.date[1].format('YYYY-MM-DD');
      delete defaultParams.date;
    }
    setQueryParams(defaultParams);
    if (activeKey === '1') {
      preRef.current?.query({
        ...defaultParams,
        groupId: currentCust.GROUP_ID,
      });
    } else if (activeKey === '2') {
      orderRef.current?.query({
        ...defaultParams,
        groupId: currentCust.GROUP_ID,
      });
    }
  }, [activeKey]);
  // 切换日期
  const handlePicker = (dates, dateStrings) => {
    const params = {
      createStart: dateStrings[0],
      createEnd: dateStrings[1],
    };
    setQueryParams(params);
  };
  const renderExtra = useMemo(
    () => {
      if (activeKey === '2') {
        return (
          <div className="extra">
            <Form style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', gap: '10px' }}>
              <Form.Item style={{ marginBottom: 0 }}>
                {getFieldDecorator('accessNum', {
                  initialValue: '',
                })(<Input placeholder="计费号" style={{ width: 150 }} />)}
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                {getFieldDecorator('date', {
                  initialValue: [moment().subtract(7, 'days'), moment()],
                })(<RangePicker placeholder={['开始日期', '结束日期']} style={{ width: 230 }} format="YYYY-MM-DD" />)}
              </Form.Item>
              <Button type="primary" onClick={handleQuery}>
                查询
              </Button>
            </Form>
          </div>
        );
      } if (activeKey === '1') {
        return (
          <div className="extra">
            <Form style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', gap: '10px' }}>
              <Form.Item style={{ marginBottom: 0 }}>
                {getFieldDecorator('orderNbr', {
                  initialValue: '',
                })(<Input placeholder="订单号" style={{ width: 150 }} />)}
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                {getFieldDecorator('date', {
                  initialValue: [moment().subtract(7, 'days'), moment()],
                })(<RangePicker placeholder={['开始日期', '结束日期']} onChange={(dates, dateStrings) => handlePicker(dates, dateStrings)} style={{ width: 230 }} format="YYYY-MM-DD" />)}
              </Form.Item>
              <Button type="primary" onClick={handleQuery}>
                查询
              </Button>
            </Form>
          </div>
        );
      }
      return <></>;
    },
    [getFieldDecorator, handleQuery, activeKey]
  );

  return (
    <>
      <div>
        <Tabs tabBarExtraContent={renderExtra} activeKey={activeKey} onChange={handleTabChange} className="tabsCustom2">
          <TabPane tab="在途预受理单" key="1">
            <PreOrderList queryParams={queryParams} cRef={preRef} />
          </TabPane>
          <TabPane tab="在途订单" key="2">
            <OrderList queryParams={queryParams} cRef={orderRef} />
          </TabPane>
        </Tabs>
      </div>
    </>
  );
};

export default connect(({ setting, groupCustView }) => ({
  size: setting.size,
  currentCust: groupCustView.currentCust,
}))(Form.create()(Index));
