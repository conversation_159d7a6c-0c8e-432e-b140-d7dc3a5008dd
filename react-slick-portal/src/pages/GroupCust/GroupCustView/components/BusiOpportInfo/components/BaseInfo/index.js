import { connect } from 'dva';
import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Spin, Steps } from 'antd';
import { BaseInfoFields, CmiotFields } from './const';

const { Step } = Steps;
const Index = props => {
  const { loading, currentCust } = props;
  useEffect(() => {
    console.log(loading);
  }, []);
  return (
    <div style={{ position: 'relative' }}>
      <div style={{ marginRight: 80 }}>
        <Spin spinning={!!loading}>
          {/* 基本信息 */}
          <Card className="cute" title="集团基础信息">
            <Descriptions column={{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }}>
              {BaseInfoFields.map(({ name, key, span }) => (
                <Descriptions.Item label={name} span={span}>
                  {currentCust[key]}
                </Descriptions.Item>
              ))}
            </Descriptions>
          </Card>
          {/* 纳税人资质信息 */}
          <Card className="cute" title="纳税人资质信息">
            {/* <Descriptions column={3}>
            {
              CmiotFields.map(({ name, key, span }) => (
                <Descriptions.Item label={name} span={span}>{currentCust[key]}</Descriptions.Item>

              ))
            }
          </Descriptions> */}
          </Card>
          {/* 附加信息 */}
          <Card className="cute" title="附加信息">
            {/* <Descriptions column={3}>
            {
              CmiotFields.map(({ name, key, span }) => (
                <Descriptions.Item label={name} span={span}>{currentCust[key]}</Descriptions.Item>

              ))
            }
          </Descriptions> */}
          </Card>
          {/* 法人身份证号管理 */}
          <Card className="cute" title="法人身份证号管理">
            {/* <Descriptions column={3}>
            {
              CmiotFields.map(({ name, key, span }) => (
                <Descriptions.Item label={name} span={span}>{currentCust[key]}</Descriptions.Item>

              ))
            }
          </Descriptions> */}
          </Card>
          {/* 地址场景补录 */}
          <Card className="cute" title="地址场景补录">
            {/* <Descriptions column={3}>
            {
              CmiotFields.map(({ name, key, span }) => (
                <Descriptions.Item label={name} span={span}>{currentCust[key]}</Descriptions.Item>

              ))
            }
          </Descriptions> */}
          </Card>
        </Spin>
      </div>
      <div style={{ position: 'fixed', top: '50%', right: 30 }}>
        <Steps direction="vertical" size="small" current={1}>
          <Step title="集团基础信息" />
          <Step title="纳税人资质信息" />
          <Step title="附加信息" />
          <Step title="法人身份证号管理" />
          <Step title="地址场景补录" />
        </Steps>
      </div>
    </div>
  );
};
export default connect(({ groupCustView, loading }) => ({
  currentCust: groupCustView.currentCust,
  loading: loading.effects['groupCustView/getCustomerInfo'],
}))(Index);
