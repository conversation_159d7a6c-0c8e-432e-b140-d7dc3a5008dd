.merge-2 {
  width: 66.66% !important;
}

/* Ant Design自定义样式 */
.orderListContainer {
  margin-top: 16px;
}

.orderListContent {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 16px;
  background-color: #f5f7fa;
}

.orderListPagination {
  margin-top: 16px;
  text-align: right;
}

.orderListItem {
  margin-bottom: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 0;
}

.orderItemContainer {
  width: 100%;
  padding: 0;
}

.orderHeader {
  background: rgba(93, 171, 230, 0.15);
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
}

.orderHeaderLeft {
  color: #666;
  font-size: 14px;
}

.orderHeaderRight {
  font-weight: 500;
}

.orderIdLabel {
  color: #333;
}

.orderContent {
  padding: 16px;
  background: linear-gradient(90deg, #F5F5FF 0%, #EDFAFD 100%);
}

.orderInfoRow {
  display: flex;
  flex-wrap: wrap;
}

.orderInfoItem {
  display: flex;
  width: 33.33%;
  margin-bottom: 12px;
}

.orderInfoLabel {
  color: #666;
  min-width: 90px;
  font-size: 14px;
}

.orderInfoValue {
  flex: 1;
  color: #333;
  word-break: break-all;
  font-size: 14px;
}

.orderDescriptions {
  width: 100%;

  :global {
    .ant-descriptions-item-label {
      color: #666;
      font-weight: normal;
      min-width: 90px;
      text-align: right;
      padding-right: 8px;
    }

    .ant-descriptions-item-content {
      color: #333;
    }
  }
}

.errorMessage {
  margin-top: 12px;
  padding: 12px;
  background-color: #fff1f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;

  :global {
    .ant-descriptions-item-label {
      color: #666;
      font-weight: normal;
      text-align: right;
      padding-right: 8px;
    }

    .ant-descriptions-item-content {
      color: #f5222d;
    }
  }
}

.errorItem {
  width: 100%;
}

.orderTagRed {
  display: inline-block;
  margin-left: 8px;
  padding: 0 8px;
  color: #fff;
  background-color: #f5222d;
  border-radius: 2px;
  font-size: 12px;
  line-height: 20px;
}

.orderTagGreen {
  display: inline-block;
  margin-left: 8px;
  padding: 0 8px;
  color: #fff;
  background-color: #52c41a;
  border-radius: 2px;
  font-size: 12px;
  line-height: 20px;
}

.marginLeft {
  margin-left: 8px;
}

.textRight {
  text-align: right;
}

// 适配原型图中的按钮样式
// :global {
//   .ant-btn-primary {
//     background-color: #1890ff;
//     border-color: #1890ff;
//   }

//   .ant-pagination-item-active {
//     border-color: #1890ff;

//     a {
//       color: #1890ff;
//     }
//   }

//   .ant-select-selection {
//     border-radius: 4px;
//   }

//   .ant-input {
//     border-radius: 4px;
//   }
// }
.serviceTable {
  :global {
    .ant-table-thead > tr > th {
      background-color: #f5f7fa;
      color: #333;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #e6f7ff;
    }
  }
}
