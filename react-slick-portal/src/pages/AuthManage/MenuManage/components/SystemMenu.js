/* eslint-disable */
import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'dva';
import { Tree, Skeleton, Spin, Input, Icon, Tooltip, Modal } from 'antd';
import request from '@/utils/request';
import ScrollBar from '@/components/ScrollBar';
import styles from '../styles.less';
import PubSub from 'pubsub-js';
import remove from 'lodash/remove';
// import 'antd/dist/antd.css';
const { TreeNode, DirectoryTree } = Tree;
const { Search } = Input;

const namespace = 'menuManage';
const eventName = 'menuManage_systemMenu';

function SystemMenu(props) {
  const [TreeData, setTreeData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedKeysArr, setSelectedKeysArr] = useState(['']);

  const [expandedKeys, setExpandedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  const [searchValue, setSearchValue] = useState('');

  // 补充：通过dva来传递选中节点的数据，主要是功能组件的组件编码校验，及功能组件的新增删除接口入参
  const saveMenuData = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveMenuData`,
      payload: params,
    });
  };

  //通过dva来传递选中节点，编辑节点，新增节点的操作
  const saveMenuID = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveMenuID`,
      payload: params,
    });
  };

  // 通过dva来保存当前的操作状态  值:disable/edit/add
  const saveBehavior = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveBehavior`,
      payload: params,
    });
  };

  // 通过dva来判断当前节点是否为最后一级节点
  const saveIsLeaf = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveIsLeaf`,
      payload: params,
    });
  };

  // 通过dva传递菜单列表数据，给菜单信息的菜单上级的下拉框使用
  const saveTreeData = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveTreeData`,
      payload: params,
    });
  };

  useEffect(() => {
    queryTreeData();
    PubSub.subscribe(`${eventName}.update`, (eventName, data) => {
      let arr = data.treeData;
      let node = data.dataList;
      data.dataList.menuType === '1000' ? (node.isLeaf = false) : (node.isLeaf = true);
      arr = arr.concat(node);
      setTreeData(arr);
      saveTreeData(arr);
      setSelectedKeysArr([`${node.menuId}`]);
      saveBehavior('disable');
      saveMenuID(node.menuId);
      saveIsLeaf(node.isLeaf);
    });
    PubSub.subscribe(`${eventName}.change`, (eventName, data) => {
      let arr = data.treeData;
      let node = data.dataList;
      data.dataList.menuType === '1000' ? (node.isLeaf = false) : (node.isLeaf = true);
      remove(arr, i => i.menuId === node.menuId);
      arr = arr.concat(node);
      setTreeData(arr);
      saveTreeData(arr);
      setSelectedKeysArr([`${node.menuId}`]);
      saveBehavior('disable');
      saveMenuID(node.menuId);
      saveIsLeaf(node.isLeaf);
    });
    return () => {
      PubSub.unsubscribe(`${eventName}`);
    };
  }, []);

  const queryTreeData = () => {
    setLoading(true);
    request('orgauth/FuncMenuController/selectAllMenu.do', {
      method: 'GET',
    }).then(res => {
      if (res.length > 0) {
        let node = {};
        let arr = [];
        res.map((item, index) => {
          node = item;
          if (item.menuType === '1000') {
            node.isLeaf = false;
          } else {
            node.isLeaf = true;
          }
          arr.push(node);
        });
        setTreeData(arr);
        setLoading(false);
        saveTreeData(arr);
      }
    });
  };

  // 由接口决定，根据parMenuId 获取同级元素
  const getParentId = parMenuId => {
    return TreeData.filter(item => {
      return item.parMenuId === parMenuId;
    });
  };

  // 对树进行了扩展操作
  const renderTreeNode = parMenuId => {
    const tmp = getParentId(parMenuId);
    if (tmp.length > 0) {
      return tmp.map(item => {
        const index = item.menuName.indexOf(searchValue);
        const beforeStr = item.menuName.substr(0, index);
        const afterStr = item.menuName.substr(index + searchValue.length);
        let title = '';

        if (item.isLeaf) {
          title = (
            <span>
              {/* <span style={{ paddingRight: '8px' }}>{item.menuName}</span> */}
              {index > -1 ? (
                <span>
                  {beforeStr}
                  <span style={{ color: '#f50' }}>{searchValue}</span>
                  {afterStr}
                </span>
              ) : (
                <span>{item.menuName}</span>
              )}

              <span className={styles.system_menu_tree_edit}>
                <Tooltip title="编辑">
                  <Icon
                    type="edit"
                    className={styles.icon}
                    onClick={event => {
                      edit(event, item);
                    }}
                  />
                </Tooltip>
                <Tooltip title="删除">
                  <Icon
                    type="delete"
                    className={styles.icon}
                    onClick={event => {
                      removeTree(event, item);
                    }}
                  />
                </Tooltip>
              </span>
            </span>
          );
        } else {
          if (getParentId(item.menuId).length === 0) {
            title = (
              <span>
                {index > -1 ? (
                  <span>
                    {beforeStr}
                    <span style={{ color: '#f50' }}>{searchValue}</span>
                    {afterStr}
                  </span>
                ) : (
                  <span>{item.menuName}</span>
                )}
                <span className={styles.system_menu_tree_edit}>
                  <Tooltip title="编辑">
                    <Icon
                      type="edit"
                      className={styles.icon}
                      onClick={event => {
                        edit(event, item);
                      }}
                    />
                  </Tooltip>
                  <Tooltip title="新增">
                    <Icon
                      type="plus-circle"
                      className={styles.icon}
                      onClick={event => {
                        add(event, item);
                      }}
                    />
                  </Tooltip>
                  <Tooltip title="删除">
                    <Icon
                      type="delete"
                      className={styles.icon}
                      onClick={event => {
                        removeTree(event, item);
                      }}
                    />
                  </Tooltip>
                </span>
              </span>
            );
          } else {
            title = (
              <span>
                {index > -1 ? (
                  <span>
                    {beforeStr}
                    <span style={{ color: '#f50' }}>{searchValue}</span>
                    {afterStr}
                  </span>
                ) : (
                  <span>{item.menuName}</span>
                )}
                <span className={styles.system_menu_tree_edit}>
                  <Tooltip title="编辑">
                    <Icon
                      type="edit"
                      className={styles.icon}
                      onClick={event => {
                        edit(event, item);
                      }}
                    />
                  </Tooltip>
                  <Tooltip title="新增">
                    <Icon
                      type="plus-circle"
                      className={styles.icon}
                      onClick={event => {
                        add(event, item);
                      }}
                    />
                  </Tooltip>
                </span>
              </span>
            );
          }
        }
        return (
          <TreeNode
            icon={({ expanded, isLeaf }) => (
              <Icon type={expanded ? 'folder-open' : isLeaf ? 'file' : 'folder'} />
            )}
            title={title}
            key={item.menuId}
            isLeaf={item.isLeaf}
            dataRef={item}
          >
            {renderTreeNode(item.menuId)}
          </TreeNode>
        );
      });
    }
  };

  // 选中节点
  const selectTreeNode = (selectedKeys, e) => {
    if (props.behavior === 'disable') {
      setSelectedKeysArr(selectedKeys);
      if (selectedKeys[0] !== undefined) {
        saveMenuID(parseInt(selectedKeys[0]));
        saveMenuData(e.node.props.dataRef);
        saveIsLeaf(e.node.props.dataRef.isLeaf);
      }
      saveBehavior('disable');
    } else {
      Modal.error({
        title: '操作有误',
        content: '请先对当前表单进行提交或者取消操作！',
      });
    }
  };
  // 编辑节点
  const edit = (event, item) => {
    event.stopPropagation();
    event.nativeEvent.stopImmediatePropagation();
    if (props.behavior === 'disable') {
      setSelectedKeysArr([`${item.menuId}`]);
      saveMenuID(item.menuId);
      saveMenuData(item);
      saveBehavior('edit');
      saveIsLeaf(item.isLeaf);
    } else {
      Modal.error({
        title: '操作有误',
        content: '请先对当前表单进行提交或者取消操作！',
      });
    }
  };
  // 添加子节点
  const add = (event, item) => {
    event.stopPropagation();
    event.nativeEvent.stopImmediatePropagation();
    if (props.behavior === 'disable') {
      setSelectedKeysArr([`${item.menuId}`]);
      saveMenuID(item.menuId);
      saveMenuData(item);
      saveBehavior('add');
      saveIsLeaf(item.isLeaf);
    } else {
      Modal.error({
        title: '操作有误',
        content: '请先对当前表单进行提交或者取消操作！',
      });
    }
  };

  const deleteMenu = item => {
    request(`orgauth/FuncMenuController/deleteMenuByMenuId.do?menuId=${item.menuId}`, {
      data: { menuId: item.menuId },
      method: 'DELETE',
    }).then(res => {
      if (res.result === 'TRUE') {
        Modal.success({ content: '删除成功' });
        let arr = TreeData;
        remove(arr, i => i.menuId === item.menuId);
        setTreeData(arr);
        saveTreeData(arr);
        setSelectedKeysArr(['']);
        // 发布订阅 清空菜单信息，及功能组件
        PubSub.publish(`menuManage_funCom.delete`);
      } else {
        //Modal.error({ content: '删除失败' });
      }
    });
  };

  //删除节点
  const removeTree = (event, item) => {
    event.stopPropagation();
    event.nativeEvent.stopImmediatePropagation();
    Modal.confirm({
      content: `确认删除菜单“${item.menuName}”及其对应组件？`,
      okText: '确定',
      cancelText: '取消',
      onOk() {
        judgeMenu(item).then(res => {
          if (res.result === 'TRUE') {
            deleteMenu(item);
            // request(`orgauth/FuncMenuController/deleteMenuByMenuId.do?menuId=${item.menuId}`, {
            //   data: { menuId: item.menuId },
            //   method: 'DELETE',
            // }).then(res => {
            //   if (res.result === 'TRUE') {
            //     Modal.success({ content: '删除成功' });
            //     let arr = TreeData;
            //     remove(arr, i => i.menuId === item.menuId);
            //     setTreeData(arr);
            //     saveTreeData(arr);
            //     setSelectedKeysArr(['']);
            //     // 发布订阅 清空菜单信息，及功能组件
            //     PubSub.publish(`menuManage_funCom.delete`);
            //   } else {
            //     Modal.error({ content: '删除失败' });
            //   }
            // });
          } else {
            // Modal.error({ content: '删除失败' });
            Modal.confirm({
              title: `菜单或其组件存在以下授权关系，是否删除?`,
              content: (
                <>
                  {res.object[0].privGrants.map(item => (
                  (item.grantObjType==='1300')?
                    <div>
                      权限 '{res.object[0].privName}' 已授权给角色 '{item.systemRoles.sysRoleName}'
                    </div>
                      :
                      (item.grantObjType==='1100')?
                      <div>
                        权限 '{res.object[0].privName}' 已授权给用户 '{item.systemUser.userName}'
                      </div>:null
                  ))}
                </>
              ),
              okText: '确定',
              cancelText: '取消',
              onOk() {
                deleteMenu(item);
              },
            });
          }
        });
      },
    });
    async function judgeMenu(params) {
      return request(
        `orgauth/FuncMenuController/judgeMenuPrivGrantById.do?menuId=${params.menuId}`,
        {
          data: { menuId: params.menuId },
          method: 'DELETE',
        }
      );
    }
  };

  const onSearch = value => {
    if (!value) {
      setExpandedKeys(['']);
      setAutoExpandParent(false);
      setSearchValue('');
      return;
    }
    let searchArr = []; // 被选中项
    let parentArr = []; // 当前选中项的父节点
    const getAllparentId = parentId => {
      TreeData.filter(item => {
        if (item.menuId === parentId) {
          parentArr.push(item.menuId);
          if (item.parMenuId != 0) {
            getAllparentId(item.parMenuId);
          }
        }
      });
    };
    TreeData.map(item => {
      if (item.menuName.indexOf(value) > -1) {
        searchArr.push(item.menuId);
        getAllparentId(item.parMenuId);
      }
    }).filter((item, i, self) => item && self.indexOf(item) === i);
    const strParentArr = [];
    parentArr.map(item => {
      strParentArr.push(`${item}`);
    });
    const strSearchArr = [];
    searchArr.map(item => {
      strSearchArr.push(`${item}`);
    });
    setExpandedKeys(strParentArr);
    setAutoExpandParent(true);
    setSearchValue(value);
  };

  const onExpand = expandedKeys => {
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(false);
  };

  return (
    <div className={styles.system_menu}>
      <div className={styles.system_menu_search}>
        <Search style={{ marginBottom: 8 }} placeholder="搜索关键字" onSearch={onSearch} />
      </div>
      <ScrollBar autoHide autoHeight autoHeightMax={props.height - 100}>
        <Spin spinning={loading}>
          <Tree
            showIcon
            expandAction="false"
            className={styles.system_menu_tree}
            onSelect={selectTreeNode}
            selectedKeys={selectedKeysArr}
            // ==
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
          >
            {renderTreeNode(0)}
          </Tree>
        </Spin>
      </ScrollBar>
    </div>
  );
}

// export default SystemMenu;
export default connect(({ menuManage }) => ({
  behavior: menuManage.behavior,
}))(SystemMenu);
// className={classNames('ant-upload-list', 'ant-upload-list-picture-card')}
