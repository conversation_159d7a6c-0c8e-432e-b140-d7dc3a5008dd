import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Tree, Spin, Input, Icon } from 'antd';
import PubSub from 'pubsub-js';
import { Link } from 'umi';
import styles from '../../../less/menuDrawer.less';
import request from '@/utils/request';
import ScrollBar from '@/components/ScrollBar';
import { LEAF_TYPE } from '@/pages/AuthManage/OperationManage/const';

const { TreeNode } = Tree;
const { Search } = Input;

const eventName = 'operationManage_systemMenu';
const linkUrl = '/AuthManage/OperationManage/OperationManual';

function OperationManualList(props) {
  const { show } = props;
  const [TreeData, setTreeData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [searchValue, setSearchValue] = useState('');

  const queryTreeData = () => {
    setLoading(true);
    request('portal/OperationDocConfController/selectAllMenu.do', {
      method: 'GET',
    }).then(res => {
      if (res.length > 0) {
        let node = {};
        const arr = [];
        res.forEach(item => {
          node = item;
          if (item.statusCd === '1000') {
            node.isLeaf = false;
          } else {
            node.isLeaf = true;
          }
          arr.push(node);
        });
        setTreeData(arr);
        setLoading(false);
      }
    });
  };

  useEffect(() => {
    queryTreeData();

    PubSub.subscribe(`${eventName}.change`, () => {
      queryTreeData();
    });

    return () => {
      PubSub.unsubscribe(`${eventName}`);
    };
  }, []);

  // 查找节点
  const onSearch = value => {
    if (!value) {
      setExpandedKeys(['']);
      setAutoExpandParent(false);
      setSearchValue('');
      return;
    }
    const searchArr = []; // 被选中项
    const parentArr = []; // 当前选中项的父节点
    const getAllparentId = parentId => {
      TreeData.filter(item => {
        if (item.operationDocConfId === parentId) {
          parentArr.push(item.operationDocConfId);
          if (item.parOperationId !== 0) {
            getAllparentId(item.parOperationId);
          }
        }
      });
    };
    TreeData.forEach(item => {
      if (item.operationTitle.indexOf(value) > -1) {
        searchArr.push(item.operationDocConfId);
        getAllparentId(item.parOperationId);
      }
    }).filter((item, i, self) => item && self.indexOf(item) === i);
    const strParentArr = [];
    parentArr.forEach(item => {
      strParentArr.push(`${item}`);
    });
    const strSearchArr = [];
    searchArr.forEach(item => {
      strSearchArr.push(`${item}`);
    });
    setExpandedKeys(strParentArr);
    setAutoExpandParent(true);
    setSearchValue(value);
  };

  // 由接口决定，根据parMenuId 获取同级元素
  const getParentId = parOperationId => TreeData.filter(item => item.parOperationId === parOperationId);

  // 渲染树节点
  const renderTreeNode = parOperationId => {
    const tmp = getParentId(parOperationId).sort((a, b) => a.operationIndex - b.operationIndex);

    let res = [];
    if (tmp.length > 0) {
      res = tmp.map(item => {
        const index = item.operationTitle.indexOf(searchValue);
        const beforeStr = item.operationTitle.substr(0, index);
        const afterStr = item.operationTitle.substr(index + searchValue.length);
        let title = '';

        if (item.operationType === LEAF_TYPE) {
          title = (
            <span>
              {index > -1 ? (
                <Link
                  className="text-ellipsis"
                  title={item.operationTitle}
                  to={`${linkUrl}?operationDocConfId=${item.operationDocConfId}`}
                >
                  <span>
                    {beforeStr}
                    <span style={{ color: '#f50' }}>{searchValue}</span>
                    {afterStr}
                  </span>
                </Link>
              ) : (
                <Link
                  className="text-ellipsis"
                  title={item.operationTitle}
                  to={`${linkUrl}?operationDocConfId=${item.operationDocConfId}`}
                >
                  <> {item.operationTitle}</>
                </Link>
              )}
            </span>
          );
        } else if (getParentId(item.operationDocConfId).length === 0) {
          title = (
            <span>
              {index > -1 ? (
                <span>
                  {beforeStr}
                  <span style={{ color: '#f50' }}>{searchValue}</span>
                  {afterStr}
                </span>
              ) : (
                <> {item.operationTitle}</>
              )}
            </span>
          );
        } else {
          title = (
            <span>
              {index > -1 ? (
                <span>
                  {beforeStr}
                  <span style={{ color: '#f50' }}>{searchValue}</span>
                  {afterStr}
                </span>
              ) : (
                <> {item.operationTitle}</>
              )}
            </span>
          );
        }
        return (
          <TreeNode
            icon={({ expanded, operationType }) => (
              <Icon type={expanded ? 'folder-open' : operationType === LEAF_TYPE ? 'file' : 'folder'} />
            )}
            title={title}
            key={item.operationDocConfId}
            isLeaf={item.operationType === LEAF_TYPE}
            dataRef={item}
          >
            {renderTreeNode(item.operationDocConfId)}
          </TreeNode>
        );
      });
    }

    return res;
  };

  const onExpand = operationKey => {
    setExpandedKeys(operationKey);
    setAutoExpandParent(false);
  };

  return (
    <div className={styles.operation_manual} style={{ display: show ? 'block' : 'none' }}>
      <div className={styles.operation_manual_search}>
        <Search style={{ marginBottom: 8 }} placeholder="搜索关键字" onSearch={onSearch} />
      </div>
      <ScrollBar autoHide autoHeight autoHeightMax={props.height - 100}>
        <Spin spinning={loading}>
          <Tree
            showIcon
            expandAction="false"
            className={styles.operation_manual_tree}
            // onSelect={selectTreeNode}
            // selectedKeys={selectedKeysArr}
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            switcherIcon={<Icon type="down" />}
          >
            {renderTreeNode(0)}
          </Tree>
        </Spin>
      </ScrollBar>
    </div>
  );
}

export default connect(() => ({
}))(OperationManualList);
