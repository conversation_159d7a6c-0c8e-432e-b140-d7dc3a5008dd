import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Form, message } from 'antd';
import { connect } from 'dva';
import { EnterpriseManagerInfoFields } from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/const';
import { qryGroupEnterpriseManagerByGroupId } from '@/pages/GroupCust/GroupCustRedListManage/services';

const EnterpriseManagerInfo = props => {
  const { baseInfo } = props;
  const [enterpriseManagerInfo, setEnterpriseManagerInfo] = useState({});
  const [openCard, setOpenCard] = useState(true); // 是否展开非必填字段

  useEffect(() => {
    if (!baseInfo.GROUP_ID) {
      // eslint-disable-next-line no-console
      console.log('partyId is null!');
      return;
    }
    qryGroupEnterpriseManagerByGroupId({ groupId: baseInfo.GROUP_ID, maskFlag: '1' }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        const datas = resultObject?.rspParam?.busiInfo?.outData;
        const firstMatchingElement = datas?.find(item => item.REL_TYPE === '1');
        setEnterpriseManagerInfo(firstMatchingElement);
      } else {
        message.error(resultMsg);
      }
    });
  }, [baseInfo.GROUP_ID]);

  return (
    <Card
      title="客户经理信息"
      id="panel-0"
      className="cute3"
      extra={!openCard ? <a onClick={() => setOpenCard(true)}>隐藏</a> : <a onClick={() => setOpenCard(false)}>展开</a>}
    >
      {openCard ? (
        <Descriptions className="cuteLabelBold" column={{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }}>
          {EnterpriseManagerInfoFields.map(({ name, key, span, options }) => (
            <Descriptions.Item label={name} span={span} key={key}>
              {!options ? enterpriseManagerInfo?.[key] : options[enterpriseManagerInfo?.[key]] ?? enterpriseManagerInfo?.[key]}
            </Descriptions.Item>
          ))}
        </Descriptions>
      ) : (
        <></>
      )}
    </Card>
  );
};

export default connect()(Form.create()(EnterpriseManagerInfo));
