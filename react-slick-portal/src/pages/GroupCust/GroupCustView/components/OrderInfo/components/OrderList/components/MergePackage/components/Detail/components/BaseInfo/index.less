.detailContainer {
  padding: 16px;
}

.detailCard {
  margin-bottom: 16px;

  :global {
    .ant-card-head {
      background-color: #f0f7ff;
      border-bottom: 1px solid #e8e8e8;
    }

    .ant-card-head-title {
      font-weight: 500;
      color: #333;
    }
  }
}

.productDescriptions {
  :global {
    .ant-descriptions-item-label {
      color: rgba(0, 0, 0, 0.65);
      font-weight: 500;
    }

    .ant-descriptions-item-content {
      color: rgba(0, 0, 0, 0.85);
    }
  }
}

.detailCollapse {
  margin-bottom: 16px;

  :global {
    .ant-collapse-header {
      background-color: #f0f7ff;
      border-radius: 4px !important;
    }

    .ant-collapse-content-box {
      padding: 0 !important;
    }
  }
}

.collapseHeader {
  display: flex;
  align-items: center;
  width: 100%;
}

.collapseIcon {
  margin-right: 8px;
}

.collapseExtra {
  margin-left: auto;
}

.innerCard {
  padding: 16px;
  background: linear-gradient(90deg, #F5F5FF 0%, #EDFAFD 100%);
}

.infoItem {
  margin-bottom: 12px;
  display: flex;
}

.infoLabel {
  color: #666;
  min-width: 120px;
  text-align: right;
  padding-right: 16px;
}

.infoValue {
  color: #333;
  flex: 1;
}

.emptyContent {
  padding: 24px;
  text-align: center;
  color: #999;
  background: linear-gradient(90deg, #F5F5FF 0%, #EDFAFD 100%);
}

.marginRight {
  margin-right: 8px;
}
