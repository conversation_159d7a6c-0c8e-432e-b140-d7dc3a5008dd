import { delay } from 'roadhog-api-doc';
import defaultSettings from '@/defaultSettings';

const proxy = {
  'POST /orgauth/DeptController/getDeptTree.do': (req, res) => {
    res.send({
      resultCode: 'TRUE',
      resultMsg: '操作成功',
      resultObject: {
        rspParam: {
          busiInfo: {
            root: {
              childNodes: {
                10000001007: {
                  groupid: null,
                  showcheck: 'true',
                  expand: 'false',
                  dataid: 'root●10000001007',
                  haschild: 'false',
                  checked: 'false',
                  disabled: 'false',
                  id: '10000001007',
                  text: '121212121',
                  href: null,
                  complete: 'false',
                  value: '10000001007',
                  order: '0',
                },
                10000001006: {
                  groupid: null,
                  showcheck: 'true',
                  expand: 'false',
                  dataid: 'root●10000001006',
                  haschild: 'false',
                  checked: 'false',
                  disabled: 'false',
                  id: '10000001006',
                  text: '222',
                  href: null,
                  complete: 'false',
                  value: '10000001006',
                  order: '2',
                },
                10000001003: {
                  groupid: null,
                  showcheck: 'true',
                  expand: 'false',
                  dataid: 'root●10000001003',
                  haschild: 'false',
                  checked: 'false',
                  disabled: 'false',
                  id: '10000001003',
                  text: '1111',
                  href: null,
                  complete: 'false',
                  value: '10000001003',
                  order: '3',
                },
                10000001008: {
                  groupid: null,
                  showcheck: 'true',
                  expand: 'false',
                  dataid: 'root●10000001008',
                  haschild: 'false',
                  checked: 'false',
                  disabled: 'false',
                  id: '10000001008',
                  text: '121212121o',
                  href: null,
                  complete: 'false',
                  value: '10000001008',
                  order: '1',
                },
              },
              groupid: null,
              showcheck: 'false',
              expand: 'false',
              dataid: 'root',
              haschild: 'true',
              checked: 'false',
              disabled: 'false',
              id: 'root',
              text: '测试集团1793008',
              href: null,
              complete: 'false',
              value: 'root',
              order: '0',
            },
          },
        },
        success: true,
      },
    });
  },
};

export default delay(proxy, defaultSettings.delay);
