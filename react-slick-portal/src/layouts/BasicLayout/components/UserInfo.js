import React, { useState } from 'react';
import { Modal, Row, Col, Icon, message, Button } from 'antd';
import classNames from 'classnames';
import { connect } from 'dva';
import router from 'umi/router';
import { useBoolean } from '@umijs/hooks';
import delay from 'lodash/delay';
import find from 'lodash/find';
import ChangePassword from '@/pages/User/components/ChangePassword';
import CollectionManageDrawer from '@/layouts/BlueStyleLayout/components/CollectionManageDrawer';
import AvatarComponent from '@/components/AvatarComponent';
import DownCenter from './DownCenter';
import request from '@/utils/request';
import '../less/menuDrawer.less';
import styles from '../less/downCenter.less';

function UserInfo({
  dispatch,
  user: { userInfo, loginIp, orgInfo, portalRoles, tenantList },
  showUserInfo,
  all,
}) {
  const { userCode, roleId, tenantId } = userInfo;
  const { orgName, orgCode } = orgInfo;
  const { postRegionName, postRegionId } = userInfo;
  const { state: visible, setTrue: show, setFalse: hide } = useBoolean(false);
  const {
    state: collectionVisible,
    setTrue: showCollection,
    setFalse: hideCollection,
  } = useBoolean(false);
  const {
    state: avatarComponentVisible,
    setTrue: showAvatarComponent,
    setFalse: hideAvatarComponent,
  } = useBoolean(false);
  const [downVisible, setDownVisible] = useState(false);

  function changeRole(e, roleId) {
    e.preventDefault();
    if (roleId) {
      showUserInfo(false);
      const modal = Modal.confirm({
        title: '切换角色会重新加载页面',
        onOk() {
          // 手动更新ok按钮loading状态
          // onOk返回promise对象时，只有在resolve()之后才会关闭
          modal.update({ okButtonProps: { loading: true } });
          return new Promise(resolve => {
            dispatch({
              type: 'login/changeRole',
              payload: roleId,
            }).then(() => {
              modal.update({ okButtonProps: { loading: true } });
              resolve();
            });
          });
        },
      });
    }
  }

  function changeTenant(e, clickTenantId) {
    e.preventDefault();
    if (tenantId === clickTenantId) {
      return;
    }
    if (clickTenantId) {
      showUserInfo(false);
      const modal = Modal.confirm({
        title: '切换租户会重新加载页面',
        onOk() {
          // 手动更新ok按钮loading状态
          // onOk返回promise对象时，只有在resolve()之后才会关闭
          modal.update({ okButtonProps: { loading: true } });
          return new Promise(resolve => {
            dispatch({
              type: 'login/changeTenant',
              payload: clickTenantId,
            }).then(() => {
              modal.update({ okButtonProps: { loading: true } });
              resolve();
            });
          });
        },
      });
    }
  }

  function getImageInfo(file) {
    const params = {
      userId: userInfo.userId,
      base64Strs: file.base64,
    };
    request('portal/FileStoreController/uploadUserPhoto.do', {
      data: params,
    }).then(result => {
      if (result.resultCode === '0') {
        dispatch({
          type: 'login/updateAvatar',
          payload: {
            avatar: result.resultObject,
          },
        });
      } else {
        message.error(result.resultMsg);
      }
    });
  }

  const handleCancel = () => {
    setDownVisible(false);
  };

  const handleChangeAccountBtnClick = () => {
    showUserInfo(false);
    dispatch({ type: 'fourAAccount/openForEdit' });
  };

  return (
    <div className="slick-dropdown">
      <ul className="dropdown-memu-row row clearfix">
        <Row gutter={16}>
          {/* 如果不存在租户列表 就隐藏租户模块 */}
          <Col xs={Array.isArray(tenantList) ? 6 : 8}>
            <li className="col-sm-3 dropdown-memu-col user-info-col">
              <div className="panel panel-default ">
                <div className="panel-heading">
                  <span className="panel-title">我的信息</span>
                </div>
                <div className="panel-body cv-income-info">
                  <div className="user-info-cont">
                    <div className="user-info-icon">
                      <i className="iconfont icon-gene-recent-actors" />
                    </div>
                    <dl className="user-info-dl">
                      <dt className="user-info-dt">
                        <Icon type="credit-card" style={{ marginRight: 5, color: '#fa9022' }} />
                        账号：
                      </dt>
                      <dd className="user-info-dd">{userCode}</dd>
                      {/* <Button className="margin-left" type="primary" size="small" onClick={handleChangeAccountBtnClick}>切换账号</Button> */}
                    </dl>
                    <dl className="user-info-dl">
                      <dt className="user-info-dt">
                        <Icon type="audit" style={{ marginRight: 5, color: '#72d1f0' }} />
                        区域：
                      </dt>
                      <dd className="user-info-dd">
                        {postRegionName}[{postRegionId}]
                      </dd>
                    </dl>
                    <dl className="user-info-dl">
                      <dt className="user-info-dt">
                        <Icon type="desktop" style={{ marginRight: 5, color: '#40fb99' }} />
                        IP地址：
                      </dt>
                      <dd className="user-info-dd">{loginIp}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </li>
          </Col>
          {/* 如果不存在租户列表 就隐藏租户模块 */}
          {Array.isArray(tenantList) && (
            <Col xs={6}>
              <li className="col-sm-5 dropdown-memu-col">
                <div className="panel panel-default ">
                  <div className="panel-heading">
                    <span className="panel-title">租户切换</span>
                  </div>
                  <div className="panel-body cv-role-info" style={{ overflowY: 'auto' }}>
                    {Array.isArray(tenantList) && tenantList.length > 0 ? (
                      <ul>
                        {tenantList.map(item => (
                          <li
                            data-role="info"
                            key={item.tenantId}
                            className={classNames({ active: tenantId === item.tenantId })}
                          >
                            <a href="#" onClick={e => changeTenant(e, item.tenantId)}>
                              {item.tenantName}
                            </a>
                          </li>
                        ))}
                      </ul>
                    ) : null}
                  </div>
                </div>
              </li>
            </Col>
          )}
          {/* 如果不存在租户列表 就隐藏租户模块 */}
          <Col xs={Array.isArray(tenantList) ? 6 : 8}>
            <li className="col-sm-5 dropdown-memu-col">
              <div className="panel panel-default ">
                <div className="panel-heading">
                  <span className="panel-title">角色切换</span>
                </div>
                <div className="panel-body cv-role-info" style={{ overflowY: 'auto' }}>
                  {portalRoles.length > 0 ? (
                    <ul>
                      {portalRoles.map(item => (
                        <li
                          data-role="info"
                          key={item.sysRoleId}
                          className={classNames({ active: roleId === item.sysRoleId })}
                        >
                          <a href="#" onClick={e => changeRole(e, item.sysRoleId)}>
                            {item.sysRoleName}
                          </a>
                        </li>
                      ))}
                    </ul>
                  ) : null}
                </div>
              </div>
            </li>
          </Col>
          {/* 如果不存在租户列表 就隐藏租户模块 */}
          <Col xs={Array.isArray(tenantList) ? 6 : 8}>
            <li className="col-sm-4 dropdown-memu-col">
              <div className="toper-user-menu">
                <div className="panel panel-default">
                  <div className="panel-heading">
                    <span className="panel-title">我的菜单</span>
                  </div>
                  <div className="panel-body">
                    <ul className="list">
                      <li
                        onClick={() => {
                          showUserInfo(false);
                          delay(() => {
                            showCollection();
                          }, 300);
                        }}
                      >
                        <span className="entrance-tit">菜单收藏</span>
                      </li>
                      {find(all, { urlAddr: '/customize/user' }) === undefined ? null : (
                        <li
                          onClick={() => {
                            showUserInfo(false);
                            delay(() => {
                              router.replace('/customize/user');
                            }, 300);
                          }}
                        >
                          <span className="entrance-tit">布局设置</span>
                        </li>
                      )}
                      {find(all, { urlAddr: '/bulletin/manage' }) === undefined ? null : (
                        <li
                          onClick={() => {
                            showUserInfo(false);
                            delay(() => {
                              router.replace('/bulletin/manage');
                            }, 300);
                          }}
                        >
                          <span className="entrance-tit">公告发布</span>
                        </li>
                      )}
                      <li
                        onClick={() => {
                          showUserInfo(false);
                          delay(() => {
                            show();
                          }, 300);
                        }}
                      >
                        <span className="entrance-tit">密码修改</span>
                      </li>
                      {/* <li>
                        <span className="entrance-tit">密码重置</span>
                      </li> */}
                      {/* <li
                        onClick={() => {
                          showUserInfo(false);
                          delay(() => {
                            showAvatarComponent();
                          }, 300);
                        }}
                      >
                        <span className="entrance-tit">头像修改</span>
                      </li> */}
                      <li
                        onClick={() => {
                          showUserInfo(false);
                          setDownVisible(true);
                          // delay(() => {
                          //   router.replace('/download');
                          // }, 300);
                        }}
                      >
                        <span className="entrance-tit">下载中心</span>
                      </li>
                      <li
                        onClick={() => {
                          showUserInfo(false);
                          router.replace('/');
                          window.scrollTo(0, 0);
                          dispatch({
                            type: 'setting/updateSettings',
                            payload: { guideVisible: true },
                          });
                        }}
                      >
                        <span className="entrance-tit">操作指引</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </li>
          </Col>
        </Row>
      </ul>
      <ChangePassword visible={visible} close={hide} />
      {
        collectionVisible && <CollectionManageDrawer visible={collectionVisible} close={hideCollection} />
      }
      <AvatarComponent
        src={`portal/FileStoreController/dfsReadImage.do?docLink=${userInfo.userPhoto}`}
        getImageInfo={getImageInfo}
        visible={avatarComponentVisible}
        close={hideAvatarComponent}
      />
      <Modal
        title="下载中心"
        visible={downVisible}
        footer={null}
        onCancel={handleCancel}
        destroyOnClose
        width={800}
        className={styles.downCenter}
      >
        <DownCenter />
      </Modal>
    </div>
  );
}

export default connect(({ login, menu }) => ({
  user: login.user,
  all: menu.all,
}))(UserInfo);
