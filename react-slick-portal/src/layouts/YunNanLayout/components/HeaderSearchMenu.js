/* eslint-disable react/no-danger */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/jsx-indent */
/* eslint-disable camelcase */
import React, { useState, useMemo, useRef } from 'react';
import { useDebounceFn } from '@umijs/hooks';
import { Input, Icon, Empty, Modal, Tag, Popconfirm, message } from 'antd';
import memoizeOne from 'memoize-one';
import isEqual from 'lodash/isEqual';
import classNames from 'classnames';
import { connect } from 'dva';
import Trigger from 'rc-trigger';
import Link from 'umi/link';
import { getItem, isEmptyStr, getFinalUrl, openWindowTab } from '@/utils/utils';
import request from '@/utils/request';
// import { uploadSearchContent } from '@/services/menu';
import { quickSearch, handleSearchHistory } from './utils';
import styles from '../less/header.less';
import 'rc-trigger/assets/index.css';
import clearIcon from '../img/clear.png';
import fireIcon from '../img/fire.png';

const _quickSearch = memoizeOne(quickSearch, isEqual);

function renderName(linkName, searchText) {
  return linkName.replace(new RegExp(searchText, 'g'), `<span style='color:#E40077;'}>${searchText}</span>`);
}

function HeaderSearchMenu({
  dispatch,
  menu: { all, appSysCode, taskSysCode },
  showDropDown,
  setShowSearchMenuPanel: setShowDropDown,
  heightSearchList,
  searchHistoryList,
}) {
  // 弹出框打开
  const [visible, setVisible] = useState(false);

  const [modalUrl, setModalUrl] = useState('');
  const [inputValue, setInputValue] = useState();
  const inputRef = useRef();
  const user = getItem('user');
  const { sessionId, userInfo } = user;

  // 按类别排序的所有菜单
  const [allByCategory, setAllByCategory] = useState(_quickSearch(all, '', appSysCode, taskSysCode)); // 搜索结果
  const { run } = useDebounceFn((_all, value, _appSysCode, _taskSysCode) => {
    setAllByCategory(_quickSearch(_all, value, _appSysCode, _taskSysCode));
  }, 300);

  // 菜单打开方式
  async function openMenu(menu) {
    // var MENU_OPEN_MODE_WORK_BENCH_LABEL = "1"; //工作台标签页打开
    // var MENU_OPEN_MODE_POPUP = "2"; //弹出框打开
    // var MENU_OPEN_MODE_NEW_BROWSER = "3"; //新开浏览器打开
    // var MENU_OPEN_MODE_BROWSER_LABEL = "4"; //浏览器新标签打开
    let menuUrl = menu.urlAddr.replace('[iframe]', '');

    const { menuCode } = menu;
    if ((menuCode && menuCode.startsWith('RWGL_MENU_TASK080')) || menu.systemCode === '727023') {
      if (!menu.urlAddr || menu.urlAddr.indexOf('menu_url') === -1) {
        message.warning('链接未配置统一任务视图路径menu_url');
        return null;
      }
      if (!menu.urlAddr || menu.urlAddr.indexOf('source') === -1) {
        message.warning('链接未配置参数source');
        return null;
      }
      if (!userInfo || !userInfo.externalUserInfos) {
        message.warning('无CRM用户信息');
        return null;
      }
      let externalUserInfo = userInfo.externalUserInfos.find(item => item.systemNbr === 'CRM' && item.currentFlag === '1000');
      if (!externalUserInfo) {
        externalUserInfo = userInfo.externalUserInfos.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000');
      }
      if (!externalUserInfo) {
        message.warning('无可用CRM用户信息');
        return null;
      }
      const crmParams = {
        ACCESS_NUM: userInfo.mobilePhone,
        source: 'ztdd',
        userInfo: userInfo.userCode,
        // staffId: externalUserInfo.externalUserCode,
      };
      const crmResponse = await request('orgauth/SystemInfoController/md5Sign.do', {
        method: 'POST',
        data: crmParams,
      });
      if (crmResponse && crmResponse.resultCode === '0') {
        if (menuUrl.indexOf('?') !== -1) {
          menuUrl = `${menuUrl}&userInfo=${userInfo.userCode}&ACCESS_NUM=${userInfo.mobilePhone}&timestamp=${crmResponse.signTimestamp}&sign=${crmResponse.signString}&staffId=${externalUserInfo.externalUserCode}`;
        } else {
          menuUrl = `${menuUrl}?userInfo=${userInfo.userCode}&ACCESS_NUM=${userInfo.mobilePhone}&timestamp=${crmResponse.signTimestamp}&sign=${crmResponse.signString}&staffId=${externalUserInfo.externalUserCode}`;
        }
      } else {
        message.warning('鉴权信息获取失败，跳转失败');
        return null;
      }
    }

    if (menuUrl.indexOf('?') === -1) {
      menuUrl += `?bss3SessionId=${sessionId}`;
    } else {
      menuUrl += `&bss3SessionId=${sessionId}`;
    }

    if (menuUrl.indexOf('{SYS_USER_ID}') !== -1) {
      menuUrl = menuUrl.replace('{SYS_USER_ID}', userInfo.userCode);
    }
    if (menuUrl.indexOf('{SYS_ORG_ID}') !== -1) {
      menuUrl = menuUrl.replace('{SYS_ORG_ID}', userInfo.userOrgId);
    }

    // 采用SSO单点校验方式进行菜单打开
    if (menuUrl.indexOf('ssoType=1') !== -1) {
      // let urlParams = parseUrlParams(menuUrl);
      const urlParams = {
        bssSessionId: sessionId,
      };

      // 菜单地址SSO加密，拼接返回的signString和singTimestamp
      const response = await request(`orgauth/SystemInfoController/calcSign.do?systemInfoId=${menu.systemCode}`, {
        method: 'POST',
        data: urlParams,
      });

      if (response && response.resultCode === '0') {
        menuUrl = `${menuUrl}&signString=${response.signString}&signTimestamp=${response.signTimestamp}`;
      }
    }

    if (menu.menuOpenMode === '4') {
      openWindowTab(menuUrl);
    } else if (menu.menuOpenMode === '3') {
      window.open(menuUrl, menu.menuName, 'width=800,height=600,location=yew,toolbar=yes');
    } else {
      setVisible(true);
      setModalUrl(menuUrl);
      setShowDropDown(false);
    }
  }

  // 负责渲染级联菜单
  function renderMenu(menu) {
    const keyword = inputRef?.current?.props?.value || '';

    // 1100 表示叶子节点
    if (parseInt(menu.menuType, 10) === 1100) {
      if (menu.menuOpenMode !== '1') {
        return (
          <a onClick={() => openMenu(menu)} className={classNames('text-ellipsis')} key={menu.menuId}>
            <span dangerouslySetInnerHTML={{ __html: renderName(menu.menuName, keyword) }} />
          </a>
        );
      }
      return (
        <Link to={getFinalUrl(menu, undefined, user)} onClick={() => setShowDropDown(false)} title={menu.menuName} className="text-ellipsis">
          <span dangerouslySetInnerHTML={{ __html: renderName(menu.menuName, keyword) }} />
        </Link>
      );
    }
    return (
      <a title={menu.menuName} href="#" className="text-ellipsis">
        {menu.menuName}
        <Icon type="right" />
      </a>
    );
  }

  /**
   *
   * @param {object[]} result 格式如下
   * [
   *   {
   *     title: '组织管理',
   *     menus: [
   *       {
   *         firstLetter: 'YGGL',
   *         iconUrl: 'icon-gene-perm-identity',
   *         menuCode: 'TYMH_MENU_019',
   *         menuDesc: '员工管理',
   *         menuId: 276111,
   *         menuIndex: 7,
   *         menuLevel: 2,
   *         menuName: '员工管理',
   *         menuOpenMode: '3',
   *         menuType: '1100',
   *         menuTypeName: '叶子菜单',
   *         parMenuId: 276110,
   *         paramEncryptType: '1000',
   *         privId: 88352,
   *         statusCd: '1000',
   *         systemCode: '727001',
   *         urlAddr: 'orgauth/modules/staff/views/StaffManageView',
   *       },
   *     ],
   *   },
   * ];
   */
  function renderAllByCategory(_all) {
    return _all.length === 0 ? (
      <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ margin: '150px 0 32px' }} />
    ) : (
      <div style={{ columnGap: 24, columnCount: 4 }}>
        {_all.map((group, n) => (
          <div key={`${group.title}_${n}`} style={{ breakInside: 'avoid' }}>
            {/**
             * TODO: 测试环境配置了一样的一级菜单导致key重复，暂时使用索引区分
             */}
            <dl>
              <dt className={styles['letter-title']}>
                <span>{group.title}</span>
              </dt>
              <dd>
                <ul>
                  {group.menus.map((menu, i) => (
                    <li className={styles['ng-item']} key={`${menu.menuId}_${i}`}>
                      {renderMenu(menu)}
                    </li>
                  ))}
                </ul>
              </dd>
            </dl>
          </div>
        ))}
      </div>
    );
  }

  const menuList = useMemo(() => renderAllByCategory(allByCategory, setShowDropDown), [allByCategory]);

  const onSearch = async text => {
    if (!showDropDown) {
      setShowDropDown(true);
    }
    run(all, text, appSysCode, taskSysCode);
    if (!isEmptyStr(text)) {
      // await uploadSearchContent(text);

      const newHistory = handleSearchHistory('add', text);
      dispatch({ type: 'menu/updateSearchHistory', payload: newHistory });
    }
    // getRecommendList();
  };

  const setAndSearch = text => {
    setInputValue(text);
    onSearch(text);
  };

  const content = (
    <div className={styles.searchDropdownBox}>
      <div className={styles.row}>
        <div className={styles.searchRecommend}>
          <div className={styles.unit}>
            <div className="clearfix">
              <div className="pull-left">
                <span className={styles.title}>历史搜索关键字</span>
              </div>
              <div className="pull-right">
                <Popconfirm
                  title="确定清空吗？"
                  onConfirm={() => {
                    handleSearchHistory('clear');

                    dispatch({ type: 'menu/updateSearchHistory', payload: [] });
                  }}
                >
                  <span className={styles.clearAll}>
                    <img alt="" src={clearIcon} width={12} height={12} className="margin-right" />
                    清空历史搜索
                  </span>
                </Popconfirm>
              </div>
            </div>
            <div>
              {searchHistoryList?.length > 0 ? (
                searchHistoryList.map(item => (
                  <Tag
                    className={classNames(styles.label)}
                    closable
                    onClose={() => {
                      const newHistory = handleSearchHistory('delete', item?.id);
                      dispatch({ type: 'menu/updateSearchHistory', payload: newHistory });
                    }}
                    onClick={() => {
                      setAndSearch(item?.searchContent);
                    }}
                    key={item.id}
                  >
                    {item.searchContent}
                  </Tag>
                ))
              ) : (
                <span style={{ color: '#999' }}>无</span>
              )}
            </div>
          </div>

          <div className={styles.unit}>
            <span className={styles.title}>推荐搜索关键字</span>
            <div>
              {heightSearchList?.length > 0 ? (
                heightSearchList.map(item => (
                  <Tag
                    className={classNames(styles.label, styles.yellow)}
                    onClick={() => {
                      setAndSearch(item?.searchContent);
                    }}
                    key={item.searchContent}
                  >
                    <img alt="" src={fireIcon} width={8} height={10} className="margin-right-sm" /> {item.searchContent}
                  </Tag>
                ))
              ) : (
                <span style={{ color: '#999' }}>无</span>
              )}
            </div>
          </div>
        </div>
        <div className={styles['letter-panel']}>
          <div className={classNames(styles['letter-category'], 'clearfix')}>{menuList}</div>
        </div>
      </div>
      <Modal visible={visible} onCancel={() => setVisible(false)} footer={null} width="800px">
        <iframe title="title" src={modalUrl} height="600px" width="752px" />
      </Modal>
    </div>
  );

  return (
    // <div
    //   className={styles.headerSearchMenuWrap}
    //   onMouseLeave={() => {
    //     inputRef.current.blur();
    //     setShowDropDown(false);
    //   }}
    // >
    <Trigger
      popupPlacement="bottomLeft"
      action={['hover']}
      popupAlign={{
        overflow: {
          adjustX: 1,
          adjustY: 1,
        },
      }}
      // getPopupContainer={() => document.getElementById('header_windows')}
      mouseEnterDelay={0.3}
      popupClassName={styles.headerSearchMenuWrap}
      builtinPlacements={{
        bottomLeft: {
          points: ['tl', 'bl'],
        },
      }}
      popupVisible={showDropDown}
      // popupTransitionName="slide-up"
      getPopupContainer={trigger => trigger.parentNode}
      popup={content}
      onPopupVisibleChange={isVisible => setShowDropDown(isVisible)}
      onPopupAlign={() => {
        // $(popupDomNode).css({
        //   left: '0',
        //   top: '60px',
        //   right: '0',
        //   position: 'fixed',
        // })
        // 防止被layout-tabs-nav遮挡
        document.querySelector('.rc-trigger-popup').parentNode.parentNode.style.zIndex = 1000;
      }}
    >
      <div className={styles.search} id="header_search">
        <Input.Search
          className={styles.searchInput}
          allowClear
          value={inputValue}
          ref={inputRef}
          placeholder="搜索全门户内容"
          // onClick={() => {
          //   setShowDropDown(true);
          // }}
          onSearch={onSearch}
          onChange={e => {
            setInputValue(e.target.value);
          }}
        />
      </div>
    </Trigger>
    // <Popover
    //   content={content}
    //   overlayClassName={styles.headerSearchMenuWrap}
    //   placement="bottomRight"
    //   trigger="hover"
    //   overlayStyle={{ top: '50px', width: '1000px', paddingTop: 0 }}
    //   // getPopupContainer={trigger => trigger.parentNode}
    //   // visible={showDropDown}
    // >
    //   <div className={styles.search}>
    //     <Input.Search
    //       className={styles.searchInput}
    //       allowClear
    //       value={inputValue}
    //       ref={inputRef}
    //       placeholder="搜索全门户内容"
    //       onClick={() => {
    //         setShowDropDown(true);
    //       }}
    //       onSearch={onSearch}
    //       onChange={e => {
    //         setInputValue(e.target.value);
    //       }}
    //     />
    //   </div>
    // </Popover>
    // </div>
  );
}

export default connect(({ menu, loading }) => ({
  menu,
  heightSearchList: menu.heightSearchList,
  searchHistoryList: menu.searchHistoryList,
  refreshLoading: loading.effects['menu/refreshMenu'],
}))(HeaderSearchMenu);
