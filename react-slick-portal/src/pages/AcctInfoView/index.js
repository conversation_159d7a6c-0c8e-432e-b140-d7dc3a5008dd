import React, { useState } from 'react';
import { Card, Form, Row, Col, Input, Button, Divider, Select, message, Descriptions, Spin, List, Modal } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { ACCT_CLASS, ACCT_PAY_TYPE, ACCT_STATUS, ACCT_TYPE, CUST_CREDIT_LEVEL } from '@/utils/consts';
import style from './index.less';
import { qryAccount, getAcctPaySubscriberAndAccNum, getGroupNameByCustId } from '@/pages/AcctInfoView/service';
import SlickTable from '@/components/SlickTable';

const Index = ({ size: { height }, form }) => {
  const { getFieldDecorator } = form;
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [accNumList, setAccNumList] = useState([]);
  // 根据custId获取custName
  const qryGroupNameByCustId = custId =>
    new Promise(resolve => {
      getGroupNameByCustId(custId)
        .then(res => {
          const { resultCode, resultObject } = res;
          if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
            resolve(resultObject?.rspParam?.busiInfo?.outData?.custName || '');
          } else {
            resolve('');
          }
        })
        .catch(() => {
          resolve('');
        });
    });
  const getTableData = params => {
    setLoading(true);
    qryAccount({
      data: {
        queryType: params.searchType,
        [params.searchType]: params.searchValue,
      },
    })
      .then(async res => {
        const { resultCode, resultObject, resultMsg } = res;
        // eslint-disable-next-line camelcase
        if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.result?.acct_list) {
          // eslint-disable-next-line camelcase
          const list = resultObject?.rspParam?.busiInfo?.result?.acct_list || [];
          // 使用 Promise.all 等待所有客户名称查询完成
          /* const listWithNames = await Promise.all(
            list.map(async item => {
              const custName = await qryGroupNameByCustId(item.custId);
              return {
                ...item,
                custName,
              };
            })
          ); */
          setDataSource(list || []);
        } else {
          message.error(resultMsg);
        }
      })
      .always(() => {
        setLoading(false);
      });
  };
  const handleSearch = () => {
    form.validateFields((err, values) => {
      if (!err) {
        getTableData(values);
      }
    });
  };

  const openModal = record => {
    setModalLoading(true);
    setVisible(true);
    getAcctPaySubscriberAndAccNum({
      acctId: record.acctId,
    })
      .then(res => {
        const { resultCode, resultObject, resultMsg } = res;
        if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
          const info = resultObject?.rspParam?.busiInfo?.outData || [];
          setAccNumList(info);
        } else {
          message.error(resultMsg);
        }
      })
      .always(() => {
        setModalLoading(false);
      });
  };

  /**
   * 渲染子项
   * @param record
   * @returns {Element}
   */

  const renderOrderItem = record => (
    <List.Item className={style.orderListItem} key={record.acctId}>
      <div className={style.orderItemContainer}>
        <div className={style.orderHeader}>
          <div className={style.orderHeaderLeft}>
            <span>账户名称：{record.acctName}</span>
            <span style={{ marginLeft: '10px' }}>账户编号：{record.acctId}</span>
          </div>
          <div className={style.orderHeaderRight}>
            <span className={style.orderIdLabel}>
              <Button
                type="link"
                onClick={() => {
                  openModal(record);
                }}
              >
                查看账户代付费手机号码信息
              </Button>
            </span>
          </div>
        </div>
        <div className={style.orderContent}>
          <Descriptions column={3} size="small" bordered={false} className={style.orderDescriptions}>
            <Descriptions.Item label="账户类型">{ACCT_TYPE[record.acctType] || '-'}</Descriptions.Item>
            <Descriptions.Item label="客户名称">{record.custName || '-'}</Descriptions.Item>
            <Descriptions.Item label="账户状态">{ACCT_STATUS[record.acctStatus]}</Descriptions.Item>
            <Descriptions.Item label="账户级别">{ACCT_CLASS[record.acctClass]}</Descriptions.Item>
            <Descriptions.Item label="账户下手机号码" span={3}>
              {record?.accessNum}
            </Descriptions.Item>
          </Descriptions>
          <Divider />
          <Descriptions column={3} className={style.orderDescriptions}>
            {/* todo */}
            <Descriptions.Item label="归属客户编号">{record?.custId}</Descriptions.Item>
            <Descriptions.Item label="消费限额">{record?.LIMIT_FEE}</Descriptions.Item>
            <Descriptions.Item label="信用级别">{CUST_CREDIT_LEVEL[record?.creditLevel]}</Descriptions.Item>
            <Descriptions.Item label="信用额度">{record?.creditScore}元</Descriptions.Item>
            <Descriptions.Item label="付费方式">{ACCT_PAY_TYPE[record?.payType] || '-'}</Descriptions.Item>
            <Descriptions.Item label="创建时间">{record.createDate || '-'}</Descriptions.Item>
            <Descriptions.Item label="状态时间">{record.statusDate || '-'}</Descriptions.Item>
          </Descriptions>
        </div>
      </div>
    </List.Item>
  );

  return (
    <>
      <Card title="账户信息查询" className="cute" style={{ minHeight: height }} bordered>
        <Form className="flow fix-label">
          <Row gutter={16}>
            <Col offset={16} span={8} className="text-right">
              <div style={{ display: 'flex' }}>
                <Form.Item style={{ marginBottom: 0 }}>
                  {getFieldDecorator('searchType', {
                    initialValue: 'acctId', // 默认查询【账户编码】
                  })(
                    <Select placeholder="请选择">
                      <Select.Option key="accessNum" value="accessNum">
                        手机号码
                      </Select.Option>
                      <Select.Option key="acctId" value="acctId">
                        账户编号
                      </Select.Option>
                    </Select>
                  )}
                </Form.Item>
                <Form.Item>
                  {getFieldDecorator('searchValue', {
                    rules: [{ required: true, message: '请输入' }],
                  })(<Input allowClear placeholder="请输入" />)}
                </Form.Item>
                <Button className="margin-left" type="primary" onClick={handleSearch}>
                  查询
                </Button>
                <Button className="margin-left" onClick={() => form.resetFields(['searchValue'])}>
                  重置
                </Button>
              </div>
            </Col>
          </Row>
        </Form>
        <div className={style.orderListContainer}>
          <Spin spinning={loading}>
            <List
              rowKey="ORDER_ID"
              dataSource={dataSource}
              renderItem={renderOrderItem}
              pagination={{
                pageSize: 10,
                showQuickJumper: true,
                showSizeChanger: true,
                showTotal: total => `共 ${total} 条记录`,
              }}
            />
          </Spin>
        </div>
      </Card>
      <Modal visible={visible} width="800px" footer={null} onCancel={() => setVisible(false)} title="账户代付费手机号码信息">
        <div style={{ height: 450 }}>
          <SlickTable
            loading={modalLoading}
            className={style.serviceTable}
            columns={[
              {
                title: '服务号码',
                dataIndex: 'ACCESS_NUM',
                key: 'accessNum',
              },
              {
                title: '用户实例编码',
                dataIndex: 'SUBSCRIBER_INS_ID',
                key: 'subscriberInsId',
              },
              {
                title: '产品线',
                dataIndex: 'PROD_LINE_NAME',
                key: 'prodLineName',
              },
              {
                title: '生效时间',
                dataIndex: 'VALID_DATE',
                key: 'validDate',
                render: val => moment(val).format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                title: '失效时间',
                dataIndex: 'EXPIRE_DATE',
                key: 'validDate',
                render: val => moment(val).format('YYYY-MM-DD HH:mm:ss'),
              },
            ]}
            dataSource={accNumList}
            bordered
          />
        </div>
      </Modal>
    </>
  );
};

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(Index));
