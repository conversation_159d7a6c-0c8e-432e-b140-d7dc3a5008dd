import React from 'react';
import { connect } from 'dva';
import { Form } from 'antd';
import { defaultEditorConfig, defaultToolbarConfig } from '../const';
import WangEditor from './WangEditor';

function RelSystemsUser(props) {
  const { editor, setEditor, height } = props;

  return (
    <>
      <WangEditor
        editor={editor}
        setEditor={setEditor}
        editorConfig={defaultEditorConfig}
        toolbarConfig={defaultToolbarConfig}
        editorParams={
          {
            minHeight: height - 256,
            height: height - 256,
          }
        }
      />

    </>
  );
}

export default connect(({ operationManage }) => ({
  operationDocConfId: operationManage.operationDocConfId,
  behavior: operationManage.behavior,
  isLeaf: operationManage.isLeaf,
  menuData: operationManage.menuData,
}))(Form.create()(RelSystemsUser));
