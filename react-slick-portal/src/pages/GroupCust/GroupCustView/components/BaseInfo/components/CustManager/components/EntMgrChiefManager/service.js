import request from '@/utils/request';

// 首席客户经理查询
export function queryChiefCustomerManagerList(data) {
  return request('portal/ChiefCustomerManagerController/queryChiefCustomerManagerList.do', {
    method: 'POST',
    data,
  });
}

// 首席客户经理新增
export function insertChiefCustomerManager(data) {
  return request('portal/ChiefCustomerManagerController/insertChiefCustomerManager.do', {
    method: 'POST',
    data,
  });
}

// 首席客户经理修改
export function updateChiefCustomerManager(data) {
  return request('portal/ChiefCustomerManagerController/updateChiefCustomerManager.do', {
    method: 'POST',
    data,
  });
}
// 首席客户经理删除
export function deleteChiefCustomerManager(data) {
  return request('portal/ChiefCustomerManagerController/deleteChiefCustomerManager.do', {
    method: 'POST',
    data,
  });
}


// 部门信息查询
export function queryChiefCustomerDepList(data) {
  return request('portal/ChiefCustomerManagerController/queryChiefCustomerDepList.do', {
    method: 'POST',
    data,
  });
}
