.content {
  margin-top: 16px;
  padding: 16px;
  background: #F9FAFB;
  border-radius: 4px 4px 4px 4px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 16px;

    .left {
      display: flex;
      gap: 16px;

      .title {
        font-weight: 500;
        font-size: 14px;
        color: #222222;
        line-height: 24px;
      }

      .tag {
        height: 22px;
        background: #F0F0F0;
        border-radius: 2px;
        border: 1px solid #D9D9D9;
        padding: 0 8px;
        font-family: <PERSON><PERSON>, Robot<PERSON>;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 20px;
      }
    }

  }

  .detail {
    background: linear-gradient(90deg, #F5F5FF 0%, #EDFAFD 100%);
    border-radius: 0px 0px 8px 8px;
    padding: 20px;

    :global {
      .ant-descriptions {


        .ant-descriptions-item-content {
          font-weight: 400;
          font-size: 12px;
          color: #0085D0;
          text-align: left;
        }

      }
    }
  }

  :global {
    .ant-descriptions {

      .ant-descriptions-item-label {
        text-align: right;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
      }

      .ant-descriptions-item-content {
        font-weight: 400;
        font-size: 12px;
        color: #666666;
      }

      .ant-descriptions-item {
        padding-bottom: 8px;
      }
    }
  }
}

.container {
  :global {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-card {
      margin-bottom: 16px;
    }

    .ant-table-wrapper {
      margin-top: 16px;
    }

    .ant-btn + .ant-btn {
      margin-left: 8px;
    }
  }
}
