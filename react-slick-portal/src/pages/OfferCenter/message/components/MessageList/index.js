/* eslint-disable global-require */
/* eslint-disable react/no-array-index-key */
import React, { useEffect, useState } from 'react';
import classnames from 'classnames';
import {
  Button, Empty, Icon, Modal, Table,
} from 'antd';
import { connect } from 'dva';
import MessageDetail from '../MessageDetail';
import style from './MessageList.module.less';
import { modifyMessage, modifyAllMessage, qryConMessageUsersGridData } from '@/services/message';
import usePageTable from '@/hooks/usePageTable';

const MessageList = props => {
  const { type, typeCount, messageTypes, dispatch } = props;
  const [currentType, setCurrentType] = useState('0');
  const [visible, setVisible] = useState(false);
  const [currentDetail, setCurrentDetail] = useState({});
  const [selectedRowKeys, setSelectRowKeys] = useState([]);
  const [selectedRow, setSelectRow] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [detailLoading, setDetailLoading] = useState(false);

  const {
    pagination, list, loading, getTableData, asyncFormChange, setPageNo,
  } = usePageTable({
    method: qryConMessageUsersGridData,
    pageParamsKey: { index: 'pageNum', size: 'pageSize' },
    pageSize: 10,
    pageType: 1,
    initLoadData: true,
  });

  const onShowDetail = r => {
    setCurrentDetail(r);
    setVisible(true);
  };

  useEffect(() => {
    if (visible) {
      document.body.style.overflowY = 'hidden';
    } else {
      document.body.style.overflowY = 'auto';
    }
  }, [visible])

  useEffect(() => {
    document.body.style.background = '#fff';
    return () => {
      document.body.style.background = '#f0f2f5';
    }
  }, [])

  useEffect(() => {
    const req = {};
    messageTypes.forEach(item => {
      if (currentType === item.messageTypeCode) {
        req.messageType = item.messageTypeCode;
      }
      if (!req.messageType) {
        req.messageType = '';
      }
    });
    if (type === 'read') {
      req.statusCd = '1100';
    } else if (type === 'unread') {
      req.statusCd = '1000';
    } else {
      req.statusCd = '';
    }
    setPageNo(1);
    asyncFormChange(req);
  }, [currentType, type]);

  const renderItem = (v, r, index) => {
    const { statusCd } = r;
    if (statusCd === "1100") {
      return (
        <span
          className={style.isRead}
          onClick={() => {
            onShowDetail(r);
            setCurrentIndex(index);
          }}
        >
          {
            r.conMessage.topFlag === '1' && (
              <Icon
                type="vertical-align-top"
                style={{ fontSize: '16px', color: '#0382c1', paddingRight: '10px' }}
              />
            )
          }
          {v}
        </span>
      );
    }
    return (
      <span
        className={style.unRead}
        onClick={() => {
          onShowDetail(r);
          setCurrentIndex(index);
        }}
      >
        {
          r.conMessage.topFlag === '1' && (
            <Icon
              type="vertical-align-top"
              style={{ fontSize: '16px', color: '#0382c1', paddingRight: '10px' }}
            />
          )
        }
        {v}
      </span>
    );
  };

  const renderDateItem = (v, r) => {
    const { statusCd } = r;
    if (statusCd === "1100") {
      return <span className={style.isRead}>{v}</span>;
    }
    return <span className={style.unReadDate}>{v}</span>;
  };

  // 删除 已读 已选消息
  const handleMessage = async handleType => {
    const tmp = [];
    selectedRowKeys.forEach(rowKey => {
      const curRow = selectedRow.filter(item => {
        return item.messageId === rowKey;
      });
      if (handleType === 'read') {
        if (curRow[0]?.statusCd !== '1100') {
          tmp.push({
            messageUsersId: curRow[0]?.messageUsersId,
          })
        }
      } else {
        tmp.push({
          messageUsersId: curRow[0]?.messageUsersId,
        })
      }
    });
    if (await modifyMessage(handleType, tmp)) {
      setSelectRowKeys([]);
      setSelectRow([]);
      getTableData();
      dispatch({
        type: 'message/getMessageType',
      });
    }
  };

  // 标记已读置灰
  const readBtnDisable = () => {
    let disable = true;
    if (selectedRowKeys.length === 0) {
      disable = true;
    }
    selectedRow.forEach(row => {
      if (row.statusCd !== '1100') {
        disable = false;
      }
    })
    return disable;
  };

  return (
    <div className={style.container}>
      <div className={style.top}>
        <span className={style.title}>全部公告</span>
        <div className={style.typeContainer}>
          <span className={style.subTitle}>公告分类：</span>
          <ul className={style.navList}>
            <li
              onClick={() => {
                setCurrentType('0');
              }}
              className={currentType === '0' ? style.check : null}
            >
              全部(
              {type === 'all' && typeCount.all}
              {type === 'unread' && typeCount.unread}
              {type === 'read' && typeCount.read}
              )
            </li>
            {
              messageTypes.map((message, key) => {
                const {
                  messageTypeName, meassageRead, messageUnRead, messageTypeCode
                } = message;
                return (
                  <li
                    key={key}
                    onClick={() => {
                      setCurrentType(messageTypeCode);
                    }}
                    className={currentType === messageTypeCode ? style.check : null}
                  >
                    {type === 'all' && `${messageTypeName}(${meassageRead + messageUnRead})`}
                    {type === 'read' && `${messageTypeName}(${meassageRead})`}
                    {type === 'unread' && `${messageTypeName}(${messageUnRead})`}
                  </li>
                );
              })
            }
          </ul>
        </div>
      </div>
      <div className={style.main}>
        <div className={style.btnList}>
          <Button
            type="primary"
            disabled={selectedRowKeys.length === 0}
            onClick={() => {
                Modal.confirm({
                  title: '请确认是否删除',
                  onOk: () => { handleMessage('del'); },
                });
              }}
          >
            删除
          </Button>
          <Button
            type="primary"
            disabled={readBtnDisable()}
            onClick={() => { handleMessage('read'); }}
          >
            标记已读
          </Button>
          <Button
            type="primary"
            disabled={list.length === 0}
            onClick={() => {
              Modal.confirm({
                title: '您是否确认删除全部消息',
                onOk: async () => {
                  const res = await modifyAllMessage('del');
                  if (res) {
                    getTableData();
                    dispatch({
                      type: 'message/getMessageType',
                    });
                  }
                },
              });
            }}
          >
            全部删除
          </Button>
          <Button
            type="primary"
            disabled={list.length === 0}
            onClick={() => {
              Modal.confirm({
                title: '您确认将全部消息标记为已读',
                onOk: async () => {
                  const res = await modifyAllMessage('read');
                  if (res) {
                    getTableData();
                    dispatch({
                      type: 'message/getMessageType',
                    });
                  }
                },
              });
            }}
          >
            全部已读
          </Button>
        </div>
        <div className={classnames('table-wrap', style.table)}>
          {
            list.length > 0 ? (
              <Table
                pagination={{
                  ...pagination,
                  pageSizeOptions: ['5', '10', '20']
                }}
                loading={loading}
                rowKey="messageId"
                columns={[
                  {
                    title: '标题内容', dataIndex: 'conMessage.messageTitle', render: renderItem
                  },
                  {
                    title: '提交时间', dataIndex: 'conMessage.createDate', width: '20%', render: renderDateItem
                  },
                ]}
                dataSource={list}
                rowSelection={{
                  type: 'checkbox',
                  selectedRowKeys,
                  onChange: (v, r) => {
                    setSelectRowKeys(v);
                    setSelectRow(r);
                  },
                }}
              />
            )
            : (
              <Empty
                description="您好，还没有消息哦！"
              />
            )
          }
        </div>
      </div>
      <MessageDetail
        visible={visible}
        detail={currentDetail}
        onClose={() => {
          setVisible(false);
          getTableData();
        }}
        onPrev={() => {
          if (currentIndex !== 0) {
            setDetailLoading(true);
            setCurrentIndex(currentIndex - 1);
            setCurrentDetail(list[currentIndex - 1]);
          }
        }}
        onNext={() => {
          if (currentIndex !== list.length - 1) {
            setDetailLoading(true);
            setCurrentIndex(currentIndex + 1);
            setCurrentDetail(list[currentIndex + 1]);
          }
        }}
        length={list.length}
        currentIndex={currentIndex}
        setDetailLoading={setDetailLoading}
        detailLoading={detailLoading}
      />
    </div>
  )
}

export default connect()(MessageList);
