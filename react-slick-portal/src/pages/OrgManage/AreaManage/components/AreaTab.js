import React, { useState, useEffect, useContext } from 'react';
import { Row, Col, Card, Form, Input, Icon, Button, Table, Tabs, Modal } from 'antd';
import LoadingComponent from '@/components/PageLoading/index';
import dynamic from 'umi/dynamic';
const { TabPane } = Tabs;
import classNames from 'classnames';
import styles from '../styles.less';
import ScrollBar from '@/components/ScrollBar';

// 涉及到的标签组件按需加载
const AreaInfo = dynamic({
  loader: () => import('./AreaInfo'),
  loading: LoadingComponent,
});
const ReleUser = dynamic({
  loader: () => import('./ReleUser'),
  loading: LoadingComponent,
});

function Tab(props) {
  const [menuKey, setMenuKey] = useState('AreaInfo');
  const [contentList, setContentList] = useState({});

  const tabList = [
    {
      key: 'AreaInfo',
      tab: '区域信息',
    },
    {
      key: 'ReleUser',
      tab: '关联用户',
    },
  ];

  useEffect(() => {
    onTabChange('AreaInfo');
  }, []);

  useEffect(() => {
    if (props.behavior !== 'disabled') {
      onTabChange('AreaInfo');
    }
  }, [props.behavior]);

  const onTabChange = key => {
    const contentMap = {
      AreaInfo: <AreaInfo />,
      ReleUser: <ReleUser />,
    };
    if (!contentList[key]) {
      contentList[key] = contentMap[key];
    }
    setContentList(contentList);
    setMenuKey(key);
  };

  return (
    <div>
      <Card
        className="gb_tabs_default"
        style={{ width: '100%', height: `${props.height}px` }}
        tabList={tabList}
        activeTabKey={menuKey}
        onTabChange={key => {
          onTabChange(key);
        }}
      >
        <div className={styles.area_tab}>
          <ScrollBar autoHide autoHeight autoHeightMax={props.height - 72}>
            {Object.keys(contentList).map(key => (
              <div
                key={key}
                style={{ display: menuKey === key ? 'block' : 'none', margin: '0 16px' }}
              >
                {contentList[key]}
              </div>
            ))}
          </ScrollBar>
        </div>
      </Card>
    </div>
  );
}
export default Tab;
