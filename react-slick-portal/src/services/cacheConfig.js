import request from '@/utils/request';

/**
 * 获取缓存列表
 * @param {object} params
 */
export async function selectCacheList(params) {
  return request('orgauth/CacheConfigController/cacheList.do', {
    data: params,
  });
}

/**
 * 创建redis
 * @param {object} params
 */
export async function createByKeyType(params) {
  return request('orgauth/CacheConfigController/createByKeyType.do', {
    data: params,
  });
}

/**
 * 删除redis
 * @param {object} params
 */
export async function delByKeyType(params) {
  return request('orgauth/CacheConfigController/delByKeyType.do', {
    data: params,
  });
}

/**
 * 批量删除redis
 * @param {object} params
 */
export async function batchDelete(params) {
  return request('orgauth/CacheConfigController/batchDelete.do', {
    data: params,
  });
}

/**
 * 查询redis详情
 * @param {object} params
 */
export async function queryCacheByKeyType(params) {
  return request('orgauth/CacheConfigController/queryCacheByKeyType.do', {
    data: params,
  });
}

/**
 * 加密
 * @param {object} encryptInfo 要加密的信息
 */
export async function encrypt(params) {
  return request('orgauth/CacheConfigController/encrypt.do', {
    data: params,
  });
}

/**
 * 公共区域模块缓存刷新
 * @param {object} params
 */
export async function reloadCommonRegionCache() {
  return request('orgauth/CommonRegionController/reloadRegionTree2Cache.do', {
    method: 'GET',
  });
}

/**
 * 公共属性模块缓存刷新
 * @param {object} params
 */
export async function reloadDomainDataCache() {
  return request('portal/DomainDataController/reloadDomainData2Cache.do', {
    method: 'GET',
  });
}

/**
 * 系统参数模块缓存刷新
 * @param {object} params
 */
export async function reloadDataDictCache() {
  return request('portal/DataDictController/reload.do', {
    method: 'GET',
  });
}

/**
 * 权限模块缓存刷新-全量
 * @param {object} params
 */
export async function loadPrivilegeCache() {
  return request('orgauth/PrivilegeCacheController/loadPrivilege2Cache.do', {
    method: 'GET',
  });
}

/**
 * 权限模块缓存刷新-增量
 * @param {object} params
 */
export async function loadIncrementalPrivilegeCache() {
  return request('orgauth/PrivilegeCacheController/loadPrivilege2Cache.do?isIncr=true', {
    method: 'GET',
  });
}

/**
 * 权限模块缓存刷新-两分钟内
 * @param {object} params
 */
export async function reloadPrivilege2Cache() {
  return request('orgauth/PrivilegeCacheController/reloadPrivilege2Cache.do', {
    method: 'GET',
  });
}
