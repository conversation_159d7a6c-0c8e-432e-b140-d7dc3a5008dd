/* eslint-disable */
.textOverflow() {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}

.textOverflowMulti(@line: 3, @bg: #fff) {
  position: relative;
  max-height: @line * 1.5em;
  margin-right: -1em;
  padding-right: 1em;
  overflow: hidden;
  line-height: 1.5em;
  text-align: justify;
  &::before {
    position: absolute;
    right: 14px;
    bottom: 0;
    padding: 0 1px;
    background: @bg;
    content: '...';
  }
  &::after {
    position: absolute;
    right: 14px;
    width: 1em;
    height: 1em;
    margin-top: 0.2em;
    background: white;
    content: '';
  }
}

// mixins for clearfix
// ------------------------
.clearfix() {
  zoom: 1;
  &::before,
  &::after {
    display: table;
    content: ' ';
  }
  &::after {
    clear: both;
    height: 0;
    font-size: 0;
    visibility: hidden;
  }
}

.gutterUtils(@type:margin; @gutter: 8px; ) {
  .temp(@type; @slick-space-base ) {
    .@{type} {
      @{type}: @slick-space-base !important;
    }
    .@{type}-left {
      @{type}-left: @slick-space-base !important;
    }
    .@{type}-top {
      @{type}-top: @slick-space-base !important;
    }
    .@{type}-right {
      @{type}-right: @slick-space-base !important;
    }
    .@{type}-bottom {
      @{type}-bottom: @slick-space-base !important;
    }
    .@{type}-h {
      @{type}: 0 @slick-space-base !important;
    }
    .@{type}-z {
      @{type}: @slick-space-base 0 !important;
    }
  }

  .temp(@type; @slick-space-base; @size;) when (@size=lg) or (@size=sm) {
    .@{type}-@{size} {
      @{type}: @slick-space-base !important;
    }
    .@{type}-left-@{size} {
      @{type}-left: @slick-space-base !important;
    }
    .@{type}-top-@{size} {
      @{type}-top: @slick-space-base !important;
    }
    .@{type}-right-@{size} {
      @{type}-right: @slick-space-base !important;
    }
    .@{type}-bottom-@{size} {
      @{type}-bottom: @slick-space-base !important;
    }
    .@{type}-h-@{size} {
      @{type}: 0 @slick-space-base !important;
    }
    .@{type}-z-@{size} {
      @{type}: @slick-space-base 0 !important;
    }
  }

  .temp(@type; @gutter);
  .temp(@type; @gutter + 8px; lg;);
  .temp(@type; @gutter - 4px; sm;);
}
