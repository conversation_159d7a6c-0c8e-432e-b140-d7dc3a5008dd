/* eslint-disable global-require */
import React from 'react';
import { Card, Tooltip, Icon } from 'antd';
import classNames from 'classnames';
import { connect } from 'dva';
import { useBoolean } from '@umijs/hooks';
import imgs from '@/layouts/YunNanLayout/components/NavImg';
// import icon from '../img/iconAdd.png';
import style from './quickAction.less';
import CollectionManageDrawer from '@/layouts/BlueStyleLayout/components/CollectionManageDrawer';
import { openMenu } from '@/utils/utils';

const domId = 'portlet-NewQuickAction';
const NewQuickAction = props => {
  const { collection, initLoading, all, dispatch } = props;
  const { state: collectionVisible, setTrue: showCollection, setFalse: hideCollection } = useBoolean(false);

  // useEffect(() => {
  //   if (collectionVisible) {
  //     document.body.style.overflowY = 'hidden !important';
  //     // document.querySelector('body').classList.add('noScroll');
  //     console.log('hidden1');
  //   } else {
  //     document.body.style.overflowY = 'auto !important';
  //     console.log('auto1');
  //   }
  // }, [collectionVisible]);

  const adderUnit = (
    <div className={style.unit}>
      <Tooltip title="添加快捷" placement="bottom">
        <a
          className={style.quickLink}
          onClick={() => showCollection()}
          // style={{ width: 80 }}
        >
          <div className={style.adder}>
            <Icon type="plus" />
          </div>
          <div className={classNames('text-ellipsis', style.menuName)}>添加快捷</div>
        </a>
      </Tooltip>
    </div>
  );

  const render = () => {
    const allUnitList = [...collection, 'adder'];
    const gap = allUnitList.length % 5;
    const emptyNum = gap === 0 ? 0 : 5 - gap;

    for (let i = 0; i < emptyNum; i += 1) {
      allUnitList.push('empty');
    }

    const rowList = [];
    for (let i = 0; i < allUnitList.length; i += 5) {
      const unitList = allUnitList.slice(i, i + 5);

      const row = (
        <div className={style.row}>
          {unitList.map(menu => {
            if (menu === 'adder') {
              return adderUnit;
            }

            if (menu === 'empty') {
              return <div className={style.unit} />;
            }

            return (
              <div className={style.unit} key={menu.menuId}>
                <Tooltip title={menu.menuName} placement="bottom">
                  <div
                    data-reg-id={`quickLink_${menu.menuCode}`}
                    key={menu.menuCode}
                    className={style.quickLink}
                    onClick={() => {
                      openMenu(menu, all, dispatch);
                    }}
                  >
                    <img
                      alt=""
                      src={imgs[menu?.iconUrl ?? 'icon1'] ?? imgs.icon1}
                      height="28"
                      width="28"
                      style={{ marginRight: 13, marginBottom: 4 }}
                    />
                    <div className={classNames(style.menuName)}>
                      <div className={classNames('text-ellipsis')}>{menu.menuName}</div>
                    </div>
                  </div>
                </Tooltip>
              </div>
            );
          })}
        </div>
      );
      rowList.push(row);
    }
    return rowList;
  };
  return (
    <Card
      title="快捷操作"
      bordered={false}
      // bodyStyle={{ height: '196px', padding: '0 0 16px 0' }}
      loading={initLoading}
      className={style.wrap}
      id={domId}
    >
      {render()}
      {collectionVisible && <CollectionManageDrawer visible={collectionVisible} close={hideCollection} />}
    </Card>
  );
};

export default connect(({ menu, loading }) => ({
  collection: menu.collection,
  all: menu.all,
  initLoading: loading.effects['menu/getInitData'],
}))(NewQuickAction);
