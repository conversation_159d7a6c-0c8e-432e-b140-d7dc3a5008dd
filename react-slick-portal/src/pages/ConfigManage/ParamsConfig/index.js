/* eslint-disable */
import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Select,
  Icon,
  Input,
  Button,
  Tag,
  Modal,
  Divider,
  message,
  Drawer,
} from 'antd';
import { connect } from 'dva';
import SlickTable from '@/components/SlickTable';
// import useSlickTable from '@/hooks/UseSlickTable';
import { useAntdTable } from '@umijs/hooks';
import { getPageSizeByCardHeight } from '@/utils/utils';
import request from '@/utils/request';
import Edit from './components/Edit';
const namespace = 'paramsConfig';
const judgeObj = obj => {
  if (Object.keys(obj).length == 0) return true;
  else return false;
};

function ParamsConfig({ size: { height }, form, dispatch }) {
  const [selectedRows, setSelectedRows] = useState([]);
  const [size, setSize] = useState(getPageSizeByCardHeight(height - 64 - 8));
  const { getFieldDecorator } = form;

  const [paramsObj, setParamsObj] = useState({});
  const [visible, setVisible] = useState(false);
  const [status, setStatus] = useState('edit');

  // 通过dva存当前需要传递的数据
  const savePassData = params => {
    dispatch({
      type: `${namespace}/savePassData`,
      payload: params,
    });
  };

  const getTableData = ({ current, pageSize, paramsObj }) => {
    return request('portal/DataDictController/selectGridData.do', {
      data: {
        page: current,
        rowNum: pageSize,
        pageNum: current,
        pageSize: pageSize,
        sortName: '',
        sortOrder: 'asc',
        filterCol: 'paramCode,paramName,paramValue,groupCode,groupName',
        ...paramsObj,
      },
    }).then(res => {
      if (Array.isArray(res.list)) {
        return {
          total: res.total,
          data: res.list.map(val => {
            const { children, ...rest } = val;
            return rest;
          }),
        };
      }
      return {
        total: 0,
        data: [],
      };
    });
  };

  const { tableProps, refresh } = useAntdTable(
    params => {
      return getTableData({ ...params, pageSize: size, paramsObj });
    },
    [paramsObj, height],
    {
      defaultPageSize: size,
      form,
    }
  );
  const { pagination, ...restTableProps } = tableProps;

  useEffect(() => {
    setSize(getPageSizeByCardHeight(height - 64 - 8));
  }, [height]);

  const submit = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      let obj = {};
      for (let item in fieldsValue) {
        if (fieldsValue[item] != undefined && fieldsValue[item] != '') {
          obj[item] = `%${fieldsValue[item]}%`;
        }
      }
      setParamsObj(obj);
    });
  };

  const handleSelectRows = value => {
    setSelectedRows(value);
  };

  const handleEdit = record => {
    setVisible(true);
    savePassData(record);
    if (judgeObj(record)) {
      setStatus('add');
    } else {
      setStatus('edit');
    }
  };

  const onClose = () => {
    setVisible(false);
  };

  const handleEffect = record => {
    request('portal/DataDictController/update.do', {
      data: { id: record.id, status: record.status === '1' ? '0' : '1' },
      method: 'PUT',
    }).then(res => {
      if (res) {
        refresh();
        record.status === '1' ? message.success('失效成功！') : message.success('生效成功！');
      } else {
        message.error('操作失败！');
      }
    });
  };

  const handleReash = () => {
    // console.log("刷新")
    Modal.confirm({
      content: '确定要刷新缓存吗?',
      onOk() {
        request('portal/DataDictController/reload.do', { method: 'GET' }).then(res => {
          res === true ? message.success('缓存刷新成功！') : message.error('缓存刷新失败！');
        });
      },
    });
  };

  const handleCopy = () => {
    let arr = [];
    selectedRows.map(item => {
      arr.push(item.id);
    });
    request('portal/DataDictController/copy.do', { data: arr }).then(res => {
      if (res && res.length > 0) {
        refresh();
        setSelectedRows([]);
        message.success('复制成功！');
      } else {
        message.error('复制失败！');
      }
    });
  };

  const handleReset = () => {
    form.resetFields();
  };

  const columns = [
    {
      title: '组编码',
      dataIndex: 'groupCode',
      ellipsis: true,
    },
    {
      title: '组名称',
      dataIndex: 'groupName',
      ellipsis: true,
    },
    {
      title: '参数编码',
      dataIndex: 'paramCode',
      ellipsis: true,
    },
    {
      title: '参数值',
      dataIndex: 'paramValue',
      ellipsis: true,
    },
    {
      title: '参数名称',
      dataIndex: 'paramName',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createDate',
      ellipsis: true,
    },
    {
      title: '排序号',
      dataIndex: 'sortIdx',
      width: '100px',
      align: 'center',
    },
    {
      title: '版本',
      dataIndex: 'versionFlag',
      width: '100px',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: '100px',
      align: 'center',
      render: (text, record) => {
        if (record.status === '1') {
          // 有效
          return <span className="text-success">有效</span>;
        } else {
          // 失效
          return <span className="text-danger">无效</span>;
        }
      },
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => {
        return (
          <span>
            <a
              onClick={() => {
                handleEdit(record);
              }}
            >
              编辑
            </a>
            <Divider type="vertical" />
            {record.status === '1' ? (
              <a
                onClick={() => {
                  handleEffect(record);
                }}
              >
                失效
              </a>
            ) : (
              <a
                onClick={() => {
                  handleEffect(record);
                }}
              >
                生效
              </a>
            )}
          </span>
        );
      },
    },
  ];

  return (
    <>
      <Card
        title="系统参数配置"
        style={{ minHeight: height }}
        className="cute"
        extra={
          <div style={{ display: 'inline-block' }}>
            <Button
              className="margin-right"
              type="primary"
              onClick={() => {
                handleEdit({});
              }}
            >
              新增
            </Button>
            <Button type="primary" onClick={handleReash}>
              刷新
            </Button>
          </div>
        }
      >
        <Form className="flow fix-label">
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label="组编码">
                {getFieldDecorator('groupCode')(<Input allowClear placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="组名称">
                {getFieldDecorator('groupName')(<Input allowClear placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="参数名称">
                {getFieldDecorator('paramName')(<Input allowClear placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="参数编码">
                {getFieldDecorator('paramCode')(<Input allowClear placeholder="请输入" />)}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label="参数值">
                {getFieldDecorator('paramValue')(<Input allowClear placeholder="请输入" />)}
              </Form.Item>
            </Col>
            <Col span={18} className="text-right">
              <Button type="primary" onClick={submit}>
                查询
              </Button>
              <Button className="margin-left" onClick={handleReset}>
                重置
              </Button>
            </Col>
          </Row>
        </Form>
        <SlickTable
          style={{ marginTop: 8 }}
          rowKey={record => record.id}
          columns={columns}
          {...restTableProps}
          data={{
            pagination: {
              ...pagination,
              pageSize: size,
            },
          }}
          pick="checkbox"
          extra={
            selectedRows.length > 0 ? (
              <Button type="primary" ghost onClick={handleCopy}>
                复制
              </Button>
            ) : null
          }
          onSelectRow={handleSelectRows}
          selectedRows={selectedRows}
        />
      </Card>
      <Drawer
        title={status === 'edit' ? '编辑系统参数配置' : '新增系统参数配置'}
        destroyOnClose
        width={620}
        onClose={onClose}
        visible={visible}
        bodyStyle={{ paddingBottom: 80 }}
      >
        <Edit close={onClose} refresh={refresh} />
      </Drawer>
    </>
  );
}

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(ParamsConfig));
