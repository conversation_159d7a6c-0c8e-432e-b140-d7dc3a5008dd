/* eslint-disable */
import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'dva';
import { Tree, Skeleton, Spin, Input, Icon, Tooltip, Modal, Result, message } from 'antd';
import request from '@/utils/request';
import ScrollBar from '@/components/ScrollBar';
import styles from '../styles.less';
import PubSub from 'pubsub-js';
import { newGetDictData } from '@/services/common';

const { TreeNode, DirectoryTree } = Tree;
const { Search } = Input;

const namespace = 'orgManage';
const eventName = 'orgManage_orgMenu';
let TreeDataSource = [];

function orgMenu(props) {
  const [allowEdit, setAllowEdit] = useState(false);
  // const [TreeData, setTreeData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedKeysArr, setSelectedKeysArr] = useState(['']);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [loadedKeys, setLoadedKeys] = useState([]);

  // 是否有无数据
  const [visiable, setVisiable] = useState(true);

  // 通过dva存当前需要传递的数据
  const saveOrgData = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveOrgData`,
      payload: params,
    });
  };

  // 通过dva存当前需要传递的数据
  const savePassData = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/savePassData`,
      payload: params,
    });
  };

  const saveBehavior = params => {
    const { dispatch } = props;
    dispatch({
      type: `${namespace}/saveBehavior`,
      payload: params,
    });
  };

  useEffect(() => {
    newGetDictData({
      groupCode: 'SYSTEM_VAR',
      paramCode: 'USE_USER_CENTER',
    }).then(res => {
      setAllowEdit(res !== '1'); // 1:不允许编辑
    });

    queryTreeData();
    // 更新与新增时更新菜单
    PubSub.subscribe(`${eventName}.Update`, () => {
      setLoadedKeys([]);
      setAutoExpandParent(false);
      setSearchValue('');
      queryTreeData();
      TreeDataSource = [];
    });
    return () => {
      PubSub.unsubscribe(`${eventName}`);
    };
  }, []);

  const queryTreeData = async () => {
    setLoading(true);
    const arrTree = await queryChildren(0).then(res => {
      if (res.length > 0) {
        let node = {};
        let arr = [];
        res.map((item, index) => {
          node = item;
          if (item.childrenCount !== 0) {
            node.children = [];
            node.isLeaf = false;
          } else {
            node.isLeaf = true;
          }
          arr.push(node);
        });
        return arr;
      } else {
        return [];
      }
    });
    TreeDataSource = arrTree;
    setLoading(false);
  };

  async function queryChildren(parentOrgId) {
    //orgauth/DepartmentController/getDepByParentId.do

    return request('orgauth/OrganizationController/getOrgByParentId.do', {
      data: {
        parentId: parentOrgId,
      },
    });
  }

  // 由接口决定，根据parentOrgId 获取同级元素
  const getParentId = parentOrgId => {
    return TreeDataSource.filter(item => {
      return JSON.stringify(item.parentOrgId) === JSON.stringify(parentOrgId);
    });
  };

  // 遍历树
  const renderTreeNode = parentOrgId => {
    const tmp = getParentId(parentOrgId);
    if (tmp.length > 0) {
      return tmp.map(item => {
        const index = item.orgName.indexOf(searchValue);
        const beforeStr = item.orgName.substr(0, index);
        const afterStr = item.orgName.substr(index + searchValue.length);
        let title = (
          <span>
            {index > -1 ? (
              <span>
                {beforeStr}
                <span style={{ color: '#f50' }}>{searchValue}</span>
                {afterStr}
              </span>
            ) : (
              <span>{item.orgName}</span>
            )}
            {allowEdit && (
              <span className={styles.org_menu_tree_edit}>
                <Tooltip title="新增下级组织">
                  <Icon
                    type="plus-circle"
                    className={styles.icon}
                    onClick={event => {
                      add(event, item);
                    }}
                  />
                </Tooltip>
              </span>
            )}
          </span>
        );
        return (
          <TreeNode title={title} key={item.orgId} isLeaf={item.isLeaf} dataRef={item}>
            {renderTreeNode(item.orgId)}
          </TreeNode>
        );
      });
    }
  };

  // 添加子节点
  const add = (event, item) => {
    event.stopPropagation();
    event.nativeEvent.stopImmediatePropagation();
    request(`orgauth/OrganizationController/get.do?id=${item.id}`, {
      data: { id: item.id },
      method: 'GET',
    }).then(res => {
      if (res) {
        console.log(res);
        saveBehavior('add');
        savePassData({
          parentOrgName: res.orgName,
          orgLevel: res.orgLevel + 1,
          createType: res.createType,
          parentOrgId: res.orgId,
          pathName: res.pathName,
          statusCd: res.statusCd,
          orgId: '',
        });
      } else {
        message.error('新增有误！');
      }
    });
  };

  // 异步加载
  const onLoadData = async function(treeNode) {
    // console.log(Array.from(searchValue).length);
    if (Array.from(searchValue).length) return;

    const orgId = treeNode.props.dataRef.orgId;
    let arrTree = await queryChildren(orgId).then(res => {
      if (res.length > 0) {
        let node = {};
        let arr = [];
        res.map((item, index) => {
          node = item;
          if (item.childrenCount !== 0) {
            node.isLeaf = false;
          } else {
            node.isLeaf = true;
          }
          arr.push(node);
        });
        return arr;
      }
    });

    // 两个数组对象合并并除去相同项
    let json = TreeDataSource.concat(arrTree);
    let newJson = [];
    for (const item1 of json) {
      let flag = true;
      for (const item2 of newJson) {
        if (item1.id == item2.id) {
          flag = false;
        }
      }
      if (flag) {
        newJson.push(item1);
      }
    }

    TreeDataSource = newJson;
  };

  const onLoad = loadedKeys => {
    setLoadedKeys(loadedKeys);
  };

  // 菜单搜索
  const onSearch = value => {
    if (!value) {
      setExpandedKeys([]);
      setLoadedKeys([]);
      setAutoExpandParent(false);
      setSearchValue('');
      queryTreeData();
      setVisiable(true);
      return;
    }
    setLoading(true);
    request('orgauth/OrganizationController/getOrgByName.do', {
      data: {
        orgName: value,
      },
    }).then(res => {
      let arr = [];
      let loadedKeysArr = [];
      if (res.length > 0) {
        let node = {};
        res.map((item, index) => {
          node = item;
          if (item.childrenCount !== 0) {
            node.children = [];
            node.isLeaf = false;
          } else {
            node.isLeaf = true;
          }
          arr.push(node);
          loadedKeysArr.push(JSON.stringify(node.orgId));
        });

        TreeDataSource = arr;
        setAutoExpandParent(true);
        setSearchValue(value);
        setExpandedKeys(loadedKeysArr);
        setLoadedKeys(loadedKeysArr);
        setLoading(false);
        setVisiable(true);
      } else {
        setLoading(false);
        setVisiable(false);
      }
    });
  };

  const onExpand = expandedKeys => {
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(false);
  };

  // 选中节点
  const selectTreeNode = (selectedKeys, e) => {
    if (selectedKeys.length === 0) {
      setSelectedKeysArr(selectedKeysArr);
    } else {
      setSelectedKeysArr(selectedKeys);
    }
    saveOrgData(e.node.props.dataRef);
  };

  return (
    <div className={styles.org_menu}>
      <div className={styles.org_menu_search}>
        <Search style={{ marginBottom: 8 }} placeholder="搜索关键字" onSearch={onSearch} />
      </div>
      <ScrollBar autoHide autoHeight autoHeightMax={props.height - 100}>
        <Spin spinning={loading}>
          {visiable ? (
            <Tree
              expandAction="false"
              loadData={onLoadData}
              className={styles.org_menu_tree}
              onSelect={selectTreeNode}
              selectedKeys={selectedKeysArr}
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              onLoad={onLoad}
              loadedKeys={loadedKeys}
            >
              {renderTreeNode(0)}
            </Tree>
          ) : (
            <Result icon={<Icon type="frown" theme="twoTone" twoToneColor="#1890ff" />} subTitle="你搜索的内容暂无数据~" />
          )}
        </Spin>
      </ScrollBar>
    </div>
  );
}

export default connect(({}) => ({}))(orgMenu);
