import dynamic from 'umi/dynamic';
import PageLoading from '@/components/PageLoading';

export const tabs = [
  {
    name: '集团在途工单',
    key: 'inTransitOrder',
    icon: 'icon-jichuxinxi',
    component: dynamic({
      loader: () => import('./components/InTransitOrder'),
      loading: PageLoading,
    }),
  },
  {
    name: '订购列表',
    key: 'OrderList',
    icon: 'icon-jichuxinxi',
    component: dynamic({
      loader: () => import('./components/OrderList'),
      loading: PageLoading,
    }),
  },
  {
    name: 'VPMN产品订购',
    key: 'VPMNOrderInfo',
    icon: 'icon-jichuxinxi',
    component: dynamic({
      loader: () => import('./components/VPMNOrderInfo'),
      loading: PageLoading,
    }),
  },

  /* {
     name: '历史订购',
     key: 'HistoryOrderIn',
     icon: 'icon-jichuxinxi',
     component: dynamic({
       loader: () => import('./components/HistoryOrderIn'),
       loading: PageLoading,
     }),
   }, */

  {
    name: '历史订单',
    key: 'HistoryOrderList',
    icon: 'icon-jichuxinxi',
    component: dynamic({
      loader: () => import('./components/HistoryOrderList'),
      loading: PageLoading,
    }),
  },
  {
    name: '行业网关受限名单',
    key: 'EntMgrRestrictedSheet',
    icon: 'icon-jichuxinxi',
    component: dynamic({
      loader: () => import('./components/EntMgrRestrictedSheet'),
      loading: PageLoading,
    }),
  },
  {
    name: '全国宽带订购明细',
    key: 'GroupAllNetLinesDetailView',
    icon: 'icon-jichuxinxi',
    component: dynamic({
      loader: () => import('./components/GroupAllNetLinesDetailView'),
      loading: PageLoading,
    }),
  },
];
