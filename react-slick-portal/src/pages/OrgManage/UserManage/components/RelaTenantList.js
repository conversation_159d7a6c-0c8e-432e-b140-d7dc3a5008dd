import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import {
  Table,
  message,
  Transfer,
} from 'antd';
import difference from 'lodash/difference';
import debounce from 'lodash/debounce';
import { useAntdTable } from '@umijs/hooks';
import styles from '../styles.less';
import request from '@/utils/request';

const TableTransfer = ({
  leftColumns,
  rightColumns,
  leftTableData,
  rightTableData,
  ...restProps
}) => (
  <Transfer {...restProps} showSelectAll={false} className={styles.resize_transfer}>
    {({ direction, onItemSelectAll, onItemSelect, selectedKeys: listSelectedKeys }) => {
      const columns = direction === 'left' ? leftColumns : rightColumns;
      const tableProps = direction === 'left' ? leftTableData : rightTableData;
      tableProps.pagination.showTotal = () => `总计 ${tableProps.pagination.total} 条`;
      const rowSelection = {
        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows.filter(item => item.id).map(item => item);
          const diffKeys = selected
            ? difference(treeSelectedKeys, listSelectedKeys)
            : difference(listSelectedKeys, treeSelectedKeys);
          onItemSelectAll(diffKeys, selected);
        },
        onSelect(item, selected) {
          onItemSelect(item, selected);
        },
        selectedRowKeys: listSelectedKeys.filter(item => item.id).map(item => item.id),
      };

      return (
        <div>
          <Table
            rowKey={record => record.id}
            columns={columns}
            // size="small"
            bordered
            rowSelection={rowSelection}
            {...tableProps}
            onRow={item => ({
              onClick: () => {
                onItemSelect(item, !listSelectedKeys.includes(item));
              },
            })}
          />
        </div>
      );
    }}
  </Transfer>
);


function RelaTenantList(props) {
  const [objId, setObjId] = useState(props.tableData.id);
  const [leftVal, setLeftVal] = useState('');
  const [rightVal, setRightVal] = useState('');
  const [valuesList, setValuesList] = useState([[]]);

  async function queryValueList() {
    let result;
    await Promise.all([
      request('portal/DomainDataController/getValuesList.do', {
        data: {
          busiNbr: 'Tenant',
          propertyName: 'statusCd',
        },
      }),
    ]).then(res => {
      result = res;
    });
    return result;
  }

  useEffect(() => {
    queryValueList().then(res => {
      setValuesList(res);
    });
  }, []);

  useEffect(() => {
    setObjId(props.tableData.id);
    setLeftVal('');
    setRightVal('');
  }, [props.tableData]);

  const getLeftTable = ({ current, pageSize }) => request('portal/TenantController/pageInfoUserRel.do', {
      data: {
        pageNum: current,
        pageSize,
        sortName: '',
        sortOrder: 'asc',
        statusCd: '1000',
        filterVal: leftVal,
        withoutUserId: objId,
      },
    }).then(res => {
      if (Array.isArray(res.list)) {
        return {
          total: res.total,
          data: res.list.map(val => {
            const { children, ...rest } = val;
            return rest;
          }),
        };
      }
      return {
        total: 0,
        data: [],
      };
    });

  const getRightTable = ({ current, pageSize }) => request('portal/TenantUserRelController/pageInfo.do', {
      data: {
        pageNum: current,
        pageSize,
        sortName: '',
        sortOrder: 'asc',
        filterVal: rightVal,
        userId: objId,
      },
    }).then(res => {
      if (Array.isArray(res.list)) {
        return {
          total: res.total,
          data: res.list.map(val => {
            const { children, ...rest } = val;
            return rest;
          }),
        };
      }
      return {
        total: 0,
        data: [],
      };
    });

  const { tableProps: leftData, refresh: leftTableRefresh } = useAntdTable(
    params => getLeftTable({ ...params, pageSize: 5 }),
    // 依赖变化会触发请求
    [objId, leftVal],
    {
      defaultPageSize: 5,
    }
  );

  const { tableProps: rightData, refresh: rightTableRefresh } = useAntdTable(
    params => getRightTable({ ...params, pageSize: 5 }),
    // 依赖变化会触发请求
    [objId, rightVal],
    {
      defaultPageSize: 5,
    }
  );

  const onChange = (moveKeys, direction, targetKeys) => {
    if (direction === 'right') {
      const params = [];
      moveKeys.map(item => {
        params.push({
          tenantId: item.id,
          userId: objId,
        });
      });

      request('portal/TenantUserRelController/createList.do', {
        data: params,
      }).then(res => {
        if (res) {
          message.success('添加租户成功');
          leftTableRefresh();
          rightTableRefresh();
        } else {
          message.error('添加租户失败');
        }
      });
    } else {
      const params = [];
      targetKeys.map(item => {
        params.push(item.relId);
      });

      request('portal/TenantUserRelController/deleteList.do', {
        data: params,
      }).then(res => {
        if (res) {
          message.success('移除租户成功');
          leftTableRefresh();
          rightTableRefresh();
        } else {
          message.error('移除租户失败');
        }
      });
    }
  };

  // 穿梭框搜索框只支持Input的changge事件，做节流处理
  const onSearch = debounce((direction, value) => {
    // console.log(direction, value);
    if (direction === 'left') {
      setLeftVal(value);
    } else {
      setRightVal(value);
    }
  }, 600);

  const leftTableColumns = [
    {
      dataIndex: 'tenantName',
      title: '租户名称',
      ellipsis: true,
    },
    {
      dataIndex: 'tenantNbr',
      title: '租户编码',
      ellipsis: true,
    },
    {
      dataIndex: 'statusCd',
      title: '租户状态',
      ellipsis: true,
      render: text => {
        let result = text;
        if (valuesList[0].length) {
          valuesList[0].map(item => {
            if (item.value === text) {
              result = item.name;
            }
          });
        }
        return result;
      },
    },
  ];
  const rightTableColumns = [
    {
      dataIndex: 'tenantName',
      title: '租户名称',
      ellipsis: true,
    },
    {
      dataIndex: 'tenantNbr',
      title: '租户编码',
      ellipsis: true,
    },
    {
      dataIndex: 'tenantStatusCd',
      title: '租户状态',
      ellipsis: true,
      render: text => {
        let result = text;
        if (valuesList[0].length) {
          valuesList[0].map(item => {
            if (item.value === text) {
              result = item.name;
            }
          });
        }
        return result;
      },
    },
  ];

  return (
    <div>
      {objId === props.tableData.id ? (
        <TableTransfer
          showSearch
          onChange={onChange}
          onSearch={onSearch}
          operations={['添加租户', '移除租户']}
          titles={['可选租户列表', '已选租户列表']}
          leftColumns={leftTableColumns}
          rightColumns={rightTableColumns}
          leftTableData={leftData}
          rightTableData={rightData}
        />
      ) : null}
    </div>
  );
}

export default connect(({ userManage }) => ({
  tableData: userManage.tableData,
}))(RelaTenantList);
