import { useState, useEffect } from 'react';
import request from '@/utils/request';

// 获取属性管理里配置的属性数据
const Index = ({ busiNbr, propertyName }) => {
  const [list, setList] = useState([]);

  useEffect(() => {
    request('portal/DomainDataController/getValuesList.do', {
      data: {
        busiNbr,
        propertyName,
      },
    }).then(res => {
      setList(res);
    });
  }, []);

  return list;
};

export default Index;
