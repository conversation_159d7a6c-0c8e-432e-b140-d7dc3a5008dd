##入参                                                                                                                                                                        
说明 |类型|默认值
:----|:-----:|-----:
基本入参  | object | { pageNum: 1, pageSize: 10, sortName: '', sortOrder: '' }  
请求地址  |  string | ''
基本入参除外 |  object | {}    

##返回值
loading , data , onChange

##案列
```javascript 
    const [params, setParams] = useState({
        filterCol: 'staffName,staffCode',
        filterVal: '',
    });
    const { tableProps } = useSlickTable(
        { pageSize: 5 },
        '/portal/orgauth/StaffController/selectStaffGridData.do',
        params
    );

    ....
    ..

    const columns = [
    {
      title: '员工标识',
      dataIndex: 'id',
    },
    {
      title: '员工名称',
      dataIndex: 'staffName',
    },
    {
      title: '员工类型',
      dataIndex: 'staffTypeName',
    },
    {
      title: '员工编码',
      dataIndex: 'staffCode',
    },
    {
      title: '员工账号',
      dataIndex: 'staffAccount',
    },
    {
      title: '状态',
      dataIndex: 'statusCdName',
    },
    {
      title: '操作',
      render: (text, record) => {
        return (
          <span>
            <a
              onClick={() => {
                console.log('1');
              }}
            >
              编辑
            </a>
          </span>
        );
      },
    },
  ];
  
  ....
  ..

  <SlickTable
        rowKey={record => record.id}
        columns={columns}
        scroll={{ x: 'max-content' }}
        {...tableProps}
    />
```