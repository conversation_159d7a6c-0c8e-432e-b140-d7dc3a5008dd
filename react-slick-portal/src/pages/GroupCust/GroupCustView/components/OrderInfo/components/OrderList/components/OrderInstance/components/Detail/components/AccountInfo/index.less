.accountInfoContainer {
  padding: 16px;
}

.accountCard {
  margin-bottom: 16px;

  :global {
    .ant-card-body {
      padding: 16px;
    }
  }
}

.accountHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}
.accountTypeMixin(@bgcolor, @txtcolor) {
  background-color: @bgcolor;
  color: @txtcolor;
}
.accountType{
  line-height: 22px;
  height: 22px;
  border-radius: 3px;
  font-size: 12px;
  padding: 2px 4px;
  /*无*/
  &.default { .accountTypeMixin(#E6FAFF,#23A0DE); }
  /*黑名单*/
  &.black { .accountTypeMixin(#F0F0F0,#333333); }
  /*红名单*/
}
.accountTitle {
  display: flex;
  align-items: center;
}
.statusMixin(@color) {
  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: @color;
    border-radius: 50%;
    margin-right: 8px;
  }
}
.accountStatus {
  /*在线*/
  &.status0 { .statusMixin(#21BF55); }
  /*离线*/
  &.status1 { .statusMixin(#E40077); }
}
.accountName {
  font-size: 18px;
  font-weight: 500;
  margin-right: 12px;
}

.authButton {
  background-color: #52c41a;
  border-color: #52c41a;
}

.accountDescriptions {
  :global {
    .ant-descriptions-item-label {
      color: #666;
      font-weight: normal;
      min-width: 90px;
    }

    .ant-descriptions-item-content {
      color: #333;
    }
  }
}

.serviceCard {
  :global {
    .ant-card-body {
      padding: 16px;
    }
  }
}

.searchBar {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.serviceTable {
  :global {
    .ant-table-thead > tr > th {
      background-color: #f5f7fa;
      color: #333;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #e6f7ff;
    }
  }
}
