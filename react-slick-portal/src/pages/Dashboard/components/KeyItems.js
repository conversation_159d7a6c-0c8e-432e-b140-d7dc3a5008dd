/* eslint-disable global-require */
import React, { useEffect } from 'react';
import { Card, Tag } from "antd";
import { connect } from "dva";
import { router } from 'umi';
import style from './keyItems.less';

const QuickAction = props => {
  const { unRead, undoCount, dispatch } = props;

  useEffect(() => {
    dispatch({
      type: 'undo/qryTasksNumbers',
    });
  }, [])

  return (
    <Card
      title="关键事项"
      bordered={false}
      bodyStyle={{ height: '196px', overflow: 'hidden', padding: 0 }}
      className={style.card}
    >
      <div className={style.container}>
        <div
          className={style.keyItem}
          onClick={() => router.push({
            pathname: '/todomanage',
          })}
        >
          <div>待办事项</div>
          <div>{undoCount}</div>
          <div>
            <Tag color="volcano" style={{ cursor: 'pointer' }}>去处理
            </Tag>
          </div>
        </div>
        <div
          className={style.keyItem}
          onClick={() => router.push({
            pathname: '/notice',
          })}
        >
          <div>未读消息</div>
          <div>{unRead}</div>
          <div>
            <Tag color="volcano" style={{ cursor: 'pointer' }}>去处理</Tag>
          </div>
        </div>
      </div>
      <img src={require('../img/bg_keyitems.png')} alt="" />
    </Card>
  )
}

export default connect(({ notice, undo }) => ({
  unRead: notice.unRead,
  undoCount: undo.undoCount
}))(QuickAction);
