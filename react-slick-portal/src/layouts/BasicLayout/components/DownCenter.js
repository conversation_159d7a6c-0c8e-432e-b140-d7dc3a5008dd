import React, { useState, useEffect } from 'react';
import { Table, Input } from 'antd';
import Link from 'umi/link';
import ScrollBar from '@/components/ScrollBar';
import request from '@/utils/request';

const { Search } = Input;

function DownCenter() {
  const [data, setData] = useState([]);
  const [searchData, setSearchData] = useState([]);
  const [searchValue, setSearchValue] = useState(false);
  const [currrentValue, setCurrrentValue] = useState();
  const [spining, setSpining] = useState(false);

  const mapFun = f1 => {
    const result = [];
    return new Promise(resolve => {
      f1.map(item => {
        request('portal/FileStoreController/queryByObjId.do', {
          data: {
            objId: item.id,
            objType: 1500,
          },
          method: 'GET',
        }).then(res => {
          const res1 = res[0];
          result.push({ ...item, ...res1 });
          if (result.length === f1.length) {
            resolve(result);
          }
        });
      });
    });
  };

  async function queryValueList() {
    const f1 = await request('portal/DownloadCenterController/getFileList.do', { method: 'GET' });
    if (f1.length === 0) {
      return f1;
    }
    const f2 = await mapFun(f1);
    return f2;
  }

  const compare = p => {
    return function(m, n) {
      const a = m[p];
      const b = n[p];
      return a - b;
    };
  };

  useEffect(() => {
    setSpining(true);
    queryValueList().then(res => {
      res.sort(compare('id'));
      setData(res);
      setSpining(false);
    });
  }, []); // eslint-disable-line

  const downloadFun = record => {
    request('portal/DownloadCenterController/updateFile.do', {
      data: {
        downloadCenterId: record.id,
        downloadCount: record.downloadCount + 1,
      },
    }).then(res => {
      if (res.resultCode === 0) {
        setSpining(true);
        queryValueList().then(res => {
          res.sort(compare('id'));
          setData(res);
          if (searchData.length) {
            const result = [];
            res.map(item => {
              if (item.downloadTitle.indexOf(currrentValue) !== -1) {
                result.push(item);
              }
            });
            result.sort(compare('id'));
            setSearchData(result);
          }
          setSpining(false);
        });
      }
    });
  };

  const columns = [
    {
      title: '文件标题',
      dataIndex: 'downloadTitle',
    },
    {
      title: '文件名',
      dataIndex: 'fileName',
      render: (text, record) => {
        return (
          <a
            target="_blank"
            href={record.fileGetUrl}
            onClick={() => {
              downloadFun(record);
            }}
          >
            {text}
          </a>
        );
      },
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      render: text => {
        /* eslint-disable */
        return text.length <= 3 ? (
          <span>{`${text} B`}</span>
        ) : text.length > 3 && text.length <= 6 ? (
          <span>{`${text.substring(0, text.length - 3)} KB`}</span>
        ) : (
          <span>{`${text.substring(0, text.length - 3) / 1000} M`}</span>
        );
        /* eslint-enable */
      },
    },
    {
      title: '文件类型',
      dataIndex: 'downloadTypeName',
    },
    {
      title: '下载量',
      dataIndex: 'downloadCount',
      align: 'center',
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => {
        return (
          <a
            target="_blank"
            href={record.fileGetUrl}
            onClick={() => {
              downloadFun(record);
            }}
          >
            下载
          </a>
        );
      },
    },
  ];

  const searchTable = value => {
    // console.log(value);
    setCurrrentValue(value);
    const result = [];
    data.map(item => {
      if (item.downloadTitle.indexOf(value) !== -1) {
        result.push(item);
      }
    });
    result.sort(compare('id'));
    setSearchData(result);
    if (value && result.length === 0) {
      setSearchValue(true);
    } else {
      setSearchValue(false);
    }
  };

  return (
    <div style={{ padding: '8' }}>
      <ScrollBar autoHide autoHeight autoHeightMax={400}>
        <Search
          placeholder="文件标题"
          onSearch={value => {
            searchTable(value);
          }}
          style={{ width: 200, float: 'right', right: 24, margin: '12px 0px', zIndex: 100 }}
        />
        <Table
          columns={columns}
          bordered
          dataSource={searchValue ? [] : searchData.length ? searchData : data} // eslint-disable-line
          pagination={false}
          style={{ padding: 24 }}
          loading={spining}
        />
      </ScrollBar>
    </div>
  );
}

export default DownCenter;
