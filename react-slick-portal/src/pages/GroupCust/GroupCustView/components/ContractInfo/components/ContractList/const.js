export const infoKeys = [

  /* 第一列 */
  [
    { label: '集团联系人', key: 'JURISTIC_NAME' },
    { label: '组织机构代码', key: 'GROUP_ORG_CODE' },
    { label: '付费账户', key: 'paymentAccount' },
  ],

  /* 第二列 */
  [
    { label: '集团联系人邮箱', key: 'contactEmail' },
    { label: '集团联系人电话', key: 'contactPhone' },
    {
      label: '是否CM-IOT集团',
      key: 'IS_CMIOT',
      options: [
        {
          format: '是',
          value: true,
        },
        {
          format: '否',
          value: false,
        },
      ],
    },
  ],

  /* 第三列 */
  [
    { label: '集团证件类型', key: 'GROUP_BUSI_LICENCE_TYPE' },
    { label: '集团证件编码', key: 'GROUP_LICENCE_NO' },
    { label: '客户级别', key: 'GROUP_LEVEL' },
  ],
];

export const queryFields = [
  { key: 'groupName', label: '集团名称', type: 'input' },
  { key: 'groupProd', label: '集团产品', type: 'input', disable: true },
  { key: 'condGroupMember', label: '集团成员号码', type: 'input', disable: true },
  { key: 'groupId', label: '集团编码', type: 'input' },
  { key: 'groupAccount', label: '集团账号', type: 'input', disable: true },
  { key: 'groupIdNum', label: '集团证件信息', type: 'input', disable: true },
  { key: 'accessNum', label: '接入号码', type: 'input', disable: true },

];
export const MODE = {
  ADD: 'add',
  EDIT: 'edit',
  VIEW: 'view',
};
