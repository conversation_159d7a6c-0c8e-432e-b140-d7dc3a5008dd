/* 基础信息菜单 */
import dynamic from 'umi/dynamic';
import PageLoading from '@/components/PageLoading';

export const leftMenu = [
  {
    name: '首页',
    icon: 'icon-shouye',
    key: 'BaseInfoMenu',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/BaseInfo/components/BaseInfo'),
      loading: PageLoading,
    }),
  },
  {
    name: '法人身份证号管理',
    icon: 'icon-shangpin',
    key: 'LegalPersonInfo',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/BaseInfo/components/BaseInfo/components/LegalPersonInfo'),
      loading: PageLoading,
    }),
  },
  {
    name: '授权人管理',
    icon: 'icon-shangpin',
    key: 'AuthManage',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/BaseInfo/components/AuthManage'),
      loading: PageLoading,
    }),
  },
  {
    name: '集团工单查询',
    icon: 'icon-dingdan',
    key: 'OrderInfoMenu',
    children: [
      {
        name: '在途预受理工单',
        key: 'preOrderList',
        icon: 'icon-dingdan',
        component: dynamic({
          loader: () => import('@/pages/GroupCust/GroupCustView/components/OrderInfo/components/InTransitOrder/components/preOrderList.js'),
          loading: PageLoading,
        }),
      },
      {
        name: '在途工单',
        key: 'orderList',
        icon: 'icon-dingdan',
        component: dynamic({
          loader: () => import('@/pages/GroupCust/GroupCustView/components/OrderInfo/components/InTransitOrder/components/orderList.js'),
          loading: PageLoading,
        }),
      },
    ],
  },
  {
    name: '商品信息',
    icon: 'icon-shangpin',
    key: 'ProductInfo',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/OrderInfo/components/OrderList/components/OrderInstance'),
      loading: PageLoading,
    }),
  },
  {
    name: '融合包信息',
    icon: 'icon-baoguo',
    key: 'MergePackageInfo',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/OrderInfo/components/OrderList/components/MergePackage'),
      loading: PageLoading,
    }),
  },
  {
    name: '账务管理',
    icon: 'icon-zhanghu',
    key: 'AccountManage',
    children: [
      {
        name: '账务信息',
        icon: 'icon-zhanghu',
        key: 'AccountInfo',
        component: dynamic({
          loader: () => import('@/pages/GroupCust/GroupCustView/components/AccountInfo/components/BaseInfo'),
          loading: PageLoading,
        }),
      },
      {
        name: '账期短信发送',
        icon: 'icon-zhanghu',
        key: 'AccountSMS',
        component: dynamic({
          loader: () => import('@/pages/GroupCust/GroupCustView/components/AccountInfo/components/CustManager/index.js'),
          loading: PageLoading,
        }),
      },
    ],
  },
  {
    name: '合同管理',
    icon: 'icon-hetong',
    key: 'ContractInfo',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/ContractInfo/components/ContractList'),
      loading: PageLoading,
    }),
  },
  {
    name: '集团成员管理',
    icon: 'icon-chengyuan',
    key: 'GroupMemberManage',
  },
  {
    name: '集团客户经理管理',
    icon: 'icon-kehuguanli',
    key: 'GroupCustManagerManage',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/BaseInfo/components/CustManager'),
      loading: PageLoading,
    }),
  },
  {
    name: '呆坏账查询',
    icon: 'icon-chaxun',
    key: 'BadDebtQuery',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/AccountInfo/components/EcBadChargeItView'),
      loading: PageLoading,
    }),
  },
  {
    name: '全国宽带订购明细',
    icon: 'icon-kuandai',
    key: 'NationalBroadbandDetail',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/OrderInfo/components/GroupAllNetLinesDetailView'),
      loading: PageLoading,
    }),
  },
  {
    name: '集团部门信息',
    icon: 'icon-bumen',
    key: 'GroupDeptInfo',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/BaseInfo/components/OrgInfo/components/DeptManage'),
      loading: PageLoading,
    }),
  },
  {
    name: '子集团信息',
    icon: 'icon-zuzhi',
    key: 'SubGroupInfo',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/BaseInfo/components/OrgInfo/components/SubGroupInfo'),
      loading: PageLoading,
    }),
  },
  {
    name: '附加信息',
    icon: 'icon-xinxi',
    key: 'ExtraInfo',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/BaseInfo/components/BaseInfo/components/ExtraInfo'),
      loading: PageLoading,
    }),
  },
  {
    name: '集团角色管理',
    icon: 'icon-jiaose',
    key: 'GroupRoleManage',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/BaseInfo/components/BaseInfo/components/EntMgrRoleInfo'),
      loading: PageLoading,
    }),
  },
  {
    name: '纳税人资质管理',
    icon: 'icon-zizhi',
    key: 'TaxpayerQualifyManage',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/BaseInfo/components/BaseInfo/components/TaxpayerInfo'),
      loading: PageLoading,
    }),
  },
  {
    name: '非结构化资料管理',
    icon: 'icon-ziliao',
    key: 'UnstructuredDataManage',
    component: dynamic({
      loader: () => import('@/pages/GroupCust/GroupCustView/components/BaseInfo/components/BaseInfo/components/UnstructuredInfo'),
      loading: PageLoading,
    }),
  },
];
