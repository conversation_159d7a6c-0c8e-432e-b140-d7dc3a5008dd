/* eslint-disable */
import React, { useState, useEffect, useContext } from 'react';
import { Row, Col, Card, Form, Input, Icon, Button, Table, Tabs, Modal } from 'antd';
import LoadingComponent from '@/components/PageLoading/index';
import dynamic from 'umi/dynamic';
const { TabPane } = Tabs;

// 涉及到的标签组件按需加载
const AppThemeInfo = dynamic({
  loader: () => import('./AppThemeInfo'),
  loading: LoadingComponent,
});
const ReleMenu = dynamic({
  loader: () => import('./ReleMenu'),
  loading: LoadingComponent,
});


function Tab(props) {
  const [menuKey, setMenuKey] = useState('AppThemeInfo');
  const [contentList, setContentList] = useState({});

  //判断编辑、新增
  useEffect(() => {
    if (props.behavior !== 'disabled') {
      onTabChange('AppThemeInfo');
    }
  }, [props.behavior]);
  const tabList = [
    {
      key: 'AppThemeInfo',
      tab: '主题信息',
    },
    {
      key: 'ReleMenu',
      tab: '关联菜单',
    },
  ];

  useEffect(() => {
    onTabChange('AppThemeInfo');
  }, []);

  useEffect(() => {
    if (props.behavior !== 'disabled') {
      onTabChange('AppThemeInfo');
    }
  }, [props.behavior]);

  const onTabChange = key => {
    const contentMap = {
      AppThemeInfo: <AppThemeInfo />,
      ReleMenu: <ReleMenu />,
    };
    if (!contentList[key]) {
      contentList[key] = contentMap[key];
    }
    setContentList(contentList);
    setMenuKey(key);
  };

  return (
    <div>
      <Card
        className="gb_tabs_samll"
        style={{ width: '100%' }}
        tabList={tabList}
        activeTabKey={menuKey}
        onTabChange={key => {
          onTabChange(key);
        }}
      >
        {Object.keys(contentList).map(key => (
          <div key={key} style={{ display: menuKey === key ? 'block' : 'none' }}>
            {contentList[key]}
          </div>
        ))}
      </Card>
    </div>
  );
}
export default Tab;
