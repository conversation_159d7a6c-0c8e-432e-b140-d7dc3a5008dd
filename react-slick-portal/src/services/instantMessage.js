import request from '@/utils/request';

/**
 *  判断是否使用OSS即时通讯会话窗口
 */
export async function checkUseOSSPage() {
  return request('portal/DataDictController/getValueByCode.do', {
    data: { groupCode: 'SYSTEM_VAR', paramCode: 'INSTANTMESSAGE_OSS' },
    method: 'get',
  }).then(res => res);
}

/**
 *  打开特定人员的聊天窗口
 */
export async function openPrivateChat(params) {
  return request('/ids-busi-service-imchat/addressBookController/v1/privateChat', {
    data: params,
    method: 'post',
  }).then(res => res);
}

/**
 *  创建聊天新群组
 */
export async function createNewGroupChat(params) {
  return request('/ids-busi-service-imchat/addressBookController/v1/createGroupChat', {
    data: params,
    method: 'post',
  }).then(res => res);
}

/**
 *  分页获取用户列表
 * @param {object} params  {"page":1,"rowNum":5,"sortName":"","sortOrder":"asc","pageNum":1,"pageSize":5}
 */
export async function pageContactUsers(params) {
  return request('portal/InstantMessageController/pageContactUsers.do', {
    data: params,
    method: 'post',
  });
}

/**
 *  分页获取最近联系人
 * @param {object} params  {"page":1,"rowNum":5,"sortName":"","sortOrder":"asc","pageNum":1,"pageSize":5}
 */
export async function pageChatUsers(params) {
  return request('portal/InstantMessageController/pageChatUsers.do', {
    data: params,
    method: 'post',
  });
}

/**
 *  分页获取聊天消息
 * @param {object} params  {"page":1,"rowNum":5,"sortName":"","sortOrder":"asc","pageNum":1,"pageSize":5}
 */
export async function pageChatContent(params) {
  return request('portal/InstantMessageController/pageChatContent.do', {
    data: params,
    method: 'post',
  });
}

/**
 *  新增消息
 * @param {object} params
 */
export async function insertInstantMessage(params) {
  return request('portal/InstantMessageController/insertInstantMessage.do', {
    data: params,
    method: 'post',
  });
}

/**
 *  批量更新消息状态
 * @param {object} params
 */
export async function updateInstantMessageByIds(params) {
  return request('portal/InstantMessageController/updateInstantMessageByIds.do', {
    data: params,
    method: 'post',
  });
}
