import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Tooltip, Icon } from 'antd';
import classNames from 'classnames';
import { ChartCard, Field, MiniArea, MiniBar, MiniProgress } from '@/components/Charts';
import IconFont from '@/components/IconFont';
import styles from './stat.less';

function ContractStat({ dispatch }) {
  return (
    <div className={classNames(styles.stat)}>
      <div className={styles.visual}>
        <IconFont type="icon-yinzhang" />
      </div>
      <div className={styles.details}>
        <div className={styles.number}>
         11
        </div>
        <div className={styles.desc}>
         合同待办
        </div>
      </div>
    </div>
    );
}

export default connect(({ bulletin }) => ({
  list: bulletin.list,
}))(ContractStat);
