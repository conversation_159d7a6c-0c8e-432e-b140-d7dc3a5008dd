.wrap {

  .tips {
    // display: flex;
    // align-items: center;
    // margin-left: 16px;
    // color: red;
    // cursor: text;
    color: #848484;
  }

  :global(.ant-col-8), :global(.ant-col-16) {
    max-height: none !important;
  }

  :global(.ant-col-24) {
    padding: 0 8px !important;
  }

  :global(.ant-empty-small) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px solid #d9d9d9;
  }

  // :global(.ant-upload-picture-card-wrapper) {
  //   display: flex;
  //   flex-direction: column;
  //   margin-bottom: 20px;

  // }

  // :global(span.ant-upload) {
  //   padding: 0 !important;
  // }

  // :global(.ant-upload-select-picture-card) {
  //   position: absolute;
  //   top: 0;
  //   height: 0;
  //   background: none;
  //   border: none;
  // }

  // :global(.ant-upload-list-picture-card) {
  //   transform: translateY(40px);
  // }
}
