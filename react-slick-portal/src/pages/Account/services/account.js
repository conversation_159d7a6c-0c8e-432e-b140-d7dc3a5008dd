import request from '@/utils/request';

/**
 *
 * @param {object} params {"page":1,"rowNum":10,"sortName":"","sortOrder":"asc","pageNum":1,"pageSize":10,"filterCol":"userName,sysUserCode,staffCode"}
 */
export async function selectGridData(params) {
  return request('orgauth/UserLoginTokenController/selectGridData.do', {
    data: params,
  });
}

export async function forceLogout(params) {
  return request('orgauth/UserLoginTokenController/forceLogout.do', {
    data: params,
  });
}
