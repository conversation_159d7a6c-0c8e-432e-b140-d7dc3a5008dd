/* eslint-disable camelcase */
import filter from 'lodash/filter';
import groupBy from 'lodash/groupBy';
import forEach from 'lodash/forEach';
import mapping, { selectMenuInfoById } from '@/services/menu';

const {
  MENU_TYPE_LEAF,
  MENU_OPEN_MODE_WORK_BENCH_LABEL,
  MENU_ENCYPT_TYPE_NUll,
  MENU_LEVEL_FIST,
  MENU_LEVEL_WORK_BENCH,
  MENU_LEVEL_CUSTOMER_VIEW,
  MENU_LEVEL_CUSTOMER_WORK_BENCH,
} = mapping;
/**
 * 判断菜单参数值，为空补全默认值
 * @param {object} menu 格式如下
 *   {
 *     privId: 89021,
 *     menuId: 276813,
 *     menuName: '潜在客户',
 *     menuLevel: 1,
 *     menuType: '1100',
 *     menuTypeName: '叶子菜单',
 *     parMenuId: 274651,
 *     parMenuName: '客户中心',
 *     menuIndex: 1,
 *     urlAddr:
 *       '[iframe]http: //10.45.46.210:8998/operator/dist/index.html#/cust/manage/potentialcustInfo?viewMode=inner&language=zh-CN',
 *     systemCode: '727002',
 *     statusCd: '1000',
 *     firstLetter: 'QZKH',
 *     iconUrl: 'icon-gene-man-manager',
 *     paramEncryptType: '1000',
 *     menuOpenMode: '1',
 *     menuCode: 'KGXT_MENU_11',
 *   }
 * @param {number} MENU_TYPE_LEAF
 * @param {number} MENU_OPEN_MODE_WORK_BENCH_LABEL
 * @param {string} MENU_ENCYPT_TYPE_NUll
 */
export function checkMenuPar(
  menu,
  MENU_TYPE_LEAF,
  MENU_OPEN_MODE_WORK_BENCH_LABEL,
  MENU_ENCYPT_TYPE_NUll
) {
  if ((menu.urlAddr === undefined || menu.urlAddr === '') && menu.menuType === MENU_TYPE_LEAF) {
    menu.urlAddr = 'http://';
  }
  if (menu.menuOpenMode === undefined || menu.menuOpenMode === '') {
    menu.menuOpenMode = MENU_OPEN_MODE_WORK_BENCH_LABEL;
  }
  if (menu.paramEncryptType === undefined || menu.paramEncryptType === '') {
    menu.paramEncryptType = MENU_ENCYPT_TYPE_NUll;
  }
  return menu;
}

/**
 * 提取一级菜单
 * @param {object[]} all 全部菜单数据，格式如上
 * @param {string} appSysCode
 * @param {string} taskSysCode
 * @return {object[]}
 */
export function getFirstLevelMenu(all, appSysCode, taskSysCode) {
  // 去掉app菜单 和 统一任务菜单
  // showFlag === '1100' 表示不可见
  const sortList = filter(all, function(menu) {
    if (
      menu.menuCode.indexOf('RWGL_MENU') === -1 &&
      parseInt(menu.systemCode, 10) !== parseInt(appSysCode, 10) &&
      parseInt(menu.systemCode, 10) !== parseInt(taskSysCode, 10) &&
      menu.showFlag !== '1100'
    ) {
      return true;
    }
    return false;
  });
  // 对菜单参数进行简单的空值判断
  for (let i = 0; i < sortList.length; i++) {
    sortList[i] = checkMenuPar(
      sortList[i],
      MENU_TYPE_LEAF,
      MENU_OPEN_MODE_WORK_BENCH_LABEL,
      MENU_ENCYPT_TYPE_NUll
    );
  }
  return filter(sortList, { menuLevel: MENU_LEVEL_FIST });
}

/**
 * 根据一级菜单id提取对应的二级菜单
 * @param {object[]} all 全部菜单数据
 * @param {number} parId 一级菜单id
 * @return {object[]}
 */
export function getSecondLevelMenuInfo(all, parId) {
  return filter(all, function(menu) {
    // showFlag === '1100' 表示不可见
    if (
      parseInt(menu.parMenuId, 10) === Number(parId) &&
      parseInt(menu.menuLevel, 10) !== MENU_LEVEL_WORK_BENCH &&
      parseInt(menu.menuLevel, 10) !== MENU_LEVEL_CUSTOMER_VIEW &&
      parseInt(menu.menuLevel, 10) !== MENU_LEVEL_CUSTOMER_WORK_BENCH &&
      menu.showFlag !== '1100'
    ) {
      // 如果是目录菜单，且目录菜单无可展示的叶子菜单，则不显示
      if (menu.menuType === '1000') {
        let thirdMenu = getThirdLevelMenuInfo(all, menu.menuId);
        if (thirdMenu && thirdMenu.length > 0) {
          return true;
        } else {
          return false;
        }
      }

      return true;
    }
    return false;
  });
}

/**
 * 据二级菜单id提取对应的三级菜单
 * @param {object[]} all
 * @param {number} parId 二级菜单id
 * @return {object[]}
 */
export function getThirdLevelMenuInfo(all, parId) {
  return filter(all, function(menu) {
    // showFlag === '1100' 表示不可见
    if (
      parseInt(menu.parMenuId, 10) === Number(parId) &&
      parseInt(menu.menuLevel, 10) !== MENU_LEVEL_WORK_BENCH &&
      parseInt(menu.menuLevel, 10) !== MENU_LEVEL_CUSTOMER_VIEW &&
      parseInt(menu.menuLevel, 10) !== MENU_LEVEL_CUSTOMER_WORK_BENCH &&
      menu.showFlag !== '1100'
    ) {
      return true;
    }
    return false;
  });
}

/**
 * 序列化,去掉app菜单,菜单参数为空时设置默认参数
 * @param {array} allByLetter 格式如下
 * [
 *   [
 *     {
 *       title: 'A-B',
 *       menus: [
 *         {
 *           firstLetter: 'APPBBPZ',
 *           iconUrl: 'icon-gene-man-manager',
 *           menuCode: 'TYMH_MENU_081',
 *           menuDesc: 'APP版本配置',
 *           menuId: 2769938,
 *           menuIndex: 2,
 *           menuLevel: 2,
 *           menuName: 'APP版本配置',
 *           menuOpenMode: '1',
 *           menuType: '1100',
 *           menuTypeName: '叶子菜单',
 *           parMenuId: 276649,
 *           paramEncryptType: '1000',
 *           privId: 991747,
 *           statusCd: '1000',
 *           systemCode: '727001',
 *           urlAddr: 'modules/appversion/views/appVersionView',
 *         },
 *       ],
 *     },
 *   ],
 *   [],
 *   []
 * ];
 * @param {string} appSysCode
 * @return {array} 格式和传入的allByLetter一致
 */
export function getAllMenuByLetter(allByLetter, appSysCode) {
  allByLetter.map(group => {
    group.map(item => {
      // 去掉app菜单
      // showFlag === '1100' 表示不可见
      item.menus = filter(item.menus, function(menu) {
        if (
          parseInt(menu.systemCode, 10) !== parseInt(appSysCode, 10) &&
          menu.showFlag !== '1100'
        ) {
          return true;
        }
        return false;
      });
      // 判断菜单参数为空时设置默认参数
      for (let j = 0; j < item.menus.length; j++) {
        item.menus[j] = checkMenuPar(item.menus[j]);
      }
      return item;
    });
    return group;
  });
  return allByLetter;
}

export function quickSearch(all, value, appSysCode, taskSysCode) {
  let menuMap = [];
  const result = [];
  // 去掉app菜单 和 统一任务菜单
  // showFlag === '1100' 表示不可见
  const sortList = filter(all, function(menu) {
    if (
      menu.menuCode.indexOf('RWGL_MENU') === -1 &&
      parseInt(menu.systemCode, 10) !== parseInt(appSysCode, 10) &&
      parseInt(menu.systemCode, 10) !== parseInt(taskSysCode, 10) &&
      menu.showFlag !== '1100'
    ) {
      return true;
    }
    return false;
  });
  if (value === '') {
    // 取叶子菜单
    const leafMenu = filter(sortList, function(menu) {
      if (
        parseInt(menu.menuType, 10) === MENU_TYPE_LEAF &&
        parseInt(menu.menuLevel, 10) !== MENU_LEVEL_WORK_BENCH &&
        parseInt(menu.menuLevel, 10) !== MENU_LEVEL_CUSTOMER_VIEW &&
        parseInt(menu.menuLevel, 10) !== MENU_LEVEL_CUSTOMER_WORK_BENCH
      ) {
        return true;
      }
      return false;
    });
    // 对查询结果进行分类
    if (leafMenu.length > 0) {
      // 按照相同父类分组
      menuMap = groupBy(leafMenu, function(menu) {
        return menu.parMenuId;
      });
    }
  } else {
    // 取叶子菜单
    const leafMenu = filter(sortList, function(menu) {
      if (
        parseInt(menu.menuType, 10) === MENU_TYPE_LEAF &&
        parseInt(menu.menuLevel, 10) !== MENU_LEVEL_WORK_BENCH &&
        parseInt(menu.menuLevel, 10) !== MENU_LEVEL_CUSTOMER_VIEW &&
        parseInt(menu.menuLevel, 10) !== MENU_LEVEL_CUSTOMER_WORK_BENCH
      ) {
        return true;
      }
      return false;
    });
    // 遍历结果，获menuName 和  firstLetter包含value的叶子菜单
    const resultMenu = filter(leafMenu, function(menu) {
      return (
        menu.menuName.indexOf(value) !== -1 || menu.firstLetter.indexOf(value.toUpperCase()) !== -1
      );
    });

    // 对查询结果进行分类
    if (resultMenu.length > 0) {
      // 按照相同父类分组
      menuMap = groupBy(resultMenu, function(menu) {
        return menu.parMenuId;
      });
    }
  }
  // 遍历list集合，获取map集合
  forEach(menuMap, async function(menuList, menuId) {
    const parNode = filter(sortList, { menuId: Number(menuId) });
    if (parNode.length > 0) {
      const parName = parNode[0].menuName;

      result.push({ title: parName, menus: menuList });
    } else {
      // 上级菜单未赋权,自动获取上级菜单
      const menu = await selectMenuInfoById(menuId);
      let parName = '无';
      if (menu.menuName !== null && menu.menuName !== undefined) {
        parName = menu.menuName;
      }
      result.push({ title: parName, menus: menuList });
    }
  });
  return result;
}

/**
 *
 * @param {object} allFormatter
 * {
      privId: 991762,
      menuId: 2769963,
      menuName: "划配规则管理2222",
      menuLevel: 1,
      menuType: "1100",
      menuTypeName: "叶子菜单",
      parMenuId: 2769961,
      menuIndex: 1,
      menuDesc: "划配规则管理",
      urlAddr: "[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/locrule/views/LocRuleView",
      systemCode: "727001",
      statusCd: "1000",
      firstLetter: "HPGZGL",
      iconUrl: "icon-gene-man-manager",
      paramEncryptType: "1000",
      menuOpenMode: "1",
      menuCode: "TYMH_MENU_RULE_MGR"
 * }
 * @return {object} 转换成histroy菜单格式
 * {
      id: 2727371,
      sysUserId: 1,
      postId: 92550,
      recentContent: "276104",
      recentType: "1",
      accessDate: "2019-12-08 11:44:48",
      iconUrl: "icon-gene-view-module",
      paramEncryptType: "1000",
      menuOpenMode: "1",
      menuName: "角色模板配置",
      urlAddr: "portal/modules/templet/views/TempletConfigView?confType=1",
      menuCode: "TYMH_MENU_025",
      recentMenuId: 2727371
 * }
 *
 */
export function transform(allFormatter) {
  return {
    id: undefined,
    sysUserId: undefined,
    postId: undefined,
    recentContent: String(allFormatter.menuId),
    recentType: undefined,
    accessDate: undefined,
    iconUrl: allFormatter.iconUrl,
    paramEncryptType: allFormatter.paramEncryptType,
    menuOpenMode: allFormatter.menuOpenMode,
    menuName: allFormatter.menuName,
    urlAddr: allFormatter.urlAddr,
    menuCode: allFormatter.menuCode,
    recentMenuId: undefined,
  };
}
