import React, { useState, useRef, useEffect } from 'react';
import { connect } from 'dva';
import { Form, Icon, Input, Button, Statistic, message } from 'antd';
import { getTerminalType, encryptUtf8 } from '@/utils/utils';
import { sendLoginSmsCode } from '@/services/login';
import styles from './less/login.less';

const FormItem = Form.Item;
const { Countdown } = Statistic;

function Phone(props) {
  const timer = useRef(null);
  // init 初始化获取验证码  loading 为验证码发送倒计时状态 reload 重获验证码
  const [codeStatus, setCodeStatus] = useState('init');
  const [validateText, setValidateText] = useState('获取验证码');

  const {
    form: { getFieldDecorator, validateFields },
    dispatch,
    submitting,
    needVerifyCode,
    verifyCode,
    setLoginWithPwd,
    setPhoneLoginInfo,
  } = props;

  // if (!needVerifyCode) {
  //   //重置验证码
  //   getVerifyCode();

  //   dispatch({
  //     type: 'login/needVerifyCode',
  //     payload: true,
  //   });
  // }

  useEffect(() => {
    getVerifyCode();
  }, []);

  const getCode = () => {
    //获取验证码前验证手机号码
    const value = props.form.getFieldValue('phone');
    if (!value || value.length < 11) {
      return;
    }

    const params = {
      mobile: encryptUtf8(value),
    };

    // 验证码
    // if (needVerifyCode === true) {
      const verifyCodeInput = props.form.getFieldValue('verifyCode');

      if (!verifyCodeInput) {
        message.warn('请输入验证码');
        return;
      }
      params.verifyCode = verifyCodeInput;
    // }

    //发送验证码
    sendLoginSmsCode(params).then(result => {
      if (result.resultCode === '0000') {
        setCodeStatus('loading');
        message.info('短信验证码已发送，请查收！');

        let time = 60;
        setValidateText(`${time}s后重新获取`);
        timer.current = setInterval(() => {
          time -= 1;
          if (time >= 0) {
            setValidateText(`${time}s后重新获取`);
          } else {
            setValidateText('重新获取验证码');
            clearInterval(timer.current);
            setCodeStatus('reload');
            //倒计时加载完成，重置验证码
            getVerifyCode();
          }
        }, 1000);
      } else {
        //重置验证码
        getVerifyCode();

        dispatch({
          type: 'login/needVerifyCode',
          payload: true,
        });
        message.error(result.resultMsg);
      }
    });
  };

  const onFinish = () => {
    setCodeStatus('reload');
  };

  function handleSubmit(e) {
    e.preventDefault();
    validateFields((err, values) => {
      if (!err) {
        const formValues = {
          clientType: getTerminalType(),
          loginWay: 'sms',
          mobileEnc: encryptUtf8(values.phone),
          smsCode: values.smsCode,
          verifyCode: values.verifyCode,
        };

        dispatch({
          type: 'login/login',
          payload: formValues,
          callback: result => {
            if (result.resultCode === 'LOGIN_ERROR_SMS_CODE_EXPIRE') {
              //短信验证码已失效,请重新获取
              //clearInterval(timer.current);
              //setCodeStatus("reload");
              //setValidateText('重新获取验证码');
            }
          },
        });

        setLoginWithPwd(false);
        setPhoneLoginInfo({
          mobileEnc: encryptUtf8(values.phone),
          smsCode: values.smsCode,
          verifyCode: values.verifyCode,
        })
      }
    });
  }

  function getVerifyCode() {
    dispatch({
      type: 'login/setVerifyCode',
    });
  }

  return (
    <Form onSubmit={handleSubmit} className="login-box" autoComplete="off">
      <FormItem className={styles.phoneForm} style={{marginBottom:1}}>
        {getFieldDecorator('phone', {
          rules: [
            {
              required: true,
              message: '请输入手机号码',
            },
            {
              required: false,
              pattern: new RegExp(/^1(3|4|5|6|7|8|9)\d{9}$/, 'g'),
              message: '请输入正确的手机号',
            },
          ],
        })(
          <Input
            prefix={
              <Icon
                type="phone"
                style={{
                  color: 'rgba(0,0,0,.25)',
                }}
              />
            }
            placeholder="请输入手机号码"
            className={styles.phoneInput}
          />
        )}
      </FormItem>

      {/* {needVerifyCode === true ? ( */}
        <FormItem style={{ marginBottom: 16 }}>
          {getFieldDecorator('verifyCode', {
            rules: [
              {
                required: true,
                // min: 8,
                message: '验证码错误',
              },
            ],
          })(
            <Input
              prefix={<Icon type="check-circle" style={{ color: 'rgba(0,0,0,.25)' }} />}
              addonAfter={
                <img
                  src={verifyCode}
                  onClick={getVerifyCode}
                  style={{ cursor: 'pointer' }}
                  width={64}
                  height={28}
                  alt="code"
                />
              }
              type="text"
              placeholder="验证码"
              className={styles.pwInput}
            />
          )}
        </FormItem>
      {/* ) : null} */}

      <FormItem style={{ marginBottom: 16 }}>
        {getFieldDecorator('smsCode', {
          rules: [
            {
              required: true,
              // min: 8,
              message: '输入的验证码有误',
            },
          ],
        })(
          <Input
          className={styles.phoneVerifyInput}
            prefix={<Icon type="lock" style={{ color: 'rgba(0,0,0,.25)' }} />}
            placeholder="请输入验证码"
            maxLength={6}
            addonAfter={
              <div style={{ minWidth: 66 }}>
                {codeStatus === 'init' ? (
                  <a onClick={getCode}>{validateText}</a>
                ) : codeStatus === 'reload' ? (
                  <a onClick={getCode}>{validateText}</a>
                ) : (
                  <a>{validateText}</a>
                )}
              </div>
              /* eslint-enable */
            }
          />
        )}
      </FormItem>

      <FormItem className="padding-top">
        {/* <a className={styles.loginLink1}>去注册</a> */}
        <Button className={styles.loginButton} type="primary" block size="large" htmlType="submit" loading={submitting}>
          登录
        </Button>
        {/* <a className={styles.loginLink2}>忘记密码?</a> */}
      </FormItem>
    </Form>
  );
}

export default connect(({ login, loading }) => ({
  submitting: loading.effects['login/login'],
  needVerifyCode: login.needVerifyCode,
  verifyCode: login.verifyCode,
}))(Form.create()(Phone));
