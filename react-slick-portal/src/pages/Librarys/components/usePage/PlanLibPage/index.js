/* eslint-disable global-require */
import React, { useEffect, useState } from 'react';
import { Typo<PERSON>, Toolt<PERSON>, Spin, Button } from 'antd';
import { qryKlKnowledgeInfoGrid, getKnowledgeIndustryOrder } from '@/pages/Librarys/services-use';
import { TYPE_VALUE, LIBRARYS_TYPE } from '@/pages/Librarys/const';
import commonStyles from '../common.module.less';

const PlanLibPage = ({ goToStep, currentKey, setCurrentKey, toTop }) => {
  const [hotPlanList, setHotPlanList] = useState([]);
  const [hotIndustryList, setHotIndustryList] = useState([]);
  const [spinning, setSpinning] = useState(false);

  const initData = async () => {
    setSpinning(true);
    const res = await qryKlKnowledgeInfoGrid({
      knowledgeClassType: TYPE_VALUE.plan,
      pageSize: 8,
    });
    setHotPlanList(res);

    const industryRes = await getKnowledgeIndustryOrder({
      knowledgeClassType: TYPE_VALUE.plan,
    });
    setHotIndustryList(industryRes);
    setSpinning(false);
    // 当从详情页返回首页时，需要设置currentKey以刷新页面，所以这里要重置currentKey以监听变化
    setCurrentKey('');
  };

  useEffect(() => {
    if (currentKey === TYPE_VALUE.plan) {
      initData();
    }
  }, [currentKey]);

  const handleShowMore = () => {
    goToStep('2', {
      searchParams: {
        knowledgeClassType: TYPE_VALUE.plan,
        sort: 4,
      },
    });
  };

  return (
    <div>
      <div className={commonStyles.topRowPhoto} />
      <Spin spinning={spinning}>
        <div className={commonStyles.module}>
          <div className={commonStyles.moduleTitle}>
            <span>热门方案</span>
            <Button type="link" className={commonStyles.showMoreBtn} onClick={handleShowMore}>
              更多 {'>>'}
            </Button>
          </div>
          <div className={commonStyles.moduleContent}>
            {Array.isArray(hotPlanList) && hotPlanList.length > 0 ? (
              hotPlanList.map(item => (
                <div
                  key={item.id}
                  className={commonStyles.moduleBlock}
                  onClick={() => {
                    goToStep('3', {
                      type: LIBRARYS_TYPE.PLAN,
                      fromHome: true,
                      knowledgeInfoId: item.knowledgeInfoId,
                    });
                  }}
                >
                  <div className={commonStyles.left}>
                    <div className={commonStyles.titleWrap}>
                      <Typography.Paragraph ellipsis={{ rows: 2 }}>
                        <font className={commonStyles.title}>{item.knowledgeName}</font>
                      </Typography.Paragraph>
                    </div>
                    <div className={commonStyles.detail}>
                      <Tooltip title={item.knowledgeDesc} placement="bottomLeft" mouseEnterDelay={1}>
                        <Typography.Paragraph ellipsis={{ rows: 2 }}>{item.knowledgeDesc}</Typography.Paragraph>
                      </Tooltip>
                    </div>
                  </div>
                  <div className={commonStyles.right}>
                    <div className={commonStyles.assist} />
                    <img
                      className={commonStyles.photo}
                      alt="avatar"
                      src={`portal/FileStoreController/download.do?docNbr=${item.iconNbr}`}
                      onError={e => {
                        e.target.src = require('@/pages/Librarys/img/default-icon.png');
                      }}
                    />
                  </div>
                </div>
              ))
            ) : (
              <span style={{ fontSize: '16px' }}>暂无数据</span>
            )}
          </div>
        </div>
        <div className={commonStyles.end} />
        <div className={commonStyles.module} style={{ backgroundColor: 'white' }}>
          <div className={commonStyles.moduleTitle}>热门专题</div>
          <div className={commonStyles.moduleContent}>
            {Array.isArray(hotIndustryList) && hotIndustryList.length > 0 ? (
              hotIndustryList.map(item => (
                <div key={item.industryType} className={commonStyles.bigBlock}>
                  <span className={commonStyles.left}>
                    {/* eslint-disable-next-line import/no-dynamic-require */}
                    <img src={require(`../../../img/${item.industryType}.png`)} alt="" />
                    <div className={commonStyles.industryFont}>{item.industryName || item.PNAME}</div>
                  </span>
                  <span className={commonStyles.right}>
                    {Array.isArray(item.subIdustryList) &&
                      item.subIdustryList.map(sub => (
                        <div
                          key={sub.industryType}
                          className={commonStyles.smallContent}
                          onClick={() => {
                            toTop();
                            goToStep('2', {
                              searchParams: {
                                knowledgeClassType: TYPE_VALUE.plan,
                                industryType: item.industryType,
                                industryFirstType: sub.industryType,
                              },
                            });
                          }}
                        >
                          <font>{sub.industryName || sub.PNAME}</font>
                        </div>
                      ))}
                  </span>
                </div>
              ))
            ) : (
              <span style={{ fontSize: '16px' }}>暂无数据</span>
            )}
          </div>
        </div>
      </Spin>
      <div className={commonStyles.end} />
    </div>
  );
};

export default PlanLibPage;
