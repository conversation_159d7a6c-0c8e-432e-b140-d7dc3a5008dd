@import './variables.less';

:global {
  .view-normal {
    padding-top: @header-height;
  }
  .view-inner {
    #layout-body {
      padding-top: 0 !important;
      padding-left: 0 !important;
    }
  }
  #root,
  #layout-container,
  #layout-body {
    height: 100%;
  }

  #layout-container,
  #layout-body,
  #layout-header,
  .layout-tabs-nav,
  .layout-tabs-nav-blue,
  .setting-drawer,
  .menu-drawer {
    transition: all 0.3s ease;
  }

  // #layout-header{
  //   background-color: #F0F6FF;
  //   border-color: #F0F6FF;
  // }

  #layout-container {
    position: relative;
    left: 0;
  }
  // OPEN SETTING DRAWER
  .setting-drawer {
    position: fixed;
    top: 0;
    right: -@setting-drawer-width;
    bottom: 0;
    z-index: 999;
    width: @setting-drawer-width;
    // transform: translate(@setting-drawer-width, 0);
  }

  .setting-drawer-active {
    left: -@setting-drawer-width !important;
    #layout-body {
      position: relative;
    }
    #layout-header,
    .layout-tabs-nav,
    .layout-tabs-nav-blue,
    .menu-drawer {
      right: @setting-drawer-width !important;
      left: -@setting-drawer-width !important;
    }
    .setting-drawer {
      right: 0;
    }
    .setting-drawer-mask {
      right: @setting-drawer-width !important;
      left: 0;
      transition: all 0.000001s ease 0.3s;
    }
  }

  .setting-drawer-mask {
    position: fixed;
    top: 0;
    right: -100%;
    bottom: 0;
    left: 100%;
    z-index: 998;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.45);
    filter: alpha(opacity=50);
  }

  // OPEN MENU DRAWER
  .menu-drawer {
    position: fixed;
    top: @header-height;
    bottom: 0;
    left: 0;
    width: 0;
  }
  .menu-drawer-active {
    #layout-body {
      padding-left: @menu-drawer-width;
    }
    .menu-drawer {
      width: @menu-drawer-width;
    }
    &.tabs-nav-fixed {
      .layout-tabs-nav {
        left: @menu-drawer-width;
      }
    }
    &.tabs-nav-fixed-blue {
      .layout-tabs-nav {
        left: @menu-drawer-width;
      }
    }
  }

  // LAYOUT TABS NAV
  .layout-tabs-nav-blue {
    height: 39px; // 务必设置一个固定高度，否则global.size.height计算失效
    margin-bottom: 0;
    padding-left: @slick-space-base;
    background-color: #fff;
    // box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.04);
    .ant-tabs-nav-container {
      height: 38px;
    }
    .ant-tabs-tab {
      margin-top: 4px;
      margin-right: 3px;
      padding: 6px;
      padding-left: 10px;
      color: #555864;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      background-color: #F6F6F7;
      border-radius: 2px 2px 0 0;
    }
    .anticon {
      margin-right: 0 !important;
      margin-left: 8px;
      font-size: 12px;
    }

    .ant-tabs-tab-active {
      margin-top: 4px;
      color: #FFF;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      background-color: #36E;
      border-radius: 2px 2px 0 0;
    }

    .ant-tabs-ink-bar {
      height: 0;
      background-color: #36E;
    }

    .ant-tabs-tab-unclosable {
      margin-right: 10px;
    }

    .ant-tabs-nav .ant-tabs-tab:hover {
      color: #FFF;
      background-color: #36E;
    }
  }

  .tabs-nav-fixed-blue {
    .layout-tabs-nav-blue {
      position: fixed;
      top: @header-height !important;
      right: 0;
      left: 0;
      z-index: 100;
    }
    #layout-body {
      padding-top: 39px;
    }
  }

  .homeTabs {
    position: fixed;
    top: -100px;
  }
}
