import { getTerminalType } from '@/services/common';

export default {
  namespace: 'common',

  state: {
    terminalTypeMap: [],
  },

  effects: {
    *getTerminalTypeMap(_, { call, put }) {
      const response = yield call(getTerminalType);

      if (Array.isArray(response)) {
        yield put({
          type: 'save',
          payload: {
            terminalTypeMap: response.filter(
              item => item.name.toLocaleLowerCase().indexOf('pad') === -1
            ),
          },
        });
      }
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
