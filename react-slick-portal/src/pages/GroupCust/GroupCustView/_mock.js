import mockjs from 'mockjs';
import { delay } from 'roadhog-api-doc';
import defaultSettings from '@/defaultSettings';

const proxy = {
  'POST /portal/GroupEnterpriseController/qryGroupEnterpriseInfo.do': (req, res) => {
    const {
      body: { queryType },
    } = req;
    res.send(
      {"resultCode":"TRUE","resultMsg":"操作成功","resultObject":{"rspParam":{"busiInfo":{"outData":[{"SEND_HQ_CODE":"","ORG_ID":"71120141","GROUP_TYPE":"1","MP_GROUP_CODE":"111","ENT_EMPLOYEE_COUNT":"1","REMARKS":"同步账户","PROVINCE_CODE":"471","PAYFOR_WAY_CODE":"1","SUPER_GROUP_ID":"","GROUP_ESTIMATE_LEVEL":"","customerManagerStaffNO":"10010183","POST_CODE":"","customerAddress":"","GROUP_SOURCE":"","IS_SEND_MANAGEONE":"","CHANNEL_ID":"null","CALLING_AREA_B":"25","IS_CMIOT":"","CALLING_AREA_A":"11002","DIRECT_CUST_ID":"","XLT_NUM":"","customerName":"测试集团1846491","GROUP_ID":"471710052548","OTH_USE_DTL":"","JK_GROUP_TYPE":"","CHIP_AREA_CODE":"4000","C_ADD_B_ENTERPRISE":"","GROUP_TYPE_DETAIL":"1","GROUP_LEVEL_NEW":"1","GROUP_ORG_CODE":"*********","IS_PHOTO":"0","FAX_NBR":"","CREATE_OP_ID":"10010183","BUSICOUNTYID":"","OP_ID":"74030002","GROUP_ADVERSARY":"","REC_TYPE":"","UNISOCIALCREDITCODE":"","ENTERPRISE_SCOPE":"1","EC_CODE":"471A471710052548","ENTERPRISE_TYPE_CODE":"","IMPORT_FLAG":"0","NET_COVER":"0","LIKE_DISCNT_MODE":"","BUSICOUNTYNAME":"","JURISTIC_TYPE_CODE":"200010","IS_BRANCH":"","CALLING_TYPE":"1","DONE_CODE":"0","TURNOVER":"","DIRECT_ORG_CODE":"","COMM_BUDGET":"","GROUP_HALF_LEVEL":"2","SUPER_GROUP_NAME":"","MGMT_COUNTY":"4000","customerLevel":"","GROUP_NAME":"测试集团1846491","REGMANADEPARTMENT":"","createTime":"2019-01-08 09:19:05","BLANK_ENTERPRISE_VISIT_ID":"","EMPLOYEE_ARPU":"","GROUP_CONTACT_PHONE":"139****9999","CUST_TIERING_LEVEL":"","CREDIT_LEVEL":"1","YEAR_GAIN":"1","GROUP_PAY_MODE":"1","GROUP_DUTY_CUST":"","SCOPE":"","JURISTIC_IDEN_NR":"1234**************","MULTI_PROVINCE":"1","TAG_DATE":"","PROD_DESC":"","EMAIL":"","ORGA_ENTERPRISE_ID":"***************","SWITCH_PHONE":"2222","contactsInfo":"","SING_OVER_LAY":"","customerType":"1","AUDIT_CHANNEL":"CRM","UNION_NUM":"","MGMT_DISTRICT":"471","BUSILOCATION":"","IS_TOP100":"0","CREATE_ORG_ID":"10010000","IS_SMALLMICRO":"","EXPIRE_DATE":"2099-12-31 23:59:59","DONE_DATE":"2025-02-26 09:47:47","P_CODE":"","GROUP_LEVEL":"","CREATE_DATE":"2019-01-08 09:19:05","PASSWORD":"3bd4cae66bc3635f","IS_DIRECT":"0","LATITUDE":"","WEBSITE":"","PTN_COVER":"0","REGION_ID":"471","GROUP_FESTIVAL":"","VALID_DATE":"2019-01-08 09:19:05","VPMN_ID":"","CALLING_AREA":"11000","ENTERPRISE_SIZE_CODE":"3","GROUP_ADDR":"********","GROUP_STATUS":"2","C_CODE":"","LONGITUDE":"","BUSI_LICENCE_NO":"123456","WRITEFEE_COUNT":"","CALLING_DIFF_TAG":"","customerStatus":"1","BUSICOMPANYID":"","IS_SEND_BBOSS":"1","MOBILE_NUM":"","SDH_COVER":"0","SERV_LEVEL":"1","PNATIONAL_GROUP_ID":"","REG_MONEY":"","FIBER_COVER":"0","PNATIONAL_GROUP_NAME":"","NON_COVER":"0","customerNumber":"471710052548","DATA_STATUS":"1","ENT_NAME":"","IS_STRATEGIC_CUSTOMER":"","JURISTIC_NAME":"张*","SYNC_WLW_STATUS":"2","ARR_WARN":"2121211","LIKE_MOBILE_TRADE":""}],"resultList":[{"RESULT_INFO":{"SEND_HQ_CODE":"","ORG_ID":"71120141","GROUP_TYPE":"1","MP_GROUP_CODE":"111","ENT_EMPLOYEE_COUNT":"1","REMARKS":"同步账户","PROVINCE_CODE":"471","PAYFOR_WAY_CODE":"1","SUPER_GROUP_ID":"","GROUP_ESTIMATE_LEVEL":"","POST_CODE":"","GROUP_SOURCE":"","IS_SEND_MANAGEONE":"","CHANNEL_ID":"null","CALLING_AREA_B":"25","IS_CMIOT":"","CALLING_AREA_A":"11002","DIRECT_CUST_ID":"","XLT_NUM":"","GROUP_ID":"471710052548","OTH_USE_DTL":"","JK_GROUP_TYPE":"","CHIP_AREA_CODE":"4000","C_ADD_B_ENTERPRISE":"","GROUP_TYPE_DETAIL":"1","GROUP_LEVEL_NEW":"1","GROUP_ORG_CODE":"*********","IS_PHOTO":"0","FAX_NBR":"","CREATE_OP_ID":"10010183","BUSICOUNTYID":"","OP_ID":"74030002","GROUP_ADVERSARY":"","REC_TYPE":"","UNISOCIALCREDITCODE":"","ENTERPRISE_SCOPE":"1","EC_CODE":"471A471710052548","ENTERPRISE_TYPE_CODE":"","IMPORT_FLAG":"0","NET_COVER":"0","LIKE_DISCNT_MODE":"","BUSICOUNTYNAME":"","JURISTIC_TYPE_CODE":"200010","IS_BRANCH":"","CALLING_TYPE":"1","DONE_CODE":"0","TURNOVER":"","DIRECT_ORG_CODE":"","COMM_BUDGET":"","GROUP_HALF_LEVEL":"2","SUPER_GROUP_NAME":"","MGMT_COUNTY":"4000","GROUP_NAME":"测试集团1846491","REGMANADEPARTMENT":"","BLANK_ENTERPRISE_VISIT_ID":"","EMPLOYEE_ARPU":"","GROUP_CONTACT_PHONE":"139****9999","CUST_TIERING_LEVEL":"","CREDIT_LEVEL":"1","YEAR_GAIN":"1","GROUP_PAY_MODE":"1","GROUP_DUTY_CUST":"","SCOPE":"","JURISTIC_IDEN_NR":"1234**************","MULTI_PROVINCE":"1","TAG_DATE":"","PROD_DESC":"","EMAIL":"","ORGA_ENTERPRISE_ID":"***************","SWITCH_PHONE":"2222","SING_OVER_LAY":"","AUDIT_CHANNEL":"CRM","UNION_NUM":"","MGMT_DISTRICT":"471","BUSILOCATION":"","IS_TOP100":"0","CREATE_ORG_ID":"10010000","IS_SMALLMICRO":"","EXPIRE_DATE":"2099-12-31 23:59:59","DONE_DATE":"2025-02-26 09:47:47","P_CODE":"","GROUP_LEVEL":"","CREATE_DATE":"2019-01-08 09:19:05","PASSWORD":"3bd4cae66bc3635f","IS_DIRECT":"0","LATITUDE":"","WEBSITE":"","PTN_COVER":"0","REGION_ID":"471","GROUP_FESTIVAL":"","VALID_DATE":"2019-01-08 09:19:05","VPMN_ID":"","CALLING_AREA":"11000","ENTERPRISE_SIZE_CODE":"3","GROUP_ADDR":"********","GROUP_STATUS":"2","C_CODE":"","LONGITUDE":"","BUSI_LICENCE_NO":"123456","WRITEFEE_COUNT":"","CALLING_DIFF_TAG":"","BUSICOMPANYID":"","IS_SEND_BBOSS":"1","MOBILE_NUM":"","SDH_COVER":"0","SERV_LEVEL":"1","PNATIONAL_GROUP_ID":"","REG_MONEY":"","FIBER_COVER":"0","PNATIONAL_GROUP_NAME":"","NON_COVER":"0","DATA_STATUS":"1","ENT_NAME":"","IS_STRATEGIC_CUSTOMER":"","JURISTIC_NAME":"张*","SYNC_WLW_STATUS":"2","ARR_WARN":"2121211","LIKE_MOBILE_TRADE":""}}]},"pubInfo":{"message":"success","code":"0","reqSerialNo":"2015060611223320345443"}}},"success":true}
    );
  },
  'POST /portal/rule_IGroupRealNameCSV_queryGroupRealNameInfo': (req, res) => {
    const {
      body: { queryType },
    } = req;
    res.send(
      mockjs.mock({
        resultCode: 'TRUE',
        resultMsg: '操作成功',
        resultObject: {
          rspParam: [
            {
              INFO_ID: '1',
              DATA_TYPE: 1,
              GROUP_ID: 'G001',
              GROUP_NAME: '测试集团1',
              NAME: '张三',
              VALID_DATE: '2023-01-01',
              EXPIRE_DATE: '2023-12-31'
            },
          ],
        },
        success: true,
      })
    );
  },
  'POST /portal/ContractController/qryUnfinishOrderInfos.do': (req, res) => {
    const { body } = req;
    const  { pageSize, currentPage } = body.pageInfo;
    res.send(
      mockjs.mock({
        "resultCode": "TRUE",
        "resultMsg": "操作成功",
        "resultObject": {
          "rspParam": {
            "busiInfo": {
              'outData':[
                {
                  ORDER_ID: 'ORD20230501001',
                  CREATE_DATE: '2023-05-01 09:30:45',
                  UNFINISH_TYPE: '正常',
                  IS_SHOW: 1,
                  BUSI_CODE: '宽带业务办理',
                  IN_MODE_CODE: '网上营业厅',
                  OFFER_INFO: '100M家庭宽带套餐',
                  CURRENT_TASK_NAME: '待审核',
                  ACCESS_NUM: '13812345678',
                  MGMT_DISTRICT: '呼和浩特',
                  AUDIT_OP_ID: '张三',
                  OP_ID: '李四',
                  DEAL_SUGGEST: '客户申请办理宽带业务，资料齐全'
                },
                {
                  ORDER_ID: 'ORD20230502002',
                  CREATE_DATE: '2023-05-02 14:22:33',
                  UNFINISH_TYPE: '异常',
                  IS_SHOW: 1,
                  BUSI_CODE: '手机号码过户',
                  IN_MODE_CODE: '营业厅',
                  OFFER_INFO: '手机号码过户服务',
                  CURRENT_TASK_NAME: '资料审核',
                  ACCESS_NUM: '13987654321',
                  MGMT_DISTRICT: '包头',
                  AUDIT_OP_ID: '王五',
                  OP_ID: '赵六',
                  DEAL_SUGGEST: '客户提交的身份证明材料不完整',
                  ERROR_MESSAGE: '身份证明材料不完整，请补充提交有效身份证正反面照片'
                },
                {
                  ORDER_ID: 'ORD20230503003',
                  CREATE_DATE: '2023-05-03 10:15:20',
                  UNFINISH_TYPE: '组异常',
                  IS_SHOW: 1,
                  BUSI_CODE: '5G套餐升级',
                  IN_MODE_CODE: 'APP',
                  OFFER_INFO: '5G畅享套餐128元',
                  CURRENT_TASK_NAME: '待支付',
                  ACCESS_NUM: '13765432109',
                  MGMT_DISTRICT: '通辽',
                  OP_ID: '钱七',
                  DEAL_SUGGEST: '客户申请升级5G套餐，等待支付',
                  ERR_MESSAGE: '支付系统异常，请稍后重试'
                },
                {
                  ORDER_ID: 'ORD20230504004',
                  CREATE_DATE: '2023-05-04 16:45:12',
                  UNFINISH_TYPE: '正常',
                  IS_SHOW: 0,
                  BUSI_CODE: '宽带提速',
                  IN_MODE_CODE: '电话客服',
                  OFFER_INFO: '宽带提速200M服务',
                  CURRENT_TASK_NAME: '待安装',
                  ACCESS_NUM: '13698765432',
                  MGMT_DISTRICT: '赤峰',
                  AUDIT_OP_ID: '孙八',
                  OP_ID: '周九',
                  DEAL_SUGGEST: '客户申请宽带提速，已通过审核，等待安装'
                },
                {
                  ORDER_ID: 'ORD20230505005',
                  CREATE_DATE: '2023-05-05 08:30:00',
                  UNFINISH_TYPE: '异常',
                  IS_SHOW: 1,
                  BUSI_CODE: '携号转网',
                  IN_MODE_CODE: '营业厅',
                  OFFER_INFO: '携号转网服务',
                  CURRENT_TASK_NAME: '资料审核',
                  ACCESS_NUM: '13512345678',
                  MGMT_DISTRICT: '鄂尔多斯',
                  AUDIT_OP_ID: '吴十',
                  OP_ID: '郑十一',
                  DEAL_SUGGEST: '客户申请携号转网，需核实原运营商欠费情况',
                  ERROR_MESSAGE: '原运营商存在未缴清费用，请客户先缴清后再办理'
                },
                {
                  ORDER_ID: 'ORD20230506006',
                  CREATE_DATE: '2023-05-06 11:20:35',
                  UNFINISH_TYPE: '正常',
                  IS_SHOW: 0,
                  BUSI_CODE: '家庭套餐办理',
                  IN_MODE_CODE: '网上营业厅',
                  OFFER_INFO: '全家享家庭套餐',
                  CURRENT_TASK_NAME: '已受理',
                  ACCESS_NUM: '13923456789',
                  MGMT_DISTRICT: '呼伦贝尔',
                  OP_ID: '马十二',
                  DEAL_SUGGEST: '客户申请办理家庭套餐，已成功受理'
                },
                {
                  ORDER_ID: 'ORD20230507007',
                  CREATE_DATE: '2023-05-07 09:10:25',
                  UNFINISH_TYPE: '组异常',
                  IS_SHOW: 1,
                  BUSI_CODE: 'ITV业务办理',
                  IN_MODE_CODE: 'APP',
                  OFFER_INFO: 'ITV高清套餐',
                  CURRENT_TASK_NAME: '待安装',
                  ACCESS_NUM: '13834567890',
                  MGMT_DISTRICT: '乌兰察布',
                  AUDIT_OP_ID: '林十三',
                  OP_ID: '王十四',
                  DEAL_SUGGEST: '客户申请ITV业务，等待安装',
                  ERR_MESSAGE: '安装区域暂无网络覆盖，需进行网络改造后安装'
                },
                {
                  ORDER_ID: 'ORD20230508008',
                  CREATE_DATE: '2023-05-08 14:50:30',
                  UNFINISH_TYPE: '正常',
                  IS_SHOW: 1,
                  BUSI_CODE: '话费充值',
                  IN_MODE_CODE: '自助终端',
                  OFFER_INFO: '话费充值100元',
                  CURRENT_TASK_NAME: '已完成',
                  ACCESS_NUM: '13745678901',
                  MGMT_DISTRICT: '巴彦淖尔',
                  OP_ID: '张十五',
                  DEAL_SUGGEST: '客户成功充值话费100元'
                }
              ]
            },
            "pubInfo": {
              "message": "success",
              "code": "0",
              "reqSerialNo": "2015060611223320345443"
            },
            "pageInfo": {
              "recordCount": currentPage
            }
          }
        },
        "success": true
      })
    );
  },
  'POST /portal/CustomerController/queryCustomerByPartyId.do': (req, res) => {
    res.send(
      {"resultCode":"TRUE","resultMsg":"操作成功","resultObject":{"rspParam":{"busiInfo":{"outData":[{"REGION_ID":"471","CREATE_OP_ID":"1071","CUST_ID":"471140024177356","CUST_SEG_ID":"100000001","VALID_DATE":"2024-10-29 14:52:51","ORG_ID":"10000000","STATUS_REASON":"","OP_ID":"1071","TERMINAL_DATE":"","REMARKS":"","MGMT_DISTRICT":"471","CREATE_ORG_ID":"10000000","EXPIRE_DATE":"2099-12-31 23:59:59","DONE_DATE":"2024-10-29 14:55:21","CHANNEL_ID":"","DONE_CODE":"0","STATUS_DATE":"","CUST_SERVICE_LEVEL":"","MGMT_COUNTY":"7101","BRAND":"","DATA_STATUS":"1","CREATE_DATE":"2024-10-29 14:52:51","PASSWORD":"","ACTIVATE_DATE":"","CUST_STATUS_ID":"","BUSI_CODE":"","CUST_SERVICE_CODE":"","PARTY_ID":"471130003642155"}]},"pubInfo":{"message":"success","code":"0","reqSerialNo":"2025031217560483198222"}}},"success":true}
    );
  },
};

export default delay(proxy, defaultSettings.delay);
