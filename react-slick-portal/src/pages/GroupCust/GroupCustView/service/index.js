import request from '@/utils/request';

/**
 * 基本信息-集团法人信息
 * operType: DELETE-删除 ADD-删除 QUERY-查询 *必传
 * groupId: 集团编码  *必传
 * @param props
 * @returns {*}
 */
export function legalPersonInfoManage(props) {
  const { operType, groupId, ...restParams } = props;

  return request('portal/GroupLegalPersionController/legalPersonInfo.do', {
    method: 'post',
    data: {
      operType,
      groupId,
      ...restParams,
    },
  });
}

/**
 * 集团文件上传
 * @param form-data
 * fileType: 文件类型 // 1-集团客户证件扫描附件 2-集团业务受理协议文件上传 3-专线业务公共信息附件 4-授权委托书以及经办人照片 5-云MAS合同附件拷贝目录 6-集团拜访文件 7-知识库文件
 * @returns {*}
 */
export function fileUpload(formData) {
  return request('portal/GroupFileController/fileUpload.do', {
    method: 'post',
    data: formData,
    processData: false,
    contentType: false,
  });
}

/**
 * 集团文件下载
 * @param fileId
 * @returns {*}
 */
export function fileDownload(fileId) {
  return request('portal/GroupFileController/fileUpload.do', {
    method: 'get',
    data: { fileId },
  });
}

/* 通过集团客户编码查询集团客户信息 */
export function qryGroupEnterpriseInfo(groupId) {
  return request('portal/GroupEnterpriseController/qryGroupEnterpriseInfo.do', {
    method: 'post',
    data: {
      type: 'cust_IQueryEnterpriseCSV_queryEnterpriseByGroupId',
      data: {
        groupId,
      },
      queryMode: '1',
    },
  });
}

/* 通过集团客户编码查询集团客户信息--不脱敏 */
export function qryGroupEnterpriseInfoNoMask(groupId) {
  return request('portal/GroupEnterpriseController/qryGroupEnterpriseInfo.do', {
    method: 'post',
    data: {
      type: 'cust_IQueryEnterpriseCSV_queryEnterpriseByGroupId',
      data: {
        groupId,
        maskFlag: '0',
      },
      queryMode: '1',
    },
  });
}

/* 查询客户信息 根据 partyid */
export function queryCustomerByPartyId(partyId) {
  return request('portal/CustomerController/queryCustomerByPartyId.do', {
    method: 'post',
    data: {
      partyId,
    },
  });
}

/* 查询集团客户经理 */
export function qryGroupEnterpriseManagerByGroupId(groupId) {
  return request('portal/GroupEnterpriseController/qryGroupEnterpriseManagerByGroupId.do', {
    method: 'post',
    data: {
      groupId,
    },
  });
}

/* 集团附加信息查询 */
export function qryEnterpriseExpandInfo(data) {
  return request('portal/GroupEnterpriseController/queryEnterpriseByOrgaEnterpriseId.do', {
    method: 'post',
    data,
  });
}

/* 集团附加信息查询 */
export function modifyEnterpriseExpandInfo(data) {
  return request('portal/GroupEnterpriseController/modifyEnterpriseExpandInfo.do', {
    method: 'post',
    data,
  });
}

/* 行业类别转换 */
export function changeCallingApi(data) {
  return request('portal/CommonStaticDataController/queryOrderFromOrderCenter.do', {
    method: 'post',
    data,
  });
}
