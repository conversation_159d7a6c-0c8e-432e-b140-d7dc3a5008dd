import React, { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Alert, Empty, Row, Col } from 'antd';
import { openMenu } from '@/utils/utils';
import imgs from '@/components/NavigateImgs';
import deleteImg from '@/pages/Dashboard/img/customNavigation/delete.png';
import addItem from '@/pages/Dashboard/img/customNavigation/addItem.png';
import SceneSelect from './SceneSelect';
import styles from '../configManage.less';

const SceneConfig = props => {
  const { sceneData, allMenu, dispatch } = props;

  const [tempData, setTempData] = useState([]);
  const [selectVisible, setSelectVisible] = useState(false);

  useEffect(() => {
    dispatch({
      type: 'customNavigation/getSceneSelectMenus',
    });
  }, []);

  const changeSceneTemp = data => {
    setTempData(data);
    dispatch({
      type: 'customNavigation/saveSceneTempData',
      payload: data,
    });
  };

  useEffect(() => {
    changeSceneTemp(sceneData);
  }, [sceneData]);

  const deleteMenu = (e, menu) => {
    e.stopPropagation();
    const list = tempData.filter(item => item.menuCode !== menu.menuCode);
    changeSceneTemp(list);
  };

  // 菜单跳转
  const handleMenuOpen = menu => {
    openMenu(menu, allMenu, dispatch);
    dispatch({
      type: 'customNavigation/toggleVisible',
      payload: false,
    });
  };

  const sceneItems = item => (
    <div key={item.menuCode} className={styles.sceneItem} onClick={() => handleMenuOpen(item)}>
      <img
        src={imgs[item.iconUrl ?? 'icon1'] ?? imgs.icon1}
        width={40}
        height={40}
        alt=""
      />
      <div className={styles.menuName} title={item.menuName}>{item.menuName}</div>
      <img src={deleteImg} height={16} width={16} alt="" onClick={e => deleteMenu(e, item)} className={styles.deleteImg} />
    </div>
  );

  return (
    <div className={styles.sceneConfig}>
      <Alert
        message={(<span>场景专区最多添加<span style={{ color: '#0085D0' }}> 10 </span>个菜单导航</span>)}
        type="info"
        showIcon
        style={{ margin: '16px 0', border: 'none', borderRadius: '4px' }}
      />
      <Row gutter={[16, 16]} style={{ height: '344px', overflow: 'hidden auto' }}>
        {tempData.length ? (
          tempData.map(item => (
            <Col span={4}>{sceneItems(item)}</Col>
          ))
        ) : null}
        <Col span={4} hidden={tempData.length >= 10}>
          <div className={`${styles.sceneItem} ${styles.itemEmpty}`} onClick={() => setSelectVisible(true)}>
            <img src={addItem} width={32} height={32} alt="" />
            <div className={styles.addTitle}>添加</div>
          </div>
        </Col>
      </Row>
      {selectVisible && (
        <SceneSelect
          visible={selectVisible}
          close={() => setSelectVisible(false)}
          tempData={tempData}
          submit={selectedList => {
            changeSceneTemp(selectedList);
          }}
        />
      )}
    </div>
  );
};
export default connect(({ customNavigation, menu }) => ({
  sceneData: customNavigation.sceneData,
  allMenu: menu.all,
}))(SceneConfig);
