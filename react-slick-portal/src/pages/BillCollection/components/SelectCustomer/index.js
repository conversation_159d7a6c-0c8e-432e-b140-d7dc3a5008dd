import React, { useImperativeHandle, useState } from 'react';
import { Card, Form, Row, Col, Descriptions, Button } from 'antd';
import { connect } from 'dva';
import ReactStars from 'react-stars/dist/react-stars';
import UserSearch from '@/pages/BillCollection/components/UserSearch';
import style from './index.less';

const SelectCustomer = props => {
  const { form, cRef, groupId, goToStep } = props;
  const { getFieldDecorator, validateFields } = form;
  const [groupInfo, setGroupInfo] = useState({});
  const [custInfo, setCustInfo] = useState({});
  const handleSelectCust = record => {
    // 处理选中的客户信息
    console.log('选中的客户信息:', record);
  };

  useImperativeHandle(cRef, () => ({
    next: () =>
      new Promise(resolve => {
        validateFields((err, values) => {
          /* if (err) {
            debugger;
            resolve(false);
            return;
          } */
          goToStep(2);
        });
      }),
  }));
  return (
    <Card className="cute" title="集团客户信息" bordered={false}>
      {/* 用户搜索 */}
      <Row>
        <Col span={8} offset={8}>
          <Form.Item>
            {getFieldDecorator('cust', {
              rules: [{ required: true, message: '请输入查询条件' }],
            })(<UserSearch callback={handleSelectCust} />)}
          </Form.Item>
        </Col>
      </Row>
      {/* 用户信息 */}
      <div className={style.content}>
        <div className={style.header}>
          {/* 用户名称 */}
          <div className={style.left}>
            <div className={style.title}>公司</div>
            <div className={style.tag}>asdas</div>
            <ReactStars count={5} size={16} edit={false} color1="#EFF0F1" color2="#FAAF0C" />
          </div>
          <Button>账单明细</Button>
        </div>
        <Descriptions column={4}>
          <Descriptions.Item label="客户名称">内蒙古移动集团有限公司</Descriptions.Item>
          <Descriptions.Item label="付费名称">xxxxx</Descriptions.Item>
          <Descriptions.Item label="付费类型">现金</Descriptions.Item>
          <Descriptions.Item label="归属地市">内蒙古呼和浩特</Descriptions.Item>
          <Descriptions.Item label="用户类型">xxx</Descriptions.Item>
          <Descriptions.Item label="客户评级">未评级</Descriptions.Item>
        </Descriptions>
        <div className={style.detail}>
          <Descriptions column={4}>
            <Descriptions.Item label="往月欠费">0.00</Descriptions.Item>
            <Descriptions.Item label="实时话费">0.00</Descriptions.Item>
            <Descriptions.Item label="预存话费">xx</Descriptions.Item>
            <Descriptions.Item label="专项结余">xxx</Descriptions.Item>
            <Descriptions.Item label="违约金合计">xx</Descriptions.Item>
            <Descriptions.Item label="减免违约金">xx</Descriptions.Item>
            <Descriptions.Item span={2} label="往月欠费（不含违约金）">
              xx
            </Descriptions.Item>
          </Descriptions>
        </div>
      </div>
    </Card>
  );
};

export default connect()(Form.create()(SelectCustomer));
