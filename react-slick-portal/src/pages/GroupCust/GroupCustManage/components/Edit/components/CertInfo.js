import { Button, Card, Col, Form, Input, Row, Select, Icon, Popover } from 'antd';
import React, { useCallback, useEffect } from 'react';
import { useBoolean } from '@umijs/hooks';
import { connect } from 'dva';
import styles from '../index.less';
import QueryGroupCert from './QueryGroupCert';
import AddGroupCert from './AddGroupCert';
import { COMPARE_STATUS_NAME, GROUP_CARD_TYPE } from '@/utils/consts';
import GroupFileUpload from '@/components/GroupFileUpload';
import TakePhoto from '@/busiComponents/TakePhoto';
import style from '@/busiComponents/TakePhoto/index.less';

const FormItem = Form.Item;
const formItemLayout = {
  // labelAlign: 'right',
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
// const ButtonGroup = Button.Group;
const CertInfo = props => {
  const { form, formData, onValuesChange, dispatch, currentFormFieldState, viewMode } = props;
  const { state: queryCertVisible, setTrue: showQueryCertModal, setFalse: hideQueryCertModal } = useBoolean(false);
  const { state: addCertVisible, setTrue: showAddCertDrawer, setFalse: hideAddCertDrawer } = useBoolean(false);
  const { getFieldDecorator, getFieldValue } = form;
  const handleChooseCert = () => {
    hideQueryCertModal();
  };
  // const renderExtraContent = useMemo(() => {
  //   if (props.editable) {
  //     return (
  //       <ButtonGroup>
  //         <Button onClick={() => showAddCertDrawer()}>新增</Button>
  //         <Button onClick={() => showQueryCertModal()}>选择已有证件</Button>
  //       </ButtonGroup>
  //     );
  //   }
  //   return null;
  // }, [props.editable]);

  // 组织机构编码校验
  const validateGroupOrgCode = (rule, value, callback) => {
    const reg = /^[0-9a-zA-Z]*$/;
    if (!value) {
      // 如果值为空，则不报错，允许为空或根据需求修改为必填项
      callback();
      return;
    }

    if (!reg.test(value)) {
      callback('集团组织机构代码含有非法字符');
      return;
    }

    callback(); // 验证通过
  };
  // 集团证件变更, 切换[证件号码],[证件名称],[生产经营具体地址]
  const changeGroupLType = e => {
    if (e === '200017') {
      // 企业机构代码证 可输入
      dispatch({
        type: 'AddGroupCust/updataFormStateAll',
        payload: {
          GROUP_LICENCE_NO: { disabled: false },
          GROUP_LICENCE_NAME: { disabled: false },
          GROUP_LICENCE_ADDRESS: { disabled: false },
        },
      });
    } else {
      // 其他 禁止输入
      dispatch({
        type: 'AddGroupCust/updataFormStateAll',
        payload: {
          GROUP_LICENCE_NO: { disabled: true },
          GROUP_LICENCE_NAME: { disabled: true },
          GROUP_LICENCE_ADDRESS: { disabled: true },
        },
      });
    }
    const params = {
      GROUP_LICENCE_NAME: '', // 证件号码
      GROUP_LICENCE_NO: '', // 证件名称
      GROUP_LICENCE_ADDRESS: '', // 生产经营具体地址
    };
    form.setFieldsValue(params);
    // onValuesChange(
    //   {
    //     'CertInfo.GROUP_LICENCE_NAME': '',
    //     'CertInfo.GROUP_LICENCE_NO': '',
    //     'CertInfo.GROUP_LICENCE_ADDRESS': '',
    //   },
    //   true
    // );
  };
  // 新增证件回调
  const handleAddCert = () => {
    hideAddCertDrawer();
  };
  const updateFormField = useCallback(
    data => {
      if (!data.field) return;
      form.setFieldsValue({
        [data.field]: data.value,
      });
    },
    [form]
  );
  useEffect(() => {
    dispatch({
      type: 'AddGroupCust/registerMethods',
      payload: {
        componentName: 'CertInfo',
        methods: { updateFormField },
      },
    });

    return () => {
      dispatch({
        type: 'AddGroupCust/unregisterMethods',
        payload: { componentName: 'CertInfo' },
      });
    };
  }, [dispatch]);

  const handleQueryPop = () => {
    hideQueryCertModal();
  };
  return (
    <>
      <Card className="cute" title="集团证件信息" headStyle={{ background: '#E7F2FB', padding: '0 20px' }} bodyStyle={{ padding: '0' }}>
        {/* <div className={styles.warp}> */}
        <Form className="flow2 fix-label" onValuesChange={onValuesChange}>
          <Row>
            <Col span={8}>
              <FormItem label="集团证件" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_LICENCE_NO', {
                  initialValue: '',
                  // rules: [{ required: true, message: '请选择证件!' }],
                })(
                  <Input
                    className={style.inputContainer}
                    style={{ width: '80%' }}
                    disabled={viewMode === 'view'}
                    readOnly
                    // enterButton
                    addonAfter={(
                      <div>
                        <Button type="primary" shape="circle" icon="search" onClick={() => showQueryCertModal()} />
                        <Button type="primary" shape="circle" icon="plus" onClick={() => showAddCertDrawer()} />
                      </div>
                    )}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="集团证件标识" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_IDEN_PHOTO', {
                  initialValue: formData?.GROUP_IDEN_PHOTO,
                })(<Input className={styles.inputStyle} disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="集团证件生效时间" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_IDEN_EFF_DATE', {
                  initialValue: formData?.GROUP_IDEN_EFF_DATE,
                })(<Input disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="集团证件失效时间" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_IDEN_EXP_DATE', {
                  initialValue: formData?.GROUP_IDEN_EXP_DATE,
                })(<Input disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            {/* 200017 */}
            <Col span={8}>
              <FormItem label="证件类型" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_BUSI_LICENCE_TYPE', {
                  initialValue: formData?.GROUP_BUSI_LICENCE_TYPE || '200010',
                  rules: [{ required: true, message: '请选择证件类型!' }],
                })(
                  <Select
                    className={styles.inputStyle}
                    placeholder="请选择"
                    onChange={changeGroupLType}
                    disabled={viewMode === 'view' || currentFormFieldState?.GROUP_BUSI_LICENCE_TYPE?.disabled}
                  >
                    {Object.keys(GROUP_CARD_TYPE).map(key => (
                      <Select.Option key={key} value={key}>
                        {GROUP_CARD_TYPE[key]}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="上传证件附件" {...formItemLayout} className={styles.fileAreaFormItem}>
                {getFieldDecorator('EcCustLicenceInfo', {
                  initialValue: formData?.EcCustLicenceInfo,
                  rules: [{ required: true, message: '请上传证件附件!' }],
                })(
                  <GroupFileUpload
                    accept=".doc,.docx,.jpg,.png,.rar,.zip,.rar,.7z"
                    fileType="1"
                    detailState={viewMode === 'view' || currentFormFieldState?.EcCustLicenceInfo?.disabled}
                    extraIcon={(
                      <Popover
                        onClick={e => {
                          e.stopPropagation();
                        }}
                        content="文件类型[.doc,.docx,jpg,.png,.rar,.zip]"
                        placement="right"
                        trigger="hover"
                      >
                        <Icon
                          onClick={e => {
                            e.stopPropagation();
                          }}
                          type="question-circle"
                          style={{ 'margin-left': '5px' }}
                        />
                      </Popover>
                    )}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="集团半年级别" {...formItemLayout}>
                {getFieldDecorator('GROUP_HALF_LEVEL', {
                  initialValue: formData?.GROUP_HALF_LEVEL || '9',
                  // rules: [{ required: true, message: '请选择证件类型!' }],
                })(<Input disabled={viewMode === 'view'} />)}
              </FormItem>
            </Col>
            <Col span={8} hidden>
              <FormItem label="证件名称" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_LICENCE_NAME', {
                  initialValue: formData?.GROUP_LICENCE_NAME,
                  // rules: [{ required: true, message: '请输入证件名称!' }],
                })(<Input className={styles.inputStyle} disabled={viewMode === 'view' || currentFormFieldState?.GROUP_LICENCE_NAME?.disabled} placeholder="请输入" />)}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={8} hidden>
              <FormItem label="证件号码" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_LICENCE_NO', {
                  initialValue: formData?.GROUP_LICENCE_NO,
                  // rules: [{ required: true, message: '请输入证件号码!' }],
                })(<Input className={styles.inputStyle} placeholder="请输入" disabled={viewMode === 'view' || currentFormFieldState?.GROUP_LICENCE_NO?.disabled} />)}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={8}>
              <FormItem label="组织机构代码" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_ORG_CODE', {
                  initialValue: formData?.GROUP_ORG_CODE,
                  rules: [
                    {
                      required: !currentFormFieldState?.GROUP_ORG_CODE?.disabled,
                      message: '请输入组织机构代码!',
                    },
                    { validator: validateGroupOrgCode },
                  ],
                })(
                  <Input
                    className={styles.inputStyle}
                    placeholder="请输入"
                    disabled={viewMode === 'view' || currentFormFieldState?.GROUP_ORG_CODE?.disabled}
                    suffix={(
                      <Popover
                        content="全国组织机构代码由八位数字（或大写拉丁字母）本体代码和一位数字（或大写拉丁字母）校验码组成"
                        placement="right"
                        trigger="hover"
                      >
                        <Icon type="question-circle" style={{ 'margin-left': '5px' }} />
                      </Popover>
                    )}
                  />
                  // <div style={{ display: 'flex' }}>
                  //   <Input className={styles.inputStyle} placeholder="请输入" disabled={currentFormFieldState?.GROUP_ORG_CODE?.disabled} style={{ flexShrink: '0' }} />
                  //   <Popover content="全国组织机构代码由八位数字（或大写拉丁字母）本体代码和一位数字（或大写拉丁字母）校验码组成" placement="right" trigger="hover">
                  //     <Icon type="question-circle" style={{ 'margin-left': '5px' }} />
                  //   </Popover>
                  // </div>
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="授权委托书拍照" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('POWER_OF_ATTORNEY', {
                  initialValue: formData?.POWER_OF_ATTORNEY,
                })(
                  <TakePhoto
                    customStyle={{ width: '80%' }}
                    certType="01"
                    fillBack="sqwts"
                    idenNbr={getFieldValue('GROUP_LICENCE_NO')}
                    onCallback={({ FILE_ID }) => form.setFieldsValue({ POWER_OF_ATTORNEY: FILE_ID })}
                    viewMode={viewMode}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="是否通过在线公司核验" {...formItemLayout} className={styles.textAreaFormItem}>
                {getFieldDecorator('GROUP_COMPARE_STATUS', {
                  initialValue: formData?.GROUP_COMPARE_STATUS,
                })(
                  <Select className={styles.inputStyle} placeholder="请选择" disabled={viewMode === 'view'}>
                    {Object.keys(COMPARE_STATUS_NAME).map(key => (
                      <Select.Option key={key} value={key}>
                        {COMPARE_STATUS_NAME[key]}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={8} />
          </Row>
        </Form>
        {/* </div> */}
      </Card>
      <QueryGroupCert
        form={form}
        onValuesChange={onValuesChange}
        handleQueryPop={() => handleQueryPop()}
        visible={queryCertVisible}
        onOk={handleChooseCert}
        onCancel={hideQueryCertModal}
      />
      <AddGroupCert
        form={form}
        onValuesChange={onValuesChange}
        visible={addCertVisible}
        onOk={handleAddCert}
        onCancel={hideAddCertDrawer}
        callback={handleAddCert}
      />
    </>
  );
};

export default connect(({ AddGroupCust }) => ({
  currentFormFieldState: AddGroupCust.currentFormFieldState,
  formData: AddGroupCust.currentForm,
}))(Form.create()(CertInfo));
