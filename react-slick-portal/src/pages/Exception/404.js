import React from 'react';
import { formatMessage } from 'umi/locale';
import { connect } from 'dva';
import router from 'umi/router';
import { Button } from 'antd';
import Link from 'umi/link';
import Exception from '@/components/Exception';

// TODO: 要先判断上一页是否存在，不存在应跳回首页
const Exception404 = ({ viewMode }) => (
  <Exception
    type="404"
    viewMode={viewMode}
    desc={formatMessage({ id: 'app.exception.description.404' })}
    linkElement={Link}
    actions={
      <Button type="primary" size="large" onClick={() => router.replace('/')}>
        返回首页
      </Button>
    }
    backText={formatMessage({ id: 'app.exception.back' })}
  />
);

export default connect(({ setting }) => ({
  viewMode: setting.viewMode,
}))(Exception404);
