.orgManage {
  display: table;
  .orgManageLeft {
    display: table-cell;
    min-width: 250px;
    vertical-align: top;
    :global {
      .ant-card .ant-card-body {
        padding: 0px;
      }
    }
  }
  .orgManageRight {
    width: 100%;
    display: table-cell;
    vertical-align: top;
    // .orgTab {
    //   :global {
    //     .ant-card .ant-card-body {
    //       padding: 0px;
    //     }
    //   }
    //   .org_tab {
    //     padding: 16px 0px;
    //   }
    // }
  }
}

.org_menu {
  .org_menu_search {
    padding: 16px 16px 0px 16px;
  }
  .org_menu_tree {
    margin: 0px 16px;
    .org_menu_tree_edit {
      padding-left: 8px;
      visibility: hidden;
      .icon {
        padding-right: 8px;
      }
      .icon:hover {
        color: #1890ff;
      }
    }
  }
  :global {
    .ant-tree li .ant-tree-node-content-wrapper:hover .ant-tree-title > span > span:nth-child(2) {
      visibility: visible;
    }
  }
}

.systemExtra {
  width: 100%;
  text-align: right;
  margin-bottom: 8px;
  div:first-child {
    display: inline-block;
    width: 216px;
    text-align: center;
  }
  div {
    display: inline-block;
  }
}

.clickRowStyle {
  background-color: #e6f7ff;
}
