.wrap {
  .item {
    // display: flex;
    // align-items: center;
    margin-bottom: 14px;

    &:last-child {
      align-items: flex-start;
    }

    .itemTit {
      width: 80px;
      margin-bottom: 4px;
    }

    .formTips {
      color: #9b9b9b;
      font-size: 14px;
    }

    .itemTit::before {
      display: inline-block;
      margin-right: 4px;
      color: #f5222d;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
    }

    .itemCon {
      width: 532px;
    }

    .inputDesc {
      flex: 1;
    }

    .content {
      width: 90%;
      height: 572px;
      border: 1px solid #d9d9d9;
      border-radius: 10px;
    }
  }
}

:global(.ant-modal-body) {
  // max-height: 498px;
  // overflow-y: auto;
}

// :global(.ant-modal-close) {
//   top: -15px;
//   right: -15px;
// }
