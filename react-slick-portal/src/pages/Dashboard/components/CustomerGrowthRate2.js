import React, { useState, useEffect, useRef } from 'react';
import { Card, Empty } from 'antd';
import { Chart, Geom, Axis, Tooltip, Legend } from 'bizcharts';
import useComponentSize from '@rehooks/component-size';
import DataSet from '@antv/data-set';
import request from '@/utils/request';

const cache = {};

function CustomerGrowthRate2() {
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState([]);
  const ref = useRef(null);
  const size = useComponentSize(ref);
  const { height } = size;
  useEffect(() => {
    async function getInitData() {
      setLoading(true);

      // 客户增长趋势
      const response1 = await request('orgauth/WorkbenchController/qryStatisticCustIndicators.do', {
        method: 'get',
        data: { statisticType: '3' },
      });

      if (
        response1 !== null &&
        response1.resultCode === '0' &&
        response1.resultObject.data.resultCode === '0'
      ) {
        const rstData = response1.resultObject.data.resultObject.custStatusStatistic;
        /**
         * rstData预期格式
         * [
         *  {
         *    cust_create_month: "201908",
         *    month_add: 97,
         *    month_basic_add: 0,
         *    month_effect_add: 0,
         *    month_normal_add: 97
         *  }
         * ]
         */
        // [
        //   {
        //     name: '基础建档',
        //     "201908":0,
        //     "201909":10,
        //   },
        //   {
        //     name: '有效建档',
        //     "201908":0,
        //     "201909":10,
        //   },
        //   {
        //     name: '正式建档',
        //     "201908":0,
        //     "201909":10,
        //   }
        // ]
        const obj1 = { name: '基础建档' };
        const obj2 = { name: '有效建档' };
        const obj3 = { name: '正式建档' };
        if (rstData !== undefined) {
          rstData.forEach(function(data) {
            obj1[data.cust_create_month] = data.month_basic_add || 0;
            obj2[data.cust_create_month] = data.month_effect_add || 0;
            obj3[data.cust_create_month] = data.month_normal_add || 0;
          });

          const ds = new DataSet();
          const dv = ds.createView().source([obj1, obj2, obj3]);
          const months = Object.keys(obj1);
          months.splice(months.indexOf('name'));
          dv.transform({
            type: 'fold',
            fields: months,
            // 展开字段集
            key: '月份',
            // key字段
            value: '建档率', // value字段
          });
          cache.chartData = dv;
          setChartData(dv);
        }
      }

      setLoading(false);
    }
    if (Object.hasOwnProperty.call(cache, 'chartData') === false) {
      getInitData();
    }
  }, []);

  let _chartData;
  if (cache.chartData === undefined) {
    _chartData = chartData;
  } else {
    _chartData = cache.chartData;
  }

  return (
    <Card bordered loading={loading}>
      <h4>客户增长趋势</h4>
      {_chartData.rows && _chartData.rows.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无数据"
          style={{ marginTop: 120 }}
        />
      ) : (
        <div>
          <Chart height={251} data={_chartData} forceFit padding="auto">
            <Legend position="top" />
            <Axis name="月份" />
            <Axis name="建档率" />
            <Tooltip />
            <Geom
              type="intervalStack"
              position="月份*建档率"
              color="name"
              style={{
                stroke: '#fff',
              }}
            />
          </Chart>
        </div>
      )}
    </Card>
  );
}

export default CustomerGrowthRate2;
