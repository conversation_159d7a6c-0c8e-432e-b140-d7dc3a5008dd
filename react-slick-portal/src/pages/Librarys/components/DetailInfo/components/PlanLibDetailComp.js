import React from 'react';
import { Descriptions, Tooltip } from 'antd';
import { connect } from 'dva';
import { renderDescItem } from '../const';
import useAttrs from '@/pages/Librarys/useAttrs';
import styles from '../DetailInfo.module.less';

const fields = [
  {
    label: '方案名称',
    key: 'knowledgeName',
    span: 2,
  },
  {
    label: '方案编号',
    key: 'knowledgeNbr',
    optionKey: '',
  },
  {
    label: '归属地市',
    key: 'regionName',
  },
  {
    label: '归属区县',
    key: 'areaName',
  },
  {
    label: '方案分类',
    key: 'knowledgeTypeName',
  },
  {
    label: '9大行业',
    key: 'industryTypeName',
  },
  {
    label: '一级行业',
    key: 'industryFirstTypeName',
  },
  {
    label: '二级行业',
    key: 'industrySubTypeName',
  },
  {
    label: '所属产品',
    key: 'prodTypeName',
  },
  {
    label: '合作方名称',
    key: 'partnerName',
  },
  {
    label: '合作方联系人',
    key: 'workerName',
  },
  {
    label: '合作方联系人电话',
    key: 'workerTel',
  },
  {
    label: '合作方类型',
    key: 'workerDepart',
  },
  {
    label: '方案联系人姓名',
    key: 'contactName',
  },
  {
    label: '方案联系人电话',
    key: 'contactPhone',
  },
  {
    label: '方案联系人邮箱',
    key: 'contactEmail',
  },
  {
    label: '方案维护人姓名',
    key: 'createStaffName',
  },
  {
    label: '方案维护人电话',
    key: 'defandTel',
  },
  {
    label: '方案维护人邮箱',
    key: 'defandMail',
  },
  {
    label: '生效时间(上架)',
    key: 'shelfTime',
  },
  {
    label: '失效时间(下架)',
    key: 'shelvesTime',
    span: 1,
  },
  {
    label: '归属人单位',
    key: 'userDepartName',
    span: 1,
  },
  {
    label: '是否包含5G',
    key: 'is5G',
    span: 1,
  },
  {
    label: '通用标签',
    key: 'generalizedLabel',
    span: 1,
  },
  {
    label: '方案关键词(标签)',
    key: 'knowledgeKeyword',
    span: 3,
  },
  {
    label: '方案描述',
    key: 'knowledgeDesc',
    span: 3,
  },
];

/**
 * 方案库基本信息组件
 * @param {props}} props
 * @returns
 */
const PlanLibDetailComp = props => {
  const { detail, colFields } = props;
  const attrs = useAttrs();

  return (
    <div>
      <Descriptions column="3">
        {fields?.map(row => {
          const text = renderDescItem({ ...row, detail, colFields, attrs });
          return (
            <Descriptions.Item label={row.label} span={row.span} key={row.key}>
              <Tooltip title={text} placement="bottomLeft" mouseEnterDelay={1}>
                <div className={`${row.key === 'knowledgeName' ? styles.description : null}`}>{text}</div>
              </Tooltip>
            </Descriptions.Item>
          );
        })}
      </Descriptions>
    </div>
  );
};

export default connect(({ librarys }) => ({ ...librarys }))(PlanLibDetailComp);
