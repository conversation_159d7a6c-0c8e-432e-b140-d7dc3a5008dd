import React, { useState, useEffect } from 'react';
import { Input, Form, Select, Upload, Row, Col, Button, Icon, Result, message, DatePicker } from 'antd';
import { usePersistFn } from '@umijs/hooks';
import moment from 'moment';
import { getItem } from '@/utils/utils';
import request from '@/utils/request';
import { screenshot } from '../utils';
import AreaTreeSelect from './AreaTreeSelect';
import styles from '../styles.less';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const tailFormItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

// 获取当前时间年月日时分秒
function getNowTime() {
  const currentDate = new Date();
  const yy = currentDate.getFullYear();
  const MM =
    currentDate.getMonth() + 1 < 10 ? `0${currentDate.getMonth() + 1}` : currentDate.getMonth() + 1;
  const dd = currentDate.getDate() < 10 ? `0${currentDate.getDate()}` : currentDate.getDate();
  const HH = currentDate.getHours() < 10 ? `0${currentDate.getHours()}` : currentDate.getHours();
  const mm =
    currentDate.getMinutes() < 10 ? `0${currentDate.getMinutes()}` : currentDate.getMinutes();
  const ss =
    currentDate.getSeconds() < 10 ? `0${currentDate.getSeconds()}` : currentDate.getSeconds();
  return yy + MM + dd + HH + mm + ss;
}

function getAreaValue(orgInfo) {
  const { parentOrgId, orgId, orgLevel } = orgInfo;
  if (orgLevel === 3) {
    return [
      {
        value: String(parentOrgId),
        orgLevel: 2,
      },
      {
        value: orgId,
        orgLevel: 3,
      },
    ];
  }
  if (orgLevel === 2) {
    return [
      {
        value: String(orgId),
        orgLevel: 2,
      },
    ];
  }
  if (orgLevel === 1) {
    return [
      {
        label: '江苏省',
        value: '99',
        orgLevel: 1,
      },
    ];
  }
  return [];
}

function openNewWindow(base64) {
  const img = new Image();
  img.src = base64;
  img.style =
    'display: block; border:2px solid #000; width: calc(100% - 32px);  -webkit-user-select: none;  margin: auto;  background-color: hsl(0, 0%, 90%);  transition: background-color 300ms;';
  const newWin = window.open('', '_blank');

  newWin.document.write(img.outerHTML);
  newWin.document.title = '预览';
  newWin.document.close();
}

// 获取业务类型初始值：若code是十位，则取前八位，否则原样返回
const getInitSubBusiType = code => {
  if (!code) {
    return undefined;
  }

  if (String(code).length === 10) {
    return String(code).slice(0, 8);
  }

  return String(code);
};

// 随机数获取 时间戳+随机数
let random = '';
for (let i = 1; i <= 3; i++) {
  random = `${random}${Math.floor(Math.random() * 10)}`;
}
const orderId = getNowTime() + random;

const uploadButton = (
  <div>
    <Icon type="plus" />
    <div className="ant-upload-text">上传</div>
  </div>
);

function MyForm1({ form, onBack, onSuccess, current }) {
  const { natureOfFailure = '1' } = current; // 1代表一键报障 2代表快捷报障

  const { getFieldDecorator, validateFields, setFieldsValue, getFieldsValue } = form;

  const [loading, setLoading] = useState(false);

  const [success, setSuccess] = useState(false);

  const { userInfo, orgInfo } = getItem('user');

  const [iosLoading, setIosLoading] = useState(false);

  const [isUpLoad, setIsUpLoad] = useState(true);

  const [orderTitle, setOrderTitle] = useState(
    `订单中心${current.name === undefined ? '' : current.name}${
      current.orderId === undefined ? orderId : current.orderId
    }`
  );

  const [busiTypes, setBusiTypes] = useState([]);
  const [subBusiTypes, setSubBusiTypes] = useState([]);

  // 通过业务类型编码获取业务类型名称
  const getbusiNameBybusiCode = code => {
    if (!Array.isArray(subBusiTypes)) {
      return undefined;
    }

    return subBusiTypes.find(item => item.value === code)?.name;
  };

  const handleSubmit = async e => {
    e.preventDefault();

    // 一键报障提交
    validateFields((err, fieldsValue) => {
      if (err) return;
      setIosLoading(true);
      // 取图片信息
      const fileData = {
        fileName: fieldsValue.fileList[0].name,
        fileContent: fieldsValue.fileList[0].thumbUrl,
      };
      // 删除多余字段
      delete fieldsValue.fileList;
      const { lanId } = fieldsValue;

      if (Array.isArray(lanId)) {
        lanId.forEach(k => {
          // 省或市
          if ([1, 2].includes(k.orgLevel)) {
            fieldsValue.lanId = k.value;
          }
          if (k.orgLevel === 3) {
            fieldsValue.regionId = k.value;
            fieldsValue.regionName = k.label;
          }
        });
      }

      request('portal/error/report', {
        data: {
          ...fileData,
          ...fieldsValue,
          natureOfFailure,
          limitTime: moment(fieldsValue?.limitTime).format('YYYY-MM-DD HH:mm:ss'),
          busiName: getbusiNameBybusiCode(fieldsValue?.busiCode),
        },
        method: 'POST',
      }).then(res => {
        setIosLoading(false);
        if (res.resultCode === '0') {
          // 成功页面
          setSuccess(true);
        } else {
          message.error('报障提交失败');
        }
      });
    });

    // const { errors, values } = await validateFields();
    // if (!errors) {
    // TODO: 提交values

    // 切换到"成功上报"界面
    //  setSuccess(true);
    // }
  };

  const normFile = e => {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  // 获取主数据-区域
  function queryBusiTypeList() {
    request('portal/DomainDataController/getValuesList.do', {
      data: {
        busiNbr: 'BaseEntity',
        propertyName: 'busiType',
      },
    }).then(res => {
      setBusiTypes(res);
      const arr = [];
      const systemValue = current.system === undefined ? '02' : current.system;
      res.map((item, index) => {
        // 01 门户， 02 订单， 03 受理
        if (item.desc === systemValue) {
        arr.push(item);
        }
        });
        setSubBusiTypes(arr);
    });
  }

  useEffect(() => {
    // queryLanIdList();
    queryBusiTypeList();
  }, []);

  // 标题自动生成
  const changeTitle = () => {
    const { reqId = [] } = getFieldsValue(['reqId']);
    const { orderId = [] } = getFieldsValue(['orderId']);
    const { orgSystem = [] } = getFieldsValue(['orgSystem']);
    setOrderTitle(`${orgSystem}+${reqId}+${orderId}`);
  };

  // 系统值变化，改变业务类型
const changeSystem = systemValue => {
  setFieldsValue({ busiName: undefined });
  const arr = [];
  busiTypes.map((item, index) => {
  // 01 门户， 02 订单， 03 受理
  if (item.desc === systemValue) {
  arr.push(item);
  }
  });
  setSubBusiTypes(arr);
  };

  const takeScreenshot = usePersistFn(async () => {
    setLoading(true);

    // const { fileList = [] } = getFieldsValue(['fileList']);

    const dataUrl = await screenshot();

    setFieldsValue({
      fileList: [
        // ...fileList,
        {
          uid: '1',
          name: `${orderId}.png`,
          status: 'done',
          thumbUrl: dataUrl,
          url: dataUrl,
        },
      ],
    });

    setIsUpLoad(false);
    setLoading(false);
  });

  // 没传截图时，自动截屏
  useEffect(() => {
    if (current.screenCapture === undefined) {
      takeScreenshot();
    } else {
      // const { fileList = [] } = getFieldsValue(['fileList']);
      setFieldsValue({
        fileList: [
          // ...fileList,
          {
            uid: '1',
            name: `${orderId}.png`,
            status: 'done',
            thumbUrl: current.screenCapture,
            url: current.screenCapture,
          },
        ],
      });
    }
  }, [takeScreenshot, current.screenCapture, getFieldsValue, setFieldsValue]);

  return (
    <div>
      {success === false ? (
        <>
          <h3 className={styles.formTitle}>业务报障单信息</h3>
          <Form {...formItemLayout}>
            <div className={styles.formSection}>基本信息</div>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="请求流程号">
                  {getFieldDecorator('reqId', {
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                    initialValue: current.orderId === undefined ? orderId : current.orderId,
                  })(<Input disabled />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="来源系统订单编号">
                  {getFieldDecorator('orderId', {
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                    initialValue: current.orderId === undefined ? orderId : current.orderId,
                  })(<Input disabled />)}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="归属地市">
                  {getFieldDecorator('lanId', {
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                    initialValue: getAreaValue(orgInfo),
                  })(<AreaTreeSelect orgInfo={orgInfo} />)}
                </Form.Item>
                {/* <Form.Item label="归属地市">
                  <AreaTreeSelect />
                </Form.Item> */}
              </Col>
              <Col span={12}>
                <Form.Item label="发起人姓名">
                  {getFieldDecorator('createName', {
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                    initialValue: userInfo.userName,
                  })(<Input disabled />)}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="发起人手机">
                  {getFieldDecorator('createPhone', {
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                    initialValue: userInfo.mobilePhone,
                  })(<Input placeholder="请输入发起人手机号" />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="申告来源">
                  {getFieldDecorator('orgSystem', {
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                    onChange: changeTitle,
                    initialValue: '2',
                  })(
                    <Select placeholder="请选择" disabled onChange={changeTitle}>
                      <Select.Option value="2">订单中心</Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="申告类别">
                  {getFieldDecorator('orderType', {
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                    initialValue: '1',
                  })(
                    <Select placeholder="请选择" disabled>
                      <Select.Option value="1">政企业务</Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="系统">
                  {getFieldDecorator('system', {
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                    initialValue: current.system === undefined ? '02' : current.system,
                  })(
                    <Select placeholder="请选择" onChange={changeSystem}>
                      <Select.Option value="01">门户</Select.Option>
                      <Select.Option value="02">订单</Select.Option>
                      <Select.Option value="03">受理</Select.Option>
                    </Select>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="业务类型">
                  {getFieldDecorator('busiCode', {
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                    initialValue: getInitSubBusiType(current?.code),
                  })(
                    <Select placeholder="请选择" onChange={changeTitle}>
                      {subBusiTypes.length
                        ? subBusiTypes.map(item => (
                          <Select.Option value={item.value} key={item.value}>
                            {item.name}
                          </Select.Option>
                            ))
                        : null}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="集团编码">
                  {getFieldDecorator('custNumber', {
                    rules: [
                      {
                        required: false,
                        message: '不能为空',
                      },
                    ],
                  })(<Input />)}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="集团名称">
                  {getFieldDecorator('custName', {
                    rules: [
                      {
                        required: false,
                        message: '不能为空',
                      },
                    ],
                  })(<Input />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="申告标题">
                  {getFieldDecorator('orderTitle', {
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                    initialValue: orderTitle,
                  })(<Input />)}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="要求完成时间" {...tailFormItemLayout}>
                  {getFieldDecorator('limitTime', {
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                  })(
                    <DatePicker format="YYYY-MM-DD HH:mm:ss" showTime />
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="申告内容" {...tailFormItemLayout}>
                  {getFieldDecorator('orderDesc', {
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                    initialValue: current.msg,
                  })(<Input.TextArea />)}
                </Form.Item>
              </Col>
            </Row>
            <div className={styles.formSection}>附件信息</div>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="附件列表" {...tailFormItemLayout}>
                  {getFieldDecorator('fileList', {
                    valuePropName: 'fileList',
                    initialValue: [],
                    getValueFromEvent: normFile,
                    rules: [
                      {
                        required: true,
                        message: '不能为空',
                      },
                    ],
                  })(
                    <Upload
                      showUploadList={{ showPreviewIcon: true, showDownloadIcon: false }}
                      // TODO: 换成图片上传接口
                      action="xxx"
                      listType="picture-card"
                      accept=".jpg,.png"
                      onPreview={file => openNewWindow(file.thumbUrl)}
                      onChange={fileType => {
                        // 图片删除时展示 上传按钮
                        if (fileType.file.status === 'removed') {
                          setIsUpLoad(true);
                        } else {
                          setIsUpLoad(false);
                        }
                      }}

                      // multiple={false}
                    >
                      {isUpLoad ? uploadButton : null}
                    </Upload>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <div className={styles.formAction}>
              {typeof onBack === 'function' ? (
                <Button type="default" onClick={onBack}>
                  取消
                </Button>
              ) : null}

              <Button ghost type="primary" onClick={() => takeScreenshot()} loading={loading}>
                自动截图
              </Button>
              <Button type="primary" onClick={handleSubmit} loading={iosLoading}>
                确认申告
              </Button>
            </div>
          </Form>
        </>
      ) : (
        <Result
          status="success"
          className={styles.success}
          title="故障上报成功"
          extra={[
            typeof onSuccess === 'function' ? (
              <Button type="primary" key="return" onClick={() => onSuccess({ key: current.key })}>
                返回列表
              </Button>
            ) : null,
          ]}
        />
      )}
    </div>
  );
}

export default Form.create()(MyForm1);
