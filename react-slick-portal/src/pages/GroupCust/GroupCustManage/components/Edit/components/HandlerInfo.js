/* eslint-disable no-console */
/* 经办人信息 */
import React, { useCallback, useEffect, useState } from 'react';
import { Card, Col, Form, Input, Row, Select } from 'antd';
import { connect } from 'dva';
import styles from '@/pages/GroupCust/GroupCustManage/components/Edit/index.less';
import TakePhoto from '@/busiComponents/TakePhoto';
// import { validateIdenNbr } from '@/utils/validator';
import Scan from '@/busiComponents/Scan';

const FormItem = Form.Item;
const formItemLayout = {
  // labelAlign: 'left',
  // labelCol: {
  //   xs: { span: 24 },
  //   // sm: { span: 4 },
  // },
  // wrapperCol: {
  //   xs: { span: 24 },
  //   sm: { span: 16 },
  // },
};
const HandlerInfo = props => {
  const { form, formData, onValuesChange, dispatch, currentFormFieldState, ReflectionGroup, viewMode } = props;
  const { getFieldDecorator, getFieldValue } = form;

  /* 证件类型 */
  // eslint-disable-next-line no-unused-vars
  // const [certTypesList, setCertTypesList] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [openCard, setOpenCard] = useState(true); // 是否展开卡片
  /**
   * 经办人校验
   */
  const customAgentValidator = (rule, value, callback) => {
    const fields = [];

    const allEmpty = fields.every(field => !getFieldValue(field)); // 检查是否全部为空
    const someNotEmpty = fields.some(field => getFieldValue(field)); // 检查是否有非空值

    if (someNotEmpty && !allEmpty) {
      // 如果有非空值且不是全部为空
      const anyFieldEmpty = fields.some(field => !getFieldValue(field)); // 检查是否有空值
      if (anyFieldEmpty) {
        callback('请填写完整经办人信息！');
      }
    }

    callback();
  };
  const updateFormField = useCallback(
    data => {
      if (!data.field) return;
      form.setFieldsValue({
        [data.field]: data.value,
      });
    },
    [form]
  );
  useEffect(() => {
    dispatch({
      type: 'AddGroupCust/registerMethods',
      payload: {
        componentName: 'HandlerInfo',
        methods: { updateFormField },
      },
    });

    return () => {
      dispatch({
        type: 'AddGroupCust/unregisterMethods',
        payload: { componentName: 'HandlerInfo' },
      });
    };
  }, [dispatch]);
  useEffect(() => {
    dispatch({
      type: 'ReflectionGroup/getReflectList',
      payload: 'IDEN_CARD_TYPE',
    });
  }, []);

  // 经办人身份证号码返回
  const handleAgentNo = e => {
    console.log('经办人身份证扫描返回', e);
  };

  return (
    <Card
      className="cute"
      title="经办人信息"
      headStyle={{ background: '#E7F2FB', padding: '0 20px' }}
      bodyStyle={{ padding: '0' }}
      // extra={openCard ? <a onClick={() => setOpenCard(false)}>隐藏</a> : <a onClick={() => setOpenCard(true)}>展开</a>}
    >
      {openCard && (
        <div className={styles.warp}>
          <Form className="flow2 fix-label" onValuesChange={onValuesChange}>
            <Row>
              {/* hasPriv('513301') hidden */}
              {/* 证件类型 */}
              <Col span={8} hidden>
                <FormItem label="证件类型" {...formItemLayout}>
                  {getFieldDecorator('AGENT_LICENCE_TYPE', {
                    initialValue: formData?.AGENT_LICENCE_TYPE || '100001',
                    rules: [{ validator: customAgentValidator }],
                  })(
                    <Select
                      disabled={viewMode === 'view'}
                      allowClear
                      showSearch
                      filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      placeholder="请选择"
                      className={styles.inputStyle}
                    >
                      {ReflectionGroup.IDEN_CARD_TYPE.map(e => (
                        <Select.Option value={e.STATIC_KEY} key={e.STATIC_KEY}>
                          {e.STATIC_VALUE}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              </Col>
              {/* 证件号码 */}
              <Col span={8}>
                <FormItem label="证件号码" {...formItemLayout}>
                  {getFieldDecorator('AGENT_LICENCE_NO', {
                    // rules: [{ validator: customAgentValidator }, { validator: validateIdenNbr(getFieldValue('AGENT_LICENCE_TYPE')) }],
                  })(
                    <div className={styles.inputStyle}>
                      <Scan
                        value={form.getFieldValue('AGENT_LICENCE_NO')}
                        onChange={e => {
                          handleAgentNo(e);
                        }}
                        onCallback={({ idenNr, name, address }) =>
                          form.setFieldsValue({
                            AGENT_LICENCE_NO: idenNr,
                            AGENT_LICENCE_NAME: name,
                            AGENT_LICENCE_ADDRESS: address,
                          })
                        }
                        disabled={viewMode === 'view'}
                      />
                    </div>
                  )}
                </FormItem>
              </Col>
              {/* 经办人拍照 */}
              <Col span={8}>
                <FormItem label="经办人拍照" {...formItemLayout}>
                  {getFieldDecorator('AGENT_IMAGE', {
                    rules: [{ validator: customAgentValidator }],
                  })(
                    <TakePhoto
                      customStyle={{ width: '80%' }}
                      certType="04"
                      fillBack="jbrpz"
                      idenNbr={getFieldValue('AGENT_LICENCE_NO')}
                      // onChange={e => { handleSuccessTP(e); }}
                      onCallback={({ FILE_ID }) => form.setFieldsValue({ AGENT_IMAGE: FILE_ID })}
                      viewMode={viewMode}
                    />
                  )}
                </FormItem>
              </Col>
              {/* 姓名 */}
              <Col span={8}>
                <FormItem label="姓名" {...formItemLayout}>
                  {getFieldDecorator('AGENT_LICENCE_NAME', {
                    rules: [{ validator: customAgentValidator }],
                  })(
                    <Input
                      className={styles.inputStyle}
                      placeholder="请输入"
                      disabled={viewMode === 'view' || currentFormFieldState?.AGENT_LICENCE_NAME?.disabled}
                    />
                  )}
                </FormItem>
              </Col>
            </Row>
            <Row>
              {/* 地址 */}
              <Col span={8}>
                <FormItem label="地址" {...formItemLayout}>
                  {getFieldDecorator('AGENT_LICENCE_ADDRESS', {
                    rules: [{ validator: customAgentValidator }],
                  })(
                    <Input
                      className={styles.inputStyle}
                      placeholder="请输入"
                      disabled={viewMode === 'view' || currentFormFieldState?.AGENT_LICENCE_ADDRESS?.disabled}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={8} />
            </Row>
          </Form>
        </div>
      )}
    </Card>
  );
};

export default connect(({ AddGroupCust, ReflectionGroup }) => ({
  formData: AddGroupCust.currentForm,
  currentFormFieldState: AddGroupCust.currentFormFieldState,
  ReflectionGroup,
}))(Form.create()(HandlerInfo));
