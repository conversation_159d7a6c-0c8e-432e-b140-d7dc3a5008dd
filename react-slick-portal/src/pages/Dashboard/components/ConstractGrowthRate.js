import React, { useState, useEffect } from 'react';
import { Row, Col, Skeleton, Empty } from 'antd';
import request from '@/utils/request';
import { Bar } from '@/components/Charts';

const cache = {};

/**
 * 异步获取数据后，需要缓存起来。避免组件未销毁时重复发起请求
 */
function ConstractGrowthRate() {
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState([]);

  useEffect(() => {
    async function getInitData() {
      setLoading(true);
      // 合同增长趋势数据获取
      const response1 = await request('orgauth/WorkbenchController/qryContractWorkNumOfMonth.do', {
        method: 'get',
      });

      if (response1 !== null && response1.resultCode === '0') {
        let data = [];
        const rstData = response1.resultObject.data.data.reverse();
        if (rstData) {
          rstData.forEach(item => data.push({ x: item.MONTH, y: item.NUM }));
          // 所有项的y都是0 约等于“没记录”，避免柱状图出现“一条线”
          if (data.filter(item => item.y > 0).length === 0) {
            data = [];
          }
          cache.chartData = data;
          setChartData(data);
        }
      }
      setLoading(false);
    }
    if (Object.hasOwnProperty.call(cache, 'chartData') === false) {
      getInitData();
    }
  }, []);

  let _chartData;

  if (cache.chartData === undefined) {
    _chartData = chartData;
  } else {
    _chartData = cache.chartData;
  }
  return (
    <Skeleton active loading={loading} paragraph={{ rows: 7 }}>
      <Row gutter={24}>
        <Col span={24}>
          {_chartData.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无数据"
              style={{ marginTop: 120 }}
            />
          ) : (
            <Bar
              height={292}
              title="合同增长趋势"
              data={_chartData}
              tooltipFormat={[
                'x*y',
                (x, y) => ({
                  name: `${x}`,
                  value: `新增合同：${y}个`,
                }),
              ]}
            />
          )}
        </Col>
      </Row>
    </Skeleton>
  );
}

export default ConstractGrowthRate;
