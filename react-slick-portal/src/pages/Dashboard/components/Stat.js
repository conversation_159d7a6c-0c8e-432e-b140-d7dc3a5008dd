import React, { useEffect } from 'react';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import router from 'umi/router';
import classNames from 'classnames';
import TweenOne from 'rc-tween-one';
import { stringify } from 'qs';
import Children from 'rc-tween-one/lib/plugin/ChildrenPlugin';
import { Card, Row, Col } from 'antd';
import IconFont from '@/components/IconFont';
import styles from './stat.less';

let timer = null;
const defaultInterval = 2 * 60; // 默认2分钟轮询间隔

TweenOne.plugins.push(Children);

function gotoPage(e, item) {
  // routerRedux.replace(`/TaskManage?menuCode=${item.menuId}&isFromUndoPage=true`)
  router.push({
    pathname: '/TaskManage',
    search: stringify({
      menuCode: item.menuId,
      isFromUndoPage: true,
    }),
  });
}
/**
 * 首页任务待办模块
 * 1、展示各类count>1的待办
 * 2、定时轮询，同步count；轮询间隔从后台取（后台如果没有设置默认取前台自定义的）
 */
function Stat({ dispatch, refreshTime, taskResultData, getInitDataLoading }) {
  useEffect(() => {
    dispatch({
      type: 'stat/getInitData',
      payload: {
        defaultInterval,
      },
    });
  }, [dispatch]);

  useEffect(() => {
    if (timer === null && refreshTime !== undefined) {
      timer = setInterval(function() {
        dispatch({
          type: 'stat/qryTasksNumbers',
        });
      }, parseInt(refreshTime, 10) * 1000);
    }
    return () => {
      clearInterval(timer);
    };
  }, [dispatch, refreshTime]);

  return (
    <>
      {getInitDataLoading || taskResultData.length === 0 ? (
        <Card loading={getInitDataLoading} />
      ) : (
        <Row gutter={8}>
          {taskResultData.map(item => {
            return (
              <Col span={6} className="margin-bottom" key={item.menuId}>
                <div className={classNames(styles.stat)}>
                  <div className={styles.visual}>
                    <IconFont type={item.icon} />
                  </div>
                  <div className={styles.details} onClick={e => gotoPage(e, item)}>
                    <div className={styles.number}>
                      <TweenOne
                        animation={{
                          Children: {
                            value: item.taskCount,
                            floatLength: 0,
                          },
                          duration: 1000,
                        }}
                      >
                        0
                      </TweenOne>
                    </div>
                    <div className={styles.desc}>{item.taskName}</div>
                  </div>
                </div>
              </Col>
            );
          })}
        </Row>
      )}
    </>
  );
}

export default connect(({ stat, loading }) => ({
  taskResultData: stat.taskResultData,
  refreshTime: stat.refreshTime,
  getInitDataLoading: loading.effects['stat/getInitData'],
}))(Stat);
