import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Button, Popover, Modal, Form, Row, Col, Card, Select, Input, Icon, message, Checkbox } from 'antd';
import style from './index.less';
import { AllApplyType, DurationApplyType, OneApplyType } from './const';
import { createVault, vaultAuthentication, refreshVaultValue } from '@/services/vaultApproval';

const { TextArea } = Input;

// const VaultApprovalPop = ((props, cRef) => {
const VaultApprovalPop = props => {
  const { popData, form, dispatch, supportApplyType, approvalOption, approverList, approvalCreateParams, onVaultSuccess } = props;
  const { getFieldDecorator, getFieldValue } = form;
  const [authModeList, setAuthModeList] = useState([]);
  const [loading1, setLoading1] = useState(false);
  const [kouling, setKouling] = useState(''); // 金库口令
  const [userName, setUserName] = useState(''); // 用户名
  const [passWord, setPassWord] = useState(''); // 密码
  const [loading2, setLoading2] = useState(false);
  const [refreshLoading, setRefReshLoading] = useState(false);
  const [approverType, setApproverType] = useState('');
  const [appStarts, setAppStarts] = useState(null);
  // 处理审批方式数组
  const { authMode } = approvalOption.appOperJKStatus;

  const authModeMap = authMode.map(e => {
    if (e === 'remoteAuth') {
      return {
        label: '远程授权',
        value: 'remoteAuth',
      };
    } if (e === 'localAuth') {
      return {
        label: '现场授权',
        value: 'localAuth',
      };
    }
    return {
      label: e,
      value: e,
    };
  });
  useEffect(() => { setAuthModeList(authModeMap); }, [popData]);
  const approverType1 = getFieldValue('APPLY_TYPE');
  useEffect(() => { setApproverType(approverType1); }, [approverType1]);
  // 申请类型处理
  const renderApplyType = () => {
    if (supportApplyType === 'all') {
      return (
        <Col span={16}>
          <Form.Item label="申请类型">
            {getFieldDecorator('APPLY_TYPE', {
              initialValue: '',
              rules: [{ required: true, message: '请选择' }],
            })(
              <Select placeholder="请选择">
                {AllApplyType.map(e => (
                  <Select.Option key={e.value} value={e.value}>
                    {e.label}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        </Col>
      );
    }
    if (supportApplyType === 'duration') {
      return (
        <Col span={16}>
          <Form.Item label="申请类型">
            {getFieldDecorator('APPLY_TYPE', {
              initialValue: '',
              rules: [{ required: true, message: '请选择' }],
            })(
              <Select placeholder="请选择">
                {DurationApplyType.map(e => (
                  <Select.Option key={e.value} value={e.value}>
                    {e.label}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        </Col>
      );
    }
    if (supportApplyType === 'one') {
      return (
        <Col span={16}>
          <Form.Item label="申请类型">
            {getFieldDecorator('APPLY_TYPE', {
              initialValue: '',
              rules: [{ required: true, message: '请选择' }],
            })(
              <Select placeholder="请选择">
                {OneApplyType.map(e => (
                  <Select.Option key={e.value} value={e.value}>
                    {e.label}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        </Col>
      );
    }
    return null;
  };

  const handleOk1 = () => {
    setLoading1(true);
    form.validateFields(async (err, value) => {
      if (!err) {
        const params = {
          ...value,
          APPROVER2: value.APPROVER2.join(','),
          BUSI_SCENE_ID: approvalOption.BUSI_SCENE_ID,
          RES_NUM: approvalOption.RES_NUM,
          OPER_CODE: approvalOption.OPER_CODE,
        };
        const resp = await createVault(params);
        setLoading1(false);
        if (resp.resultCode === 'TRUE' && resp.resultObject) {
          const approvalCreate = {
            isShowfirst: false,
            isShowSecond: true,
            approvalCreate: resp.resultObject,
          };
          dispatch({
            type: 'vaultApproval/VaultUpData',
            payload: { approvalCreate },
          });
        } else {
          message.error(resp.resultMsg);
        }
      }
    });
  };

  const handleOk2 = async () => {
    if (approvalCreateParams.authMode === 'remoteAuth' && kouling === '') {
      message.error('请输入口令');
      return;
    }
    if (approvalCreateParams.authMode === 'localAuth' && (userName === '' || passWord === '')) {
      message.error('请输入用户名或密码');
      return;
    }
    const params = {
      REQUEST_ID: approvalCreateParams.requestID,
      AUTH_MODE: approvalCreateParams.authMode,
      PASS_CODE: kouling, // 口令
      OPER_CODE: approvalOption.OPER_CODE,
      APPROVER_USER: userName, // 用户名
      APPROVER_PWD: userName, // 密码
      BUSI_SCENE_ID: approvalOption.BUSI_SCENE_ID,
    };
    setLoading2(true);
    const resp = await vaultAuthentication(params);
    setLoading2(false);
    if (resp.resultCode === 'TRUE' && resp.resultObject.authResult === 'true') {
      const actionName = popData.functionString;
      message.success('审批成功');
      const vaultIsPass = true;
      setAppStarts(false);
      // 执行回调
      onVaultSuccess(actionName, vaultIsPass);
      dispatch({
        type: 'vaultApproval/refreshDataPop',
        payload: {},
      });
    } else {
      message.error(resp.resultMsg);
    }
  };

  // 关闭弹窗1
  const handleCloseOne = () => {
    dispatch({
      type: 'vaultApproval/VaultResetDataOne',
      payload: {},
    });
  };

  // 关闭弹窗2
  const handleCloseTwo = () => {
    setAppStarts(false);
    dispatch({
      type: 'vaultApproval/VaultResetDataTwo',
      payload: {},
    });
  };
  const handleKeyChange = e => {
    setKouling(e.target.value);
  };
  const handleUserNameChange = e => {
    setUserName(e.target.value);
  };
  const handlePasswordChange = e => {
    setPassWord(e.target.value);
  };
  // 刷新状态
  const handleRefreshState = async () => {
    setRefReshLoading(true);
    const params = {
      REQUEST_ID: approvalCreateParams.requestID,
    };
    const resp = await refreshVaultValue(params);
    setRefReshLoading(false);
    if (resp.resultObject && resp.resultCode === 'TRUE') {
      setAppStarts(resp.resultObject.APPLY_STATUS);
    }
  };

  return (
    <>
      <Modal
        width={800}
        visible={popData.isShowfirst}
        closable={false}
        footer={[
          <Button onClick={() => handleCloseOne()}>
            取消
          </Button>,
          <Button type="primary" loading={loading1} onClick={() => handleOk1()}>
            提交
          </Button>,
        ]}
        destroyOnClose
      >
        <Card title="金库审批认证" className="cute" bordered>
          <Form className="flow fix-label">
            <Row gutter={24} type="flex" justify="center">
              <Col span={16}>
                <Form.Item label="查询类型">
                  {getFieldDecorator('AUTH_MODE', {
                    initialValue: '',
                    rules: [{ required: true, message: '请选择' }],
                  })(
                    <Select placeholder="请选择">
                      {authModeList.map(e => (
                        <Select.Option key={e.value} value={e.value}>
                          {e.label}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24} type="flex" justify="center">
              {renderApplyType()}
            </Row>
            <Row gutter={24} type="flex" justify="center">
              <Col span={16}>
                <Form.Item label={`${approverType === 'one' ? '次数' : '申请时长'}`}>
                  {getFieldDecorator('TIMING', {
                    initialValue: '1',
                  })(
                    <input disabled style={{ width: '100%' }} />
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24} type="flex" justify="center">
              <Col span={16}>
                <Form.Item label="审批人" className={style.textAreaFormItem}>
                  {getFieldDecorator('APPROVER2', {
                    initialValue: [],
                    rules: [{ required: true, message: '请选择' }],
                  })(
                    <Checkbox.Group>
                      {approverList.map(e => (
                        <div style={{ paddingTop: '8px' }}>
                          <Checkbox key={e.APPROVER_TEL} value={e.APPROVER}>
                            {`${e.APPROVER_NAME}(${e.APPROVER},${e.APPROVER_TEL})`}
                          </Checkbox>
                        </div>
                      ))}
                    </Checkbox.Group>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24} type="flex" justify="center">
              <Col span={16}>
                <Form.Item label="申请原因" className={style.textAreaFormItem}>
                  {getFieldDecorator('APPLY_REASON', {
                    initialValue: '',
                    rules: [{ required: true, message: '请输入' }],
                  })(
                    <div style={{ display: 'flex' }}>
                      <TextArea maxLength={150} rows={3} allowClear />
                      <Popover content="8个字符（4个汉字）以上，不允许全数字或全字母，150字以内" placement="right" trigger="hover">
                        <Icon type="question-circle" style={{ 'margin-left': '5px' }} />
                      </Popover>
                    </div>
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card>
      </Modal>
      <Modal
        width={800}
        visible={popData.isShowSecond}
        footer={[
          <Button onClick={() => handleCloseTwo()}>
            取消
          </Button>,
          <Button type="primary" loading={loading2} onClick={() => handleOk2()}>
            提交
          </Button>,
        ]}
        closable={false}
        destroyOnClose
      >
        <Card title="金库审批认证" className="cute" bordered>
          <div className={style.approvalDiv}>
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>审批将在 <span style={{ color: 'red' }}>{approvalCreateParams.authEndTime}</span> 超时</div>
            <div style={{ display: 'flex' }}>
              <div className={style.approvalLeft}>
                <div>申请ID：</div>
                <div>授权方式：</div>
                {
                  approvalCreateParams.authMode === 'remoteAuth' ? (<div style={{ lineHeight: '32px' }}>请输入金库口令：</div>) : (
                    <>
                      <div style={{ lineHeight: '32px' }}>审批人账号：</div>
                      <div style={{ lineHeight: '32px' }}>审批人密码：</div>
                    </>
                  )
                }
                <div style={{ marginTop: '5px' }}>审批状态：</div>
                <div>审批人：</div>
              </div>
              <div className={style.approvalRight}>
                <div>{approvalCreateParams.requestID}</div>
                <div>{approvalCreateParams.authMode === 'remoteAuth' ? '远程授权（有验证码方式）' : '现场授权'}</div>
                {
                  approvalCreateParams.authMode === 'remoteAuth' ? (
                    <div style={{ marginBottom: '0' }}>
                      <Input onChange={e => handleKeyChange(e)} allowClear />
                    </div>
                  ) : (
                    <div style={{ marginBottom: '0', height: 'auto' }}>
                      <div>
                        <Input onChange={e => handleUserNameChange(e)} allowClear />
                      </div>
                      <div>
                        <Input onChange={e => handlePasswordChange(e)} allowClear />
                      </div>
                    </div>
                  )
                }
                <div style={{ display: 'flex', marginTop: '5px' }}>
                  <div style={{ marginRight: '20px', marginBottom: '0' }}>{appStarts || approvalCreateParams.APPLY_STATUS}</div>
                  <Button size="small" type="primary" style={{ marginBottom: '0' }} loading={refreshLoading} className={style.refreshBtn} onClick={() => handleRefreshState()}>刷新</Button>
                </div>
                <div>
                  {approvalCreateParams?.approverList?.length > 0 ? (
                    approvalCreateParams.approverList.map((e, index) => (
                      <div style={{ height: 'auto' }} key={index}>
                        {`${e.APPROVER_NAME}(${e.APPROVER},${e.APPROVER_TEL})`}
                      </div>
                    ))
                  ) : (<></>)}
                </div>
              </div>
            </div>
          </div>
        </Card>
      </Modal>
    </>
  );
};

export default connect(({ vaultApproval }) => ({
  popData: vaultApproval.data,
  approvalOption: vaultApproval.data.approvalOption,
  supportApplyType: vaultApproval.data.approvalOption.appOperJKStatus.supportApplyType,
  approverList: vaultApproval.data.approvalOption.appOperJKStatus.approverList,
  approvalCreateParams: vaultApproval.data.approvalCreate,
}))((Form.create()(VaultApprovalPop)));
