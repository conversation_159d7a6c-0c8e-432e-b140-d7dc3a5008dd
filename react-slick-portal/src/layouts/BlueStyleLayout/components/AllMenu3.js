/* eslint-disable */
import React, { useEffect } from 'react';
import { connect } from 'dva';
import { Menu, Icon, Empty, Popover, Row, Col, Divider } from 'antd';
import Link from 'umi/link';
import memoizeOne from 'memoize-one';
import isEqual from 'lodash/isEqual';
import sortBy from 'lodash/sortBy';
import classNames from 'classnames';
// import useComponentSize from '@rehooks/component-size';
import findIndex from 'lodash/findIndex';
import find from 'lodash/find';
import mapping from '@/services/menu';
import { getFirstLevelMenu, getSecondLevelMenuInfo, getThirdLevelMenuInfo } from './utils';
import { getFinalUrl } from '@/utils/utils';
import styles from '../less/header.less';
import styles3 from '../less/allMenu3.less';

const { SubMenu } = Menu;
const { MENU_TYPE_DIR } = mapping;

const _getFirstLevelMenu = memoizeOne(getFirstLevelMenu, isEqual);

const _getSecondLevelMenuInfo = memoizeOne(getSecondLevelMenuInfo, isEqual);
const _getThirdLevelMenuInfo = memoizeOne(getThirdLevelMenuInfo, isEqual);

function AllMenu3({ menu: { all, appSysCode, taskSysCode } }) {
  function serialize(all, appSysCode, taskSysCode) {
    const result = [];
    const menuLel1 = _getFirstLevelMenu(all, appSysCode, taskSysCode);
    menuLel1.forEach(item1 => {
      if (item1.menuType === `${MENU_TYPE_DIR}`) {
        let children = sortBy(_getSecondLevelMenuInfo(all, item1.menuId), 'children');

        if (Array.isArray(children) && children.length > 0) {
          children = children.map(item2 => {
            const grandson = sortBy(_getThirdLevelMenuInfo(all, item2.menuId), 'children');
            if (Array.isArray(grandson) && grandson.length > 0) {
              item2.children = grandson;
            }
            return item2;
          });

          item1.children = children;
        }
      }
      result.push(item1);
    });
    return result;
  }

  const menuArr = serialize(all, appSysCode, taskSysCode);
  // console.log(menuArr);
  window.menuArr = menuArr;

  const renderNode = menuArr => {
    return menuArr.map(item => {
      if (item.children) {
        return (
          <SubMenu key={item.menuId} title={item.menuName}>
            {renderNode(item.children)}
          </SubMenu>
        );
      } else {
        return (
          <Menu.Item key={item.menuId}>
            <Link
              to={getFinalUrl(item)}
            >
              {item.menuName}
            </Link>
          </Menu.Item>
        );
      }
    });
  };

  return (
    <div className={styles3.navMenu}>
      <Menu mode="horizontal">{renderNode(menuArr)}</Menu>
    </div>
  );
}

export default connect(({ menu }) => ({
  menu,
}))(AllMenu3);
