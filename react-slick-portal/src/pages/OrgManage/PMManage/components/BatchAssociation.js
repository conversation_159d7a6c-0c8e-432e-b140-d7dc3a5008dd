import React, { useEffect, useState } from 'react';
import { Row, Col, Form, Input, DatePicker, Checkbox, message, Select, Modal, Button } from 'antd';
import moment from 'moment';
import { useAntdTable } from '@umijs/hooks';
import request from '@/utils/request';
import { operActionLog } from '@/utils/utils';
import SlickTable from '@/components/SlickTable';
import styles from '../styles.less';

const { Search, TextArea } = Input;
const { Option } = Select;

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
const formTailLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16, offset: 8 },
};

const BatchAssociation = ({ form, setAssociationVisiable, params: roleParams, setParams, curUser }) => {
  const { getFieldDecorator } = form;
  const dateFormat = 'YYYY-MM-DD';

  // 筛选项相关
  const [systemList, setSystemList] = useState([]);
  const [systemId, setSystemId] = useState(null);
  const [searchValue, setSearchValue] = useState('');

  // 列表相关
  const [selectRowsKeys, setSelectRowsKeys] = useState([]);
  const [allSelectRows, setAllSelectRows] = useState([]);

  const [loading, setLoading] = useState(false);
  const [valuesList, setValuesList] = useState([[], []]);
  const [isRequired, setIsRequired] = useState(false); // 授权回收日期是否必填
  const [isDisabled, setIsDisabled] = useState(false); // 授权管理分类、授权操作类型是否禁用


  // 获取归属系统数据
  const getSystemList = () => {
    try {
      request('orgauth/SystemInfoController/getSystemInfoList.do', {
        method: 'GET',
      }).then(res => {
        setSystemList(res);
      });
    } catch (error) {
      message.error(error);
    }
  };

  const queryValueList = async () => {
    const result = await Promise.all([
      request('portal/DomainDataController/getValuesList.do', {
        data: {
          busiNbr: 'SystemUserRole',
          propertyName: 'manageClass',
        },
      }),
      request('portal/DomainDataController/getValuesList.do', {
        data: {
          busiNbr: 'SystemUserRole',
          propertyName: 'operType',
        },
      }),
    ]);
    if (result) {
      setValuesList(result);
    }
  };

  useEffect(() => {
    getSystemList();
    queryValueList();
  }, []);

  // 获取用户列表数据
  const getRolesTableData = params => {
    const { current, pageSize = 10, ...restParams } = params;
    return request('orgauth/SystemRolesController/selectSystemRolesGridDataAll.do', {
      data: {
        filterCol: 'sysRoleName,sysRoleCode',
        filterSysUserId: curUser.sysUserId,
        page: current,
        pageNum: current,
        pageSize,
        filterVal: searchValue,
        sysCode: systemId || undefined,
        ...restParams,
      },
    }).then(res => {
      if (Array.isArray(res?.list)) {
        return {
          total: res.total,
          data: res.list,
        };
      }
      return {
        total: 0,
        data: [],
      };
    });
  };

  // 用户列表数据查询
  const { tableProps } = useAntdTable(
    params => getRolesTableData({ ...params }),
    [systemId, searchValue],
    { form }
  );
  const { pagination, ...restTableProps } = tableProps;

  const columns = [
    {
      title: '角色名称',
      dataIndex: 'sysRoleName',
      ellipsis: true,
    },
    {
      title: '角色编码',
      dataIndex: 'sysRoleCode',
      ellipsis: true,
    },
    {
      title: '角色类型',
      dataIndex: 'sysRoleTypeName',
      ellipsis: true,
    },
    {
      title: '归属系统',
      dataIndex: 'sysCodeName',
      ellipsis: true,
    },
  ];

  // 角色选择区域
  const rolesSelect = () => (
    <div style={{ marginBottom: '10px' }}>
      <Row type="flex" justify="end" style={{ marginBottom: '8px' }}>
        <Col span={4}>
          <Select
            placeholder="归属系统"
            allowClear
            showSearch
            style={{ width: '100%' }}
            optionFilterProp="children"
            onChange={value => {
                setSystemId(value);
              }}
          >
            {
                systemList.map(item => (
                  <Option value={item.systemInfoId} key={item.systemInfoId}>
                    {item.systemName}
                  </Option>
                )) || []
              }
          </Select>
        </Col>
        <Col span={6} className="margin-left">
          <Search
            placeholder="角色名称、角色编码"
            onSearch={value => {
                setSearchValue(value);
              }}
          />
        </Col>
      </Row>
      <SlickTable
        rowKey={record => record.id}
        columns={columns}
        {...restTableProps}
        loading={restTableProps?.loading}
        data={{ pagination }}
        selectedRowKeys={selectRowsKeys}
        allSelectedRows={allSelectRows}
        onSelectRow={rows => {
          setSelectRowsKeys(rows.map(item => item.id));
          setAllSelectRows(rows);
        }}
        pick="checkbox"
      />
    </div>
  );

  const changeDate = date => {
    if (moment(date).format(dateFormat) < moment().format(dateFormat)) {
      form.setFieldsValue({ recyclingDate: undefined });
      message.error('到期回收日期小于当前日期，请重新选择！');
    }
  };

  // 是否临时角色值改变，是临时角色，到期回收日期必填，授权管理分类、授权管理类型默认为“使用权限”、“不允许”，不可编辑
  const changeTemporary = e => {
    if (e.target.checked) {
      const classValue = valuesList[0].length ? valuesList[0].find(item => item.name === '使用权限')?.value : undefined;
      const typeValue = valuesList[1].length ? valuesList[1].find(item => item.name === '不允许')?.value : undefined;
      setIsRequired(true);
      setIsDisabled(true);
      form.setFieldsValue({ manageClass: classValue, operType: typeValue });
    } else {
      setIsRequired(false);
      setIsDisabled(false);
      form.setFieldsValue({ manageClass: undefined, operType: undefined, recyclingDate: undefined });
    }
  };

  // 提示内容
  const ConfirmContent = () => (
    <div className={styles.confirmRoles}>
      {
        allSelectRows.map(item => (
          <div className={styles.confirmItem}>{`${item.sysRoleName} ${item.sysRoleCode}`}</div>
        ))
      }
    </div>
  );

  // 提交授权
  const handleSubmit = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      const { isTemporary, recyclingDate, manageClass, operType, remark } = fieldsValue;
      if (selectRowsKeys.length === 0) {
        message.error('请选择需授权角色！');
      } else if (isTemporary && moment(recyclingDate).format(dateFormat) < moment().format(dateFormat)) {
        message.error('到期回收日期小于当前日期，请重新选择！');
      } else {
        Modal.confirm({
          title: `是否批量关联以下${isTemporary ? '临时' : ''}角色？`,
          content: <ConfirmContent />,
          onOk() {
            setLoading(true);
            try {
              request('orgauth/SystemUserRoleController/batchSaveUserRole.do', {
                data: {
                  sysUserId: curUser.sysUserId,
                  sysRoleIds: selectRowsKeys,
                  manageClass,
                  operType,
                  remark,
                  expDate: recyclingDate ? `${moment(recyclingDate).format(dateFormat)} 00:00:00` : undefined,
                },
              }).then(res => {
                setLoading(false);
                if (res?.success) {
                  setAssociationVisiable(false);
                  setParams({
                    ...roleParams,
                    gb_reset: true,
                  });
                  // 记录系统操作日志
                  operActionLog({ actionType: '1100', actionModule: '3290500', action: '1900', handResult: 'success', actionMsg: '' });
                  message.success('授权成功！');
                } else {
                  // 记录系统操作日志
                  operActionLog({ actionType: '1100', actionModule: '3290500', action: '1900', handResult: res?.success, actionMsg: res?.resultMsg });
                  message.error(res?.retMsg);
                }
              }).catch(() => {
                setLoading(false);
                message.error('调用接口失败');
              });
            } catch (error) {
              setLoading(false);
              message.error(error);
            }
          },
        });
      }
    });
  };

  return (
    <div className={styles.batchAssociation}>
      <div className={styles.formContent}>
        <Row>
          <Col>
            {rolesSelect()}
          </Col>
        </Row>
        <Form>
          <Row>
            <Col span={12}>
              <Form.Item {...formTailLayout}>
                {getFieldDecorator('isTemporary')(
                  <Checkbox onChange={changeTemporary}>
                    是否设置临时角色（立即生效）
                  </Checkbox>
              )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="到期回收日期" {...formItemLayout}>
                {getFieldDecorator('recyclingDate', {
                rules: [
                  {
                    required: isRequired,
                    message: '到期回收日期不能为空',
                  },
                ],
              })(
                <DatePicker style={{ width: '100%' }} disabled={!isDisabled} onChange={changeDate} />
              )}
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Form.Item label="授权管理分类" {...formItemLayout}>
                {getFieldDecorator('manageClass', {
                rules: [
                  {
                    required: true,
                    message: '授权管理分类不能为空',
                  },
                ],
              })(
                <Select placeholder="请选择" disabled={isDisabled}>
                  {valuesList[0].length
                    ? valuesList[0].map(item => (
                      <Option value={`${item.value}`} key={item.value}>
                        {item.name}
                      </Option>
                    ))
                    : null}
                </Select>
              )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="授权操作类型" {...formItemLayout}>
                {getFieldDecorator('operType', {
                  rules: [
                    {
                      required: true,
                      message: '授权操作类型不能为空',
                    },
                  ],
                })(
                  <Select placeholder="请选择" disabled={isDisabled}>
                    {valuesList[1].length
                      ? valuesList[1].map(item => (
                        <Option value={`${item.value}`} key={item.value}>
                          {item.name}
                        </Option>
                        ))
                      : null}
                  </Select>
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Form.Item label="备注" {...formItemLayout}>
                {getFieldDecorator('remark')(<TextArea rows={3} placeholder="请输入" />)}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div className={styles.btnGroup}>
        <Button onClick={() => setAssociationVisiable(false)} style={{ marginRight: '8px' }}>取消</Button>
        <Button type="primary" onClick={handleSubmit} loading={loading}>确定</Button>
      </div>
    </div>
  );
};
export default Form.create()(BatchAssociation);
