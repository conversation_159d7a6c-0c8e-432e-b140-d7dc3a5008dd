import React, { useEffect, useState } from 'react';
import { Modal, message, Card, Row, Col, Button, Input, Form, Switch, Select } from 'antd';
import { connect } from 'dva';
import PropTypes from 'prop-types';
import { DndProvider, DragSource, DropTarget } from 'react-dnd';
import { findIndex } from 'lodash';
import HTML5Backend from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import SlickTable from '@/components/SlickTable';
import imgs from './GroupImgs';
import styles from '../../less/styles.less';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};

const GroupConfiguration = props => {
  const {
    visible,
    close,
    groups,
    onOk,
    size,
    form: { getFieldDecorator, validateFields, setFieldsValue, resetFields },
  } = props;

  const [groupData, setGroupData] = useState(groups);
  const [curGroup, setCurGroup] = useState(groups[0] || {});

  const [isEdit, setIsEdit] = useState(false);
  const [addVisible, setAddVisible] = useState(false);

  const [selectVisible, setSelectVisible] = useState(false);
  const [addModuleData, setAddModuleData] = useState([]);
  const [selectedModule, setSelectedModule] = useState([]);

  // 处理模块添加列表数据
  const handleGroupData = groupList => {
    if (groupList.length > 0) {
      const modules = groupList.filter(item => item.groupName !== curGroup.groupName).reduce((arr, group) => {
        const moduleList = group.moduleList?.map(item => ({ ...item, groupName: group.groupName })) || [];
        return [...arr, ...moduleList];
      }, []);
      setAddModuleData(modules);
    }
  };

  useEffect(() => {
    handleGroupData(groupData);
  }, [groupData, curGroup]);

  // 提交，更新布局
  const handleSubmit = e => {
    e.preventDefault();
    const hasModules = groupData.every(item => item.moduleList.length > 0);
    if (!hasModules) {
      message.error('存在分组没有选择模块，请选择后提交。');
    } else {
      onOk(groupData);
      close();
    }
  };

  // 打开新增分组
  const openAddGroup = () => {
    setIsEdit(false);
    setAddVisible(true);// 等待编辑表单加载后设值
    setTimeout(() => {
      resetFields();
    }, 0);
  };

  // 打开编辑分组
  const openEditGroup = record => {
    setIsEdit(true);
    setAddVisible(true);

    // 等待编辑表单加载后设值
    setTimeout(() => {
      setFieldsValue(record);
    }, 0);
  };

  // 删除分组
  const deleteGroup = record => {
    Modal.confirm({
      title: `确认删除分组${record.groupName}吗？分组内的模块都将被释放。`,
      onOk() {
        const newGroupData = groupData.filter(item => item.groupName !== record.groupName).map(item => {
          if (item.groupName === '未分组') {
            return { ...item, moduleList: [...item.moduleList, ...record.moduleList] };
          }
          return item;
        });
        setGroupData(newGroupData);
        setCurGroup(newGroupData[0]);
      },
    });
  };

  // 删除模块
  const deleteModule = record => {
    Modal.confirm({
      title: `确认移除模块${record.menuName}吗？`,
      onOk() {
        const newGroupData = groupData.map(item => {
          if (item.groupName === curGroup.groupName) {
            setCurGroup({ ...curGroup, moduleList: item.moduleList.filter(list => list.menuId !== record.menuId) });
            return { ...item, moduleList: item.moduleList.filter(list => list.menuId !== record.menuId) };
          }
          if (item.groupName === '未分组') {
            return { ...item, moduleList: [...item.moduleList, record] };
          }
          return item;
        });
        if (findIndex(groupData, ['groupName', '未分组']) === -1) {
          const newGroup = { groupName: '未分组', groupShow: true, groupDesc: '', moduleList: [record] };
          newGroupData.push(newGroup);
        }
        setGroupData(newGroupData);
      },
    });
  };

  // 分组列
  const groupColumns = [
    { dataIndex: 'groupName', title: '分组名称', width: '20%', ellipsis: true },
    { dataIndex: 'groupDesc', title: '分组描述', width: '20%', ellipsis: true },
    {
      dataIndex: 'groupShow',
      title: '默认展示',
      width: '20%',
      render: text => text ? '是' : '否',
    },
    {
      dataIndex: 'groupIcon',
      title: '分组图标',
      width: '20%',
      render: text => text ? <img alt="" src={imgs[text]} height="15" width="15" /> : null,
    },
    {
      title: '操作',
      render: (text, record) => (
        <div>
          {record.groupName !== '未分组' && (
            <>
              <a onClick={() => openEditGroup(record)} style={{ marginRight: '8px' }}>
                编辑
              </a>
              <a onClick={() => deleteGroup(record)}>
                删除
              </a>
            </>
          )}
        </div>
      ),
    },
  ];

  const rowClassName = record => (record.groupName === curGroup.groupName ? `${styles.clickRowStyle}` : '');

  const groupRowSelect = record => {
    setCurGroup(record);
  };

  // 已选模块列
  const moduleColumns = [
    { dataIndex: 'menuName', title: '模块名称', width: '25%', ellipsis: true },
    { dataIndex: 'menuCode', title: '模块编码', width: '25%', ellipsis: true },
    { dataIndex: 'menuDesc', title: '模块描述', ellipsis: true },
    { dataIndex: 'menuModuleTypeName', title: '模块类型', ellipsis: true },
    {
      title: '操作',
      render: (text, record) => (
        <div>
          {curGroup.groupName !== '未分组' && (
            <a onClick={() => deleteModule(record)}>
              移除
            </a>
          )}
        </div>
      ),
    },
  ];

  // 新增模块列
  const addModuleColumns = [
    { dataIndex: 'menuName', title: '模块名称', width: '20%', ellipsis: true },
    { dataIndex: 'menuCode', title: '模块编码', width: '25%', ellipsis: true },
    { dataIndex: 'groupName', title: '模块分组', width: '14%', ellipsis: true },
    { dataIndex: 'menuDesc', title: '模块描述', ellipsis: true },
    { dataIndex: 'menuModuleTypeName', title: '模块类型', width: '12%', ellipsis: true },
  ];

  // 新增或编辑分组
  const addOrEditSubmit = () => {
    validateFields((err, fieldsValue) => {
      if (err) {
        return;
      }
      let group = {};
      if (isEdit) {
        const existIndex = groupData.findIndex(item => item.groupName === fieldsValue.groupName && item.groupName !== curGroup.groupName);
        if (existIndex !== -1) {
          message.error(`分组名称“${fieldsValue.groupName}”已存在，请重新填写。`);
          return;
        }
        const filterGroup = groupData.map(item => {
          if (item.groupName === curGroup.groupName) {
            return { ...item, ...fieldsValue, groupShow: true };
          }
          return item;
        });
        setGroupData(filterGroup);
      } else {
        const existIndex = groupData.findIndex(item => item.groupName === fieldsValue.groupName);
        if (existIndex !== -1) {
          message.error(`分组名称“${fieldsValue.groupName}”已存在，请重新填写。`);
          return;
        }
        group = { ...fieldsValue, groupShow: true, moduleList: [] };
        setGroupData([...groupData, group]);
      }
      setCurGroup(group);
      setAddVisible(false);
    });
  };

  // 添加模块
  const selectSubmit = () => {
    if (selectedModule.length === 0) {
      message.warn('别着急，先选中一个模块');
    } else {
      const newGroups = groupData.map(item => {
        if (item.groupName === curGroup.groupName) {
          setCurGroup({ ...curGroup, moduleList: [...item.moduleList, ...selectedModule] });
          return { ...item, moduleList: [...item.moduleList, ...selectedModule] };
        }
        const selectedKey = selectedModule.map(module => module.menuId);
        return {
          ...item,
          moduleList: item.moduleList.filter(module => !selectedKey.includes(module.menuId)),
        };
      }).filter(item => item.groupName !== '未分组' || (item.groupName === '未分组' && item.moduleList.length > 0));
      setGroupData(newGroups);
      setSelectVisible(false);
    }
  };

  // 分组列拖动调整顺序相关
  let dragingIndex = -1;

  const BodyRow = ({ isOver, connectDragSource, connectDropTarget, moveRow, ...restProps }) => {
    const style = { ...restProps.style, cursor: 'move' };

    let { className } = restProps;
    if (isOver) {
      if (restProps.index > dragingIndex) {
        className += ' drop-over-downward';
      }
      if (restProps.index < dragingIndex) {
        className += ' drop-over-upward';
      }
    }

    return connectDragSource(
      connectDropTarget(<tr {...restProps} className={className} style={style} />),
    );
  };

  const rowSource = {
    beginDrag(prop) {
      dragingIndex = prop.index;
      return {
        index: prop.index,
      };
    },
  };

  const rowTarget = {
    drop(prop, monitor) {
      const dragIndex = monitor.getItem().index;
      const hoverIndex = prop.index;

      if (dragIndex === hoverIndex) {
        return;
      }
      prop.moveRow(dragIndex, hoverIndex);
      monitor.getItem().index = hoverIndex;
    },
  };

  const DragableBodyRow = DropTarget('row', rowTarget, (connector, monitor) => ({
    connectDropTarget: connector.dropTarget(),
    isOver: monitor.isOver(),
  }))(
    DragSource('row', rowSource, connector => ({
      connectDragSource: connector.dragSource(),
    }))(BodyRow),
  );

  const components = {
    body: {
      row: DragableBodyRow,
    },
  };

  const moveRow = (dragIndex, hoverIndex) => {
    const dragRow = groupData[dragIndex];
    setGroupData(update(groupData, { $splice: [[dragIndex, 1], [hoverIndex, 0, dragRow]] }));
  };

  return (
    <Modal
      title="模块分组配置"
      width={size.width * 0.7}
      bodyStyle={{ height: size.height * 0.7, overflow: 'auto' }}
      visible={visible}
      onOk={handleSubmit}
      onCancel={close}
      destroyOnClose
      centered
      maskClosable={false}
      okButtonProps={{ htmlType: 'submit' }}
    >
      <Row gutter={8}>
        <Col span={10}>
          <Card
            title="模块分组列表"
            extra={(
              <div className={styles.extra}>
                <Button
                  className="margin-left"
                  type="primary"
                  onClick={openAddGroup}
                >
                  新增
                </Button>
              </div>
            )}
          >
            <DndProvider backend={HTML5Backend}>
              <SlickTable
                rowKey={record => record.groupName}
                components={components}
                onRow={(record, index) => ({
                  index,
                  moveRow,
                  onClick: () => {
                    groupRowSelect(record);
                  },
                })}
                dataSource={groupData}
                columns={groupColumns}
                rowClassName={rowClassName}
              />
            </DndProvider>

          </Card>
        </Col>
        <Col span={14}>
          <Card
            title="已选模块"
            extra={(
              <div className={styles.extra}>
                <Button
                  className="margin-left"
                  type="primary"
                  disabled={curGroup.groupName === '未分组'}
                  onClick={() => setSelectVisible(true)}
                >
                  添加
                </Button>
              </div>
            )}
          >
            <SlickTable
              rowKey={record => record.menuId}
              dataSource={curGroup.moduleList || []}
              columns={moduleColumns}
            />
          </Card>
        </Col>
      </Row>
      {
        addVisible && (
          <Modal
            title={`${isEdit ? '编辑' : '新增'}模块分组`}
            width={size.width * 0.35}
            visible={addVisible}
            onOk={addOrEditSubmit}
            onCancel={() => setAddVisible(false)}
            destroyOnClose
            centered
            okButtonProps={{ htmlType: 'submit' }}
          >
            <Form {...formItemLayout}>
              <Row type="flex">
                <Col span={24}>
                  <Form.Item label="分组名称">
                    {getFieldDecorator('groupName', {
                      rules: [
                        {
                          required: true,
                          message: '分组名称不能为空',
                        },
                      ],
                    })(<Input placeholder="请输入" allowClear />)}
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <Form.Item label="默认展示" valuePropName="checked">
                    {getFieldDecorator('groupShow')(<Switch defaultChecked disabled />)}
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <Form.Item label="分组图标">
                    {getFieldDecorator('groupIcon')(
                      <Select allowClear placeholder="请选择" style={{ width: '50%' }}>
                        {Object.entries(imgs).map(([key, value]) => (
                          <Select.Option value={key} key={key}>
                            <img alt={key} src={value} height="15" width="15" style={{ marginBottom: 3 }} />
                            <span style={{ marginLeft: 10 }}>{key}</span>
                          </Select.Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <Form.Item label="分组描述">
                    {getFieldDecorator('groupDesc')(<Input.TextArea placeholder="请输入" rows={4} />)}
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Modal>
        )
      }
      {
        selectVisible && (
          <Modal
            title="添加模块"
            width={size.width * 0.5}
            visible={selectVisible}
            onOk={selectSubmit}
            onCancel={() => setSelectVisible(false)}
            destroyOnClose
            centered
            okButtonProps={{ htmlType: 'submit' }}
          >
            <SlickTable
              rowKey={record => record.menuId}
              pick="checkbox"
              onSelectRow={rows => {
                setSelectedModule(rows);
              }}
              dataSource={addModuleData}
              columns={addModuleColumns}
            />
          </Modal>
        )
      }
    </Modal>
  );
};

GroupConfiguration.propTypes = {
  visible: PropTypes.bool.isRequired,
  groups: PropTypes.arrayOf(PropTypes.object).isRequired,
  close: PropTypes.func.isRequired,
  onOk: PropTypes.func.isRequired,
};

export default connect(({ setting }) => ({
  size: setting.size,
}))(Form.create()(GroupConfiguration));
