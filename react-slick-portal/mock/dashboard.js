import mockjs from 'mockjs';
import { delay } from 'roadhog-api-doc';
import defaultSettings from '../src/defaultSettings';

const proxy = {
  'POST /portal/TempletController/getTempletModuleReact.do': (req, res) => {
    res.status(200).send({
      resultCode: '0',
      resultObject: {
        id: 1388008,
        templetName: '444',
        templetDesc: '4444',
        moduleRels: [
          {
            id: 3753968,
            menuId: 3610504,
            x: '0',
            y: '3.23',
            w: '12',
            h: '2.16',
            menuName: 'Portlet - 关键指标',
            urlAddr: '[portlet]KeyIndex',
            templetModuleRelId: 3753968,
          },
          {
            id: 3733985,
            menuId: 3610503,
            x: '0',
            y: '0',
            w: '24',
            h: '1.35',
            menuName: 'Portlet - 系统公告',
            urlAddr: '[portlet]Bulletin',
            templetModuleRelId: 3733985,
          },
          {
            id: 4003966,
            menuId: 5470504,
            x: '0',
            y: '1.35',
            w: '24',
            h: '1.88',
            menuName: 'Portlet - 待办2',
            urlAddr: '[portlet]Stat',
            templetModuleRelId: 4003966,
          },
          {
            id: 3733987,
            menuId: 3610507,
            x: '12',
            y: '3.23',
            w: '12',
            h: '3.7',
            menuName: 'Portlet - 客户状态分布',
            urlAddr: '[portlet]CustomerDistribution',
            templetModuleRelId: 3733987,
          },
          {
            id: 3733986,
            menuId: 3610508,
            x: '0',
            y: '5.39',
            w: '12',
            h: '3.7',
            menuName: 'Portlet - 商机状态分布',
            urlAddr: '[portlet]BusinessDistribution',
            templetModuleRelId: 3733986,
          },
          {
            id: 3733988,
            menuId: 3610506,
            x: '0',
            y: '9.09',
            w: '24',
            h: '3.64',
            menuName: 'Portlet - 增长趋势',
            urlAddr: '[portlet]GrowthRate',
            templetModuleRelId: 3733988,
          },
        ],
        templetId: 1388008,
      },
    });
  },
  'POST /portal/TempletController/getTempletModule.do': (req, res) => {
    res.status(200).send({
      id: 68045,
      templetName: '管思坤-一线营销-PC卡片',
      layoutType: 3,
      sysPostId: 92550,
      sysUserId: 1,
      moduleRels: [
        {
          id: 714777,
          moduleId: 11721174,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 0,
          module: {
            id: 11721174832,
            moduleName: '预警-68045',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11750994,
                moduleId: 11721174832,
                moduleIndex: 0,
                funcMenu: {
                  id: 27615409,
                  menuId: 27615409,
                  menuName: '预警',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 7,
                  parMenuId: 276103,
                  menuDesc: '公告',
                  urlAddr: '[portlet]ContractStat',
                  x: 0,
                  y: 0,
                  w: 6,
                  h: 1.18,
                  minW: 12,
                  maxW: 24,
                  minH: 1.18,
                  maxH: 1.18,
                  sysCode: '727001',
                  firstLetter: 'XTGG',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_001',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11750994,
              },
            ],
            moduleId: 11721174832,
          },
          templetModuleRelId: 714777,
        },
        {
          id: 714777,
          moduleId: 11721174,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 0,
          module: {
            id: 11721174,
            moduleName: '系统公告-68045',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11750994,
                moduleId: 11721174,
                moduleIndex: 0,
                funcMenu: {
                  id: 276109,
                  menuId: 276109,
                  menuName: '系统公告',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 7,
                  parMenuId: 276103,
                  menuDesc: '公告',
                  urlAddr: '[portlet]Bulletin',
                  x: 0,
                  y: 0,
                  w: 16,
                  h: 2.16,
                  minW: 12,
                  maxW: 24,
                  minH: 2.16,
                  maxH: 2.16,
                  sysCode: '727001',
                  firstLetter: 'XTGG',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_001',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11750994,
              },
            ],
            moduleId: 11721174,
          },
          templetModuleRelId: 714777,
        },
        {
          id: 714777,
          moduleId: 11721174,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 0,
          module: {
            id: 1172117481,
            moduleName: '客户建档率-68045',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11750994,
                moduleId: 11721174,
                moduleIndex: 0,
                funcMenu: {
                  id: 276109,
                  menuId: 27610981,
                  menuName: '客户建档率',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 7,
                  parMenuId: 276103,
                  menuDesc: '客户建档率',
                  urlAddr: '[portlet]KeyIndex3',
                  x: 0,
                  y: 3.17,
                  w: 6,
                  h: 1.82,
                  minW: 6,
                  maxW: 24,
                  minH: 1.82,
                  maxH: 1.82,
                  sysCode: '727001',
                  firstLetter: 'XTGG',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_00811',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11750994,
              },
            ],
            moduleId: 11721174,
          },
          templetModuleRelId: 714777,
        },
        {
          id: 71477782,
          moduleId: 11721174,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 0,
          module: {
            id: 1172117482,
            moduleName: '线索转化率-68045',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11750994,
                moduleId: 11721174,
                moduleIndex: 0,
                funcMenu: {
                  id: 276109,
                  menuId: 27610982,
                  menuName: '线索转化率',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 7,
                  parMenuId: 276103,
                  menuDesc: '线索转化率',
                  urlAddr: '[portlet]KeyIndex2',
                  x: 8,
                  y: 3.17,
                  w: 6,
                  h: 1.82,
                  minW: 6,
                  maxW: 24,
                  minH: 1.82,
                  maxH: 1.82,
                  sysCode: '727001',
                  firstLetter: 'XTGG',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_00821',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11750994,
              },
            ],
            moduleId: 11721174,
          },
          templetModuleRelId: 714777,
        },
        {
          id: 71477783,
          moduleId: 11721174,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 0,
          module: {
            id: 1172117483,
            moduleName: '商机转化率-68045',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11750994,
                moduleId: 11721174,
                moduleIndex: 0,
                funcMenu: {
                  id: 276109,
                  menuId: 27610983,
                  menuName: '商机转化率',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 7,
                  parMenuId: 276103,
                  menuDesc: '商机转化率',
                  urlAddr: '[portlet]KeyIndex1',
                  x: 16,
                  y: 3.17,
                  w: 8,
                  h: 1.82,
                  minW: 6,
                  maxW: 24,
                  minH: 1.82,
                  maxH: 1.82,
                  sysCode: '727001',
                  firstLetter: 'XTGG',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_00831',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11750994,
              },
            ],
            moduleId: 11721174,
          },
          templetModuleRelId: 714777,
        },
        {
          id: 714689,
          moduleId: 11721421,
          templetId: 68043,
          sysPostId: 92548,
          relType: '1',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 1,
          module: {
            id: 11721421,
            moduleName: '关键指标',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11751241,
                moduleId: 11721421,
                moduleIndex: 0,
                funcMenu: {
                  id: 2769844,
                  menuId: 2769844,
                  menuName: '关键指标',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 129,
                  parMenuId: 276103,
                  menuDesc: '关键指标',
                  urlAddr: '[portlet]KeyIndex',
                  x: 0,
                  y: 1.35,
                  w: 16,
                  h: 2.16,
                  minW: 12,
                  maxW: 24,
                  minH: 2.16,
                  maxH: 2.16,
                  sysCode: '727001',
                  firstLetter: 'GJZB',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_0100',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11751241,
              },
            ],
            moduleId: 11721421,
          },
          templetModuleRelId: 714689,
        },
        {
          id: 714778,
          moduleId: 11721175,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 1,
          module: {
            id: 11721175,
            moduleName: '待办-68045',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11750995,
                moduleId: 11721175,
                moduleIndex: 0,
                funcMenu: {
                  id: 276108,
                  menuId: 276108,
                  menuName: '待办',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 9,
                  parMenuId: 276103,
                  menuDesc: '待办',
                  urlAddr: '[portlet]Undo',
                  x: 16,
                  y: 0,
                  w: 8,
                  h: 2.16,
                  minW: 8,
                  maxW: 24,
                  minH: 2.16,
                  maxH: 2.16,
                  sysCode: '727001',
                  firstLetter: 'DB',
                  iconUrl: 'icon-gene-redpacket',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_005',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11750995,
              },
            ],
            moduleId: 11721175,
          },
          templetModuleRelId: 714778,
        },
        {
          id: 7147781,
          moduleId: 117211751,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 1,
          module: {
            id: 117211751,
            moduleName: '增长趋势',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11750995,
                moduleId: 117211751,
                moduleIndex: 0,
                funcMenu: {
                  id: 27610822,
                  menuId: 27610822,
                  menuName: '增长趋势',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 9,
                  parMenuId: 276103,
                  menuDesc: '增长趋势',
                  urlAddr: '[portlet]GrowthRate',
                  x: 0,
                  y: 3.51,
                  w: 24,
                  h: 3.64,
                  minW: 12,
                  maxW: 24,
                  minH: 3.64,
                  maxH: 3.64,
                  sysCode: '727001',
                  firstLetter: 'DB',
                  iconUrl: 'icon-gene-redpacket',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_005',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11750995,
              },
            ],
            moduleId: 117211751,
          },
          templetModuleRelId: 7147781,
        },
        {
          id: 714780,
          moduleId: 11721449,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 3,
          module: {
            id: 11721449,
            moduleName: '客户状态分布',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11751269,
                moduleId: 11721449,
                moduleIndex: 0,
                funcMenu: {
                  id: 2770201,
                  menuId: 2770201,
                  menuName: '客户状态分布',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 137,
                  parMenuId: 276103,
                  menuDesc: '客户状态分布',
                  urlAddr: '[portlet]CustomerDistribution',
                  x: 0,
                  y: 7.15,
                  w: 12,
                  h: 3.7,
                  minW: 8,
                  maxW: 24,
                  minH: 3.7,
                  maxH: 3.7,
                  sysCode: '727001',
                  firstLetter: 'RWGS',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_0109',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11751269,
              },
            ],
            moduleId: 11721449,
          },
          templetModuleRelId: 714780,
        },
        {
          id: 714780,
          moduleId: 117214491,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 3,
          module: {
            id: 117214491,
            moduleName: '商机状态分布',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11751269,
                moduleId: 117214491,
                moduleIndex: 0,
                funcMenu: {
                  id: 2770202,
                  menuId: 2770202,
                  menuName: '商机状态分布',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 137,
                  parMenuId: 276103,
                  menuDesc: '商机状态分布',
                  urlAddr: '[portlet]BusinessDistribution',
                  x: 12,
                  y: 7.15,
                  w: 12,
                  h: 3.7,
                  minW: 8,
                  maxW: 24,
                  minH: 3.7,
                  maxH: 3.7,
                  sysCode: '727001',
                  firstLetter: 'RWGS',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_0109',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11751269,
              },
            ],
            moduleId: 117214491,
          },
          templetModuleRelId: 714780,
        },
      ],
      terminalType: '1000',
      confType: '1',
      templetId: 68045,
    });
  },
  'GET /portal/ModuleController/getPostModuleAndRel.do': (req, res) => {
    res.status(200).send({
      modules: [
        {
          id: 11721174,
          moduleName: '系统公告-68045',
          moduleType: '1',
          moduleLevel: 1,
          details: [
            {
              id: 11750994,
              moduleId: 11721174,
              moduleIndex: 0,
              funcMenu: {
                id: 276109,
                menuId: 276109,
                menuName: '系统公告',
                menuType: '1100',
                menuLevel: -1,
                menuIndex: 7,
                parMenuId: 276103,
                menuDesc: '公告',
                urlAddr: '[portlet]Bulletin',
                x: 0,
                y: 0,
                w: 24,
                h: 1.35,
                minW: 12,
                maxW: 24,
                minH: 1.35,
                maxH: 1.35,
                sysCode: '727001',
                firstLetter: 'XTGG',
                iconUrl: 'icon-gene-man-manager',
                paramEncryptType: '1000',
                menuOpenMode: '1',
                menuCode: 'TYMH_MENU_001',
                ifMainName: '',
                menuTypeName: '叶子菜单',
              },
              moduleDetailId: 11750994,
            },
          ],
          moduleId: 11721174,
        },
        {
          id: 11721421,
          moduleName: '关键指标',
          moduleType: '1',
          moduleLevel: 1,
          details: [
            {
              id: 11751241,
              moduleId: 11721421,
              moduleIndex: 0,
              funcMenu: {
                id: 2769844,
                menuId: 2769844,
                menuName: '关键指标',
                menuType: '1100',
                menuLevel: -1,
                menuIndex: 129,
                parMenuId: 276103,
                menuDesc: '关键指标',
                urlAddr: '[portlet]KeyIndex',
                x: 0,
                y: 1.35,
                w: 16,
                h: 2.16,
                minW: 12,
                maxW: 24,
                minH: 2.16,
                maxH: 2.16,
                sysCode: '727001',
                firstLetter: 'GJZB',
                iconUrl: 'icon-gene-man-manager',
                paramEncryptType: '1000',
                menuOpenMode: '1',
                menuCode: 'TYMH_MENU_0100',
                ifMainName: '',
                menuTypeName: '叶子菜单',
              },
              moduleDetailId: 11751241,
            },
          ],
          moduleId: 11721421,
        },
        {
          id: 11721175,
          moduleName: '待办-68045',
          moduleType: '1',
          moduleLevel: 1,
          details: [
            {
              id: 11750995,
              moduleId: 11721175,
              moduleIndex: 0,
              funcMenu: {
                id: 276108,
                menuId: 276108,
                menuName: '待办',
                menuType: '1100',
                menuLevel: -1,
                menuIndex: 9,
                parMenuId: 276103,
                menuDesc: '待办',
                urlAddr: '[portlet]Undo',
                x: 16,
                y: 1.35,
                w: 8,
                h: 2.16,
                minW: 8,
                maxW: 24,
                minH: 2.16,
                maxH: 2.16,
                sysCode: '727001',
                firstLetter: 'DB',
                iconUrl: 'icon-gene-redpacket',
                paramEncryptType: '1000',
                menuOpenMode: '1',
                menuCode: 'TYMH_MENU_005',
                ifMainName: '',
                menuTypeName: '叶子菜单',
              },
              moduleDetailId: 11750995,
            },
          ],
          moduleId: 11721175,
        },
        {
          id: 117211751,
          moduleName: '增长趋势',
          moduleType: '1',
          moduleLevel: 1,
          details: [
            {
              id: 11750995,
              moduleId: 117211751,
              moduleIndex: 0,
              funcMenu: {
                id: 27610822,
                menuId: 27610822,
                menuName: '增长趋势',
                menuType: '1100',
                menuLevel: -1,
                menuIndex: 9,
                parMenuId: 276103,
                menuDesc: '增长趋势',
                urlAddr: '[portlet]GrowthRate',
                x: 0,
                y: 3.51,
                w: 24,
                h: 3.64,
                minW: 12,
                maxW: 24,
                minH: 3.64,
                maxH: 3.64,
                sysCode: '727001',
                firstLetter: 'DB',
                iconUrl: 'icon-gene-redpacket',
                paramEncryptType: '1000',
                menuOpenMode: '1',
                menuCode: 'TYMH_MENU_005',
                ifMainName: '',
                menuTypeName: '叶子菜单',
              },
              moduleDetailId: 11750995,
            },
          ],
          moduleId: 117211751,
        },
        {
          id: 11721449,
          moduleName: '客户状态分布',
          moduleType: '1',
          moduleLevel: 1,
          details: [
            {
              id: 11751269,
              moduleId: 11721449,
              moduleIndex: 0,
              funcMenu: {
                id: 2770201,
                menuId: 2770201,
                menuName: '客户状态分布',
                menuType: '1100',
                menuLevel: -1,
                menuIndex: 137,
                parMenuId: 276103,
                menuDesc: '客户状态分布',
                urlAddr: '[portlet]CustomerDistribution',
                x: 0,
                y: 7.15,
                w: 12,
                h: 3.7,
                minW: 8,
                maxW: 24,
                minH: 3.7,
                maxH: 3.7,
                sysCode: '727001',
                firstLetter: 'RWGS',
                iconUrl: 'icon-gene-man-manager',
                paramEncryptType: '1000',
                menuOpenMode: '1',
                menuCode: 'TYMH_MENU_0109',
                ifMainName: '',
                menuTypeName: '叶子菜单',
              },
              moduleDetailId: 11751269,
            },
          ],
          moduleId: 11721449,
        },
        {
          id: 117214491,
          moduleName: '商机状态分布',
          moduleType: '1',
          moduleLevel: 1,
          details: [
            {
              id: 11751269,
              moduleId: 117214491,
              moduleIndex: 0,
              funcMenu: {
                id: 2770202,
                menuId: 2770202,
                menuName: '商机状态分布',
                menuType: '1100',
                menuLevel: -1,
                menuIndex: 137,
                parMenuId: 276103,
                menuDesc: '商机状态分布',
                urlAddr: '[portlet]BusinessDistribution',
                x: 12,
                y: 7.15,
                w: 12,
                h: 3.7,
                minW: 8,
                maxW: 24,
                minH: 3.7,
                maxH: 3.7,
                sysCode: '727001',
                firstLetter: 'RWGS',
                iconUrl: 'icon-gene-man-manager',
                paramEncryptType: '1000',
                menuOpenMode: '1',
                menuCode: 'TYMH_MENU_0109',
                ifMainName: '',
                menuTypeName: '叶子菜单',
              },
              moduleDetailId: 117512691,
            },
          ],
          moduleId: 117214491,
        },
      ],
      templetModuleRels: [],
    });
  },
  'GET /orgauth/WorkbenchController/qryTasksNumbers.do': (req, res) => {
    res.status(200).send({
      key: 'svcCont',
      svcContContext: {},
      resultCode: '0',
      resultObject: {
        require_obj: false,
        key: 'resultObject',
        data: {
          details: [
            {
              count: 173,
              status: 'TODO',
              taskInfos: [
                {
                  taskType: 'WARNING',
                  subTaskInfo: [
                    { count: 0, taskType: 'HIGH_DANGER', status: 'TODO', parTaskType: 'WARNING' },
                    { count: 0, taskType: 'COLOR_LIGHT', status: 'TODO', parTaskType: 'WARNING' },
                    {
                      count: 0,
                      taskType: 'CONTRACT_TIME_OUT',
                      status: 'TODO',
                      parTaskType: 'WARNING',
                    },
                    {
                      count: 0,
                      taskType: 'DELAY_APPROVAL',
                      status: 'TODO',
                      parTaskType: 'WARNING',
                    },
                  ],
                },
                {
                  taskType: 'MARKETING',
                  subTaskInfo: [{ count: 0, taskType: 'SPECIAL_MARKETING' }],
                },
                {
                  taskType: 'SERVICE_TASK',
                  subTaskInfo: [{ count: 0, taskType: 'BIRTHDAY_SERVICE' }],
                },
                { taskType: 'OPP_TASK', subTaskInfo: [{ count: 3, taskType: 'OPPORTUNITY' }] },
                { taskType: 'DAILY_TASK', subTaskInfo: [{ count: 0, taskType: 'DAILY' }] },
                {
                  taskType: 'VISIT_TASK',
                  subTaskInfo: [
                    { count: 0, taskType: 'VISIT_DAILY_TASK' },
                    { count: 0, taskType: 'CLUE_TASK' },
                    { count: 0, taskType: 'OPPT_TASK' },
                  ],
                },
                { taskType: 'CUST_TYPE', subTaskInfo: [{ count: 0, taskType: 'CUST_SERVICE' }] },
                { taskType: 'ORDER_TYPE', subTaskInfo: [{ count: 5, taskType: 'ORDER_SERVICE' }] },
                {
                  taskType: 'AGREEMENT_TYPE',
                  subTaskInfo: [
                    { count: 26, taskType: 'AGREEMENT_SERVICE' },
                    { count: 21, taskType: 'AGREEMENT_TEMPLET_SERVICE' },
                  ],
                },
                {
                  taskType: 'SURVEY_TYPE',
                  subTaskInfo: [{ count: 4, taskType: 'SURVEY_TASKS_SERVICE' }],
                },
                { taskType: 'JLB_TYPE', subTaskInfo: [] },
                {
                  taskType: 'CPC_TYPE',
                  subTaskInfo: [
                    { count: 75, taskType: 'PRODUCT_IMPORT' },
                    { count: 26, taskType: 'PRODUCT_SHELVES' },
                    { count: 2, taskType: 'GOODS_AUDIT' },
                    { count: 5, taskType: 'GOODS_SHELVES' },
                    { count: 6, taskType: 'CHANNEL_CHANGE' },
                  ],
                },
              ],
            },
            {
              count: 479,
              status: 'HAVE_DONE',
              taskInfos: [
                {
                  taskType: 'WARNING',
                  subTaskInfo: [
                    {
                      count: 0,
                      taskType: 'HIGH_DANGER',
                      status: 'HAVE_DONE',
                      parTaskType: 'WARNING',
                    },
                    {
                      count: 0,
                      taskType: 'COLOR_LIGHT',
                      status: 'HAVE_DONE',
                      parTaskType: 'WARNING',
                    },
                    {
                      count: 0,
                      taskType: 'CONTRACT_TIME_OUT',
                      status: 'HAVE_DONE',
                      parTaskType: 'WARNING',
                    },
                    {
                      count: 0,
                      taskType: 'DELAY_APPROVAL',
                      status: 'HAVE_DONE',
                      parTaskType: 'WARNING',
                    },
                  ],
                },
                {
                  taskType: 'MARKETING',
                  subTaskInfo: [{ count: 0, taskType: 'SPECIAL_MARKETING' }],
                },
                {
                  taskType: 'SERVICE_TASK',
                  subTaskInfo: [{ count: 0, taskType: 'BIRTHDAY_SERVICE' }],
                },
                { taskType: 'OPP_TASK', subTaskInfo: [{ count: 5, taskType: 'OPPORTUNITY' }] },
                { taskType: 'DAILY_TASK', subTaskInfo: [{ count: 0, taskType: 'DAILY' }] },
                {
                  taskType: 'VISIT_TASK',
                  subTaskInfo: [
                    { count: 0, taskType: 'VISIT_DAILY_TASK' },
                    { count: 0, taskType: 'CLUE_TASK' },
                    { count: 0, taskType: 'OPPT_TASK' },
                  ],
                },
                { taskType: 'CUST_TYPE', subTaskInfo: [{ count: 4, taskType: 'CUST_SERVICE' }] },
                { taskType: 'ORDER_TYPE', subTaskInfo: [{ count: 6, taskType: 'ORDER_SERVICE' }] },
                {
                  taskType: 'AGREEMENT_TYPE',
                  subTaskInfo: [
                    { count: 29, taskType: 'AGREEMENT_SERVICE' },
                    { count: 21, taskType: 'AGREEMENT_TEMPLET_SERVICE' },
                  ],
                },
                {
                  taskType: 'SURVEY_TYPE',
                  subTaskInfo: [{ count: 0, taskType: 'SURVEY_TASKS_SERVICE' }],
                },
                { taskType: 'JLB_TYPE', subTaskInfo: [] },
                {
                  taskType: 'CPC_TYPE',
                  subTaskInfo: [
                    { count: 141, taskType: 'PRODUCT_IMPORT' },
                    { count: 86, taskType: 'PRODUCT_SHELVES' },
                    { count: 111, taskType: 'GOODS_AUDIT' },
                    { count: 49, taskType: 'GOODS_SHELVES' },
                    { count: 27, taskType: 'CHANNEL_CHANGE' },
                  ],
                },
              ],
            },
            {
              count: 73,
              status: 'FINISHED',
              taskInfos: [
                {
                  taskType: 'WARNING',
                  subTaskInfo: [
                    {
                      count: 0,
                      taskType: 'HIGH_DANGER',
                      status: 'FINISHED',
                      parTaskType: 'WARNING',
                    },
                    {
                      count: 0,
                      taskType: 'COLOR_LIGHT',
                      status: 'FINISHED',
                      parTaskType: 'WARNING',
                    },
                    {
                      count: 0,
                      taskType: 'CONTRACT_TIME_OUT',
                      status: 'FINISHED',
                      parTaskType: 'WARNING',
                    },
                    {
                      count: 0,
                      taskType: 'DELAY_APPROVAL',
                      status: 'FINISHED',
                      parTaskType: 'WARNING',
                    },
                  ],
                },
                {
                  taskType: 'MARKETING',
                  subTaskInfo: [{ count: 0, taskType: 'SPECIAL_MARKETING' }],
                },
                {
                  taskType: 'SERVICE_TASK',
                  subTaskInfo: [{ count: 0, taskType: 'BIRTHDAY_SERVICE' }],
                },
                { taskType: 'OPP_TASK', subTaskInfo: [{ count: 0, taskType: 'OPPORTUNITY' }] },
                { taskType: 'DAILY_TASK', subTaskInfo: [{ count: 0, taskType: 'DAILY' }] },
                {
                  taskType: 'VISIT_TASK',
                  subTaskInfo: [
                    { count: 0, taskType: 'VISIT_DAILY_TASK' },
                    { count: 0, taskType: 'CLUE_TASK' },
                    { count: 0, taskType: 'OPPT_TASK' },
                  ],
                },
                { taskType: 'CUST_TYPE', subTaskInfo: [{ count: 0, taskType: 'CUST_SERVICE' }] },
                { taskType: 'ORDER_TYPE', subTaskInfo: [{ count: 0, taskType: 'ORDER_SERVICE' }] },
                {
                  taskType: 'AGREEMENT_TYPE',
                  subTaskInfo: [
                    { count: 0, taskType: 'AGREEMENT_SERVICE' },
                    { count: 0, taskType: 'AGREEMENT_TEMPLET_SERVICE' },
                  ],
                },
                {
                  taskType: 'SURVEY_TYPE',
                  subTaskInfo: [{ count: 0, taskType: 'SURVEY_TASKS_SERVICE' }],
                },
                { taskType: 'JLB_TYPE', subTaskInfo: [] },
                {
                  taskType: 'CPC_TYPE',
                  subTaskInfo: [
                    { count: 12, taskType: 'PRODUCT_IMPORT' },
                    { count: 13, taskType: 'PRODUCT_SHELVES' },
                    { count: 14, taskType: 'GOODS_AUDIT' },
                    { count: 11, taskType: 'GOODS_SHELVES' },
                    { count: 23, taskType: 'CHANNEL_CHANGE' },
                  ],
                },
              ],
            },
          ],
        },
      },
      resultData: {
        details: [
          {
            count: 173,
            status: 'TODO',
            taskInfos: [
              {
                taskType: 'WARNING',
                subTaskInfo: [
                  { count: 0, taskType: 'HIGH_DANGER', status: 'TODO', parTaskType: 'WARNING' },
                  { count: 0, taskType: 'COLOR_LIGHT', status: 'TODO', parTaskType: 'WARNING' },
                  {
                    count: 0,
                    taskType: 'CONTRACT_TIME_OUT',
                    status: 'TODO',
                    parTaskType: 'WARNING',
                  },
                  { count: 0, taskType: 'DELAY_APPROVAL', status: 'TODO', parTaskType: 'WARNING' },
                ],
              },
              { taskType: 'MARKETING', subTaskInfo: [{ count: 0, taskType: 'SPECIAL_MARKETING' }] },
              {
                taskType: 'SERVICE_TASK',
                subTaskInfo: [{ count: 0, taskType: 'BIRTHDAY_SERVICE' }],
              },
              { taskType: 'OPP_TASK', subTaskInfo: [{ count: 3, taskType: 'OPPORTUNITY' }] },
              { taskType: 'DAILY_TASK', subTaskInfo: [{ count: 0, taskType: 'DAILY' }] },
              {
                taskType: 'VISIT_TASK',
                subTaskInfo: [
                  { count: 0, taskType: 'VISIT_DAILY_TASK' },
                  { count: 0, taskType: 'CLUE_TASK' },
                  { count: 0, taskType: 'OPPT_TASK' },
                ],
              },
              { taskType: 'CUST_TYPE', subTaskInfo: [{ count: 0, taskType: 'CUST_SERVICE' }] },
              { taskType: 'ORDER_TYPE', subTaskInfo: [{ count: 5, taskType: 'ORDER_SERVICE' }] },
              {
                taskType: 'AGREEMENT_TYPE',
                subTaskInfo: [
                  { count: 26, taskType: 'AGREEMENT_SERVICE' },
                  { count: 21, taskType: 'AGREEMENT_TEMPLET_SERVICE' },
                ],
              },
              {
                taskType: 'SURVEY_TYPE',
                subTaskInfo: [{ count: 4, taskType: 'SURVEY_TASKS_SERVICE' }],
              },
              { taskType: 'JLB_TYPE', subTaskInfo: [] },
              {
                taskType: 'CPC_TYPE',
                subTaskInfo: [
                  { count: 75, taskType: 'PRODUCT_IMPORT' },
                  { count: 26, taskType: 'PRODUCT_SHELVES' },
                  { count: 2, taskType: 'GOODS_AUDIT' },
                  { count: 5, taskType: 'GOODS_SHELVES' },
                  { count: 6, taskType: 'CHANNEL_CHANGE' },
                ],
              },
            ],
          },
          {
            count: 479,
            status: 'HAVE_DONE',
            taskInfos: [
              {
                taskType: 'WARNING',
                subTaskInfo: [
                  {
                    count: 0,
                    taskType: 'HIGH_DANGER',
                    status: 'HAVE_DONE',
                    parTaskType: 'WARNING',
                  },
                  {
                    count: 0,
                    taskType: 'COLOR_LIGHT',
                    status: 'HAVE_DONE',
                    parTaskType: 'WARNING',
                  },
                  {
                    count: 0,
                    taskType: 'CONTRACT_TIME_OUT',
                    status: 'HAVE_DONE',
                    parTaskType: 'WARNING',
                  },
                  {
                    count: 0,
                    taskType: 'DELAY_APPROVAL',
                    status: 'HAVE_DONE',
                    parTaskType: 'WARNING',
                  },
                ],
              },
              { taskType: 'MARKETING', subTaskInfo: [{ count: 0, taskType: 'SPECIAL_MARKETING' }] },
              {
                taskType: 'SERVICE_TASK',
                subTaskInfo: [{ count: 0, taskType: 'BIRTHDAY_SERVICE' }],
              },
              { taskType: 'OPP_TASK', subTaskInfo: [{ count: 5, taskType: 'OPPORTUNITY' }] },
              { taskType: 'DAILY_TASK', subTaskInfo: [{ count: 0, taskType: 'DAILY' }] },
              {
                taskType: 'VISIT_TASK',
                subTaskInfo: [
                  { count: 0, taskType: 'VISIT_DAILY_TASK' },
                  { count: 0, taskType: 'CLUE_TASK' },
                  { count: 0, taskType: 'OPPT_TASK' },
                ],
              },
              { taskType: 'CUST_TYPE', subTaskInfo: [{ count: 4, taskType: 'CUST_SERVICE' }] },
              { taskType: 'ORDER_TYPE', subTaskInfo: [{ count: 6, taskType: 'ORDER_SERVICE' }] },
              {
                taskType: 'AGREEMENT_TYPE',
                subTaskInfo: [
                  { count: 29, taskType: 'AGREEMENT_SERVICE' },
                  { count: 21, taskType: 'AGREEMENT_TEMPLET_SERVICE' },
                ],
              },
              {
                taskType: 'SURVEY_TYPE',
                subTaskInfo: [{ count: 0, taskType: 'SURVEY_TASKS_SERVICE' }],
              },
              { taskType: 'JLB_TYPE', subTaskInfo: [] },
              {
                taskType: 'CPC_TYPE',
                subTaskInfo: [
                  { count: 141, taskType: 'PRODUCT_IMPORT' },
                  { count: 86, taskType: 'PRODUCT_SHELVES' },
                  { count: 111, taskType: 'GOODS_AUDIT' },
                  { count: 49, taskType: 'GOODS_SHELVES' },
                  { count: 27, taskType: 'CHANNEL_CHANGE' },
                ],
              },
            ],
          },
          {
            count: 73,
            status: 'FINISHED',
            taskInfos: [
              {
                taskType: 'WARNING',
                subTaskInfo: [
                  { count: 0, taskType: 'HIGH_DANGER', status: 'FINISHED', parTaskType: 'WARNING' },
                  { count: 0, taskType: 'COLOR_LIGHT', status: 'FINISHED', parTaskType: 'WARNING' },
                  {
                    count: 0,
                    taskType: 'CONTRACT_TIME_OUT',
                    status: 'FINISHED',
                    parTaskType: 'WARNING',
                  },
                  {
                    count: 0,
                    taskType: 'DELAY_APPROVAL',
                    status: 'FINISHED',
                    parTaskType: 'WARNING',
                  },
                ],
              },
              { taskType: 'MARKETING', subTaskInfo: [{ count: 0, taskType: 'SPECIAL_MARKETING' }] },
              {
                taskType: 'SERVICE_TASK',
                subTaskInfo: [{ count: 0, taskType: 'BIRTHDAY_SERVICE' }],
              },
              { taskType: 'OPP_TASK', subTaskInfo: [{ count: 0, taskType: 'OPPORTUNITY' }] },
              { taskType: 'DAILY_TASK', subTaskInfo: [{ count: 0, taskType: 'DAILY' }] },
              {
                taskType: 'VISIT_TASK',
                subTaskInfo: [
                  { count: 0, taskType: 'VISIT_DAILY_TASK' },
                  { count: 0, taskType: 'CLUE_TASK' },
                  { count: 0, taskType: 'OPPT_TASK' },
                ],
              },
              { taskType: 'CUST_TYPE', subTaskInfo: [{ count: 0, taskType: 'CUST_SERVICE' }] },
              { taskType: 'ORDER_TYPE', subTaskInfo: [{ count: 0, taskType: 'ORDER_SERVICE' }] },
              {
                taskType: 'AGREEMENT_TYPE',
                subTaskInfo: [
                  { count: 0, taskType: 'AGREEMENT_SERVICE' },
                  { count: 0, taskType: 'AGREEMENT_TEMPLET_SERVICE' },
                ],
              },
              {
                taskType: 'SURVEY_TYPE',
                subTaskInfo: [{ count: 0, taskType: 'SURVEY_TASKS_SERVICE' }],
              },
              { taskType: 'JLB_TYPE', subTaskInfo: [] },
              {
                taskType: 'CPC_TYPE',
                subTaskInfo: [
                  { count: 12, taskType: 'PRODUCT_IMPORT' },
                  { count: 13, taskType: 'PRODUCT_SHELVES' },
                  { count: 14, taskType: 'GOODS_AUDIT' },
                  { count: 11, taskType: 'GOODS_SHELVES' },
                  { count: 23, taskType: 'CHANNEL_CHANGE' },
                ],
              },
            ],
          },
        ],
      },
    });
  },
};

export default delay(proxy, defaultSettings.delay);
