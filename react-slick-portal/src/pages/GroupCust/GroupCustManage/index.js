import React, { useEffect, useRef } from 'react';
import dynamic from 'umi/dynamic';
import { connect } from 'dva';
import StepWizard from '@/components/StepWizard';
import PageLoading from '@/components/PageLoading';
import Edit from './components/Edit';

const List = dynamic({
  loader: () => import('./components/List'),
  loading: PageLoading,
});

function Index(props) {
  const { extOrderNbr, viewMode } = props;
  const wizardRef = useRef(null);

  useEffect(() => {
    if (extOrderNbr && wizardRef.current) {
      // 跳到 Edit 步骤（第二个 step，index 从 1 开始）
      wizardRef.current.goToStep(2);
    }
  }, [extOrderNbr]);

  return (
    <StepWizard
      isLazyMount
      instance={wizard => {
        wizardRef.current = wizard;
      }}
    >
      <List />
      <Edit extOrderNbr={extOrderNbr} viewMode={viewMode} destroy={!extOrderNbr} isBackButton={!extOrderNbr} />
    </StepWizard>
  );
}

export default connect(() => ({}))(Index);
