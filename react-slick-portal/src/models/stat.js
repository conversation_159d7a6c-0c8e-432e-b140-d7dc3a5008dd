import { message } from 'antd';
import flatMap from 'lodash/flatMap';
import find from 'lodash/find';
import result from 'lodash/result';
import reduce from 'lodash/reduce';
import sortBy from 'lodash/sortBy';
import { getTodoRefreshTime, getDataDictByGroup, qryTasksNumbers } from '@/services/undo';

const arr = [
  {
    countType: 'single',
    taskName: '订单任务',
    icon: 'icon-wanggou',
    code: 'MENU_DDRW_TODO',
    taskType: 'ORDER_SERVICE',
    taskCount: 0,
  },
  {
    taskName: '合同任务',
    countType: 'single',
    icon: 'icon-yinzhang',
    code: 'MENU_HTRW_TODO',
    taskType: 'AGREEMENT_SERVICE',
    taskCount: 0,
  },
  {
    taskName: '合同模板任务',
    countType: 'single',
    code: 'MENU_HTMBRW_TODO',
    icon: 'icon-rongyu',
    taskType: 'AGREEMENT_TEMPLET_SERVICE',
    taskCount: 0,
  },
  {
    taskName: '商机任务',
    countType: 'single',
    code: 'MENU_SJRW_TODO',
    icon: 'icon-xunzhang',
    taskType: 'OPPORTUNITY',
    taskCount: 0,
  },
  {
    taskName: '拜访任务',
    countType: 'single',
    code: 'MENU_RCRW_TODO',
    icon: 'icon-rili',
    taskType: 'OPP_SALE_VISIT_TASK',
    taskCount: 0,
  },
  {
    taskName: '营销任务',
    countType: 'multi',
    code: 'MENU_YJRW_TODO',
    icon: 'icon-lanjia',
    taskType: 'MARKETING',
    taskCount: 0,
  },
  {
    taskName: '产商品任务',
    countType: 'multi',
    code: 'MENU_CPC_TODO',
    icon: 'icon-biaoqian',
    taskType: 'CPC_TYPE',
    taskCount: 0,
  },
  {
    taskName: '勘察任务',
    countType: 'single',
    code: 'MENU_CKRW_TODO',
    icon: 'icon-anquan',
    taskType: 'SURVEY_TASKS_SERVICE',
    taskCount: 0,
  },
  {
    taskName: '客户任务',
    countType: 'single',
    code: 'MENU_KHRW_TODO',
    icon: 'icon-shoucangyonghu',
    taskType: 'CUST_SERVICE',
    taskCount: 0,
  },
  {
    taskName: '生日关怀',
    countType: 'single',
    code: 'MENU_FWRW_TODO',
    icon: 'icon-aixin',
    taskType: 'BIRTHDAY_SERVICE',
    taskCount: 0,
  },
  {
    taskName: '项目任务',
    countType: 'single',
    code: 'MENU_BPC_TODO',
    icon: 'icon-shoucang',
    taskType: 'BPC_SERVICE',
    taskCount: 0,
  },
];
/**
 * 按taskCount降序排列，显示前8个
 * countType: 'multi',表示按组计数；sigle表示按单个计数
 *
 * [
 *  {
 *    taskName: '日常任务',
 *    taskCount: taskInfo.count,
 *    menuId: classData.MENU_RCRW_TODO,
 *    icon: 'icon-chengjia',
 *  }
 * ]
 * @return {object[] | null}
 */
function serialize(taskInfos, mapping) {
  return sortBy(
    arr.map(item => {
      const target1 = find(mapping, { paramCode: item.code });
      const subTaskInfos = flatMap(taskInfos, item => item.subTaskInfo);
      const target2 =
        item.countType === 'single'
          ? find(subTaskInfos, { taskType: item.taskType })
          : reduce(find(taskInfos, { taskType: item.taskType }).subTaskInfo, (total, b) => {
              return { count: (total.count || total) + b.count };
            });
      if (target1 !== undefined && target2 !== undefined) {
        return {
          ...item,
          menuId: target1.paramValue,
          taskCount: target2.count,
        };
      }
      return item;
    }),
    function(info) {
      return -parseInt(info.taskCount, 10);
    }
  ).slice(0, 8);
}

export default {
  namespace: 'stat',

  state: {
    taskResultData: [],
    mapping: [],
  },

  effects: {
    *getInitData({ payload }, { call, put }) {
      const [refreshTime, response2, response3] = yield [
        call(getTodoRefreshTime, { defValue: payload.defaultInterval }),
        call(getDataDictByGroup),
        call(qryTasksNumbers),
      ];
      const result = {};
      if (refreshTime) {
        result.refreshTime = parseInt(refreshTime, 10);
      } else {
        result.refreshTime = parseInt(payload.defaultInterval, 10);
      }
      if (response2 && response2.length > 0) {
        result.mapping = response2;
      }
      yield put({
        type: 'save',
        payload: result,
      });
      yield put({
        type: 'qryTasksNumbers',
      });
    },
    *qryTasksNumbers(_, { call, put, select }) {
      const response = yield call(qryTasksNumbers);
      const mapping = yield select(state => state.stat.mapping);

      if (response.resultCode === '0' && result(response, 'resultData.details') !== undefined) {
        const targetObject = find(response.resultData.details, { status: 'TODO' });

        if (targetObject !== undefined) {
          const { taskInfos = [] } = targetObject;

          yield put({
            type: 'save',
            payload: {
              taskResultData: serialize(taskInfos, mapping),
            },
          });
        }
      }
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
