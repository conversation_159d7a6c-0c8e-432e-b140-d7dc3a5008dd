import request from '@/utils/request';

// 获取权限列表
export function queryPermissionsAllList(props) {
  const { current, pageSize = 5, ...restParams } = props;

  return request('orgauth/PrivilegeController/queryPermissionsAllList.do', {
    data: {
      pageNum: current,
      pageSize,
      ...restParams,
    },
  }).then(res => {
    if (Array.isArray(res?.list)) {
      return {
        total: res.total,
        data: res.list,
      };
    }
    return {
      total: 0,
      data: [],
    };
  });
}

// 根据权限id和类型（互斥/依赖）获取权限互斥列表和权限依赖列表
export function selectPermissionsRelList(props) {
  const { current, pageSize = 5, privId, ...restParams } = props;
  if (!privId) {
    return Promise.resolve({
      total: 0,
      data: [],
    });
  }

  return request('orgauth/PermissionsRelController/selectPermissionsRelList.do', {
    data: {
      pageNum: current,
      pageSize,
      privId,
      ...restParams,
    },
  }).then(res => {
    if (Array.isArray(res?.list)) {
      return {
        total: res.total,
        data: res.list,
      };
    }
    return {
      total: 0,
      data: [],
    };
  });
}

// 新增页面互斥和依赖权限数据列表
export function queryExcPermissionsList(props) {
  const { current, pageSize = 10, privId, ...restParams } = props;
  if (!privId) {
    return Promise.resolve({
      total: 0,
      data: [],
    });
  }

  return request('orgauth/PrivilegeController/queryExcPermissionsList.do', {
    data: {
      pageNum: current,
      pageSize,
      privId,
      ...restParams,
    },
  }).then(res => {
    if (Array.isArray(res?.list)) {
      return {
        total: res.total,
        data: res.list,
      };
    }
    return {
      total: 0,
      data: [],
    };
  });
}

export function insertPermissionsRelList(props) {
  return request('orgauth/PermissionsRelController/insertPermissionsRelList.do', {
    data: {
      ...props,
    },
  });
}

export function deletePermissionsRelList(idList = []) {
  return request('orgauth/PermissionsRelController/deletePermissionsRelList.do', {
    data: idList,
  });
}

// 获取归属系统
export async function getSystemInfoList() {
  return request(
    'orgauth/SystemInfoController/getSystemInfoList.do',
    {
      method: 'GET',
    }
  );
}

// 获取权限下拉框
export async function selectPermissonAttrValueList() {
  return request(
    'orgauth/PermissionsRelController/selectPermissonAttrValueList.do',
    {
      method: 'GET',
    }
  ).then(res => res?.resultObject);
}

// 删除权限
export async function delPrivilegeById(id) {
  return request(`orgauth/PrivilegeController/delPrivByIds.do?privIds=${id}`, {
    method: 'delete',
  }).then(res => res);
}
