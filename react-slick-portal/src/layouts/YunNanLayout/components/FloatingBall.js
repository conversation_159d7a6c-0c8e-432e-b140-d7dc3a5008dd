import React, { useEffect, useState } from 'react';
import { Icon } from 'antd';
import PubSub from 'pubsub-js';
import { setItem, getItem } from '@/utils/utils';
import AIIcon from '../img/AI_icon.gif';

const ballWidth = 66; // 悬浮球的宽度
const ballHeight = 78; // 悬浮球的高度

const FloatingBall = () => {
  const { userCode } = getItem('user');

  const [position, setPosition] = useState({
    top: window.innerHeight - ballHeight - 10,
    left: window.innerWidth - ballWidth - 10,
  });
  // const [isDragging, setIsDragging] = useState(false);
  // const [hasDragged, setHasDragged] = useState(false);
  // const [offset, setOffset] = useState({ x: 0, y: 0 });
  const [minAI, setMinAI] = useState(false);

  const handlePosition = flag => {
    if (flag) {
      setPosition({
        top: window.innerHeight - ballHeight - 10,
        left: window.innerWidth - 36,
        transform: 'rotate(-50deg)',
        width: ballWidth * 0.8,
        height: ballHeight * 0.8,
      });
    } else {
      setPosition({
        top: window.innerHeight - ballHeight - 10,
        left: window.innerWidth - ballWidth - 10,
        width: ballWidth,
        height: ballHeight,
      });
    }
  };

  const handleMinAI = flag => {
    setMinAI(flag);
    handlePosition(flag);
    setItem(`${userCode}_AIshow_status`, flag, 'localStorage');
  };

  const handleClick = () => {
    if (minAI) {
      handleMinAI(false);
    } else {
      // 打开智能助手
      PubSub.publish('systemOpt_openAIModal', {
        keyword: '',
        isCenter: false,
      });
    }
  };

  useEffect(() => {
    handlePosition(minAI);
  }, [window.innerHeight, window.innerWidth]);

  /**
   * 拖拽功能
   */
  // const handleMouseDown = e => {
  //   e.preventDefault();
  //   const rect = e.target.getBoundingClientRect();
  //   setOffset({
  //     x: e.clientX - rect.left,
  //     y: e.clientY - rect.top,
  //   });
  //   setIsDragging(true);
  //   setHasDragged(false);
  // };

  // const handleMouseMove = e => {
  //   if (isDragging) {
  //     const newTop = Math.min(window.innerHeight - ballHeight, Math.max(0, e.clientY - offset.y));
  //     const newLeft = Math.min(window.innerWidth - ballWidth, Math.max(0, e.clientX - offset.x));

  //     setPosition({
  //       top: newTop,
  //       left: newLeft,
  //     });
  //     setHasDragged(true);
  //   }
  // };

  // const handleMouseUp = () => {
  //   setIsDragging(false);
  // };

  // useEffect(() => {
  //   if (isDragging) {
  //     document.addEventListener('mousemove', handleMouseMove);
  //     document.addEventListener('mouseup', handleMouseUp);
  //   }
  //   return () => {
  //     document.removeEventListener('mousemove', handleMouseMove);
  //     document.removeEventListener('mouseup', handleMouseUp);
  //   };
  // }, [isDragging]);

  return (
    <>
      <div
        style={{
          position: 'fixed',
          background: 'transparent',
          zIndex: 1000,
          ...position,
        }}
      >
        <div
          style={{
            position: 'absolute',
            right: 0,
            top: 0,
            width: '100%',
            height: '100%',
            zIndex: 1000,
          }}
          // onMouseDown={handleMouseDown}
          onClick={handleClick}
        >
          <img src={AIIcon} alt="" width="100%" height="100%" />
        </div>
        {
          minAI ? null : (
            <Icon
              type="close-circle"
              theme="filled"
              style={{
                position: 'absolute',
                right: 6,
                top: 4,
                zIndex: 1001,
              }}
              onClick={() => handleMinAI(true)}
            />
          )
        }
      </div>
    </>
  );
};

export default FloatingBall;
