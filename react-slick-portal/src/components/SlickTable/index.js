import React, { PureComponent, Fragment } from 'react';
import { Table } from 'antd';
import PropTypes from 'prop-types';
import { FormattedMessage } from 'umi/locale';
import numeral from 'numeral';
import isArray from 'lodash/isArray';
import isFunction from 'lodash/isFunction';
import forIn from 'lodash/forIn';
import find from 'lodash/find';
import classNames from 'classnames';
import styles from './index.less';

function initTotalList(columns) {
  const totalList = [];
  columns.forEach(column => {
    if (column.needTotal) {
      totalList.push({ ...column, total: 0 });
    }
  });
  return totalList;
}

class SlickTable extends PureComponent {
  constructor(props) {
    super(props);
    const { columns, selectedRowKeys = [], allSelectedRows = [] } = props;
    const needTotalList = initTotalList(columns);

    this.state = {
      selectedRowKeys,
      allSelectedRows,
      needTotalList,
    };
  }

  static getDerivedStateFromProps(nextProps) {
    // clean state
    if (isArray(nextProps.selectedRows) && nextProps.selectedRows.length === 0) {
      const needTotalList = initTotalList(nextProps.columns);
      return {
        selectedRowKeys: [],
        allSelectedRows: [],
        needTotalList,
      };
    }
    return null;
  }

  /**
   *
   * @params {number[]} selectedRowKeys 当前所有选中项（包括非当前页）主键对应的所有值
   * @params {number[]} selectedRows 服务端分页模式 只会返回当前页所有选中行的数据
   */
  handleRowSelectChange = (selectedRowKeys, selectedRows) => {
    // eslint-disable-next-line prefer-const
    let { needTotalList, allSelectedRows } = this.state;

    const {
      data: { list },
      dataSource,
      onSelectRow,
      rowKey,
    } = this.props;
    const tableDataInCurrentPage = Array.isArray(dataSource) ? dataSource : list;
    const result = [];
    let key = '';
    // 找出key
    if (isFunction(rowKey)) {
      // rowKey()返回的是值，根据值提取对应的key
      forIn(tableDataInCurrentPage[0], (val, k) => {
        if (val === rowKey(tableDataInCurrentPage[0])) {
          key = k;
        }
      });
    } else if (typeof rowKey === 'string') {
      key = rowKey;
    } else {
      key = 'key';
    }
    // 根据selectedRowKeys提取所有选中行的全部数据
    selectedRowKeys.forEach(val => {
      const item = find([...allSelectedRows, ...tableDataInCurrentPage], { [key]: val });
      if (item !== undefined) {
        result.push(item);
      }
    });

    needTotalList = needTotalList.map(item => ({
      ...item,
      total: selectedRows.reduce((sum, val) => sum + parseFloat(val[item.dataIndex], 10), 0),
    }));

    if (onSelectRow) {
      onSelectRow(result);
    }

    this.setState({ selectedRowKeys, needTotalList, allSelectedRows: result });
  };

  handleTableChange = (pagination, filters, sorter) => {
    const { onChange } = this.props;
    if (onChange) {
      onChange(pagination, filters, sorter);
    }
  };

  cleanSelectedKeys = () => {
    this.handleRowSelectChange([], []);
  };

  renderExtra = (extra, selectedRowKeys) => {
    if (typeof extra === 'function') {
      return (
        <div className="margin-right" style={{ display: 'inline-block' }}>
          {extra(selectedRowKeys)}
        </div>
      );
    }
    if (React.isValidElement(extra)) {
      return (
        <div className="margin-right" style={{ display: 'inline-block' }}>
          {extra}
        </div>
      );
    }
    return null;
  };

  render() {
    const { selectedRowKeys, needTotalList } = this.state;
    const { data = {}, rowKey, className, rowSelection, extra = null, ...rest } = this.props;
    const { list = [], pagination } = data;
    const paginationProps = {
      showSizeChanger: false,
      showQuickJumper: true,
      showTotal: total => (
        <Fragment>
          <FormattedMessage id="component.slickTable.total" />
            &nbsp;
          {numeral(total).format('0,0')}&nbsp;
          <FormattedMessage id="component.slickTable.records" />
        </Fragment>
      ),
      ...pagination,
    };
    const newRowSelection =
      rest.pick === undefined
        ? null
        : {
            selectedRowKeys: rest.selectedRowKeysNew || selectedRowKeys,
            type: rest.pick,
            onChange: (keys, selectedRows) => this.handleRowSelectChange(keys, selectedRows),
            fixed: true,
            columnWidth: 40,
            getCheckboxProps: record => ({
              disabled: record.disabled,
            }),
            ...rowSelection,
          };

    return (
      <div className={styles.wrapper}>
        <Table
          rowKey={rowKey || 'key'}
          bordered
          size="middle"
          rowSelection={newRowSelection}
          // title={title}
          dataSource={list}
          pagination={paginationProps}
          onChange={this.handleTableChange}
          className={classNames('slick-table', className || '')}
          {...rest}
        />
        <div className="slick-table-extra">
          {this.renderExtra(extra, selectedRowKeys)}
          {selectedRowKeys.length > 0 && rest.pick === 'checkbox' ? (
            <>
              <span className={styles.records}>
                <FormattedMessage id="component.slickTable.selected" />
              </span>
              <span className="text-info bold " style={{ margin: '0 4px' }}>
                {selectedRowKeys.length}
              </span>
              <span className={styles.records}>
                <FormattedMessage id="component.slickTable.item" />
              </span>
              {needTotalList.map(item => (
                <span className="margin-left" key={item.dataIndex}>
                  {item.title}
                  <FormattedMessage id="component.slickTable.total" />
                  &nbsp;
                  <span style={{ fontWeight: 600 }}>
                    {item.render ? item.render(item.total) : item.total}
                  </span>
                </span>
              ))}
              <a onClick={this.cleanSelectedKeys} className="margin-left text-danger">
                <FormattedMessage id="component.slickTable.clear" />
              </a>
            </>
          ) : null}
        </div>
      </div>
    );
  }
}

SlickTable.defaultProps = {
  data: {
    list: [],
    pagination: {},
  },
  pick: undefined,
  extra: null,
  onSelectRow: undefined,
};

SlickTable.propTypes = {
  data: PropTypes.object,
  onSelectRow: PropTypes.func,
  pick: PropTypes.oneOf(['checkbox', 'radio']),
  extra: PropTypes.oneOfType([PropTypes.func, PropTypes.element]),
};

export default SlickTable;
