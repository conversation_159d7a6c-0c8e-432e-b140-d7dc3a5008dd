import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Button, message, Form, Row, Col, Input, Drawer } from 'antd';
import { useBoolean } from '@umijs/hooks';
import { connect } from 'dva';
import style from './index.less';
import { legalPersonInfoManage } from '@/pages/GroupCust/GroupCustView/service';
import GroupFileUpload from '@/components/GroupFileUpload';
import { validatePhone } from '@/utils/validator';

const Index = ({ form, currentCust }) => {
  const { getFieldDecorator } = form;
  const { state: modalVisible, setTrue: show, setFalse: hide } = useBoolean(false);
  const [list, setList] = useState([]);
  const [addLoading, setAddLoading] = useState(false);
  const [loading, setLoading] = useState(false);

  const queryInfo = () => {
    setLoading(true);
    legalPersonInfoManage({
      operType: 'QUERY',
      param: {
        operType: 'QUERY',
        groupId: currentCust.GROUP_ID,
      },
    }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.data) {
        setList(resultObject.rspParam.busiInfo.data);
      } else {
        message.error(resultMsg);
      }
      setLoading(false);
    });
  };

  useEffect(() => {
    if (currentCust.GROUP_ID) {
      queryInfo();
    }
  }, [currentCust]);

  const handleAdd = () => {
    form.validateFields((err, values) => {
      if (!err) {
        setAddLoading(true);
        const params = {
          operType: 'ADD',
          param: {
            operType: 'ADD',
            groupId: currentCust.GROUP_ID,
            orgaEnterpriseId: currentCust.ORGA_ENTERPRISE_ID,
            personScanIdCard: values.file?.[0]?.fileId,
            legalPersonName: values.legalPersonName,
            legalPersonPhone: values.legalPersonPhone,
            legalPersonIden: values.legalPersonIden,
          },
        };
        legalPersonInfoManage(params)
          .then(res => {
            const { resultCode, resultMsg } = res;
            if (resultCode === 'TRUE') {
              message.success('添加成功');
              hide();
              queryInfo();
            } else {
              message.error(resultMsg);
            }
          })
          .always(() => {
            setAddLoading(false);
          });
      }
    });
  };

  const handleDel = legalPersonId => () => {
    legalPersonInfoManage({
      operType: 'DELETE',
      param: {
        operType: 'DELETE',
        groupId: currentCust.GROUP_ID,
        legalPersonId,
      },
    })
      .then(res => {
        const { resultCode, resultMsg } = res;
        if (resultCode === 'TRUE') {
          message.success('删除成功');
          hide();
          queryInfo();
        } else {
          message.error(resultMsg);
        }
      })
      .always(() => {
        setAddLoading(false);
      });
  };
  const validatorFile = (rule, value, callback) => {
    if (!value || value?.length === 0) {
      callback('请上传文件');
    } else {
      callback();
    }
  };
  return (
    <div className={style.legalPersonContent}>
      {list.length > 0 ? (
        list.map(item => (
          <Card
            key={item.LEGAL_PERSON_ID}
            title={item.LEGAL_PERSON_NAME}
            extra={(
              <Button type="link" onClick={handleDel(item.LEGAL_PERSON_ID)}>
                删除
              </Button>
            )}
            className="legalPersonItem"
          >
            <Descriptions>
              <Descriptions.Item label="身份证号码">{item.LEGAL_PERSON_IDEN}</Descriptions.Item>
              <Descriptions.Item label="手机号码">{item.LEGAL_PERSON_PHONE}</Descriptions.Item>
              <Descriptions.Item label="操作时间">{item.CREATE_DATE}</Descriptions.Item>
            </Descriptions>
          </Card>
        ))
      ) : (
        <div className="empty" onClick={show}>
          <Button type="dashed" icon="plus" disabled={loading} loading={loading}>
            添加
          </Button>
        </div>
      )}
      {modalVisible && (
        <Drawer title="添加法人" width={520} onClose={hide} visible={modalVisible} destroyOnClose>
          <Form layout="horizontal">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="法人姓名">
                  {getFieldDecorator('legalPersonName', {
                    initialValue: currentCust.JURISTIC_NAME,
                    rules: [{ required: true, message: '请输入法人姓名' }],
                  })(<Input allowClear disabled />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="法人手机号">
                  {getFieldDecorator('legalPersonPhone', { rules: [{ required: true, message: '请输入法人手机号' }, { validator: validatePhone }] })(<Input allowClear />)}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="法人身份证号">
                  {getFieldDecorator('legalPersonIden', { rules: [{ required: true, message: '请输入法人身份证号' }] })(
                    <Input placeholder="请输入" allowClear />
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="个人证件扫描件" extra="文件类型:(.jpg)，大小限制:30MB">
                  {getFieldDecorator('file', {
                    rules: [{ required: true, message: '请上传个人证件扫描件' }, { validator: validatorFile }],
                  })(<GroupFileUpload accept="image/jpeg" fileType="1" />)}
                </Form.Item>
              </Col>
            </Row>
          </Form>
          <div className={style['form-drawer-bottom']}>
            <Button loading={addLoading} style={{ marginRight: '8px' }} onClick={handleAdd} type="primary">
              确定
            </Button>
            <Button onClick={hide}>取消</Button>
          </div>
        </Drawer>
      )}
    </div>
  );
};

export default connect(({ groupCustView, loading }) => ({
  currentCust: groupCustView.currentCust,
  loading: loading.effects['groupCustView/getCustomerInfo'],
}))(Form.create()(Index));
