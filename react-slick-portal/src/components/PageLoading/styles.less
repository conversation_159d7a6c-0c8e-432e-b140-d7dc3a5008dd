:global {
  #nprogress {
    .bar {
      background: @blue-6;
      position: fixed;
      z-index: 1031;
      top: 0;
      left: 0;
      width: 100%;
      height: 2px;
      transform: translate3d(-92%, 0px, 0px);
      transition: all 200ms linear 0s;
    }
    .peg {
      display: block;
      position: absolute;
      right: 0px;
      width: 100px;
      height: 100%;
      -webkit-box-shadow: 0 0 10px #29d, 0 0 5px #29d;
      box-shadow: 0 0 10px #29d, 0 0 5px #29d;
      opacity: 1;
      -webkit-transform: rotate(3deg) translate(0px, -4px);
      -ms-transform: rotate(3deg) translate(0px, -4px);
      transform: rotate(3deg) translate(0px, -4px);
    }
    .spinner {
      display: block;
      position: fixed;
      z-index: 1031;
      top: 15px;
      right: 15px;
    }
  }
}
