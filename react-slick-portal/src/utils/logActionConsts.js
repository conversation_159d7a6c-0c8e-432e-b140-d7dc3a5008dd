/**
 * 系统操作日志，操作类型
 */
export const ACTION_TYPE = {
  ACTION_TYPE_MENU: '1000', // 菜单点击 1000
  ACTION_TYPE_FUNC: '1100', // 功能操作 1100
  ACTION_TYPE_ERROR: '1200', // 系统异常 1200
  ACTION_TYPE_OTHER: '1500', // 其他 1500
};

export const ACTION = {
  ACTION_CLICK: '1000', // 菜单点击 1000
  ACTION_D: '1100', // 密码修改  1100
  ACTION_QUERY: '1200', // 查询    1200
  ACTION_ADD: '1300', // 新增    1300
  ACTION_UPDATE: '1400', // 修改    1400
  ACTION_DELETE: '1500', // 删除    1500
  ACTION_EXPORT: '1600', // 导出    1600
  ACTION_EFFECT: '1700', // 生效    1700
  ACTION_FAILURE: '1800', // 失效    1800
  ACTION_ADD_USER_ROLE: '1900', // 用户关联角色    1900
  ACTION_DEL_USER_ROLE: '2000', // 删除用户角色    2000
  ACTION_USER_GRANT: '2100', // 用户授权  2100
  ACTION_CANCEL_USER_GRANT: '2200', // 取消用户授权    2200
  ACTION_ADD_ROLE_USER: '2300', // 角色关联用户    2300
  ACTION_DEL_ROLE_USER: '2400', // 删除角色用户    2400
  ACTION_ROLE_GRANT: '2500', // 角色授权  2500
  ACTION_CANCEL_ROLE_GRANT: '2600', // 取消角色授权    2600
  ACTION_ADD_COMP: '2700', // 组件新增  2700
  ACTION_UPDATE_COMP: '2800', // 组件修改  2800
  ACTION_DEL_COMP: '2900', // 组件删除  2900
  ACTION_REFRESH: '3000', // 刷新    3000
  // 3100、3200后端枚举表示单点登录的登入和登出，所以这边枚举定义忽略3100、3200
  AUTH_MUTEX_ADD: '3300', // 新增互斥权限    3300
  AUTH_MUTEX_DEL: '3400', // 删除互斥权限    3400
  AUTH_DEP_ADD: '3500', // 新增依赖权限    3500
  AUTH_DEP_DEL: '3600', // 删除依赖权限    3600
  ACTION_SYNC: '3700', // 同步    3700
};

/**
 *处理结果 1200 未处理
 */
export const RESULT = {
  RESULT_SUCCESS: '',
  RESULT_DEL: '1000',
  RESULT_FAILURE: '1200', // 1100 未处理
  // RESULT_NOT_PROCESS: '1200', // 1200 未处理
};

function Map() {
  this.keys = [];
  this.data = {};

  // eslint-disable-next-line func-names
  this.set = function (key, value) {
    if (this.data[key] == null) {
      if (this.keys.indexOf(key) === -1) {
        this.keys.push(key);
      }
    }
    this.data[key] = value;
  };

  // eslint-disable-next-line func-names
  this.get = function (key) {
    return this.data[key];
  };
}

const actionsLogMap = new Map();
// 用户
actionsLogMap.set('/SystemUserController/selectUserGridData', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3290500',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/SystemUserController/updateUser', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3290500',
  action: ACTION.ACTION_UPDATE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('SystemUserController/addUser', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3290500',
  action: ACTION.ACTION_ADD,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/SystemUserController/successUser', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3290500',
  action: ACTION.ACTION_EFFECT,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/SystemUserController/failureUser', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3290500',
  action: ACTION.ACTION_FAILURE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/SystemUserController/syncUser', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3290500',
  action: ACTION.ACTION_SYNC,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/SystemUserRoleController/deleteUserRolesByIds', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3290500',
  action: ACTION.ACTION_DEL_USER_ROLE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
// 菜单
actionsLogMap.set('/FuncMenuController/selectAllMenu', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780366',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/FuncMenuController/addMenu', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780366',
  action: ACTION.ACTION_ADD,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/FuncMenuController/updateMenu', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780366',
  action: ACTION.ACTION_UPDATE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/FuncMenuController/deleteMenuByMenuId', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780366',
  action: ACTION.ACTION_DELETE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/FuncCompController/addFuncComp', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780366',
  action: ACTION.ACTION_ADD_COMP,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/FuncCompController/updateFuncComp', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780366',
  action: ACTION.ACTION_UPDATE_COMP,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/FuncCompController/deleteComponentByCompIdList', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780366',
  action: ACTION.ACTION_DEL_COMP,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});

// 角色
actionsLogMap.set('/SystemRolesController/selectSystemRolesGridData', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780373',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_FAILURE,
  actionMsg: '',
});
actionsLogMap.set('/SystemRolesController/saveSystemRoles', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780373',
  action: ACTION.ACTION_ADD,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/SystemRolesController/update', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780373',
  action: ACTION.ACTION_UPDATE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/SystemRolesController/removeRoleRelIds', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780373',
  action: ACTION.ACTION_DELETE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/SystemUserRoleController/delUserRolesByIds', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780373',
  action: ACTION.ACTION_DEL_ROLE_USER,
  handResult: RESULT.RESULT_FAILURE,
  actionMsg: '',
});

// 公告管理
actionsLogMap.set('/BulletinController/selectBulletinManageGridData', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780368',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/BulletinController/createBulletin', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780368',
  action: ACTION.ACTION_ADD,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/BulletinController/effectiveBulletin', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780368',
  action: ACTION.ACTION_UPDATE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/BulletinController/removeBulletin', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780368',
  action: ACTION.ACTION_DELETE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/BulletinController/updateBulletin', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '2780368',
  action: ACTION.ACTION_UPDATE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});

// 系统参数配置：
actionsLogMap.set('/DataDictController/selectGridData', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3590502',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/DataDictController/create', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3590502',
  action: ACTION.ACTION_ADD,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/DataDictController/update', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3590502',
  action: ACTION.ACTION_UPDATE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/DataDictController/reload', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3590502',
  action: ACTION.ACTION_REFRESH,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});

// 系统信息配置：
actionsLogMap.set('/SystemInfoController/selectGridData', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3600502',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/SystemInfoController/create', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3600502',
  action: ACTION.ACTION_ADD,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/SystemInfoController/update', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3600502',
  action: ACTION.ACTION_UPDATE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/SystemInfoController/deleteBySystemInfoIdList', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '3600502',
  action: ACTION.ACTION_DELETE,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});

// 门户刷新：
actionsLogMap.set('/DomainDataController/reloadDomainData2Cache', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '276123',
  action: ACTION.ACTION_REFRESH,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/PrivilegeCacheController/loadPrivilege2Cache', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '276123',
  action: ACTION.ACTION_REFRESH,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/PrivilegeCacheController/reloadPrivilege2Cache', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '276123',
  action: ACTION.ACTION_REFRESH,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
actionsLogMap.set('/WorkbenchController/refreshRecentHourCache', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '276123',
  action: ACTION.ACTION_REFRESH,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});

// 密码修改
actionsLogMap.set('/SystemUserController/updateSysUserPwd', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '276123',
  action: ACTION.ACTION_D,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
// 集团客户管理-查询（集团用户标识）
actionsLogMap.set('/GroupEnterpriseController/queryGroupEnterpriseBySubscriberInsId', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '1740207447079322',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
// 集团客户管理-查询（集团服务号码查询）
actionsLogMap.set('/GroupEnterpriseController/queryGroupEnterpriseByAccessNum', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '1740207447079322',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
// 集团客户管理-查询（个人号码查询）
actionsLogMap.set('/GroupEnterpriseController/queryGroupEnterpriseByPersonNum', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '1740207447079322',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
// 集团客户管理-查询（宽带账号）
actionsLogMap.set('/GroupEnterpriseController/queryGroupEnterpriseByWidenetAccessNum', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '1740207447079322',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
// 集团客户管理-查询（集团客户经理编码）
actionsLogMap.set('/GroupEnterpriseController/queryGroupEnterpriseByOperatorCode', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '1740207447079322',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
// 集团客户管理-查询（集团编码）
actionsLogMap.set('/GroupEnterpriseController/queryGroupEnterpriseByGroupId', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '1740207447079322',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
// 集团客户管理-查询（集团账号）
actionsLogMap.set('/GroupEnterpriseController/queryGroupEnterpriseByAcctId', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '1740207447079322',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
// 集团客户管理-查询（集团名称查询）
actionsLogMap.set('/GroupEnterpriseController/queryGroupEnterpriseByGroupName', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '1740207447079322',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
// 集团客户管理-查询（成员服务号码）
actionsLogMap.set('/GroupEnterpriseController/queryGroupEnterpriseByMebAccessNum', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '1740207447079322',
  action: ACTION.ACTION_QUERY,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
// 集团客户管理-导出
actionsLogMap.set('/GroupEnterpriseController/exportGroupEnterpriseExcelFile', {
  actionType: ACTION_TYPE.ACTION_TYPE_FUNC,
  actionModule: '1740207447079322',
  action: ACTION.ACTION_EXPORT,
  handResult: RESULT.RESULT_SUCCESS,
  actionMsg: '',
});
// 错误编码翻译表
const codeMessage = {
  0: '请求超时',
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

function operDiffActionLog(url, status, exceptionObject) {
  let start = 0;
  let end = 0;
  let time = 0;
  if (url.indexOf('?') > -1) {
    // eslint-disable-next-line no-param-reassign
    url = url.substring(0, url.indexOf('?'));
  }
  // eslint-disable-next-line no-plusplus
  for (let i = url.length - 1; i >= 0; i--) {
    if (url.charAt(i) === '.') {
      end = i;
      // eslint-disable-next-line no-continue
      continue;
    }
    if (url.charAt(i) === '/') {
      // eslint-disable-next-line no-plusplus
      time++;
      if (time === 2) {
        start = i;
        break;
      }
    }
  }
  const afterTransferUrl = url.substring(start, end);
  // todo : map的key的太长会影响性能问题
  let action = actionsLogMap.get(afterTransferUrl);
  // 不存在action且不是异常，则不处理
  if (action == null && exceptionObject == null) {
    return;
  }
  if (exceptionObject) {
    // 异常的时候，action可能不存在，需要做判空和赋值处理
    action = action == null ? {} : action;
    action.handResult = RESULT.RESULT_FAILURE;
    action = { ...action, ...exceptionObject };
  } else {
    // 修改状态值
    status
      ? (action.handResult = RESULT.RESULT_SUCCESS)
      : (action.handResult = RESULT.RESULT_FAILURE);
  }
  // eslint-disable-next-line no-undef
  $.ajax({
    type: 'POST',
    url: 'orgauth/SystemActionLogController/collectSystemActionLog.do',
    headers: {
      CsrfToken: sessionStorage.getItem('CsrfToken'),
    },
    data: JSON.stringify(action),
    dataType: 'json',
    contentType: 'application/json',
  });
}

// eslint-disable-next-line no-undef
$(document).ajaxSuccess((event, XMLHttpRequest, ajaxOptions) => {
  const { url } = ajaxOptions;
  const result = XMLHttpRequest.responseJSON;
  // 需要过滤自身
  if (url.indexOf('collectSystemActionLog') > -1) {
    return;
  }
  if (result) {
    operDiffActionLog(url, true);
  } else {
    operDiffActionLog(url, false);
  }
  // console.log(event, XMLHttpRequest, ajaxOptions)
});

// eslint-disable-next-line no-undef
$(document).ajaxError((event, XMLHttpRequest, ajaxOptions) => {
  const { url } = ajaxOptions;
  const params = {
    errorType: codeMessage[XMLHttpRequest.status],
    actionType: ACTION_TYPE.ACTION_TYPE_ERROR,
    errorCode: XMLHttpRequest.status,
    actionMsg: `${url}:${codeMessage[XMLHttpRequest.status]}`,
  };
  // 需要过滤自身
  if (url.indexOf('collectSystemActionLog') > -1) {
    return;
  }
  operDiffActionLog(url, false, params);
  // console.log(event, XMLHttpRequest, ajaxOptions)
});
