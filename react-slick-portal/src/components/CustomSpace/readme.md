# CustomSpace

antd@3.26.0 中缺少的 Space 组件，可自定义间距、对齐方式、分隔符、排列方向等。


## 用法

```jsx
import CustomSpace from 'CustomSpace';

function SomeComponent() {
  return (
    <CustomSpace size={8}>
      <Button>Button 1</Button>
      <Button>Button 2</Button>
      <Button>Button 3</Button>
    </CustomSpace>
  );
}
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| size | 间距大小，单位为像素值或字符串（例如"small", "middle", "large"） | number \| string | 0 |
| align | 对齐方式 | string(start/end/center/baseline) | "start" |
| split | 分隔符，可以为 ReactNode | ReactNode \| null | null |
| wrap | 是否自动换行 | boolean | false |
| direction | 排列方向，可选 "horizontal", "vertical", "inline", "inline-block" | string | "horizontal" |
| style | 自定义样式 | object | - |
| className | 自定义 class | string | - |

### Events

无

## 示例

### 水平排列

```jsx
<CustomSpace>
  <Button>Button 1</Button>
  <Button>Button 2</Button>
  <Button>Button 3</Button>
</CustomSpace>
```

### 垂直排列

```jsx
<CustomSpace direction="vertical">
  <Button>Button 1</Button>
  <Button>Button 2</Button>
  <Button>Button 3</Button>
</CustomSpace>
```

### 自动换行

```jsx
<CustomSpace wrap>
  <Button>Button 1</Button>
  <Button>Button 2</Button>
  <Button>Button 3</Button>
  <Button>Button 4</Button>
  <Button>Button 5</Button>
  <Button>Button 6</Button>
</CustomSpace>
```

### 分隔符

```jsx
<CustomSpace split={<Divider type="vertical" />}>
  <Button>Button 1</Button>
  <Button>Button 2</Button>
  <Button>Button 3</Button>
</CustomSpace>
```

### 对齐方式

```jsx
<CustomSpace align="center">
  <Button>Button 1</Button>
  <Button>Button 2</Button>
  <Button>Button 3</Button>
</CustomSpace>
```
