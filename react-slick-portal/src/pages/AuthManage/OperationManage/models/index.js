export default {
  namespace: 'operationManage',
  state: {
    menuId: '',
    behavior: 'disable',
    isLeaf: false, // 选中的节点是否叶子菜单
    treeData: [], // 帮助手册左侧树数据
    menuTreeData: [], // 关联菜单数据
    menuData: {}, // 左侧选中的菜单数据
    operationDocConfId: '', // 帮助手册目录id
  },

  effects: {},

  reducers: {
    updateData(state, { payload: params }) {
      return {
        ...state,
        ...params,
      };
    },
    saveMenuData(state, { payload: params }) {
      return {
        ...state,
        menuData: params,
      };
    },
    saveMenuID(state, { payload: params }) {
      return {
        ...state,
        menuId: params,
      };
    },
    saveBehavior(state, { payload: params }) {
      return {
        ...state,
        behavior: params,
      };
    },
    saveTreeData(state, { payload: params }) {
      return {
        ...state,
        treeData: params,
      };
    },
    saveMenuTreeData(state, { payload: params }) {
      return {
        ...state,
        menuTreeData: params,
      };
    },
    saveIsLeaf(state, { payload: params }) {
      return {
        ...state,
        isLeaf: params,
      };
    },
  },
};
