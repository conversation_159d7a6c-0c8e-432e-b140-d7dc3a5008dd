.wrap {
  width: 100%;
  overflow-y: auto;

  :global(.ant-modal) {
    top: 50px !important
  }

  :global(.pdf-canvas) {
    text-align: center;
  }

  :global(.pdf-viewer-container) {
    text-align: center;
  }

  @keyframes rotating {
    from {transform: rotate(0)}
    to {transform: rotate(360deg)}
  }

  :global(.pdf-loading) {
    display: inline-block;
    width: 96px;
    height: 96px;
    margin-top: 25%;
    font-size: 0;
    text-align: center;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAMAAADVRocKAAABgFBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVaoAhNsAhN0AhN0AgM8Ahd4Ag90AgtoAhNwAhd0AftMAhNsAhN4AfNEAgdcAhd4Ag9sAftUAgNMAd90AfdUAgNYAedcAdtgAhN0AfdIAgdYAhN4AgtkAgNUAgNUAgNcAg9wAdNEAgNsAZswAg9sAgNUAg9sAgNYAgNkAgNUAgtoAg9sAftQAgNUAgNgAhd4AhN0AfdEAgtkAgNcAhN0AgNcAgNUAg9sAgNcAgNcAfdQAg9wAgNUAgtsAgdUAftYAgNMAfdUAgdYAhN0Ag9sAedsAgNIAgNUAgM8AgNgAYJ8AXJkAec4AgNcAeNIAetMAg9sAgNUAgtoAgNcAhN0AgNMAg9sAgNcAgNwAg9oAftUAgNUAhd0Ag9sAgdcAgdcAfdIAfNgAhNwAhNkAgNUAf9UAftcAf9UAftQARHEAeswAe8wAhd4Ad+ySAAAAf3RSTlMAEhEOAQUCBhADCQ8ECwcMDQoIA9X57RD+4azs+kvd/SGS/NRPUg83XhMN4D99968eNqHSCw4FzQbWiRRcss5NgY/07j2wjvNyDMtmhi/RaMRhXTorcfHHFShIIBowMnyTES7IYquF+0zQkRbDZyry3mdzOSfrGxKXR6NZInk8JmKNMgAAA3lJREFUeF612udbE1kUBvD3zNzpaYQkJBC6ugo2EFQURcVCsXfXtr333t9/fXMTd8NjRFLm/L7myZR75rZzLroUBXG24CbPJ8qV+dlHo9cPLswhNd6Q60jLS/6venN63zIGZnIFR9peTHCnmdu3DmAQXtaRJsfNx0Em8g1OH5q6cfjOmWOlOpuOjq4Z9ClwX1089tBpafzqqUlaK3eH0YcgEcvNGezq8YPztCa2e75FxpUGJ/axh/sXH7LhxGX0wjTbPiwadGHkwjrJ6uJZdM0LbVyL6NbmkUskK/vRpSFpyPvowfErbJg+iS74tvUTDz0ar5HcmsKeotA+vkHPRs6RHHsHe8g4Ik4Ofdkok9fexVsFjkgYoU/3xsj66l7XT3z07UmNrK6+vX1cgwE8K5H1XVspGvj6wPsl8toukfZDkcRgQM9q5NgU3sQVCX0M7MkYuXXyzf3XiZCCe2XyPXTwRCSHVGyQ7BiXTCiSR0rOkZXXx9ZsM8ApGamRi509wENqxsnq5de/oDxSdIU8MYy2QMTxkaLjl8httCUiRaTqCDkxvPMFQqRrc528uzMCRaTsArli2n3MMUjZyENyrd0HYqTuIjmKJuOI+EjdffLoAVg5ERcKzpO3YBVEclDwgLwNy7EhVvB4kjPLrW/IhYpT5L7WRBNDxVVyutXLPKgYJ2+2QgAdS3VW5xCphQAokQsIRPJQcow8iFgtxsAZ8jqyIgGU3CFHURDJQMlh8hFckQhKbpCzSER8KJki5xGKGCg5RFbgiEDLabKsfgP1JlIN8lOypvqZfkCWtDvah6pDxUfkx9qD3Sfqw7XqhDNLLmhOmZufcnJOc9L/jPxce9nyhfrCS3Pp+OUMZ5Y1F78b5Ffay/evNTcg39gNiPYWSnUT+C25pryNLRnFjfhShVxVTiV8N6ycDPlePZ2jnZDSTKnNk4vKScH1s4ppzR9I7ldMzP5YJ6cVU8s//WxTy3rJ8V9scvypXnr/1xWb3tcrUJjfyPLveiUWP/mDk3/qFYmiUP76+x+9MlfO/jejVqgzebFvr1Zq9BIRcX2tYqmfl4YhrXIvis3ys6dUsDbF5vNkjU7J3Y8daXAzKocGTM4VKwkUjj3Ai11HLDdI8+AGjB9lgjjv/vdL1kvt6Eknp5AzaR2e6eC4Qx5S0j7+EzpOmLiFbBxE6M6/Q+aqJubtPIkAAAAASUVORK5CYII=);
    background-repeat: no-repeat;
    -webkit-animation: rotating 2s linear infinite;
    animation: rotating 2s linear infinite;
  }

}

.modalWrap {
  :global(.document-container) {
    background-image: url('../../img/watermark.png') !important;
    background-repeat: repeat !important;
  }

  :global(.spreadsheet-viewer::after) {
    position: absolute;
    top: 10%;
    display: block;
    width: 100%;
    height: 100%;
    background-image: url('../../img/watermark.png') !important;
    background-repeat: repeat !important;
    content: '';
  }
}

.contentWrap {
  display: inline-block;
  width: 100%;
  height: 100%;
  min-height: 200px;
  text-align: center;
  background-color: white;
}

.excel-wrap {
  height: 100%;
  background-color: white;

  .spin {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  .watermark {
    background-image: url('../../img/watermark.png');
    background-repeat: repeat;
    background-size: 300px;
  }

  .grid-map {
    display: inline-block;

    .sheet {
      display: inline-block;
      .row {
        white-space: nowrap;
      }
    }

    .excelTable {
      table-layout: fixed;
      th, td{
        padding: 0;
      }

      th {
        background-color: #ccc;
      }

      .headCol {
        width: 35px;
        background-color: #ccc;
      }

      .grid-block {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 45px;
        padding: 3px;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
      }
    }

    :global{
      .react-resizable-handle {
        margin: 0;
        padding: 0;
        background-image: none;
      }

      .react-resizable-handle-e {
        position: static;
        float: right;
        width: 5px;
        height: 100%;
        margin-right: -3px;
        transform: none;
      }

      .react-resizable-handle-s {
        left: 0;
        width: 100%;
        height: 5px;
        margin-bottom: -3px;
        transform: none;
      }
    }
  }
}
