import React, { useEffect } from 'react';
import dynamic from 'umi/dynamic';
import { connect } from 'dva';
import StepWizard from '@/components/StepWizard';
import PageLoading from '@/components/PageLoading';

const IndexLibrary = props => {
  const { type } = props.match.params;

  const List = dynamic({
    loader: () => import('../components/List'),
    loading: PageLoading,
  });

  const Add = dynamic({
    loader: () => import('../components/Add'),
    loading: PageLoading,
  });

  useEffect(() => {
    props.dispatch({
      type: 'librarys/initData',
    });
  }, []);

  return (
    <StepWizard isLazyMount initialStep={1}>
      <List location={props.location} type={type} destroy />
      <Add type={type} destroy />
    </StepWizard>
  );
};

export default connect()(IndexLibrary);
