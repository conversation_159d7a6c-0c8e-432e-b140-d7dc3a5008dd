.wrap {
  width: 100%;

  .title {
    width: 100%;
    margin: a auto;

    :global(.ant-tabs-nav-wrap) {
      margin-left: -4px;
      padding-left: 4px;
      overflow: auto;
      color: #333;
      font-weight: 500;
      font-size: 18px;
      background-color: #fff;
    }

    :global(.ant-tabs-nav) {
      display: -ms-flexbox;
      display: flex;
      -ms-flex-direction: row;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      width: 1200px;
      height: 70px;
      margin: 0 auto;
      -ms-flex-pack: start;
      -ms-flex-align: center;

      :global(.ant-tabs-tab) {
        margin: 0 50px 0 0;
      }
    }

    :global(.ant-tabs-bar) {
      margin: 0;
    }
  }

  .back {
    position: fixed;
    right: 50px;
    bottom: 200px;
    cursor: pointer;
  }

  :global(.ant-tabs-nav-container) {
    box-shadow: 0 11px 10px 0 rgba(0, 0, 0, 0.06);
  }
}
