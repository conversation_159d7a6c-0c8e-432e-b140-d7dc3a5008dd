// 系统推荐数据
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Card, Form, Row, Col, Select, Divider, Input, Button, DatePicker, Model } from 'antd';
import { useAntdTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import styles from './systemPushData.less';

const Index = props => {
  const { temp } = props ?? {};

  return (
    <div className={styles.card}>
      <Card
        title="系统推送数据"
        className="cute"
      />
    </div>
  );
};

export default Index;
