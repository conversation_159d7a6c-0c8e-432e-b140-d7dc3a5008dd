import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Card, Col, DatePicker, Form, message, Row, Select } from 'antd';
import { connect } from 'dva';
import moment from 'moment/moment';
import styles from '@/pages/GroupCust/GroupCustView/components/AccountInfo/components/CustManager/index.less';
import { disableEndDate, disableStartDate } from '@/utils/validator';
import {
  queryAccountInfo,
  queryContactInfo,
  sendBillingSms,
} from '@/pages/GroupCust/GroupCustView/components/AccountInfo/components/CustManager/service';

const { MonthPicker } = DatePicker;
const Index = ({ form, ...otherProps }) => {
  const { currentCust } = otherProps;
  const [sendLoading, setSendLoading] = useState(false);
  const [accountInformation, setAccountInformation] = useState([]);
  const [accountLoading, setAccountLoading] = useState(false);
  const [contactInformation, setContactInformation] = useState([]);
  const [contactLoading, setContactLoading] = useState(false);
  const { getFieldDecorator } = form;

  useEffect(() => {
    const abortController = new AbortController(); // 1. 创建中止控制器
    const { signal } = abortController;

    const fetchAccountInfo = async () => {
      setAccountLoading(true);
      try {
        const res = await queryAccountInfo({ groupId: currentCust.GROUP_ID }, { signal }); // 2. 传递中止信号
        const { resultCode, resultObject, resultMsg } = res;

        if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
          setAccountInformation(resultObject.rspParam.busiInfo.outData);
        } else {
          message.error(resultMsg || '获取账户信息失败');
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          // 4. 忽略中止错误
          message.error('请求失败，请稍后重试');
          setAccountInformation([]); // 5. 错误时保留空状态
        }
      } finally {
        setAccountLoading(false); // 6. 统一清理加载状态
      }
    };

    fetchAccountInfo();

    return () => abortController.abort(); // 7. 清理函数中止请求
  }, [currentCust.GROUP_ID]); // ✅ 添加依赖项

  useEffect(() => {
    const abortController = new AbortController(); // 创建中止控制器
    const { signal } = abortController;

    const fetchContactInfo = async () => {
      setContactLoading(true);
      try {
        const res = await queryContactInfo({ groupId: currentCust.GROUP_ID }, { signal }); // 传递中止信号
        const { resultCode, resultObject, resultMsg } = res;

        if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData?.contactInfos) {
          setContactInformation(resultObject.rspParam.busiInfo.outData.contactInfos);
        } else {
          message.error(resultMsg || '获取联系人信息失败');
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          // 忽略中止错误
          // ✅ 更安全的错误处理
          message.error('请求失败，请稍后重试');
          setContactInformation([]); // 保持空状态而非测试数据
        }
      } finally {
        setContactLoading(false);
      }
    };

    fetchContactInfo();

    // ✅ 清理函数：组件卸载时中止请求
    return () => abortController.abort();
  }, [currentCust.GROUP_ID]); // ✅ 添加 groupId 依赖

  /**
   * 发送消息
   */
  const sendMessage = () => {
    form.validateFields((err, values) => {
      const abortController = new AbortController(); // 1. 创建中止控制器
      const { signal } = abortController;
      if (!err) {
        const fetchBillingSms = async () => {
          setSendLoading(true);
          try {
            const res = await sendBillingSms(
              {
                data: {
                  groupId: currentCust.GROUP_ID,
                  groupName: currentCust.GROUP_NAME,
                  sendNumber: values.contactInformation,
                  acctId: values.accountInformation,
                  startMonth: moment(values.startDate).format('yyyy-MM'),
                  endMonth: moment(values.endDate).format('yyyy-MM'),
                },
              },
              { signal }
            ); // 传递中止信号
            const { resultCode, resultMsg } = res;
            if (resultCode === 'TRUE') {
              message.info('短信发送成功！');
            } else {
              message.error(resultMsg || '获取联系人信息失败');
            }
          } catch (error) {
            if (error.name !== 'AbortError') {
              // 忽略中止错误
              // ✅ 更安全的错误处理
              message.error('请求失败，请稍后重试');
            }
          } finally {
            setSendLoading(false);
          }
        };
        setSendLoading(false);
        fetchBillingSms();
      }
      // ✅ 清理函数：组件卸载时中止请求
      return () => abortController.abort();
    });
  };

  const reset = () => {
    form.resetFields();
  };

  return (
    <div className={styles.container}>
      <Card title="账期选择" className="cute" bordered={false}>
        <Form>
          <Row type="flex" justify="start">
            <Col span={6} style={{ marginRight: '20px' }}>
              <Form.Item label="开始时间">
                {getFieldDecorator('startDate', {
                  rules: [{ required: true, message: '请选择开始时间!' }],
                })(
                  <MonthPicker format="yyyy-MM" allowClear disabledDate={disableStartDate(form.getFieldValue('endDate'))} style={{ width: '100%' }} />
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="结束时间">
                {getFieldDecorator('endDate', {
                  rules: [{ required: true, message: '请选择结束时间!' }],
                })(
                  <MonthPicker format="yyyy-MM" allowClear disabledDate={disableEndDate(form.getFieldValue('startDate'))} style={{ width: '100%' }} />
                )}
              </Form.Item>
            </Col>
            <Col span={6} />
          </Row>
          <Row type="flex" justify="start">
            <Col span={6} style={{ marginRight: '20px' }}>
              <Form.Item label="联系人信息">
                {getFieldDecorator('contactInformation', {
                  rules: [{ required: true, message: '请选择联系人信息!' }],
                  initialValue: '',
                })(
                  <Select placeholder="请选择" allowClear loading={contactLoading}>
                    {contactInformation?.map(item => (
                      <Select.Option value={item.ACCESS_NUM} key={item.ACCESS_NUM}>
                        {item.OUT_TEXT}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="账户信息">
                {getFieldDecorator('accountInformation', {
                  rules: [{ required: true, message: '请选择账户信息!' }],
                  initialValue: '',
                })(
                  <Select placeholder="请选择" allowClear loading={accountLoading}>
                    {accountInformation.map(item => (
                      <Select.Option value={item.ACCT_ID} key={item.ACCT_ID}>
                        {item.OUT_TEXT}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={6} />
          </Row>
          <Row type="flex" justify="start">
            <Col span={6} className="text-left">
              <Button disabled={sendLoading} loading={sendLoading} type="default" className="inline-block margin-left" onClick={sendMessage}>
                发送
              </Button>
              <Button className="margin-left" onClick={reset}>
                重置
              </Button>
            </Col>
            <Col span={6} />
            <Col span={6} />
          </Row>
        </Form>
      </Card>
    </div>
  );
};

export default connect(({ setting, groupCustView }) => ({
  size: setting.size,
  currentCust: groupCustView.currentCust,
}))(Form.create()(Index));
