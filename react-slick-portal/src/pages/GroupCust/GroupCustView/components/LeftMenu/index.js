import React, { useState } from 'react';
import { connect } from 'dva';
import { Menu, Modal, Input, List } from 'antd';
import styles from './index.less';
import { leftMenu } from '@/pages/GroupCust/GroupCustView/const';
import { Frame0Icon, FrameIcon, ICON_COLORS, OpenSvgIcon } from './menuIcons';
// import CustSearch from './components/CustSearch';

const LeftMenu = props => {
  const { dispatch, currentLeftMenu, loading, searchModalVisible, setSearchModalVisible } = props;
  const [searchText, setSearchText] = useState('');
  const [searchResults, setSearchResults] = useState([]);

  // 扁平化菜单数据，用于搜索
  const flattenMenuItems = (items, parentKey = '') => {
    let result = [];
    items.forEach(item => {
      const currentKey = parentKey ? `${parentKey}/${item.key}` : item.key;
      result.push({
        name: item.name,
        key: currentKey,
        component: item.component,
      });

      if (item.children) {
        result = result.concat(flattenMenuItems(item.children, currentKey));
      }
    });
    return result;
  };

  const allMenuItems = flattenMenuItems(leftMenu);

  // 处理搜索功能
  const handleSearch = value => {
    setSearchText(value);
    if (!value) {
      setSearchResults([]);
      return;
    }

    const results = allMenuItems.filter(item =>
      item.name.toLowerCase().includes(value.toLowerCase()) && item.component
    );
    setSearchResults(results);
  };

  // 处理菜单项选择
  const handleMenuClick = ({ key }) => {
    if (loading) {
      return;
    }
    dispatch({
      type: 'groupCustView/selectLeftMenu',
      payload: key,
    });
  };

  // 处理搜索结果选择
  const handleSearchResultClick = key => {
    setSearchModalVisible(false);
    setSearchText('');
    setSearchResults([]);

    dispatch({
      type: 'groupCustView/selectLeftMenu',
      payload: key,
    });
  };

  // 渲染菜单项
  const renderMenuItem = ({ menu, level, parentKey = '', idx }) => {
    const currentKey = parentKey ? `${parentKey}/${menu.key}` : menu.key;
    const currentLevel = level || 1;
    if (menu.children) {
      return (
        <Menu.SubMenu
          key={currentKey}
          title={(
            <span>
              { level === 1 && <FrameIcon index={idx} style={{ marginRight: 8 }} />}
              <span>{menu.name}</span>
            </span>
          )}
        >
          {menu.children.map(child => renderMenuItem({ menu: child, level: currentLevel, parentKey: currentKey }))}
        </Menu.SubMenu>
      );
    }

    // 叶子菜单项
    return (
      <Menu.Item key={currentKey}>
        <span>{menu.name}</span>
      </Menu.Item>
    );
  };

  // 获取当前选中的菜单路径数组
  const getSelectedKeys = (menuItems, path) => {
    if (!path) return [];

    const keys = path.split('/');
    const result = [];
    let currentPath = '';

    // 构建完整的路径数组
    keys.forEach(key => {
      currentPath = currentPath ? `${currentPath}/${key}` : key;
      result.push(currentPath);
    });

    return result;
  };

  const selectedKeys = getSelectedKeys(leftMenu, currentLeftMenu);

  return (
    <div className={styles.groupInfoContainer}>
      {/* 查询条件 */}
      {/* <div className={styles.searchWrapper}>
        <CustSearch />
      </div> */}
      {/* 菜单 */}
      <Menu
        mode="inline"
        selectedKeys={selectedKeys}
        onClick={handleMenuClick}
        className={styles.antMenu}
      >
        {leftMenu.map((item, idx) => renderMenuItem({
          menu: item, level: 1, index: idx,
        }))}
      </Menu>

      {/* 搜索弹窗 */}
      <Modal
        title="菜单搜索 (Ctrl+K)"
        visible={searchModalVisible}
        onCancel={() => setSearchModalVisible(false)}
        footer={null}
        width={500}
        destroyOnClose
        maskClosable
        centered
      >
        <Input.Search
          placeholder="输入关键字搜索菜单..."
          value={searchText}
          onChange={e => handleSearch(e.target.value)}
          onSearch={handleSearch}
          autoFocus
          className={styles.searchInput}
        />
        <List
          className={styles.searchResultList}
          dataSource={searchResults}
          renderItem={item => (
            <List.Item
              onClick={() => handleSearchResultClick(item.key)}
              className={styles.searchResultItem}
            >
              <div>{item.name}</div>
            </List.Item>
          )}
          locale={{ emptyText: searchText ? '没有找到匹配的菜单项' : '输入关键字开始搜索' }}
        />
      </Modal>
    </div>
  );
};

export default connect(({ groupCustView, loading }) => ({
  loading: loading.effects['groupCustView/getCustomerInfo'],
  ...groupCustView,
}))(LeftMenu);
