import React, { useState, useEffect } from 'react';
// import { useBoolean, useClickAway } from '@umijs/hooks';
import { Empty, Form, Alert, Divider, Spin, Card, Menu } from 'antd';
import { connect } from 'dva';
import request from '@/utils/request';
import styles from './index.less';
import { getItem, openMenu } from '@/utils/utils';

function ToDoManage({ dispatch, all }) {
  const [todoList, settodoList] = useState([]);
  const [sortNum, setsortNum] = useState(0);
  const [spinning, setspinning] = useState(true);
  useEffect(async () => {
    const { sessionId } = getItem('user');
    const todoListTag = [];
    const result = await request('portal/UnifiedTodoCoreController/qryTask.do', {
      method: 'get',
      data: { tokenId: sessionId },
    });

    if (result) {
      // console.log(res);
      // eslint-disable-next-line array-callback-return
      result.resultObject.taskInfos.map(item1 => {
        // eslint-disable-next-line array-callback-return
        item1.subTaskInfo.map(item2 => {
          if (item2.todo > 0) {
            todoListTag.push(item2);
          }
        });
      });
      // console.log(todoListTag);
      // console.log(todoListTag.length);

      settodoList(todoListTag);
      setsortNum(todoListTag.length);
      setspinning(false);
    }
  }, []);
  const messageContent = (
    <>
      <span>
        当前共有<span className={styles.sortnum}>{sortNum}</span>类待办事项
      </span>
    </>
  );

  return (
    <>
      <Spin spinning={spinning}>
        {/* <div style={{marginLeft:5,backgroundColor:'white'}}> */}
        {/* <div className={styles.itemstitle}>待办事项</div> */}
        <Card title={<span className={styles.itemstitle}>待办事项</span>}>
          {sortNum > 0 ? (
            <>
              <div className={styles.warning}>
                <Alert message={messageContent} type="warning" showIcon />
              </div>
              <Menu mode="inline">
                {todoList.map(item => (
                  <Menu.Item key={item.menuCode}>
                    <div
                      // data-reg-id={`${currentType?.name}_${item.menuCode}`}
                      onClick={() => {
                        const menu2 = all.find(item2 => item2.menuCode === item.menuCode);
                        menu2.menuOpenMode = '4';
                        openMenu(menu2, all, dispatch);
                      }}
                    >
                      【待处理{item.taskTypeName}】有待您处理的{item.taskTypeName}
                      {item.todo}条
                    </div>
                    <Divider />
                  </Menu.Item>
                ))}
              </Menu>
            </>
          ) : (
            <Empty />
          )}
        </Card>
        {/* </div> */}
      </Spin>
    </>
  );
}

export default connect(({ menu, setting }) => ({
  size: setting.size,
  all: menu.all,
}))(Form.create()(ToDoManage));
