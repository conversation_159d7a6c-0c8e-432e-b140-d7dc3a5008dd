import React, { useEffect, useReducer } from 'react';
import { Radio } from 'antd';
import find from 'lodash/find';
import get from 'lodash/get';
import classNames from 'classnames';
import MyForm1 from './MyForm1';
import MyForm2 from './MyForm2';
import styles from '../styles.less';

const mapping = [
  {
    key: 'bomc',
    label: '业务报障',
    content: MyForm1,
  },
  {
    key: 'eoms',
    label: '网络报障',
    content: MyForm2,
  },
];

const initialState = {
  tabKey: 'bomc', // 默认显示B域
  multiple: true,
};

function init(current) {
  const { reportTo = 'bomc' } = current;

  // reportTo预期值：`bomc`|`eoms` | `all`，分别表示B域，O域和全部
  return {
    ...initialState,
    tabKey: reportTo === 'all' ? 'bomc' : reportTo,
    multiple: reportTo === 'all',
  };
}

function reducer(state, action) {
  const { type, payload } = action;
  if (type === 'setKey') {
    return {
      ...state,
      tabKey: payload,
    };
  }

  if (type === 'syncProps') {
    const { multiple, tabKey } = payload;
    return {
      ...state,
      tabKey,
      multiple,
    };
  }

  throw Error();
}

function FormLayout({ current, onBack, onSuccess }) {
  const [state, dispatch] = useReducer(reducer, current, init);

  useEffect(() => {
    const { reportTo } = current;
    dispatch({
      type: 'syncProps',
      payload: {
        tabKey: reportTo === 'all' ? 'bomc' : reportTo,
        multiple: reportTo === 'all',
      },
    });
  }, [current]);

  const Component = get(find(mapping, { key: state.tabKey }), 'content') || MyForm1;

  return (
    <>
      {state.multiple === true ? (
        <div className={classNames('text-center', 'margin-bottom', styles.tabs)}>
          <Radio.Group
            value={state.tabKey}
            buttonStyle="solid"
            size="large"
            onChange={e => dispatch({ type: 'setKey', payload: e.target.value })}
          >
            {mapping.map(k => (
              <Radio.Button key={k.key} value={k.key}>
                {k.label}
              </Radio.Button>
            ))}
          </Radio.Group>
        </div>
      ) : null}

      <div className={styles.body}>
        <div className={classNames(styles.myForm)}>
          <Component current={current} onBack={onBack} onSuccess={onSuccess} />
        </div>
      </div>
    </>
  );
}

export default FormLayout;
