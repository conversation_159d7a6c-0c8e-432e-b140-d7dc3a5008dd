import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, DatePicker, Radio } from 'antd';
import moment from 'moment';
import PropTypes from 'prop-types';

const { RangePicker } = DatePicker;

const HolidayForm = props => {
  const { form, visible, onCancel, onSubmit, confirmLoading, initialValues = {} } = props;
  const { getFieldDecorator } = form;
  const [isSingleDay, setIsSingleDay] = useState(true);

  // 检查当前选择的日期范围是否为单天
  useEffect(() => {
    if (visible) {
      // 初始化时检查日期范围
      const initialStartDate = initialValues.startDate;
      const initialEndDate = initialValues.endDate;

      if (initialStartDate && initialEndDate) {
        setIsSingleDay(initialStartDate === initialEndDate);
      } else if (initialValues.date) {
        setIsSingleDay(true);
      }
    }
  }, [visible, initialValues]);

  const handleSubmit = () => {
    form.validateFields((err, values) => {
      if (!err) {
        // 如果不是单天，自动设置为全天
        if (!isSingleDay) {
          values.timePeriod = 'fullDay';
        }
        onSubmit(values);
      }
    });
  };

  // 处理日期范围变化
  const handleDateRangeChange = dates => {
    if (dates && dates.length === 2) {
      const startDate = dates[0].format('YYYY-MM-DD');
      const endDate = dates[1].format('YYYY-MM-DD');
      const isSameDay = startDate === endDate;
      setIsSingleDay(isSameDay);

      // 如果不是单天，自动设置为全天
      if (!isSameDay) {
        form.setFieldsValue({ timePeriod: 'fullDay' });
      }
    }
  };

  // 禁用当前日期之前的日期
  const disabledDate = current => current && current < moment().startOf('day');

  return (
    <Modal
      title={initialValues.id ? '编辑节假日' : '新增节假日'}
      visible={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      confirmLoading={confirmLoading}
    >
      <Form labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
        {initialValues.id && (
          <Form.Item style={{ display: 'none' }}>
            {getFieldDecorator('id', {
              initialValue: initialValues.id,
            })(<Input />)}
          </Form.Item>
        )}

        <Form.Item label="日期范围" required>
          {getFieldDecorator('dateRange', {
            // eslint-disable-next-line no-nested-ternary
            initialValue: initialValues.startDate && initialValues.endDate
              ? [moment(initialValues.startDate), moment(initialValues.endDate)]
              : initialValues.date
                ? [moment(initialValues.date), moment(initialValues.date)]
                : null,
            rules: [{ required: true, message: '请选择日期范围' }],
          })(
            <RangePicker
              style={{ width: '100%' }}
              disabledDate={disabledDate}
              placeholder={['开始日期', '结束日期']}
              onChange={handleDateRangeChange}
            />
          )}
        </Form.Item>

        {isSingleDay && (
          <Form.Item label="时间段" required>
            {getFieldDecorator('timePeriod', {
              initialValue: initialValues.timePeriod || 'fullDay',
              rules: [{ required: true, message: '请选择时间段' }],
            })(
              <Radio.Group>
                <Radio value="fullDay">全天</Radio>
                <Radio value="morning">上午</Radio>
                <Radio value="afternoon">下午</Radio>
              </Radio.Group>
            )}
          </Form.Item>
        )}

        <Form.Item label="备注">
          {getFieldDecorator('remark', {
            initialValue: initialValues.remark,
          })(<Input.TextArea rows={4} placeholder="请输入备注信息" />)}
        </Form.Item>
      </Form>
    </Modal>
  );
};

HolidayForm.propTypes = {
  form: PropTypes.object.isRequired,
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  confirmLoading: PropTypes.bool,
  initialValues: PropTypes.object,
};

HolidayForm.defaultProps = {
  confirmLoading: false,
  initialValues: {},
};

export default Form.create()(HolidayForm);
