.btn {
  /*position: fixed;
  right: 0;
  bottom: 0;
  z-index: 9;*/
  width: 100%;
  padding: 8px;
  line-height: 100%;
  background: rgb(255, 255, 255);
  border-top: 1px solid rgb(232, 232, 232);
  box-shadow: rgba(0, 0, 0, 0.03) 0 -1px 2px;

  .right {
    float: right;
  }
}

.content {
  position: relative;
  padding: 16px 0;
}

// 响应式表单容器 - 调整间距
.warp {
  padding: 4px 0;

  :global {
    .ant-form-item {
      margin-bottom: 16px !important; // 适中的表单项间距
    }

    .ant-form-item-control {
      line-height: 24px !important;
    }

    .ant-form-item-label {
      padding-bottom: 4px !important; // 减少标签底部间距

      > label {
        font-weight: 500;
        color: rgba(0, 0, 0, 0.75);
      }
    }

    // 响应式行布局 - 适中的间距
    .ant-row {
      margin-left: -8px !important;
      margin-right: -8px !important;

      // 超大屏幕：一行两个
      @media (min-width: 1600px) {
        .ant-col-8 {
          width: 50%;
          padding: 0 8px;
          margin-bottom: 12px;
        }
      }

      // 大屏幕：一行两个
      @media (min-width: 1200px) and (max-width: 1599px) {
        .ant-col-8 {
          width: 50%;
          padding: 0 8px;
          margin-bottom: 12px;
        }
      }

      // 中等屏幕：一行一个
      @media (min-width: 768px) and (max-width: 1199px) {
        .ant-col-8 {
          width: 100%;
          padding: 0 8px;
          margin-bottom: 12px;
        }
      }

      // 小屏幕：一行一个
      @media (max-width: 767px) {
        .ant-col-8 {
          width: 100%;
          padding: 0 6px;
          margin-bottom: 12px;
        }
      }

      // 文本域列的响应式处理
      .ant-col-12 {
        padding: 0 8px;
        margin-bottom: 12px;

        @media (max-width: 768px) {
          width: 100%;
          padding: 0 6px;
        }
      }
    }
  }

  .textAreaFormItem {
    :global {
      .ant-form-item-control {
        height: auto !important;
        line-height: 100% !important;

        .ant-form-item-children {
          height: auto !important;
          line-height: 100% !important;
        }

        textarea {
          height: auto;
          min-height: 80px;
          border-radius: 6px;
          transition: all 0.3s;

          &:hover {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }

          &:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }
    }
  }
}

.form-drawer-bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 4px 4px;
}

// 响应式两列布局 - 适中的设计
.twoColumnLayout {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 24px;
  margin: 0 -12px;
  padding: 12px 0;

  // 超大屏幕：并排显示
  @media (min-width: 1600px) {
    gap: 28px;
    margin: 0 -14px;

    .columnLeft,
    .columnRight {
      flex: 1;
      padding: 14px 14px;
      min-width: 0;
    }
  }

  // 大屏幕：并排显示
  @media (min-width: 1200px) and (max-width: 1599px) {
    gap: 24px;
    margin: 0 -12px;

    .columnLeft,
    .columnRight {
      flex: 1;
      padding: 0 12px;
      min-width: 0;
    }
  }

  // 中等屏幕：垂直堆叠，避免拥挤
  @media (min-width: 768px) and (max-width: 1199px) {
    flex-direction: column;
    gap: 20px;
    margin: 0;

    .columnLeft,
    .columnRight {
      flex: none;
      padding: 0;
      width: 100%;
    }
  }

  // 小屏幕：垂直堆叠
  @media (max-width: 767px) {
    flex-direction: column;
    gap: 16px;
    margin: 0;

    .columnLeft,
    .columnRight {
      flex: none;
      padding: 0;
      width: 100%;
    }
  }
}

// 美化的表单卡片样式
.formCard {
  margin-bottom: 24px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  :global {
    .ant-card-head {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-bottom: 1px solid #e2e8f0;
      padding: 0 24px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 24px;
        right: 24px;
        height: 2px;
        background: linear-gradient(90deg, #0085D0, #40a9ff);
        border-radius: 1px;
      }
    }

    .ant-card-head-title {
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      padding: 20px 0;
      font-size: 16px;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 4px;
        height: 16px;
        background: linear-gradient(135deg, #0085D0, #40a9ff);
        border-radius: 2px;
        margin-right: 12px;
      }
    }

    .ant-card-extra {
      display: flex;
      gap: 12px;
      padding: 20px 0;
      flex-wrap: wrap;

      .ant-btn {
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s;

        &.ant-btn-primary {
          background: linear-gradient(135deg, #0085D0, #40a9ff);
          border: none;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);

          &:hover {
            background: linear-gradient(135deg, #40a9ff, #0085D0);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
            transform: translateY(-1px);
          }
        }
      }
    }

    .ant-card-body {
      padding: 40px 32px;
      background: #ffffff;

      // 响应式内边距
      @media (max-width: 768px) {
        padding: 24px 16px;
      }
    }

  }
}

// 美化的复制按钮
.copyButton {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0085D0, #40a9ff);
  color: white;
  margin: 120px auto;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;

  // 添加脉冲动画效果
  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    background: linear-gradient(135deg, #0085D0, #40a9ff);
    opacity: 0.3;
    animation: pulse 2s infinite;
  }

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 24px rgba(24, 144, 255, 0.6);
    background: linear-gradient(135deg, #40a9ff, #0085D0);

    &::before {
      animation-duration: 1s;
    }
  }

  &:active {
    transform: scale(0.95);
  }

  .copyIcon {
    font-size: 24px;
    z-index: 1;
  }

  // 响应式调整
  @media (max-width: 1199px) {
    margin: 20px auto;
    position: relative;
    align-self: center;
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

