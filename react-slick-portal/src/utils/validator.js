import moment from 'moment';
import {
  isBlank,
  isBlankSpace,
  isLeapYear,
  isNumberCode,
  isNumberLetter,
  isRepeatCode,
  isSerialCode,
  isSpecialCharacter,
  isUpCharCode,
  isUpperCaseAndNumber,
} from '@/utils/utils';

/**
 * 禁用开始时间的选择条件
 * @param endDate
 * @param accuracy 精度 year month date hour minute second 若accuracy = 'day' → today 为今天的 23:59:59。
 * @returns {(function(*): (boolean))|*}
 */
export const disableStartDate = (endDate, accuracy = 'day') => current => {
  const currentMoment = moment(current);

  // 无效日期则禁用
  if (!currentMoment.isValid()) {
    return true;
  }

  // 如果存在有效的结束日期，且当前日期在结束日期之后则禁用
  if (endDate) {
    const endMoment = moment(endDate).endOf(accuracy);
    if (endMoment.isValid() && currentMoment.isAfter(endMoment, accuracy)) {
      return true;
    }
  }

  return false;
};

/**
 * 禁用结束时间的选择条件
 * @param startDate
 * @param accuracy 精度 year month date hour minute second 若accuracy = 'day' → today 为今天的 23:59:59。
 * @returns {(function(*): (boolean))|*}
 */
export const disableEndDate = (startDate, accuracy = 'day') => current => {
  const currentMoment = moment(current);

  // 无效日期则禁用
  if (!currentMoment.isValid()) {
    return true;
  }

  // 如果存在有效的开始日期，且当前日期在开始日期之前则禁用
  if (startDate) {
    const startMoment = moment(startDate);
    if (startMoment.isValid() && currentMoment.isBefore(startMoment, accuracy)) {
      return true;
    }
  }

  return false;
};

// 证件号码校验
export const validateIdenNbr = idenType => (rule, idenNr = '', callback) => {
  // 非空检验不再此处处理
  if (isBlank(idenNr)) {
    callback('证件号码不能为空!');
  }
  // 不能包含空格
  const blankReg = /\s/;
  if (blankReg.test(idenNr)) {
    callback('证件号码不能包含空格！');
  }
  // 身份证
  if (idenType === '100001' || idenType === '100002') {
    const factorArr = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1];
    const varArray = [];
    let lngProduct = 0;
    let intCheckDigit;
    const idNumber = idenNr;
    if (idenNr.length !== 18) {
      callback('身份证号码必须为18位！');
    }
    for (let i = 0; i < idenNr.length; i += 1) {
      varArray[i] = idNumber.charAt(i);
      if ((varArray[i] < '0' || varArray[i] > '9') && i !== 17) {
        callback('错误的身份证号码!');
      }
      if (i < 17) {
        varArray[i] *= factorArr[i];
      }
    }
    if (idenNr.length === 18) {
      const birthYear = parseInt(idNumber.substr(6, 4), 10);
      const leapYearRegex = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}[0-9Xx]$/;
      const commonYearRegex = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|1[0-9]|2[0-8]))[0-9]{3}[0-9Xx]$/;
      const birthDateRegex = isLeapYear(birthYear) ? leapYearRegex : commonYearRegex;

      if (!birthDateRegex.test(idNumber)) {
        callback('身份证号码出生日期超出范围或含有非法字符！');
        return;
      }

      for (let i = 0; i < 17; i += 1) {
        lngProduct += varArray[i];
      }
      intCheckDigit = 12 - (lngProduct % 11);
      // eslint-disable-next-line default-case
      switch (intCheckDigit) {
        case 10:
          intCheckDigit = 'X';
          break;
        case 11:
          intCheckDigit = 0;
          break;
        case 12:
          intCheckDigit = 1;
          break;
      }
      if (`${varArray[17].toUpperCase()}` !== `${intCheckDigit}`) {
        callback('身份证号码校验错误！');
        return;
      }
      // 判断用户身份证号码出生日期是否小于等于当前日期
      const idcardDateNum = parseInt(idNumber.substr(6, 8), 10);
      const theDate = new Date();
      const year = theDate.getFullYear();
      const month = theDate.getMonth() + 1;
      const day = theDate.getDate();
      const theDateNum = parseInt(year, 10) * 10000 + parseInt(month, 10) * 100 + day;

      if (idcardDateNum > theDateNum) {
        callback('身份证出生日期已超出系统当前日期！');
      }
    }
    callback();
  } else if (idenType === '100005' || idenType === '100027') {
    // 护照
    if (idenNr.length < 6 || idenNr.length > 16) {
      callback('证件号码字符位数要求大于等于6位，小于等于16位！');
    }
    if (isSerialCode(idenNr)) {
      callback('证件号码不可以为阶梯数字！');
    }
    if (!isNumberLetter(idenNr)) {
      callback('证件号码格式不正确！');
    }
    if (isRepeatCode(idenNr)) {
      callback('证件号码不得为同一数字！');
    }
    callback();
  } else if (idenType === '100007') {
    // 港澳身份证
    if (idenNr.length !== 9 && idenNr.length !== 11) {
      callback('证件号码固定为9位或11位！');
    }
    const firstNr = idenNr.charAt(0);
    const remainderNr = idenNr.substring(1, idenNr.length - 1);
    if (firstNr !== 'H' && firstNr !== 'M') {
      callback('证件号码开头必须是大写字母H或M，其余必须全部是数字！');
    }
    if (!isNumberCode(remainderNr)) {
      callback('证件号码开头必须是大写字母H或M，其余必须全部是数字！');
    }
    if (isSerialCode(remainderNr)) {
      callback('证件号码不可以为阶梯数字！');
    }
    if (isRepeatCode(remainderNr)) {
      callback('证件号码不得为同一数字！');
    }
    callback();
  } else if (idenType === '100008') {
    // 台胞证
    // 前2位“TW”或 “LXZH”字符，后面是阿拉伯数字、英文大写字母与半角“（）”的组合；
    const twoNr = idenNr.substring(0, 2);
    const fourNr = idenNr.substring(0, 4);
    // 证件号码为8位时，必须全部是阿拉伯数字。
    if (idenNr.length === 8) {
      if (!isNumberCode(idenNr)) {
        callback('证件号码必须全部是阿拉伯数字！');
      }
    } else if (twoNr === 'TW' || fourNr === 'LXZH') {
      if (!isUpperCaseAndNumber(idenNr)) {
        callback(' 证件号码前2两位为TW或前四位为 LXZH字符，其余后面是阿拉伯数字、英文大写字母与半角()的组合！');
      }
    } else if (idenNr.length === 13 || idenNr.length === 14) {
      // 证件号码为11-12 位，前10位为阿拉伯数字，最后,1-2位为校验码，括号内为英文字母或阿拉伯数字；即整体长度13或14位，底11位固定为全角或半角的括号“（”或“(”，第12位为数字或大写英文字母，第13位为数字、大写英文字母，和全角或半角的括号“)”或“）”，第14位如果有，必须是全角或半角的括号“)”或“）”。
      const preTenNr = idenNr.substring(0, 10);
      if (!isNumberCode(preTenNr)) {
        callback('证件号码前10位必须为数字，剩余位可以是数字或大写字母且必须放于括号中！');
      }
      const preBrackets = idenNr.charAt(10);
      const endBrackets = idenNr.charAt(idenNr.length - 1);
      if (!((preBrackets === '(' && endBrackets === ')') || (preBrackets === '（' && endBrackets === '）'))) {
        callback('证件号码前10位必须为数字，剩余位可以是数字或大写字母且必须放于括号中！');
      }
      const twelveWords = idenNr.charAt(11);
      if (!isUpCharCode(twelveWords) && !isNumberCode(twelveWords)) {
        // 12位必须是大写字母或者数字
        callback('证件号码前10位必须为数字，剩余位可以是数字或大写字母且必须放于括号中！');
      }

      const thirteenWords = idenNr.charAt(12);
      if (!isUpCharCode(thirteenWords) && !isNumberCode(thirteenWords) && thirteenWords !== ')' && thirteenWords !== '）') {
        // 14位时  13位必须是大写字母或者数字
        callback('证件号码前10位必须为数字，剩余位可以是数字或大写字母且必须放于括号中！');
      }
    } else {
      callback('证件信息不符合规范，证件号码可以为8位、13位、14位或者前2两位为TW或前四位为 LXZH字符！');
    }
    callback();
  } else if (idenType === '100010') {
    // 军官证
    if (idenNr.length < 6 || idenNr.length > 8) {
      callback('证件号码字符位数要求大于等于6位，小于等于8位！');
    }
    if (!isNumberCode(idenNr)) {
      callback('证件号码格式不正确！');
    }
    if (isSerialCode(idenNr)) {
      callback('证件号码不可以为阶梯数字！');
    }
    if (isRepeatCode(idenNr)) {
      callback('证件号码不得为同一数字！');
    }
    callback();
  } else if (idenType === '100011') {
    // 警官证
    if (idenNr.length < 6 || idenNr.length > 8) {
      callback('证件号码字符位数要求大于等于6位，小于等于8位！');
    }
    if (!isNumberCode(idenNr)) {
      callback('证件号码格式不正确！');
    }
    if (isSerialCode(idenNr)) {
      callback('证件号码不可以为阶梯数字！');
    }
    if (isRepeatCode(idenNr)) {
      callback('证件号码不得为同一数字！');
    }
    callback();
  } else if (idenType === '200006') {
    // 单位证件
    if (idenNr.length !== 15) {
      callback('单位证件号码位数要求等于15位！');
    }
    const reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g');
    if (reg.test(idenNr)) {
      callback('证件号码不能出现汉字！');
    }
    if (!isNumberLetter(idenNr)) {
      callback('单位证件号码只可以输入数字或字母！');
    }
    if (isSerialCode(idenNr)) {
      callback('证件号码不可以为阶梯数字！');
    }
    if (isRepeatCode(idenNr)) {
      callback('证件号码不得为同一数字！');
    }
    callback();
  } else if (idenType === '200009') {
    // 集团单位组织机构代码证
    if (idenNr.length !== 10) {
      callback('集团单位组织机构代码证件号码字符位数要求等于10位！');
    }

    const preEightNr = idenNr.substring(0, 8);
    const nightNr = idenNr.charAt(8);
    const tenNr = idenNr.charAt(9);

    const reg = /^[A-Z0-9]+$/;
    if (!reg.test(preEightNr) || nightNr !== '-' || !reg.test(tenNr)) {
      callback('组织机构代码证件号码只可以为前8位为数字或大写字母，第九位固定为-，第10位为数字或大写字母！');
    }

    if (isSerialCode(preEightNr)) {
      callback('证件号码不可以为阶梯数字！');
    }

    if (isRepeatCode(preEightNr)) {
      callback('证件号码不得为同一数字！');
    }
    callback();
  } else if (idenType === '200010') {
    // 新版营业执照（三证合一）
    if (idenNr.length !== 18) {
      callback('新版营业执照证件号码字符位数要求固定为18位！');
    }

    const threenEightNr = idenNr.substring(2, 8);

    if (!isNumberCode(threenEightNr)) {
      callback('证件号码中第3位-第8位固定为阿拉伯数字,其他位数可以是阿拉伯数字或大写英文字母！');
    }

    const reg = /^[A-Z0-9]+$/;
    if (!reg.test(idenNr)) {
      callback('证件号码中第3位-第8位固定为阿拉伯数字,其他位数可以是阿拉伯数字或大写英文字母！');
    }
    callback();
  } else if (idenType === '200002') {
    // 营业执照
    if (idenNr.length !== 18) {
      callback('18位统一社会信用代码字符位数要求固定为18位！');
    }
    const reg200002 = new RegExp('[\\u4E00-\\u9FFF]+', 'g');
    if (reg200002.test(idenNr)) {
      callback('证件号码不能出现汉字！');
    }
    const preEightNr = idenNr.substring(0, 8);
    const threenEightNr = idenNr.substring(2, 8);
    if (!isNumberCode(threenEightNr)) {
      callback('证件号码中第3位-第8位固定为阿拉伯数字,其他位数可以是阿拉伯数字或大写英文字母！');
    }
    if (isRepeatCode(preEightNr)) {
      callback('证件号码不得为同一数字！');
    }
    const reg = /^[A-Z0-9]+$/;
    if (!reg.test(idenNr)) {
      callback('证件号码中第3位-第8位固定为阿拉伯数字,其他位数可以是阿拉伯数字或大写英文字母！');
    }
    callback();
  } else if (idenType === '300000') {
    // 军队代码
    if (idenNr.length > 5) {
      callback('军队代码字符位数要求小于等于5位！');
    }
    const reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g');
    if (reg.test(idenNr)) {
      callback('证件号码不能出现汉字！');
    }
    const preEightNr = idenNr.substring(0, 5);
    const threenEightNr = idenNr.substring(0, 1);
    if (threenEightNr === '0') {
      callback('证件号码中的首位不能为0！');
    }
    if (isSerialCode(preEightNr)) {
      callback('证件号码不可以为阶梯数字！');
    }

    if (isRepeatCode(preEightNr)) {
      callback('证件号码不得为同一数字！');
    }
    if (isSpecialCharacter(idenNr)) {
      callback('包含特殊字符，请检查！');
    }
    callback();
  } else if (['300001', '300002', '300004', '300005', '300006', '300007', '300008'].includes(idenType)) {
    // 有偿服务许可证、事业单位法人证书、社团法人证书
    if (idenNr.length > 18) {
      callback('证件字符位数要求最长18位！');
    }
    const reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g');
    if (reg.test(idenNr)) {
      callback('证件号码不能出现汉字！');
    }
    if (isSpecialCharacter(idenNr)) {
      callback('证件号码不允许出现特殊符号！');
    }
    const preEightNr = idenNr.substring(0, idenNr.length - 1);
    if (isSerialCode(preEightNr)) {
      callback('证件号码不可以为阶梯数字！');
    }

    if (isRepeatCode(preEightNr)) {
      callback('证件号码不得为同一数字！');
    }
    callback();
  } else if (idenType === '300009') {
    // 单位证明
    if (idenNr.length !== 10) {
      callback('单位证明字符位数要求等于10位！');
    }
    const twoChar = idenNr.substring(0, 4);
    if (twoChar === 'NM04') {
      const fourChar = idenNr.substr(4, 2);
      if (!isNumberCode(fourChar)) {
        callback('单位证明中第5位和第6位固定为阿拉伯数字！');
      }
      const preEightNr = idenNr.substring(6, 4);
      if (isSerialCode(preEightNr)) {
        callback('证件号码不可以为阶梯数字！');
      }
      if (isRepeatCode(preEightNr)) {
        callback('证件号码不得为同一数字！');
      }
    } else {
      callback('单位证明字符位数要求等于10位,并且以NM04XX开始');
    }
    callback();
  } else if (idenType === '300010') {
    // 个人有效证件+店铺门头照
    if (idenNr.length !== 20) {
      callback('个人有效证件+店铺门头照证件号码位数要求等于20位！');
    }
    const twoChar = idenNr.substring(0, 2);
    const eighteenNr = idenNr.substr(2, 18);
    if (twoChar === 'NM') {
      if (isSerialCode(eighteenNr)) {
        callback('证件号码不可以为阶梯数字！');
      }
      if (isRepeatCode(eighteenNr)) {
        callback('证件号码不得为同一数字！');
      }
    } else {
      callback('个人有效证件+店铺门头照证件号码位数要求等于20位,并且以NM开始！');
    }
    callback();
  } else if (idenType === '100024') {
    // 港澳台居民居住证
    if (idenNr.length < 2) {
      callback('证件号码必须为81/82/83开头！');
    }
    const twoChar = idenNr.substring(0, 2);
    if (twoChar !== '81' && twoChar !== '82' && twoChar !== '83') {
      callback('证件号码必须为81/82/83开头！');
    }
    callback();
  }
  callback();
};
// 经办人证件号校验
export const validateHandleIdenNbr = (grpIdenType, grpIdenNr) => (rule, pIdenNr, callback) => {
  if (isBlank(pIdenNr)) {
    callback();
  }
  if (grpIdenType === '300010') {
    // 集团证件是 [个人有效证件+店铺门头照],集团证件的后18位 必须是经办人的 身份证号
    const grpIdenNrCha = grpIdenNr.substr(2, 18);
    if (grpIdenNrCha !== pIdenNr) {
      callback("证件号码位长为20位，设为'NM+经办人身份证号'");
    }
  }
  callback();
};
// 客户名称校验
export const validateIdenNameByIdenType = idenTypeCode => (rule, custName = '', callback) => {
  if (isBlank(custName)) {
    callback();
  }
  if (idenTypeCode === '200006' || idenTypeCode === '200009' || idenTypeCode === '200010' || idenTypeCode === '200002') {
    let strlen = 0;
    for (let i = 0; i < custName.length; i += 1) {
      if (custName.charCodeAt(i) > 255) {
        // 如果是汉字，则字符串长度加2
        strlen += 2;
      }
    }
    if (strlen < 8) {
      callback('客户姓名需大于等于8个字符，(大于等于四个字)且必须为双字符！');
    }
  } else if (['300000', '300001', '300002', '300004', '300005', '300006', '300007', '300008', '300009', '300010'].includes(idenTypeCode)) {
    if (isNumberLetter(custName)) {
      callback('不能包含数字和字母!');
    }
    let strlen = 0;
    for (let i = 0; i < custName.length; i += 1) {
      if (custName.charCodeAt(i) > 255) {
        // 如果是汉字，则字符串长度加2
        strlen += 2;
      }
    }
    if (['300004', '300005', '300006', '300007', '300008', '300009', '300010'].includes(idenTypeCode)) {
      if (strlen < 6) {
        callback('客户姓名需大于等于6个字符，(大于等于三个字)且必须为双字符！');
      }
    } else if (strlen < 8) {
      callback('客户姓名需大于等于8个字符，(大于等于四个字)且必须为双字符！');
    }
  }
  if (idenTypeCode !== '100005') {
    if (isBlankSpace(custName)) {
      callback('不能包含空格！');
    }
    if (isNumberLetter(custName)) {
      callback('不能包含数字和字母！');
    }
    if (isSpecialCharacter(custName)) {
      callback('包含特殊字符，请检查！');
    }
    if (custName.length < 2) {
      callback('不能少于2个汉字！');
    }
  }
  callback();
};

// 证件地址校验
export const validateIdenAddressByIdenType = idenType => (rule, idenAddress = '', callback) => {
  const keyWord = ['省', '市', '区', '县', '乡', '镇', '村', '盟', '旗', '苏木', '嘎查'];
  let count = 0;
  for (let i = 0; i < keyWord.length; i += 1) {
    const key = keyWord[i];
    if (idenAddress.indexOf(key) !== -1) {
      count += 1;
    }
  }

  if (idenType === '100001' || idenType === '200002' || idenType === '200006' || idenType === '200009' || idenType === '100002') {
    if (count === 0) {
      callback('地址信息中应至少包含省、市、区、县、乡、镇、村、盟、旗、苏木、嘎查中的一个！');
    }
  }
  let strlen = 0;
  for (let i = 0; i < idenAddress.length; i += 1) {
    if (idenAddress.charCodeAt(i) > 255) {
      // 如果是汉字，则字符串长度加2
      strlen += 2;
    } else {
      strlen += 1;
    }
  }

  if (idenType === '100001' || idenType === '100002') {
    // 身份证
    if (strlen < 16) {
      callback('身份证为有效证件类型时，证件地址中至少包含8个汉字！');
    }
  } else if (idenType === '100005' || idenType === '100027') {
    // 护照
    if (strlen < 12) {
      callback('证件地址必须大于等于12个字符，允许有汉字、数字、字母和括号！');
    }
    if (isNumberCode(idenAddress)) {
      callback('证件地址不能全为数字，允许有汉字、数字、字母和括号！');
    }
  } else if (idenType === '100007') {
    if (strlen < 12) {
      callback('证件地址中至少包含6个汉字！');
    }
  } else if (idenType === '100008') {
    if (strlen < 12) {
      callback('证件地址中至少包含6个汉字！');
    }
  } else if (idenType === '100010') {
    // 军官证
    if (strlen < 12) {
      callback('证件地址中至少包含6个汉字！');
    }
  } else if (idenType === '100011') {
    // 警官证
    if (strlen < 12) {
      callback('证件地址中至少包含6个汉字！');
    }
  } else if (idenType === '200006') {
    if (strlen < 12) {
      callback('证件地址中至少包含6个汉字！');
    }
  } else if (idenType === '200009') {
    if (strlen < 12) {
      callback('证件地址中至少包含6个汉字！');
    }
  } else if (idenType === '200010') {
    if (strlen < 12) {
      callback('证件地址中至少包含6个汉字！');
    }
  } else if (['200002', '300000', '300001', '300002', '300004', '300005', '300006', '300007', '300008', '300009', '300010'].includes(idenType)) {
    if (strlen < 12) {
      callback('证件地址中至少包含6个汉字！');
    }
    if (isBlankSpace(idenAddress)) {
      callback('证件地址中不能包含空格！');
    }
  }

  callback();
};

// 客户名称校验
export const validateGroupName = async (rule, value, callback) => {
  // 检查是否为空
  if (!value || value.trim() === '') {
    callback('集团名称不能为空');
  }

  // 检查是否包含空格
  const reg1 = /[  ]+/g;
  if (reg1.test(value)) {
    callback('集团名称不能包含空格');
  }

  // 检查是否包含非法字符
  // eslint-disable-next-line no-useless-escape
  const reg2 = /[#$%^&*\/\`~ <>"?@*]+/g;
  if (reg2.test(value)) {
    callback('集团名称不能包含非法字符');
  }
};
export const validatePhone = (_, value, callback) => {
  // 使用正则表达式进行手机号验证
  const reg = /^1[0-9]{10}$/;
  if (value && !reg.test(value)) {
    callback(new Error('请输入有效的电话号码'));
  }
  callback();
};
export const validateEmail = (_, value) => {
  // 使用正则表达式进行手机号验证
  // eslint-disable-next-line no-useless-escape
  const reg = /^[\w-\+]+(\.[\w]+)*@[\w-]+(\.[\w]+)*(\.[a-zA-Z]{2,})$/;
  if (value && !reg.test(value)) {
    return Promise.reject(new Error('请输入有效的邮件地址'));
  }
  return Promise.resolve();
};
export const validateMoney = (_, value) => {
  // 使用正则表达式进行手机号验证
  const reg = /^\d+(?=\.{0,1}\d+$|$)/;
  if (value && !reg.test(value)) {
    return Promise.reject(new Error('请输入有效的邮件地址'));
  }
  return Promise.resolve();
};

/* 纳税人是别号 */
export const validateTaxId = (_, value, callback) => {
  // 可以为空
  if (!value) {
    callback();
    return;
  }
  // 规则1: 检查是否包含中文
  const chineseReg = /[\u4E00-\u9FFF]/;
  // 规则2: 检查全角字符（非ASCII字符）
  // eslint-disable-next-line no-control-regex
  const fullWidthReg = /[^\x00-\x80]/;

  const fullWidthChars = []; // 存储全角字符用于提示

  // 逐字符校验
  // eslint-disable-next-line no-restricted-syntax,no-unused-vars
  for (const char of value) {
    // 规则1优先判断：发现中文立即报错
    if (chineseReg.test(char)) {
      callback(`税号不能包含中文${char}`);
      return; // 立即终止校验
    }

    // 规则2：收集全角字符（稍后统一报错）
    if (fullWidthReg.test(char)) {
      fullWidthChars.push(`'${char}'`);
    }
  }

  // 处理全角字符错误
  if (fullWidthChars.length > 0) {
    callback(`税号不能为全角字符：${fullWidthChars.join(',')}`);
    return;
  }

  // 通过校验
  callback();
};
