/* eslint-disable global-require */
import React, { useState, useEffect } from 'react';
import { message, Rate, Divider, Form, Input, Button } from 'antd';
import { connect } from 'dva';
import { sendCommentScore, qryKlKnowledgeScoreList, sendInnerCommentScore } from '@/pages/Librarys/services-use';
import styles from './Comment.module.less';
import commonStyles from '../common.module.less';

const Comment = ({ form, knowledgeInfoId = '', user }) => {
  const { getFieldDecorator, getFieldValue, setFieldsValue } = form;
  // 完整性
  const [completeRate, setCompleteRate] = useState(0);
  // 复制性
  const [copyRate, setCopyRate] = useState(0);
  // 应用性
  const [applyRate, setApplyRate] = useState(0);
  // 综合评分
  const [finalRate, setFinalRate] = useState(0);
  // 综合评分(用于展示星星的，必为整数或0.5结尾)
  const [finalRateForShow, setFinalRateForShow] = useState(0);
  const [loadingAnonymous, setLoadingAnonymous] = useState(false);
  const [loading, setLoading] = useState(false);
  const [innerLoadingAnonymous, setInnerLoadingAnonymous] = useState(false);
  const [innerloading, setInnerLoading] = useState(false);
  const [commentList, setCommentList] = useState([]);
  // 用来控制是否显示各条评分的回复是否展开
  const [openReplyList, setOpenReplyList] = useState([]);
  // 用来控制是否显示各条评分的回复框
  const [showReplyList, setShowReplyList] = useState([]);
  // 各条评论的回复框未提交文本
  const [replyInfoList, setReplyInfoList] = useState([]);
  // 当前的回复框所在的索引
  const [currentReplyIndex, setCurrentReplyIndex] = useState(-1);

  // 保存当前回复框内的值
  const saveReplyContent = () => {
    // 如果不是第一次打开回复框
    if (currentReplyIndex !== -1) {
      // 获取并保存回复框的值
      const beforeReplyContent = getFieldValue('innerEvaMessage');
      setReplyInfoList([...replyInfoList.slice(0, currentReplyIndex), beforeReplyContent, ...replyInfoList.slice(currentReplyIndex + 1)]);
    }
  };

  const getCommentList = async knowledgeScoreId => {
    const res = await qryKlKnowledgeScoreList({ knowledgeInfoId });
    setCommentList(res);
    setReplyInfoList(new Array(res.length));
    // // 因为保存后会刷新列表，回复框会隐藏，所以要先保存当前回复框内的值，防止丢失
    // saveReplyContent();

    // // 设置回复框文本列表的长度，使用头插因为返回的新内容在数组头部
    // const newReplyInfoList = replyInfoList;
    // while (res.length > newReplyInfoList.length) {
    //   newReplyInfoList.unshift('');
    // }
    // setReplyInfoList(newReplyInfoList);

    // 设置控制显示回复框的列表、控制展开回复内容的列表
    const newReplyList = [];
    const newOpenReplyList = [];
    for (let i = 0; i < res.length; i += 1) {
      newReplyList.push(false);

      // 追评后展开回复框
      if (res[i].knowledgeScoreId === knowledgeScoreId) {
        newOpenReplyList.push(true);
      } else {
        newOpenReplyList.push(false);
      }
    }

    setShowReplyList(newReplyList);
    setOpenReplyList(newOpenReplyList);

    return res;
  };

  const initData = async () => {
    getCommentList();
  };

  const submitInnerComment = async (anonymous, knowledgeScoreId) => {
    const innerEvaMessage = getFieldValue('innerEvaMessage');
    if (!innerEvaMessage) {
      message.warning('评价内容为空！');
      return;
    }

    const params = {
      knowledgeScoreId,
      relaId: knowledgeScoreId,
      evaMessage: innerEvaMessage,
      commentsName: anonymous ? '匿名' : '',
    };

    if (anonymous) {
      setInnerLoadingAnonymous(true);
    } else {
      setInnerLoading(true);
    }

    const result = await sendInnerCommentScore(params);
    if (result === true) {
      await getCommentList(knowledgeScoreId);
    }

    setInnerLoadingAnonymous(false);
    setInnerLoading(false);
  };

  const submitComment = async anonymous => {
    const evaMessage = getFieldValue('evaMessage');
    if (!evaMessage) {
      message.warning('评价内容为空！');
      return;
    }
    const params = {
      knowledgeInfoId,
      completeRate,
      copyRate,
      applyRate,
      commentsName: anonymous ? '匿名' : '',
      evaMessage,
    };

    if (anonymous) {
      setLoadingAnonymous(true);
    } else {
      setLoading(true);
    }
    const result = await sendCommentScore(params);
    if (result === true) {
      setApplyRate(0);
      setCompleteRate(0);
      setCopyRate(0);
      setFieldsValue({
        evaMessage: '',
      });

      await getCommentList();
    }
    setLoadingAnonymous(false);
    setLoading(false);
  };

  // 计算综合评分，保留一位小数，四舍五入
  const caculateRate = (_copyRate, _applyRate, _completeRate) => {
    if (Number.isNaN(Number(_copyRate)) || Number.isNaN(Number(_copyRate)) || Number.isNaN(Number(_copyRate))) {
      return Number(0).toFixed(1);
    }

    const str = String(((Number(_copyRate) + Number(_applyRate) + Number(_completeRate)) / 3).toFixed(3));
    let result = Number(str.slice(0, 3));
    const nextNum = Number(str.slice(3, 4));
    if (nextNum >= 5) {
      result += 0.1;
    }
    return result.toFixed(1);
  };

  // 设置用于展示的综合评分，将1.x都化简为1.5，用于展示半星
  const transferRate = rate => {
    const simpleNum = String(rate).slice(0, 1);
    if (Number(simpleNum) < Number(rate)) {
      return Number(Number(simpleNum) + 0.5);
    }
    return Number(simpleNum);
  };

  useEffect(() => {
    initData();
  }, []);

  useEffect(() => {
    // 计算综合评分，保留一位小数，四舍五入
    const rate = caculateRate(copyRate, applyRate, completeRate);
    setFinalRate(rate);

    // 设置用于展示的综合评分，将1.x都化简为1.5，用于展示半星
    const rateForShow = transferRate(rate);
    setFinalRateForShow(rateForShow);
  }, [copyRate, applyRate, completeRate]);

  const innerReply = (replyList, commentIndex) => {
    if (replyList?.length === 0) {
      return <></>;
    }

    let finalReplyList = replyList;
    if (openReplyList[commentIndex] === false) {
      finalReplyList = [replyList[0]];
    }
    return (
      <div style={{ marginTop: '15px' }}>
        {Array.isArray(replyList) && replyList?.length > 1 && (
          <div className={styles.showInner}>
            <div className={styles.button}>
              <Button
                type="link"
                onClick={() => {
                  setOpenReplyList([...openReplyList.slice(0, commentIndex), !openReplyList[commentIndex], ...openReplyList.slice(commentIndex + 1)]);
                }}
              >
                {openReplyList[commentIndex] === true ? '收起回复' : '展开回复'}
              </Button>
            </div>
          </div>
        )}
        <div className={styles.innerReply}>
          {finalReplyList.map((reply, index) => (
            <div>
              <div className={styles.innerComment} key={reply.id}>
                <div className={styles.left}>
                  <img className={styles.commentPhoto} src={require('@/pages/Librarys/img/user-head-default.png')} alt="" />
                </div>
                <div className={styles.right}>
                  <div className={styles.rateRow}>
                    <span className={styles.name}>{reply.commentsName}</span>
                    <span className={styles.date}>{reply.createDate}</span>
                  </div>
                  <div className={styles.textRow}>
                    <span className={styles.textAreaTitle}>评论：</span>
                    <span className={styles.textArea}>{reply.evaMessage}</span>
                  </div>
                </div>
              </div>
              {index < finalReplyList.length - 1 && <Divider />}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={commonStyles.module} style={{ backgroundColor: 'white' }}>
      <div className={commonStyles.moduleTitle}>评价</div>
      <div className={styles.wrap}>
        <div className={styles.comment}>
          <div className={styles.left}>
            <img className={styles.commentPhoto} src={require('@/pages/Librarys/img/user-head-default.png')} alt="" />
            <div className={styles.commentName}>{user?.userInfo?.userName}</div>
          </div>
          <div className={styles.right}>
            <div className={styles.rateRow}>
              <span className={styles.title}>
                评分：<span>{finalRate}</span>
              </span>
              <Rate allowHalf disabled value={finalRateForShow} />
              <span className={styles.tip}>（友情提示：请从完整性、复制性和应用性三个方面进行综合打分）</span>
            </div>
            <div className={styles.detailRate}>
              <span className={styles.title}>完整性：</span>
              <Rate
                value={completeRate}
                onChange={value => {
                  setCompleteRate(Number(value));
                }}
              />
            </div>
            <div className={styles.detailRate}>
              <span className={styles.title}>复制性：</span>
              <Rate
                value={copyRate}
                onChange={value => {
                  setCopyRate(Number(value));
                }}
              />
            </div>
            <div className={styles.detailRate}>
              <span className={styles.title}>应用性：</span>
              <Rate
                value={applyRate}
                onChange={value => {
                  setApplyRate(Number(value));
                }}
              />
            </div>
            <div className={styles.textRow}>
              <span className={styles.textAreaTitle}>评论：</span>
              <Form style={{ marginTop: '10px' }}>
                <Form.Item>{getFieldDecorator('evaMessage')(<Input.TextArea allowClear placeholder="留下您的宝贵意见吧" />)}</Form.Item>
              </Form>
            </div>
            <div className={styles.buttonRow}>
              <Button
                loading={loadingAnonymous}
                type="primary"
                onClick={() => {
                  submitComment(true);
                }}
                style={{ marginRight: '15px' }}
              >
                匿名发表
              </Button>
              <Button
                loading={loading}
                type="primary"
                onClick={() => {
                  submitComment(false);
                }}
              >
                发表
              </Button>
            </div>
          </div>
        </div>
        <Divider />
        {commentList.map((comment, index) => {
          const currentRate = caculateRate(comment.integrityScore, comment.replicationScore, comment.appliedScore);
          const rateForStart = transferRate(currentRate);
          const replyList = comment.klKnowledgeEvaluations.slice(1);
          return (
            <div>
              <div className={styles.comment} key={comment.id}>
                <div className={styles.left}>
                  <img className={styles.commentPhoto} src={require('@/pages/Librarys/img/user-head-default.png')} alt="" />
                  <div className={styles.commentName}>{comment.commentsName}</div>
                </div>
                <div className={styles.right}>
                  <div className={styles.rateRow}>
                    <span className={styles.titleWrap}>
                      <span className={styles.title}>评分：{currentRate}</span>
                      <Rate allowHalf disabled value={rateForStart} />
                    </span>
                    <span className={styles.time}>{comment.createDate}</span>
                  </div>
                  <div className={styles.textRow}>
                    <span className={styles.textAreaTitle}>评论：</span>
                    <span className={styles.textArea}>{comment.klKnowledgeEvaluations?.[0].evaMessage}</span>
                  </div>
                  {innerReply(replyList, index)}
                  {!showReplyList[index] && (
                    <div className={styles.buttonRow}>
                      <Button
                        onClick={() => {
                          saveReplyContent();
                          setCurrentReplyIndex(index);
                          // 获取并回填当前索引位置保存的值
                          setFieldsValue({
                            innerEvaMessage: replyInfoList[index],
                          });
                          const newReplyList = [];
                          for (let i = 0; i < showReplyList.length; i += 1) {
                            if (i === index) {
                              newReplyList.push(true);
                            } else {
                              newReplyList.push(false);
                            }
                          }
                          setShowReplyList(newReplyList);
                        }}
                      >
                        我也说一句
                      </Button>
                    </div>
                  )}
                  {showReplyList[index] && (
                    <div>
                      <div className={styles.textRow}>
                        <span className={styles.textAreaTitle}>回复：</span>
                        <Form style={{ marginTop: '10px' }}>
                          <Form.Item>
                            {getFieldDecorator('innerEvaMessage')(<Input.TextArea allowClear placeholder="留下您的宝贵意见吧" />)}
                          </Form.Item>
                        </Form>
                      </div>
                      <div className={styles.buttonRow}>
                        <Button
                          loading={innerLoadingAnonymous}
                          type="primary"
                          onClick={() => {
                            submitInnerComment(true, comment.id);
                          }}
                          style={{ marginRight: '15px' }}
                        >
                          匿名发表
                        </Button>
                        <Button
                          loading={innerloading}
                          type="primary"
                          onClick={() => {
                            submitInnerComment(false, comment.id);
                          }}
                        >
                          发表
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <Divider />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default connect(({ login }) => ({
  user: login.user,
}))(Form.create()(Comment));
