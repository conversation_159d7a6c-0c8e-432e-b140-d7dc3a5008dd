/* eslint-disable no-shadow */
import React, { useEffect, useRef, useState } from 'react';
import { Form, Row, Col, Input, Button, message, Icon, Table } from 'antd';
import { connect } from 'dva';
import { useAntdTable } from '@umijs/hooks';
import moment from 'moment';
import SlickTable from '@/components/SlickTable';
import { getPageSizeByCardHeight } from '@/utils/utils';
import { getListMarge } from '../../services';
import style from './index.less';

const cellStyle = {
  maxWidth: 200,
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  cursor: 'pointer',
};

const getTableData = ({ current, pageSize, ...rest }) => {
  const { accessNum, offerName, groupId } = rest;
  const params = {
    groupId,
    accessNum, // 融合包服务号码
    offerName, // 融合包名称
    offerId: '', // 商品编码
    status: '', // 状态
    pageFlag: '1', // 传1为分页
    isHis: '0', // 0 当前 1历史
    pageInfo: {
      currentPage: current,
      pageSize,
    },
  };
  return getListMarge(params).then(res => {
    const { resultCode, resultObject, resultMsg } = res;
    if (resultCode === 'TRUE' && resultObject?.data) {
      return {
        total: resultObject?.count,
        data: resultObject?.data,
      };
    }
    message.error(resultMsg);
    return {
      total: 0,
      data: [],
    };
  });
};
const Index = ({ size: { height }, form, ...otherProps }) => {
  const [expandedRows, setExpandedRows] = useState([]);
  const [size, setSize] = useState(getPageSizeByCardHeight(height));
  const { goToStep, refreshFlag, currentCust, toggleParentTab } = otherProps;
  const tableRef = useRef(null);
  const { getFieldDecorator } = form;
  const {
    tableProps,
    refresh,
    search: { submit, reset },
  } = useAntdTable(params => getTableData({ ...params, pageSize: size, groupId: currentCust.GROUP_ID }),
   [height], // 查询条件变化会发生请求 高度变化 加载
    {
    defaultPageSize: size,
    form,
  });

  const { pagination, ...restTableProps } = tableProps;

  useEffect(() => {
    if (refreshFlag) {
      refresh();
    }
  }, [refreshFlag]);

  // 根据size.height 变化即时更新pageSize
  useEffect(() => {
    setSize(getPageSizeByCardHeight(height));
  }, [height]);

  const goDetail = record => {
    toggleParentTab(false);
    goToStep(2, { offerInst: record });
  };

  // 展开嵌套内容
  const handleExpand = key => {
    const newExpandedRows = [...expandedRows];
    if (newExpandedRows.includes(key)) {
      newExpandedRows.splice(newExpandedRows.indexOf(key), 1);
    } else {
      newExpandedRows.push(key);
    }
    setExpandedRows(newExpandedRows);
  };
  const sonColumns = [
      {
        title: '服务号码',
        dataIndex: 'ACCESS_NUM',
        key: 'accessNum',
        align: 'center',
        width: 100,
        render: text => (
          <text>
            {text}
          </text>
        ),
      },
      {
        title: '商品名称',
        dataIndex: 'OFFER_NAME',
        key: 'offerName',
        align: 'center',
        width: 100,
        onCell: () => ({
          style: cellStyle,
        }),
      },
      {
        title: '账户编码',
        dataIndex: 'ACCT_ID',
        key: 'acctId',
        align: 'center',
        width: 80,
      },
      {
        title: '开通时间',
        dataIndex: 'CREATE_DATE',
        key: 'createDate',
        align: 'center',
        width: 80,
        render: text => {
          if (text) {
            return moment(text).format('YYYY-MM-DD HH:mm:ss');
          }
          return '';
        },
      },
      {
        title: '操作',
        align: 'center',
        key: 'action',
        width: 50,
        render: (text, record) => (
          <span onClick={() => goDetail(record)} style={{ color: '#1890ff' }}>
            详情
          </span>
       ),
      },
  ];

 /**
 * 渲染嵌套内容
 * @param record
 * @returns {Element}
 */
const expandedRowRender = (record, index, indent, expanded) => (
  <div className={`${style.expandedRowRender} ${expanded ? style.visible : style.hidden}`}>
    <Table rowKey={record => record.SUBSCRIBER_INS_ID} columns={sonColumns} dataSource={record.SUB_OFFERS} pagination={false} />
  </div>
);

  return (
    <>
      <Form className="flow2 fix-label" onSubmit={submit}>
        <Row gutter={24}>
          <Col span={8} style={{ display: 'flex' }}>
            <Form.Item label="融合包服务号码" />
            <Form.Item>
              {getFieldDecorator('accessNum', {
                    initialValue: '',
                  })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8} style={{ display: 'flex' }}>
            <Form.Item label="融合包名称" />
            <Form.Item>
              {getFieldDecorator('offerName', {
                    initialValue: '',
                  })(<Input placeholder="请输入" allowClear />)}
            </Form.Item>
          </Col>
          <Col span={8} className="text-right">
            <Button className="margin-left" onClick={reset}>
              重置
            </Button>
            <Button type="primary" className="margin-left" htmlType="submit" loading={tableProps.loading}>
              查询
            </Button>
          </Col>
        </Row>
      </Form>
      <SlickTable
        bordered={false}
        style={{ marginTop: 10 }}
        ref={tableRef}
        rowKey={record => record.SUBSCRIBER_INS_ID}
        {...restTableProps}
        expandedRowKeys={expandedRows}
        expandIcon={() => null} // 移除默认的第一列展开图标
        expandIconAsCell={false} // 设置展开图标在表格中不显示为列
        rowClassName={record => (expandedRows.includes(record.index) ? `${style.expandedRow}` : '')}
        expandedRowRender={expandedRowRender}
        data={{
            pagination: {
              ...pagination,
              pageSize: size,
            },
          }}
        columns={[
            {
              title: '融合包服务号码',
              dataIndex: 'ACCESS_NUM',
              key: 'accessNum',
              render: text => (
                <text>
                  {text}
                </text>
              ),
            },
            {
              title: '融合包名称',
              dataIndex: 'OFFER_NAME',
              key: 'offerName',
              onCell: () => ({
                style: cellStyle,
              }),
            },
            {
              title: '账户编码',
              dataIndex: 'ACCT_ID',
              key: 'acctId',
            },
            {
              title: '开户时间',
              dataIndex: 'CREATE_DATE',
              key: 'createDate',
              render: text => {
                if (text) {
                  return moment(text).format('YYYY-MM-DD HH:mm:ss');
                }
                return '';
              },
            },
            {
              title: '操作',
              dataIndex: 'index',
              key: 'index',
              render: (text, record) => (
                <div>
                  {/* <Icon
                    style={{ marginRight: '8px' }}
                    type={expandedRows.includes(record.SUBSCRIBER_INS_ID) ? 'caret-down' : 'caret-right'}
                    onClick={() => handleExpand(record.SUBSCRIBER_INS_ID)}
                  /> */}
                  {
                    expandedRows.includes(record.SUBSCRIBER_INS_ID) ? (
                      <span onClick={() => handleExpand(record.SUBSCRIBER_INS_ID)} style={{ color: '#1890ff' }}>
                        收起
                      </span>
                    ) : (
                      <span onClick={() => handleExpand(record.SUBSCRIBER_INS_ID)} style={{ color: '#1890ff' }}>
                        展开
                      </span>
                    )
                  }

                </div>
              ),
            },
          ]}
      />

    </>
  );
};

export default connect(({ setting, groupCustView }) => ({
  size: setting.size,
  currentCust: groupCustView.currentCust,
}))(Form.create()(Index));
