.meteor {
  width: 85px;
  height: 85px;
  z-index: 1;
  position: absolute;
  background: url(../img/meteor.png) no-repeat;
  background-size: contain;
}

.meteor1 {
  top: 200px;
  left: 200px;
}

.meteor2 {
  top: 200px;
  left: 50%;
  margin-left: 200px;
}

.meteor3 {
  top: 100px;
  right: 300px;
}

.meteor4 {
  top: 150px;
  right: 300px;
}

.meteorFla {
  animation: meteor 5s ease-in infinite;
}

.meteorFla2 {
  animation: meteor2 4s ease-in infinite;
}

.meteorFla3 {
  animation: meteor3 3s linear infinite;
}

.meteorFla4 {
  animation: meteor4 2s linear infinite;
}

@keyframes meteor {
  0% {
    transform: translate(200px, -200px);
  }

  90% {
    transform: translate(-180px, 180px);
    opacity: 1;
  }

  100% {
    transform: translate(-200px, 200px);
    opacity: 0;
  }
}

@keyframes meteor2 {
  0% {
    transform: translate(200px, -200px);
  }

  90% {
    transform: translate(-480px, 480px);
    opacity: 1;
  }

  100% {
    transform: translate(-500px, 500px);
    opacity: 0;
  }
}

@keyframes meteor3 {
  0% {
    transform: translate(200px, -200px);
  }

  90% {
    transform: translate(-480px, 480px);
    opacity: 1;
  }

  100% {
    transform: translate(-500px, 500px);
    opacity: 0;
  }
}

@keyframes meteor4 {
  0% {
    transform: translate(200px, -200px);
  }

  90% {
    transform: translate(-180px, 180px);
    opacity: 1;
  }

  100% {
    transform: translate(-200px, 200px);
    opacity: 0;
  }
}
