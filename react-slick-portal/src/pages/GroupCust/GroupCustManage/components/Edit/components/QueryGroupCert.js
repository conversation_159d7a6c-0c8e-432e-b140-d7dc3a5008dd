/* eslint-disable no-console */
/* 集团证件查询 */
import React, { useState } from 'react';
import { connect } from 'dva';
import { Button, Col, Form, Input, message, Modal, Row, Select } from 'antd';
import { useFormTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import {
  checkBlackList,
  queryCmOrderOperatorInfoByOrderId,
  queryEnterpriseDetail,
  queryIdentInfos,
  queryFileVal,
  queryhasPriv,
  queryProductionApi,
} from '@/pages/GroupCust/GroupCustManage/services';
import { GROUP_CARD_TYPE } from '@/utils/consts';

const cellStyle = {
  maxWidth: 200,
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
};

// 查询证件
const getTableData = ({ current, pageSize }, formData) =>
  queryIdentInfos({
    idenNr: formData.idenNr,
    idenTypeCode: formData.idenTypeCode,
    pageFlag: 1,
    pageInfo: {
      currentPage: current,
      pageSize,
    },
  }).then(res => {
    const { resultCode, resultObject, resultMsg } = res;
    if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
      return {
        total: parseInt(resultObject?.rspParam?.pageInfo?.recordCount, 10),
        list: resultObject?.rspParam?.busiInfo?.outData,
      };
    }
    message.error(resultMsg);
    return {
      total: 0,
      list: [],
    };
  });
const QueryGroupCert = props => {
  const { visible, onCancel, form, dispatch, onValuesChange, handleQueryPop } = props;
  const { getFieldDecorator } = form;
  const [selectRows, setSelectRows] = useState([]);
  // const [confirmLoading, setConfirmLoading] = useState(false);
  const [selectLoading, setSelectLoading] = useState(false);

  const {
    tableProps,
    search: { submit, reset },
  } = useFormTable(getTableData, {
    defaultPageSize: 10,
    form,
    manual: true,
  });
  const { pagination, ...restTableProps } = tableProps;

  const checkBlackListByIdenNr = idenNr =>
    new Promise(resolve => {
      checkBlackList({ idenCode: idenNr }).then(res => {
        const { resultCode, resultObject, resultMsg } = res;
        if (resultCode === 'TRUE') {
          resolve(resultObject?.blackStatus);
        } else {
          message.error(`证件校验失败:${resultMsg}`);
        }
      });
    });

  // 权限判断
  const hasPriv = () => {
    const idList = [{
      moduleName: 'jtmc',
      id: '56000051', // 有权限可以修改 集团名称
    },
    {
      moduleName: 'jbrxx',
      id: '513301', // 有权限可以修改经办人拍照后的值
    },
    {
      moduleName: 'qyxx',
      id: '52000042', // 有权限归属信息地市可以改
    },
    // {
    //   moduleName: 'addCert',
    //   id: '52000016', // 新增 证件拍照信息 能否修改 待确定
    // },
    // {
    //   moduleName: 'addHandle',
    //   id: '56000025', // 新增证件经办人证件号、经办人、联系地址 有权限可以修改，没权限只能扫  待确定
    // }
    ];
    // id:52000017 跳过在线比对的权限
    // const resp = await queryhasPriv(id);
    idList.forEach(async e => {
      const resp = await queryhasPriv(e.id);
      // 有权限 可以修改集团名称
      if (resp.resultCode === 'TRUE' && resp.resultObject === 'true' && e.id === '56000051') {
        dispatch({
          type: 'AddGroupCust/updataFormStateAll',
          payload: {
            GROUP_NAME: { disabled: false },
          },
        });
      }

      // 有权限 可以修改经办人拍照后的值
      if (resp.resultCode === 'TRUE' && resp.resultObject === 'true' && e.id === '513301') {
        dispatch({
          type: 'AddGroupCust/updataFormStateAll',
          payload: {
            AGENT_LICENCE_NAME: { disabled: false },
            AGENT_LICENCE_ADDRESS: { disabled: false },
          },
        });
      }

      // 有权限 归属信息地市可以改
      if (resp.resultCode === 'TRUE' && resp.resultObject === 'true' && e.id === '52000042') {
        dispatch({
          type: 'AddGroupCust/updataFormStateAll',
          payload: {
            BelongingAreaInfo: { disabled: false },
          },
        });
      }
    });
  };

  // 企业信息详情查询
  const queryEnterprise = grpIdenInfo => {
    const { IDEN_NAME, IDEN_TYPE_CODE, IDEN_NR } = grpIdenInfo;
    // 300000: '军队代码',
    // 300001: '有偿服务许可证',
    // 300009: '单位证明',
    // 300010: '个人有效证件',
    // 关于政企客户行业验真改造需求,BSCNM_FUNC_20221130_0009  以下证件类型不致信
    if (IDEN_TYPE_CODE === '300009' || IDEN_TYPE_CODE === '300001' || IDEN_TYPE_CODE === '300010') {
      // 把输入框可输入
      dispatch({
        type: 'AddGroupCust/updataFormStateAll',
        payload: {
          GROUP_LICENCE_NAME: { disabled: false }, // 集团证件 --证件名称
          GROUP_LICENCE_NO: { disabled: false }, // 集团证件 -- 证件号码
          GROUP_LICENCE_ADDRESS: { disabled: false }, // 集团证件 -- 生产经营具体地址
          GROUP_ORG_CODE: { disabled: false }, // 集团证件 -- 组织机构代码
          GROUP_NAME: { disabled: false }, // 基本信息 -- 集团名称
          JURISTIC_NAME: { disabled: false }, // 基本信息 -- 法人名称
          REG_MONEY: { disabled: false }, // 基本信息 -- 注册资金
          buIndustrySub: { disabled: false }, // 基本信息 -- 政企客户行业类型
          REGMANADEPARTMENT: { disabled: false }, // 基本信息 -- 登记管理部门
        },
      });
      return;
    }

    // 如果为军队代码证件，不致信，行业门类固定国防
    if (IDEN_TYPE_CODE === '300000') {
      const paramsForm = {
        CALLING_AREA: '13100',
      };
      form.setFieldsValue(paramsForm);
      // 更改状态
      dispatch({
        type: 'AddGroupCust/updataFormState',
        payload: {
          formName: 'CALLING_AREA',
          state: { disabled: true, visible: true },
        },
      });
      onValuesChange(
        {
          'IndustryInfo.CALLING_AREA': '13100',
        },
        true
      );
      // todo: changeCALLING_AREA_A(); 草稿？
      return;
    }
    form.setFieldsValue({ CALLING_AREA: undefined });
    // 把输入框禁用
    const formState = {
      GROUP_LICENCE_NAME: { disabled: true }, // 集团证件 --证件名称
      GROUP_LICENCE_NO: { disabled: true }, // 集团证件 -- 证件号码
      GROUP_LICENCE_ADDRESS: { disabled: true }, // 集团证件 -- 生产经营具体地址
      GROUP_ORG_CODE: { disabled: true }, // 集团证件 -- 组织机构代码
      GROUP_NAME: { disabled: true }, // 基本信息 -- 集团名称
      JURISTIC_NAME: { disabled: true }, // 基本信息 -- 法人名称
      REG_MONEY: { disabled: true }, // 基本信息 -- 注册资金
      buIndustrySub: { disabled: true }, // 基本信息 -- 政企客户行业类型
      REGMANADEPARTMENT: { disabled: true }, // 基本信息 -- 登记管理部门
      CALLING_AREA: { disabled: false }, // 分类信息 -- 行业门类
    };
    dispatch({
      type: 'AddGroupCust/updataFormStateAll',
      payload: formState,
    });

    // 查询企业详细信息查询
    let keyType = '1';
    let searchKey = IDEN_NAME;
    if (['200002', '200010'].includes(IDEN_TYPE_CODE)) {
      keyType = '3';
      searchKey = IDEN_NR;
    }
    const params = {
      keyType,
      searchKey,
    };

    // 查询企业信息详情
    queryEnterpriseDetail(params).then(res => {
      console.log('查询企业信息详情res==', res);
      // setConfirmLoading(false);

      const { resultCode } = res;
      const { resultObject } = res;
      if (resultCode === 'TRUE' && resultObject?.busiInfo) { // 上线修改
        handleQueryPop();
        setSelectLoading(false);
        setSelectRows([]);
        // resultObject = {
        //   busiInfo: {
        //     ecDetailInfo: {
        //       buIndustry: '互联网-省专单位',
        //       entName: '2025测试集客AA百六十六',
        //       entInCome: '',
        //       opTo: '2053-10-19',
        //       hasDranchoffice: '0',
        //       buIndustrySubID: '151',
        //       industry: 'C',
        //       registeredCountry: '新城区',
        //       type: '1',
        //       actAddr: '',
        //       buIndustrySub: '互联网-省专',
        //       entType: '有限责任公司分公司（非自然人投资或控股的法人独资）',
        //       engName: '',
        //       empNum: '1042',
        //       orgCode: '701486373',
        //       regCap: '',
        //       registeredCity: '呼和浩特市',
        //       tel: '***********',
        //       isAbnormaloperation: '0',
        //       coordinateAddr: '东经：111.675911 北纬：40.815415',
        //       opsScope: '在内蒙古自治区经营基础电信业务（具体经营范围以许可证为准）；经营增值电信业务（具体经营范围以许可证为准）；从事移动通信、ＩＰ电话和因特网等网络设计、投资和建设；移动通信、ＩＰ电话和因特网设施的安装、工程施工和维修；经营与移动通信、ＩＰ电话和因特网业务相关的系统集成、漫游结算清算、技术开发、技术服务、设备的销售，以及其他电信及信息服务；出售、出租移动电话终端设备；ＩＰ电话设备、因特网设备及其配件，并提供售后服务；业务培训、会议服务，租赁业务；设计、制作、发布代理各类广告；互联网零售；经营互联网生活服务平台；计算机软件开发、销售；日用百货、电子产品、智能设备、智能穿戴设备销售；票务代理服务；代收居民水电费及其他费用',
        //       industrySub: '16',
        //       regManaDepartment: '工商',
        //       opFrom: '2004-10-20',
        //       email: '<EMAIL>',
        //       registeredProvince: '内蒙古自治区',
        //       isWrongfulFaith: '0',
        //       address: '内蒙古自治区呼和浩特市新城区呼伦南路5号',
        //       wSite: '',
        //       apprDate: '2025-03-19',
        //       uniscid: '11115252220116699653',
        //       esDate: '2004-10-20',
        //       isDishonesty: '0',
        //       regOrg: '呼和浩特市新城区市场监督管理局',
        //       name: '王大大',
        //       entStatus: '在营（开业）企业',
        //       dataSource: '1',
        //       CALLING_AREA: '11000',
        //       CALLING_AREA_A: '12378',
        //     },
        //     reqSerialNo: '47120250512214410875',
        //     resCode: '00',
        //   },
        // };
        // console.log('查询企业详细信息出参', res);
        const { ecDetailInfo, reqSerialNo } = resultObject?.busiInfo;
        const { resCode } = resultObject?.busiInfo;
        const { param } = resultObject?.busiInfo;

        // setConfirmLoading(false);
        if (param) {
          // 证件工商登记状态异常，不允许提交
          message.warn(param);
          return;
        }
        if (resCode === '00' && ecDetailInfo) {
          dispatch({
            type: 'AddGroupCust/updataOther',
            payload: { needOaAudit: false },
          });
          const {
            entName,
            orgCode,
            uniscid,
            address,
            entType,
            entInCome,
            tel,
            name,
            regCap,
            registeredProvince,
            registeredCity,
            registeredCountry,
            buIndustry,
            buIndustrySub,
            empNum,
            regManaDepartment,
            CALLING_AREA,
            CALLING_AREA_A,
          } = ecDetailInfo;

          if (registeredProvince || registeredCity || registeredCountry) {
            console.log('生产经营地址', registeredProvince, registeredCity, registeredCountry);
            const addressParams = {
              regProvice: registeredProvince,
              regCity: registeredCity,
              regCountry: registeredCountry,
            };
            queryProductionApi(addressParams).then(resp => {
              if (resp.resultCode === 'TRUE' && resp.resultObject.regProvice) {
                const { regProvice, regCity, regCountry } = resp.resultObject;
                form.setFieldsValue({ BUSILOCATIONS: [regProvice, regCity, regCountry] });
              }
            });
          }

          if (CALLING_AREA && CALLING_AREA_A) {
            form.setFieldsValue({
              CALLING_AREA_INFO: [CALLING_AREA, CALLING_AREA_A],
              CALLING_AREA_A,
              CALLING_AREA,
            });
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { CALLING_AREA_INFO: { disabled: true } },
            });
          }

          // 企业名称 GROUP_NAME  oldName:GROUP_NAME_OLDS
          if (entName) {
            const paramsFillForm = {
              GROUP_NAME: entName,
            };
            form.setFieldsValue(paramsFillForm);
            dispatch({
              type: 'AddGroupCust/updataOther',
              payload: { GROUP_NAME_OLDS: entName },
            });
            // 『集团名称]
            onValuesChange(
              {
                'BaseInfo.GROUP_NAME': entName,
              },
              true
            );
          } else {
            // 没有值 集团名称允许输入
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { GROUP_NAME: { disabled: false } },
            });
          }
          // [是否分支机构] 回填
          // [企业标准名称] 回填
          if (IDEN_NAME === entName && IDEN_NAME !== '') {
            const paramsEntName = {
              IS_BRANCH: '0',
              ENT_NAME: '',
            };
            form.setFieldsValue(paramsEntName);
            onValuesChange(
              {
                'BaseInfo.IS_BRANCH': '0',
                'BaseInfo.ENT_NAME': '',
              },
              true
            );
          }

          // 组织机构代码
          if (orgCode) {
            const paramsFillForm = {
              GROUP_ORG_CODE: orgCode,
            };
            form.setFieldsValue(paramsFillForm);
            // [组织机构代码] 回填
            onValuesChange(
              {
                'CertInfo.GROUP_ORG_CODE': orgCode,
              },
              true
            );
          } else {
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { GROUP_ORG_CODE: { disabled: false } },
            });
          }

          // 证件号码
          if (uniscid) {
            const paramsFillForm = {
              GROUP_LICENCE_NO: uniscid,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'CertInfo.GROUP_LICENCE_NO': uniscid,
              },
              true
            );
          } else {
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { GROUP_LICENCE_NO: { disabled: false } },
            });
          }

          // 营业场所
          if (address) {
            const paramsFillForm = {
              GROUP_LIGROUP_LICENCE_ADDRESSCENCE_NO: address,
            };
            form.setFieldsValue(paramsFillForm);
            // 营业场所
            onValuesChange(
              {
                'CertInfo.GROUP_LICENCE_ADDRESS': address,
              },
              true
            );
          }

          // 集客企业类型
          if (entType) {
            const paramsFillForm = {
              JK_GROUP_TYPE: entType,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.JK_GROUP_TYPE': entType,
              },
              true
            );
          }

          // 集团年营业额
          if (entInCome) {
            const paramsFillForm = {
              YEAR_GAIN: entInCome,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.YEAR_GAIN': entInCome,
              },
              true
            );
          }

          // 企业联系电话
          if (tel) {
            const paramsFillForm = {
              GROUP_CONTACT_PHONE: tel,
            };
            form.setFieldsValue(paramsFillForm);
            // 企业联系电话
            onValuesChange(
              {
                'BaseInfo.GROUP_CONTACT_PHONE': tel,
              },
              true
            );
          }

          // 法人代表
          if (name) {
            const paramsFillForm = {
              JURISTIC_NAME: name,
            };
            form.setFieldsValue(paramsFillForm);
            // 企业联系电话
            onValuesChange(
              {
                'BaseInfo.JURISTIC_NAME': name,
              },
              true
            );
          } else {
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { JURISTIC_NAME: { disabled: false } },
            });
          }

          // 注册资本
          if (regCap) {
            const paramsFillForm = {
              REG_MONEY: regCap,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.REG_MONEY': regCap,
              },
              true
            );
          } else {
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { REG_MONEY: { disabled: false } },
            });
          }
          if (regCap == null || regCap === '' || typeof regCap === 'undefined') {
            message.warning('提示信息，该企业注册资金为空！');
          }
          // 注册地市
          if (registeredCity) {
            const paramsFillForm = {
              MGMT_DISTRICT: registeredCity,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'RegionInfo.MGMT_DISTRICT': registeredCity,
              },
              true
            );
          }

          // 注册区县
          if (registeredCountry) {
            const paramsFillForm = {
              MGMT_COUNTY: registeredCountry,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'RegionInfo.MGMT_COUNTY': registeredCountry,
              },
              true
            );
          }

          // 政企客户行业门类
          if (buIndustry) {
            const paramsFillForm = {
              buIndustry,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.buIndustry': buIndustry,
              },
              true
            );
          }

          // 政企客户行业类型
          if (buIndustrySub) {
            const paramsFillForm = {
              buIndustrySub,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.buIndustrySub': buIndustrySub,
              },
              true
            );
          }

          // 企业员工数
          if (empNum) {
            const paramsFillForm = {
              ENT_EMPLOYEE_COUNT: empNum,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.ENT_EMPLOYEE_COUNT': empNum,
              },
              true
            );
          }

          // 登记管理部门
          if (regManaDepartment) {
            if (regManaDepartment === '机构编制' || regManaDepartment === '中央军委改革和编制办公室') {
              dispatch({
                type: 'AddGroupCust/updataFormStateAll',
                payload: { buIndustrySub: { disabled: false } },
              });
            }
            const paramsFillForm = {
              REGMANADEPARTMENT: regManaDepartment,
            };
            form.setFieldsValue(paramsFillForm);
            onValuesChange(
              {
                'BaseInfo.REGMANADEPARTMENT': regManaDepartment,
              },
              true
            );
          } else {
            dispatch({
              type: 'AddGroupCust/updataFormStateAll',
              payload: { buIndustrySub: { disabled: false }, REGMANADEPARTMENT: { disabled: false } },
            });
          }
        } else if (resCode === '02') {
          message.warning(resultObject.busiInfo.rspMsg);
          dispatch({
            type: 'AddGroupCust/updataFormStateAll',
            payload: {
              CALLING_AREA_A: { disabled: false },
              CALLING_AREA: { disabled: false },
              CALLING_AREA_INFO: { disabled: false }, // 行业可以输入
              buIndustrySub: { disabled: false }, // 登记管理部门
              REGMANADEPARTMENT: { disabled: false }, // 基本信息 -- 登记管理部门
              GROUP_NAME: { disabled: false }, // 集团名称允许输入
              GROUP_ORG_CODE: { disabled: false }, // 组织机构代码
              GROUP_LICENCE_NO: { disabled: false }, // 证件号码
              JURISTIC_NAME: { disabled: false }, // 法人代表
              REG_MONEY: { disabled: false }, // 注册资本
            },
          });
        } else {
          dispatch({
            type: 'AddGroupCust/updataFormStateAll',
            payload: {
              buIndustrySub: { disabled: false },
              REGMANADEPARTMENT: { disabled: false },
              GROUP_NAME: { disabled: false },
              GROUP_ORG_CODE: { disabled: false },
              GROUP_LICENCE_NO: { disabled: false },
              JURISTIC_NAME: { disabled: false },
              REG_MONEY: { disabled: false },
            },
          });
          message.warning(`企业详细信息查询失败，无符合条件记录。继续建档将提交到OA审批！${reqSerialNo ? `请求流水：${reqSerialNo}` : ''}`);
        }
      } else {
        message.warning(res.resultMsg);
        setSelectLoading(false);
        handleQueryPop();
        setSelectRows([]);
        dispatch({
          type: 'AddGroupCust/updataFormStateAll',
          payload: {
            buIndustrySub: { disabled: false },
            REGMANADEPARTMENT: { disabled: false },
            GROUP_NAME: { disabled: false },
            GROUP_ORG_CODE: { disabled: false },
            GROUP_LICENCE_NO: { disabled: false },
            JURISTIC_NAME: { disabled: false },
            REG_MONEY: { disabled: false },
          },
        });
      }

      // 权限校验
      hasPriv();
    });
  };

  // 查询集团证件经办人
  const getOperatorInfoByIdenNr = grpIdenInfo => {
    queryCmOrderOperatorInfoByOrderId(grpIdenInfo.IDEN_ID).then(res => {
      console.log('查询集团证件经办人res==', res);
      const { resultCode, resultObject } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo) {
        // 获取企业详情
        queryEnterprise(grpIdenInfo);
        const operatorInfo = resultObject?.rspParam?.busiInfo.outData;
        // 更新 [经办信息]
        const paramsForm = {
          AGENT_LICENCE_NO: operatorInfo.OPERATOR_IDEN_NR,
          AGENT_LICENCE_NAME: operatorInfo.OPERATOR_NAME,
          AGENT_LICENCE_ADDRESS: operatorInfo.OPERATOR_ADDRESS,
          AGENT_IMAGE: operatorInfo.PARTY_ID,
        };
        form.setFieldsValue(paramsForm);
        onValuesChange(
          {

            'HandlerInfo.AGENT_LICENCE_NO': operatorInfo.OPERATOR_IDEN_NR,
            'HandlerInfo.AGENT_LICENCE_NAME': operatorInfo.OPERATOR_NAME,
            'HandlerInfo.AGENT_LICENCE_ADDRESS': operatorInfo.OPERATOR_ADDRESS,
            'HandlerInfo.AGENT_IMAGE': operatorInfo.PARTY_ID,

          },
          true
        );
      } else {
        // setConfirmLoading(false);
        message.error('未查询到经办人信息');
      }
    });
  };

  // 获取文件信息接口
  const queryFildValue = async fileId => {
    const params = { fileId };
    const resp = await queryFileVal(params);
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.ret.length !== 0) {
      const fileInfo = resp.resultObject.rspParam.busiInfo.ret[0];
      dispatch({
        type: 'AddGroupCust/updataFormStateAll',
        payload: {
          EcCustLicenceInfo: { disabled: true },
        },
      });
      form.setFieldsValue(
        { EcCustLicenceInfo: [{
          uid: fileInfo.FILE_ID,
          fileId: fileInfo.FILE_ID,
          name: fileInfo.FILE_NAME,
          status: 'done',
        }] });
    }
  };

  // 选择证件回填
  const handleSelect = () => {
    // setConfirmLoading(true);
    const grpIdenInfo = selectRows[0][0];
    const { IDEN_NAME } = grpIdenInfo;

    // 如果有附件 禁用上传附件
    if (grpIdenInfo.SCAN) {
      queryFildValue(grpIdenInfo.SCAN);
    }

    const paramsForm = {
      // EcCustLicenceInfo: grpIdenInfo.SCAN, // 证件附件信息
      GROUP_BUSI_LICENCE_TYPE: grpIdenInfo.IDEN_TYPE_CODE, // 集团证件类型
      GROUP_LICENCE_NO: grpIdenInfo.IDEN_NR, // 集团证件号码
      GROUP_LICENCE_NAME: IDEN_NAME, // 集团证件名称
      GROUP_LICENCE_ADDRESS: grpIdenInfo.IDEN_ADDRESS, // 生产经营具体地址
      GROUP_IDEN_PHOTO: grpIdenInfo.PHOTO, // 集团证件标识
      GROUP_IDEN_EFF_DATE: grpIdenInfo.IDEN_EFF_DATE, // 集团证件生效时间
      GROUP_IDEN_EXP_DATE: grpIdenInfo.IDEN_EXP_DATE, // 集团证件失效时间
      GROUP_COMPARE_STATUS: grpIdenInfo.ENT_RESULT !== '1' ? '0' : '1', // 是否通过在线公司核验
    };
    dispatch({
      type: 'AddGroupCust/updataFormStateAll',
      payload: {
        GROUP_BUSI_LICENCE_TYPE: { disabled: true },
      },
    });

    form.setFieldsValue(paramsForm); // 回填[集团证件信息]
    if (grpIdenInfo.IDEN_TYPE_CODE) {
      dispatch({
        type: 'AddGroupCust/updataFormStateAll',
        payload: {
          GROUP_BUSI_LICENCE_TYPE: { disabled: true },
        },
      });
    }
    // 查询工商标签，打标商客场景 暂时注释
    // dispatch({
    //   type: 'AddGroupCust/callComponentMethod',
    //   payload: {
    //     componentName: 'IndustryInfo',
    //     methodName: 'getBusiLabel',
    //     args: {
    //       idenNr: grpIdenInfo.IDEN_NR,
    //       idenType: grpIdenInfo.IDEN_TYPE_CODE,
    //       idenName: grpIdenInfo.IDEN_NAME,
    //     },
    //   },
    // });
    // 获取证件经办人信息,回填【集团证件信息-证件附件】
    getOperatorInfoByIdenNr(grpIdenInfo);

    // 更新行业门类信息
    // dispatch({
    //   type: 'AddGroupCust/callComponentMethod',
    //   payload: {
    //     componentName: 'IndustryInfo',
    //     methodName: 'getIndustryInfo',
    //     args: {
    //       idenNr: grpIdenInfo.IDEN_NR,
    //       idenType: grpIdenInfo.IDEN_TYPE_CODE,
    //     },
    //   },
    // });
  };

  const selectGrpIden = async () => {
    if (selectRows && selectRows.length === 0) {
      message.warn('请选择一条记录');
      return;
    }
    setSelectLoading(true);
    const { ENT_RESULT: compareStatus, IDEN_NR } = selectRows[0][0];

    // 黑名单校验
    const blackStatus = await checkBlackListByIdenNr(IDEN_NR);
    if (blackStatus === 'true') {
      message.error('该证件号为黑名单号码,无法进行集团客户新建!');
      return;
    }
    // 查询工商标签，打标商客场景
    // getBusiLabel(grpIdenInfo.get('IDEN_NR'), grpIdenInfo.get('IDEN_TYPE_CODE'));

    if (compareStatus !== '1') {
      Modal.confirm({
        // okButtonProps: { loading: true },
        title: '此证件未进行企业致信或企业致信不通过，是否继续？',
        onOk() {
          handleSelect();
        },
        onCancel() {
          setSelectLoading(false);
        },
      });
    } else {
      handleSelect();
    }
  };

  const handleSearch = () => {
    form.validateFields(['idenTypeCode', 'idenNr'], err => {
      if (!err) {
        submit();
      }
    });
  };
  return (
    visible && (
      <Modal
        title="集团证件查询"
        destroyOnClose
        width="80%"
        visible={visible}
        confirmLoading={selectLoading}
        onCancel={onCancel}
        onOk={selectGrpIden}
      >
        <div>
          <Form className="flow fix-label">
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="证件类型">
                  {getFieldDecorator('idenTypeCode', {
                    rules: [{ required: true, message: '证件类型不能为空' }],
                  })(
                    <Select
                      allowClear
                      showSearch
                      filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      placeholder="请选择"
                    >
                      {Object.keys(GROUP_CARD_TYPE).map(key => (
                        <Select.Option key={key} value={key}>
                          {GROUP_CARD_TYPE[key]}
                        </Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="证件号码">
                  {getFieldDecorator('idenNr', {
                    rules: [{ required: true, message: '证件号码不能为空' }],
                  })(<Input allowClear placeholder="请输入" />)}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="单位名称">{getFieldDecorator('idenName')(<Input allowClear placeholder="请输入" />)}</Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={8} offset={16} className="text-right">
                <Button type="primary" onClick={() => handleSearch()} loading={tableProps.loading}>
                  查询
                </Button>
                <Button className="margin-left" onClick={reset}>
                  重置
                </Button>
              </Col>
            </Row>
          </Form>
          <SlickTable
            style={{ marginTop: 20 }}
            pick="radio"
            onSelectRow={row => setSelectRows([row])}
            selectedRows={selectRows}
            rowKey={record => record.IDEN_ID}
            {...restTableProps}
            data={{
              pagination: {
                ...pagination,
                pageSize: 10,
              },
            }}
            columns={[
              {
                title: '证件类型',
                dataIndex: 'IDEN_TYPE_CODE',
                key: 'IDEN_TYPE_CODE',
                render: _ => GROUP_CARD_TYPE?.[_],
              },
              {
                title: '证件号码',
                dataIndex: 'IDEN_NR',
                key: 'idenNr',
              },
              {
                title: '单位名称',
                dataIndex: 'IDEN_NAME',
                key: 'idenName',
                onCell: () => ({
                  style: cellStyle,
                }),
              },
              {
                title: '单位地址',
                dataIndex: 'IDEN_ADDRESS',
                key: 'idenAddress',
                onCell: () => ({
                  style: cellStyle,
                }),
              },
              {
                title: '在线比对核验状态',
                dataIndex: 'COMPARE_STATUS_STR',
                key: 'compareStatusStr',
                render: (text, record) => {
                  if (['200009', '200010'].includes(record.IDEN_TYPE_CODE)) {
                    return text;
                  }
                  return null;
                },
              },
              {
                title: '是否使用',
                dataIndex: 'IS_USED',
                key: 'isUsed',
                render: text => (text === '1' ? '是' : '否'),
              },

              /* {
            title: '附件',
            dataIndex: 'SCAN',
            key: 'scan',
            render: text => (
              <Button type="link" onClick={() => handleDownload}>
                {text}
              </Button>
            ),
          }, */
              /* {
            title: '操作',
            align: 'center',
            key: 'action',
            width: 180,
            fixed: 'right',
            render: (_, record) => {
              const buttons = [
                <a onClick={() => handleViewOper(record)}>查看</a>,
                <a onClick={() => handleModifyOper(record)}>修改</a>,
                <a onClick={() => handleDeleteOper(record)}>删除</a>,
              ];
              return (
                <CustomSpace align="center" wrap split={<Divider type="vertical" />}>
                  {buttons.map(item => item)}
                </CustomSpace>
              );
            },
          }, */
            ]}
          />
        </div>
      </Modal>
    )
  );
};
export default connect(({ AddGroupCust }) => ({
  groupCertList: AddGroupCust.groupCertList,
  formData: AddGroupCust.currentForm,
}))(Form.create()(QueryGroupCert));
