.wrap {
  width: 100%;
  margin-top: 6.5px;
  // padding-bottom: 50px;
  background-color: #fff;

  :global{
    .ant-card-title{
      padding: 0;
    }
    .ant-card-head-wrapper{
      .ant-card-extra{
        .ant-input-search {
          .ant-input{
            width: 372px;
            height: 32px;
          }
        }
      }
    }
    .ant-card-body{
      padding: 16px 0;
    }
    .ant-card-head-title {
      padding-bottom: 0;
    }
    .ant-empty {
      height: 380px;
      padding-top: 150px;
    }
  }

  .nav {
    margin: 0 auto;
    // box-shadow: 0 11px 10px 0 rgba(0, 0, 0, 0.06);

    .menu {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 750px;
      margin: 0;
      padding-left: 0;
      color: #333;
      font-weight: 600;
      font-size: 16px;
      list-style: none;

      li {
        position: relative;
        display: flex;
        align-items: center;
        height: 48px;
        margin-bottom: 0;
        // margin-left: 27px;
        cursor: pointer;
      }

      .currentMenuItem {
        // color: #0382c1;

        &::after {
          position: absolute;
          bottom: 0;
          left: 40%;
          display: block;
          width: 50px;
          height: 3px;
          margin-left: -18px;
          background-color: #36E;
          content: '';
        }
      }
      .hoverCurrentItem {
        // color: #0382c1;

        &::after {
          position: absolute;
          bottom: 0;
          left: 40%;
          display: block;
          width: 50px;
          height: 3px;
          margin-left: -18px;
          background-color: #36E;
          content: '';
        }
      }
    }
  }

  .catName {
    display: inline-block;
    min-width: 70px;
    text-align: center;

  }

  .submenu {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    width: 1200px;
    // min-height: 61px;
    margin-left: 16px;
    // padding-bottom: 40px;
    padding-left: 0;
    color: #333;
    font-size: 14px;
    list-style: none;

    li {
      padding-right: 64px;
      cursor: pointer;
    }

    .subCurrentMenuItem {
      color: #0382c1;
    }
  }

  .offers {
    max-width: 1200px;
    margin: 0 auto 20px;

    :global(.ant-pagination) {
      margin-top: 25px;
      margin-left: 24px;
    }
  }
}

.searchHis {
  :global {
    .ant-popover-content {
      .ant-popover-arrow {
        display: none !important;
      }
      .ant-popover-inner-content {
        padding: 0;
      }
    }
  }
  .historyItem {
    height: 30px;
    padding: 5px 16px;
    cursor: pointer;
    &:hover {
      background-color: #e6f7ff;
    }
  }
}
