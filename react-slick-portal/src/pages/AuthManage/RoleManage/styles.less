.extra {
  display: flex;
  flex-direction: row;
  div:first-child {
    :global {
      .ant-input-group-compact {
        width: 300px;
      }
    }
  }
}
.resize_transfer {
  :global {
    .ant-transfer-list-header > .ant-transfer-list-header-selected {
      span:first-child {
        display: none;
      }
      span:nth-child(2) {
        position: relative;
        // left: 12px;
        right: 0;
      }
    }
  }
}

.systemExtra {
  width: 100%;
  margin-bottom: 8px;
  text-align: right;
  div:first-child {
    display: inline-block;
    width: 216px;
    text-align: center;
    vertical-align: bottom;
  }
  div {
    display: inline-block;
  }
}

.clickRowStyle {
  background-color: #e6f7ff;
}

.relejurdict{
  :global(.ant-result){
    padding: 16px;
  }
}
