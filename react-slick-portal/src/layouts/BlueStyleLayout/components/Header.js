import React, { Component } from 'react';
import { Layout, Icon, Badge, Modal, Divider } from 'antd';
import { formatMessage } from 'umi/locale';
import classNames from 'classnames';
import { connect } from 'dva';
import Link from 'umi/link';
import Trigger from 'rc-trigger';
import Debounce from 'lodash-decorators/debounce';
import SelectLang from '@/components/SelectLang';
import { enableLanguage } from '@/defaultSettings';
import RecentMenuDropdown from './RecentMenuDropdown';
import AllMenu2 from './AllMenu2';
import CollectionMenuDropdown from './CollectionMenuDropdown';
// import SearchIcon from './SearchIcon';
import 'rc-trigger/assets/index.css';
import styles from '../less/header.less';
import UserInfo from './UserInfo';
import AllMenu from './AllMenu';
import avatar from '../img/user-head.png';
import { isEmptyStr } from '@/utils/utils';
import { loginoutResService } from '@/services/common';

const { Header } = Layout;

class HeaderView extends Component {
  constructor(props) {
    super(props);

    this.state = {
      showMenuPanel: false,
      showUserPanel: false,
    };
  }
  // console.log('body',document.body.clientHeight);

  handleLogout = e => {
    e.preventDefault();
    const { dispatch, login: { loginoutResSerList } } = this.props;
    // 手动更新ok按钮loading状态
    // onOk返回promise对象时，只有在resolve()之后才会关闭
    const modal = Modal.confirm({
      title: formatMessage({ id: 'logoutConfirm' }),
      onOk() {
        loginoutResService({ loginoutResSerList });

        modal.update({ okButtonProps: { loading: true } });
        return new Promise(resolve => {
          dispatch({
            type: 'login/logout',
          }).then(() => {
            modal.update({ okButtonProps: { loading: true } });
            resolve();
          });
        }).catch(() => console.log('Oops errors!'));
      },
    });
  };

  toggleSettingDrawer = () => {
    const {
      dispatch,
      setting: { settingDrawerActive },
    } = this.props;

    dispatch({
      type: 'setting/toggleSettingDrawer',
      payload: !settingDrawerActive,
    });
  };

  toggleMenuDrawer = () => {
    const {
      dispatch,
      setting: { menuDrawerActive },
    } = this.props;
    dispatch({
      type: 'setting/toggleMenuDrawer',
      payload: !menuDrawerActive,
    });
    this.triggerResizeEvent();
  };

  hideAllMenu = isVisible => {
    if (isVisible) {
      this.setState({ showMenuPanel: true }, () => {
        document.querySelector('body').classList.add('noScroll');
      });
    } else {
      this.setState({ showMenuPanel: false }, () => {
        document.querySelector('body').classList.remove('noScroll');
      });
    }
  };

  showUserInfo = isVisible => {
    if (isVisible) {
      this.setState({ showUserPanel: true });
    } else {
      this.setState({ showUserPanel: false });
    }
  };

  handleLock = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'login/lock',
    });
  };

  // 图片无法加载时，使用默认图片
  imgExists = e => {
    // 默认图片
    const imgUrl = avatar;
    if (imgUrl) {
      e.target.src = imgUrl;
    }
  };

  // 300毫秒是侧边栏menu-drawer动画的过渡时间，保证在动画结束后触发
  @Debounce(300)
  // eslint-disable-next-line class-methods-use-this
  triggerResizeEvent() {
    const event = document.createEvent('HTMLEvents');
    event.initEvent('resize', true, false);
    window.dispatchEvent(event);
  }

  render() {
    const {
      login: {
        user: {
          userInfo: { userName, userPhoto },
          userCode,
        },
      },
      setting: { showLeftMenu },
      unRead,
    } = this.props;

    return (
      <Header id="layout-header" className={classNames(styles.wrapper, styles.wrapperlight)}>
        <div className="media-box">
          <div className="media-left">
            {/* 侧边栏开关 */}
            {/* {showLeftMenu && (
              <span className={styles.actionblue} onClick={this.toggleMenuDrawer}>
                <Icon type="align-left" style={{ color: '#141828' }} />
              </span>
            )} */}
            {/* 全部菜单 */}
            <Trigger
              popupPlacement="bottomLeft"
              action={['hover']}
              // action={['click']}
              popupAlign={{
                overflow: {
                  adjustX: 1,
                  adjustY: 1,
                },
              }}
              // getPopupContainer={() => document.getElementById('layout-header')}
              mouseEnterDelay={0.3}
              popupClassName={styles.allMenuDropdown}
              builtinPlacements={{
                bottomLeft: {
                  points: ['tl', 'bl'],
                },
              }}
              popupVisible={this.state.showMenuPanel}
              popupTransitionName="slide-left"
              popup={<AllMenu hideAllMenu={this.hideAllMenu} />}
              onPopupVisibleChange={isVisible => this.hideAllMenu(isVisible)}
              onPopupAlign={() => {
                // $(popupDomNode).css({
                //   left: '0',
                //   top: '60px',
                //   right: '0',
                //   position: 'fixed',
                // })
                // 防止被layout-tabs-nav遮挡
                document.querySelector(
                  '.rc-trigger-popup'
                ).parentNode.parentNode.style.zIndex = 1000;
              }}
            >
              <span
                className={classNames(styles.allMenu, styles.actionblue, {
                  [styles.active]: this.state.showMenuPanel,
                })}
              >
                <span className={classNames(styles.menuimg, styles.actionblue)}>
                  <span className={classNames(styles.menuicon, styles.actionblue)} />
                </span>
                <span className={classNames(styles.fontMenu, styles.actionblue)}>菜单</span>
              </span>
            </Trigger>
            {/* {!showLeftMenu && (
              <>
                <CollectionMenuDropdown />
                <RecentMenuDropdown />
              </>
            )} */}
            <Divider type="vertical" />
            {/* 返回首页 */}
            <Link to="/" className={classNames(styles.main, styles.actionblue)}>
              <span className={classNames(styles.home, styles.actionblue)} />
            </Link>
            <Link
              to="/"
              className={classNames(styles.fontHome, styles.actionblue)}
              style={{ color: '#141828' }}
            >
              {userCode}的工作台
            </Link>
            {/* logo */}
            {/* <span className={classNames(styles.logo, styles.actionblue)} /> */}
            {/* title */}
            {/* <span className={classNames(styles.title, styles.actionblue)}>
              {formatMessage({ id: 'app.title' })}
            </span> */}
          </div>
          <div className="media-right">
            {/* 快捷搜索 */}
            {/* <SearchIcon /> */}
            {/* 系统消息 */}
            <Link to="/notice" className={classNames(styles.notice, styles.actionblue)}>
              {unRead ? (
                <Badge count={unRead} style={{ transform: 'translate(80%, -60%)' }}>
                  <Icon type="message" style={{ color: '#141828' }} />
                </Badge>
              ) : (
                <Icon type="message" style={{ color: '#141828' }} />
              )}
            </Link>
            {/* 退出 */}
            <span
              onClick={this.handleLogout}
              className={classNames(styles.logout, styles.actionblue)}
            >
              <Icon type="poweroff" style={{ color: '#141828' }} />
            </span>
            {/* 用户面板 */}
            <Trigger
              popupPlacement="bottomLeft"
              action={['hover']}
              popupAlign={{
                overflow: {
                  adjustX: 1,
                  adjustY: 1,
                },
              }}
              mouseEnterDelay={0.3}
              popupClassName={styles.allMenuDropdown}
              builtinPlacements={{
                bottomLeft: {
                  points: ['tl', 'bl'],
                },
              }}
              popupTransitionName="slide-up"
              popup={<UserInfo showUserInfo={this.showUserInfo} />}
              popupVisible={this.state.showUserPanel}
              onPopupVisibleChange={isVisible => this.showUserInfo(isVisible)}
            >
              <div
                className={classNames(styles.avatar, styles.actionblue, {
                  [styles.active]: this.state.showUserPanel,
                })}
              >
                <img
                  alt="avatar"
                  src={isEmptyStr(userPhoto) ? '' : `portal/FileStoreController/dfsReadImage.do?docLink=${userPhoto}`}
                  onError={this.imgExists.bind(this)}
                  height="24"
                  width="24"
                />
                <span className="margin-left-sm" style={{ color: '#141828' }}>
                  {userName}
                </span>
                <Icon type="down" style={{ color: '#141828' }} />
              </div>
            </Trigger>

            {/* 切换语言 */}
            {enableLanguage ? <SelectLang className={classNames(styles.actionblue)} /> : null}

            {/* 锁屏 */}
            {/* <span onClick={this.handleLock} className={classNames(styles.actionblue)}>
            <Icon type="lock" />
          </span> */}
            {/* 设置面板开关 */}
            <span className={styles.actionblue} onClick={this.toggleSettingDrawer}>
              <Icon type="align-right" style={{ color: '#141828' }} />
            </span>
          </div>
          {/* <div className="media-body">
            <AllMenu2 hideAllMenu={this.hideAllMenu} />
          </div> */}
        </div>
      </Header>
    );
  }
}

export default connect(({ login, setting, notice }) => ({
  login,
  setting,
  unRead: notice.unRead,
}))(HeaderView);
