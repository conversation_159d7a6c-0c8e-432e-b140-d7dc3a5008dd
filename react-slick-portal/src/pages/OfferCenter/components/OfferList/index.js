/* eslint-disable global-require */
import { Button, message } from 'antd';
import React, { useState, useEffect } from 'react';
import router from 'umi/router';
import { connect } from 'dva';
import { getOfferOrderUrlById } from '@/services/offer';
import { collectOfferByIds, cancelCollectOfferByIds } from '@/services/collection';
import style from './OfferList.module.less';

const OfferItem = props => {
  const { data, curSearchKey, dispatch, isSearch, getTableData } = props;

  const collectOffer = async () => {
    const res = await collectOfferByIds([data.offerId]);
    if (res.resultCode === 'FALSE') {
      message.warning(res.resultMsg);
    } else {
      message.success('已收藏');
      if (isSearch) {
        dispatch({
          type: 'offer/getProdListByKey',
          payload: curSearchKey,
        });
      }
      if (getTableData) {
        getTableData();
      }
      dispatch({
        type: 'offer/getOfferCollectList'
      });
    }
  }

  const getImgUrl = () => {
    if (data.files) {
      if (data.files.length > 0) {
        const file = data.files.filter(item => (item.docSubType === '1'));
        return file[0]?.docNbr;
      };
    };
    if (data.imageDatas) {
      if (data.imageDatas.length > 0) {
        const file = data.imageDatas.filter(item => (item.docSubType === '1'));
        return file[0]?.docNbr;
      };
    };
    return null;
  };

  const cancelCollectOffer = async () => {
    const res = await cancelCollectOfferByIds([data.offerId]);
    if (!res.error) {
      message.success('已取消收藏');
      if (isSearch) {
        dispatch({
          type: 'offer/getProdListByKey',
          payload: curSearchKey,
        })
      }
      if (getTableData) {
        getTableData();
      }
      dispatch({
        type: 'offer/getOfferCollectList'
      });
    }
  }

  return (
    <div className={style.offerItem}>
      <div className={style.itemImg}>
        <img
          src={`portal/FileStoreController/download.do?docNbr=${getImgUrl()}`}
          onClick={() => {
            router.push(`/offerCenter/detail/${data.offerId}`);
          }}
          alt=""
          className={style.itemImage}
        />
      </div>

      <div className={style.content}>
        <span className={style.title}>
          <span
            onClick={() => {
              router.push(`/offerCenter/detail/${data.offerId}`);
            }}
            className={style.itemTitle}
          >
            {data.offerName}
          </span>
          {
            data.collectionFlag === 1 || data.collect === '1' ? (
              <img
                src={require('./img/sc_ac.png')}
                alt=""
                className={style.collect}
                onClick={cancelCollectOffer}
              />
            ) : (
              <img
                src={require('./img/uncollected.png')}
                alt=""
                className={style.collect}
                onClick={collectOffer}
              />
            )
          }
        </span>
        <span
          className={style.desc}
          onClick={() => {
            router.push(`/offerCenter/detail/${data.offerId}`);
          }}
        >
          {data.offerDesc}
        </span>
      </div>
      <div className={style.orderBtn}>
        <Button
          type="primary"
          onClick={e => {
            e.stopPropagation();
            getOfferOrderUrlById(data.offerId);
          }}
        >
          <img src={require(`./img/shop_car.png`)} alt="购物" style={{ marginTop: '-1px', marginRight: '6px' }} />
          立即订购
        </Button>
      </div>
    </div>
  );
};

const OfferList = props => {
  const { list, curSearchKey, dispatch, isSearch, getTableData } = props;
  return (
    <div className={style.container}>
      <div>
        <ul className={style.offerList}>
          {
            list && list.map((item, index) =>
              <OfferItem
                data={item}
                curSearchKey={curSearchKey}
                dispatch={dispatch}
                isSearch={isSearch}
                getTableData={getTableData}
                // eslint-disable-next-line react/no-array-index-key
                key={index}
              />)
          }
        </ul>
      </div>
    </div>
  );
};

export default connect(({ offer }) => ({
  curSearchKey: offer.curSearchKey,
}))(OfferList);
