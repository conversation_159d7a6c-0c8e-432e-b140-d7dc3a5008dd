import { useEffect, useState } from 'react';
import { getIndustryList, getSubInsdustryList, queryCommonRegion, getRegisterAreaInfo } from '../../services';
import { LIBRARYS_TYPE } from '@/pages/Librarys/const';

export default function useLibAdd({ type }) {
  const { librarys = {}, login } = window.g_app._store.getState();

  // 区域信息
  const [regionList, setCurrentRegionList] = useState([]);

  // 行业信息
  const [industryList, setIndustryList] = useState([]);
  const [firstIndustryList, setFirstIndustryList] = useState([]);
  const [subIndustryList, setSubIndustryList] = useState([]);

  // 注册省、城市、县区信息
  const [registerProvinceList, setRegisterProvinceList] = useState([]);
  const [registerCityList, setRegisterCityList] = useState([]);
  const [registerAreaList, setRegisterAreaList] = useState([]);

  const getRegisterAreaNameByVal = val =>
    registerProvinceList.find(item => item.value === val)?.label ??
    registerCityList.find(item => item.value === val)?.label ??
    registerAreaList.find(item => item.value === val)?.label;

  const initData = async () => {
    const isIndustry = [
      LIBRARYS_TYPE.PLAN,
      LIBRARYS_TYPE.CASE,
      LIBRARYS_TYPE.BID,
      LIBRARYS_TYPE.PROD,
      LIBRARYS_TYPE.COOP,
      LIBRARYS_TYPE.HARDWARE,
      LIBRARYS_TYPE.EXPERIENCE].includes(type);
    if (isIndustry) {
      const res = await getIndustryList();
      if (Array.isArray(res)) {
        setIndustryList(res);
      }
    }

    if (type === LIBRARYS_TYPE.COOP) {
      const list = await getRegisterAreaInfo();
      setRegisterProvinceList(list);
    }
    // 注册省信息
  };

  useEffect(() => {
    initData();
  }, [type]);

  /**
   * 城市变更
   * @param {城市id} pId
   */
  const onChangeCity = async pId => {
    const res = await queryCommonRegion({
      regionLevel: '3',
      parRegionId: pId,
    });
    setCurrentRegionList(res);
  };

  /**
   * 修改行业类型
   * @param {行业id} industryId
   */
  const onChangeIndustry = async industryId => {
    const subRes = await getSubInsdustryList({
      stype: industryId,
    });
    if (Array.isArray(subRes)) {
      setFirstIndustryList(subRes);
    }
  };

  /**
   * 修改一级行业类型
   * @param {一级行业id} industryFirstId
   */
  const onChangeIndustryFirst = async industryFirstId => {
    const subRes = await getSubInsdustryList({
      stype: industryFirstId,
    });
    if (Array.isArray(subRes)) {
      setSubIndustryList(subRes);
    }
  };

  /**
   * 修改注册地市
   * @param {地市} code
   */
  const onChangeRegisterCity = async code => {
    const res = await getRegisterAreaInfo(code);
    setRegisterAreaList(res);
  };

  /**
   * 修改注册省
   * @param {县区} code
   */
  const onChangeRegisterProvince = async code => {
    const res = await getRegisterAreaInfo(code);
    setRegisterCityList(res);
    setRegisterAreaList([]);
  };

  const initCityListByProvince = async val => {
    if (registerCityList.length > 0) {
      return;
    }
    const res = await getRegisterAreaInfo(val);
    setRegisterCityList(res);
  };

  const initAreaListByCity = async val => {
    if (registerAreaList.length > 0) {
      return;
    }
    const res = await getRegisterAreaInfo(val);
    setRegisterAreaList(res);
  };

  return {
    cityList: librarys.cityList,
    attrs: {
      ...librarys.attrs,
    },
    onChangeCity,
    onChangeIndustry,
    onChangeIndustryFirst,
    onChangeRegisterCity,
    onChangeRegisterProvince,
    initCityListByProvince,
    initAreaListByCity,
    getRegisterAreaNameByVal,
    regionList,
    industryList,
    firstIndustryList,
    subIndustryList,
    registerProvinceList,
    registerCityList,
    registerAreaList,
    userInfo: login?.user?.userInfo || {},
  };
}
