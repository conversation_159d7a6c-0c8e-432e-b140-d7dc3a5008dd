/* eslint-disable no-console */
import React, { useState, useEffect } from 'react';
import { Button, Input, message, Modal } from 'antd';
import { connect } from 'dva';
import { getDataDictByCode } from '@/services/common';
import style from './index.less';
import request from '@/utils/request';
import { initParamsApi } from './services';


const TakePhoto = props => {
  const {
    value,
    onCallback,
    certType,
    idenNbr,
    disabled,
    customStyle,
    fillBack,
    viewMode,
  } = props;

  const [visible, setVisible] = useState(false);
  const [iframeUrl, setIframeUrl] = useState('');
  const [scanUrl, setScanUrl] = useState('');
  const [phoneInfo, setPhoneInfo] = useState(null);
  const getScanUrl = async () => {
    const res = await getDataDictByCode({
      groupCode: 'BUSINESS_SYSTEM_URL',
      paramCode: 'GROUP_CERT_FHOTO',
    });

    if (res) {
      setScanUrl(res.paramValue);
      // setScanUrl('https://10.220.67.133:29600/nm/bp421.go?method=groupCertFhotoInit');
    }
  };

  const initParams = async () => {
    const resp = await initParamsApi();
    if (resp.resultCode === 'TRUE' && resp.resultObject) {
      return resp.resultObject;
    }
    return false;
  };
  useEffect(() => {
    getScanUrl();
  }, []);

  // 点击拍照按钮
  const handleTakePhoto = async () => {
    // 00 集团证件 04 经办人人像  01 授权委托书
    if (['00', '04', '01'].includes(certType) && !idenNbr) {
      message.error('请输入证件号码!');
      return;
    }
    const backInfo = await initParams();


    if (!backInfo) {
      message.error('打开拍照失败');
    }
    const timeStampNow = new Date().getTime();
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const params = {
      workNo: backInfo.OP_ID,
      workName: backInfo.OP_NAME,
      opDate: `${year}${month}${day}`,
      groupCertNo: idenNbr,
      certType,
      timeStamp: timeStampNow,
      groupId: backInfo.ORG_ID,
      groupName: backInfo.ORG_NAME,
      groupSerialNo: '',
    };
    setPhoneInfo(params);
    if (!scanUrl) {
      message.error('拍照地址获取异常，请关闭重试');
      return;
    }
    // console.log('拍照地址', `${new URLSearchParams(JSON.stringify(params))}&localization=Y`);

    const phoneUrl = `${scanUrl}&jsonStr=${encodeURIComponent(JSON.stringify(params))}&localization=Y`;
    setIframeUrl(phoneUrl);
    setVisible(true);
  };

  // 拍照成功后回调回填值
  const handleSuccess = () => {
    // console.log('获取拍照照片参数', phoneInfo);
    request('portal/NoPaperInfoController/getGrpIdenImageData.do', {
      method: 'POST',
      data: {
        ...phoneInfo,
        timeStamp: `${phoneInfo.timeStamp}`,
      },
    }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      console.log('paiwanzhao', fillBack, resultObject);

      if (resultCode === 'TRUE') {
        // 回填授权书
        if (fillBack === 'sqwts') {
          // console.log('授权委托书回填', resultObject);
          onCallback({ FILE_ID: resultObject?.FILE_ID });
        }

        // 回填经办人身份证
        if (fillBack === 'jbrpz') {
          // console.log('经办人拍照回填', resultObject);
          onCallback({ FILE_ID: resultObject?.FILE_ID });
        }
        if (fillBack === 'addZjpz') {
          // console.log('新增集团证件拍照回填', resultObject);
          onCallback({ FILE_ID: resultObject?.FILE_ID });
        }
        if (fillBack === 'addJbrpz') {
          // console.log('新增集团经办人拍照', resultObject);
          onCallback({ FILE_ID: resultObject?.FILE_ID });
        }
        setVisible(false);
      } else {
        message.error(resultMsg);
      }
    });
  };
  return (
    <div className={style.inputContainer}>
      <Input
        style={customStyle}
        readOnly
        value={value}
        addonAfter={
          !disabled && (
            <Button type="primary" onClick={() => handleTakePhoto()}>
              拍照
            </Button>
          )
        }
        disabled={viewMode === 'view'}
      />
      <Modal title="拍照" visible={visible} onCancel={() => setVisible(false)} onOk={handleSuccess} width="800px">
        <iframe src={iframeUrl} height="600px" width="752px" title="iframe" />
      </Modal>
    </div>
  );
};
export default connect(({ login }) => ({
  user: login.user,
}))(TakePhoto);
