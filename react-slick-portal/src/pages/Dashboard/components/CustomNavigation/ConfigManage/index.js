import React, { useState } from 'react';
import { Tabs } from 'antd';
import UniversalConfig from './components/UniversalConfig';
// import SceneConfig from './components/SceneConfig';
import styles from './configManage.less';

const ConfigManage = () => {
  const [activeKey, setActiveKey] = useState('universal');

  const tabsData = [
        {
          label: '通用导航',
          key: 'universal',
          children: <UniversalConfig />,
        },
        // {
        //   label: '场景专区',
        //   key: 'scene',
        //   children: <SceneConfig />,
        // },
  ];

  return (
    <div className={styles.configManage}>
      <Tabs
        activeKey={activeKey}
        type="card"
        tabBarGutter={8}
        onChange={key => setActiveKey(key)}
        tabBarStyle={{ marginBottom: 0 }}
      >
        {
          tabsData.map(item => (
            <Tabs.TabPane tab={item.label} key={item.key}>
              {item.children}
            </Tabs.TabPane>
          ))
        }
      </Tabs>
    </div>

  );
};
export default ConfigManage;
