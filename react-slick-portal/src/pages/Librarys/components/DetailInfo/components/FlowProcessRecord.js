import React, { useEffect, useState } from 'react';
import { Card, Table } from 'antd';
import { getKnowledgeOrderList } from '../../../services';

const FlowProcessRecord = ({ knowledgeInfoId }) => {
  const [recordList, setRecordList] = useState([]);
  const getTableData = async () => {
    if (knowledgeInfoId) {
      const res = await getKnowledgeOrderList({ knowledgeInfoId });
      setRecordList(res);
    }
  };

  useEffect(() => {
    getTableData();
  }, [knowledgeInfoId]);

  const columns = [
    {
      title: '流程环节',
      dataIndex: 'traceName',
      render: text => text ?? '-',
    },
    {
      title: '处理人',
      dataIndex: 'userName',
      render: text => text ?? '-',
    },
    {
      title: '联系电话',
      dataIndex: 'userPhone',
      render: text => text ?? '-',
    },
    {
      title: '处理时间',
      dataIndex: 'updatedTime',
      render: text => text ?? '-',
    },
    {
      title: '处理状态',
      dataIndex: 'stateName',
      render: text => text ?? '-',
    },
    {
      title: '处理结果',
      dataIndex: 'auditStateName',
      render: text => text ?? '-',
    },
    {
      title: '处理意见',
      dataIndex: 'remark',
      render: text => text ?? '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdTime',
      render: text => text ?? '-',
    },
  ];

  return (
    <Card title="处理记录">
      <Table pagination={false} columns={columns} dataSource={recordList} />
    </Card>
  );
};

export default FlowProcessRecord;
