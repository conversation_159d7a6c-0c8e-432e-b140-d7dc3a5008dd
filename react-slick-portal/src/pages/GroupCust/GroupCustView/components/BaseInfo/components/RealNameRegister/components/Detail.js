import React from 'react';
import { But<PERSON>, Card, Descriptions } from 'antd';
import { connect } from 'dva';

const Detail = props => {
  const {
    detail,
  } = props;

  /**
   * 返回
   */
  const goBack = () => {
    props.goToStep(1);
  };
  const handleDownloadFile = fileId => {
    const url = `portal/GroupFileController/fileDownload.do?fileId=${fileId}`;
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.target = '_blank';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };
  const renderCompare = e => {
    const peopleFace = JSON.parse(e);
    if (!e) {
      return (
        <Descriptions.Item label="人像比对结果">-</Descriptions.Item>
      );
    }
    if (detail?.COMPARE_RESULT === '0') {
      return (
        <Descriptions.Item label="人像比对结果">未人脸比对</Descriptions.Item>
      );
    }
    if (peopleFace?.MESSAGE) {
      return (
        <Descriptions.Item label="人像比对结果">{peopleFace?.MESSAGE}</Descriptions.Item>
      );
    }
    return <Descriptions.Item label="人像比对结果">-</Descriptions.Item>;
  };

  return (
    <Card title="详细信息" className="cute" extra={<Button onClick={goBack}>返回</Button>}>
      <Descriptions className="cute" column={{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }}>
        <Descriptions.Item label="集团编号">{detail?.GROUP_ID || '-'}</Descriptions.Item>
        <Descriptions.Item label="集团名称">{detail?.GROUP_NAME || '-'}</Descriptions.Item>
        <Descriptions.Item label="数据类型">{detail?.DATA_TYPE === 1 ? '经办人' : '责任人'}</Descriptions.Item>
        <Descriptions.Item label="姓名">{detail?.NAME || '-'}</Descriptions.Item>
        <Descriptions.Item label="证件类型">{detail?.CERT_TYPE || '-'}</Descriptions.Item>
        <Descriptions.Item label="证件编号">{detail?.CERT_NO || '-'}</Descriptions.Item>
        <Descriptions.Item label="证件地址">{detail?.CERT_ADDRESS || '-'}</Descriptions.Item>
        <Descriptions.Item label="EMAIL">{detail?.EMAIL || '-'}</Descriptions.Item>
        <Descriptions.Item label="联系电话">{detail?.BILL_ID || '-'}</Descriptions.Item>
        <Descriptions.Item label="生效时间">{detail?.VALID_DATE || '-'}</Descriptions.Item>
        <Descriptions.Item label="失效时间">{detail?.EXPIRE_DATE || '-'}</Descriptions.Item>
        {/* <Descriptions.Item label="人像比对结果">{detail?.COMPARE_RESULT || '-'}</Descriptions.Item> */}
        {renderCompare(detail?.COMPARE_RESULT)}
        <Descriptions.Item label="授权书">
          <span style={{ color: 'blue', cursor: 'pointer' }} onClick={() => handleDownloadFile(detail?.AUTHORIZATION_LETTER)}>
            {detail?.AUTHORIZATION_LETTER || '-'}
          </span>
        </Descriptions.Item>
        <Descriptions.Item label="无纸化信息留存查询标识">{detail?.PROTOCOL || '-'}</Descriptions.Item>
        <Descriptions.Item label="录入说明">{detail?.INPUT_DESC || '-'}</Descriptions.Item>
        <Descriptions.Item label="备注">{detail?.REMARKS || '-'}</Descriptions.Item>
        <Descriptions.Item label="无纸化回调结果">{detail?.EXT1 || '-'}</Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default connect(({ setting }) => ({
  size: setting.size,
}))(Detail);
