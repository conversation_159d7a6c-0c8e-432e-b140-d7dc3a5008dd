import React, { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Card } from 'antd';
import { leftMenu } from '@/pages/GroupCust/GroupCustView/const';
import style from './index.less';

const Right = props => {
  const { currentLeftMenu, isSelectGroup } = props;
  const [height, setHeight] = useState('');

  // 监听窗口变化
  useEffect(() => {
    setHeight(props.size.height);
  }, [props.size.height]);

  // 根据路径查找对应的组件
  const findComponent = (menuItems, path) => {
    if (!path) return null;

    const keys = path.split('/');
    const targetKey = keys[keys.length - 1]; // 获取最后一个key作为目标key

    const findInMenu = items => {
      // eslint-disable-next-line no-restricted-syntax
      for (const item of items) {
        if (item.key === targetKey && item.component) {
          return item.component;
        }
        if (item.children) {
          const found = findInMenu(item.children);
          if (found) return found;
        }
      }
      return null;
    };

    return findInMenu(menuItems);
  };

  const SelectedComponent = findComponent(leftMenu, currentLeftMenu);

  // 获取当前选中的菜单名称
  const getCurrentMenuName = (menuItems, path) => {
    if (!path) return '';

    const keys = path.split('/');
    const targetKey = keys[keys.length - 1];

    const findName = items => {
      // eslint-disable-next-line no-restricted-syntax
      for (const item of items) {
        if (item.key === targetKey) {
          return item.name;
        }
        if (item.children) {
          const found = findName(item.children);
          if (found) return found;
        }
      }
      return '';
    };

    return findName(menuItems);
  };

  const currentMenuName = getCurrentMenuName(leftMenu, currentLeftMenu);

  return (
    <div className={style.right} id="right" style={{ maxHeight: height }}>
      {!isSelectGroup && SelectedComponent && (
        <Card
          className={style.card}
          title={currentMenuName}
          bordered={false}
          bodyStyle={{ padding: '24px' }}
        >
          <SelectedComponent />
        </Card>
      )}
    </div>
  );
};

export default connect(({ setting, groupCustView }) => ({
  size: setting.size,
  isSelectGroup: groupCustView.isSelectGroup,
  ...groupCustView,
}))(Right);
