/* eslint-disable no-console */
/* eslint-disable no-undef */
import React, { useState } from 'react';
import moment from 'moment';
import nzh from 'nzh/cn';
import { parse, stringify } from 'qs';
import CryptoJS from 'crypto-js';
import { Modal, Icon, message } from 'antd';
import pick from 'lodash/pick';
import { SESSION_PREFIX } from '@/defaultSettings';
import request from '@/utils/request';

const _storageMode = ['sessionStorage', 'localStorage'];

export function fixedZero(val) {
  return val * 1 < 10 ? `0${val}` : val;
}

export function getTimeDistance(type) {
  const now = new Date();
  const oneDay = 1000 * 60 * 60 * 24;

  if (type === 'today') {
    now.setHours(0);
    now.setMinutes(0);
    now.setSeconds(0);
    return [moment(now), moment(now.getTime() + (oneDay - 1000))];
  }

  if (type === 'week') {
    let day = now.getDay();
    now.setHours(0);
    now.setMinutes(0);
    now.setSeconds(0);

    if (day === 0) {
      day = 6;
    } else {
      day -= 1;
    }

    const beginTime = now.getTime() - day * oneDay;

    return [moment(beginTime), moment(beginTime + (7 * oneDay - 1000))];
  }

  if (type === 'month') {
    const year = now.getFullYear();
    const month = now.getMonth();
    const nextDate = moment(now).add(1, 'months');
    const nextYear = nextDate.year();
    const nextMonth = nextDate.month();

    return [
      moment(`${year}-${fixedZero(month + 1)}-01 00:00:00`),
      moment(moment(`${nextYear}-${fixedZero(nextMonth + 1)}-01 00:00:00`).valueOf() - 1000),
    ];
  }

  const year = now.getFullYear();
  return [moment(`${year}-01-01 00:00:00`), moment(`${year}-12-31 23:59:59`)];
}

export function getPlainNode(nodeList, parentPath = '') {
  const arr = [];
  nodeList.forEach(node => {
    const item = node;
    item.path = `${parentPath}/${item.path || ''}`.replace(/\/+/g, '/');
    item.exact = true;
    if (item.children && !item.component) {
      arr.push(...getPlainNode(item.children, item.path));
    } else {
      if (item.children && item.component) {
        item.exact = false;
      }
      arr.push(item);
    }
  });
  return arr;
}

export function digitUppercase(n) {
  return nzh.toMoney(n);
}

function getRelation(str1, str2) {
  if (str1 === str2) {
    console.warn('Two path are equal!'); // eslint-disable-line
  }
  const arr1 = str1.split('/');
  const arr2 = str2.split('/');
  if (arr2.every((item, index) => item === arr1[index])) {
    return 1;
  }
  if (arr1.every((item, index) => item === arr2[index])) {
    return 2;
  }
  return 3;
}

function getRenderArr(routes) {
  let renderArr = [];
  renderArr.push(routes[0]);
  for (let i = 1; i < routes.length; i += 1) {
    // 去重
    renderArr = renderArr.filter(item => getRelation(item, routes[i]) !== 1);
    // 是否包含
    const isAdd = renderArr.every(item => getRelation(item, routes[i]) === 3);
    if (isAdd) {
      renderArr.push(routes[i]);
    }
  }
  return renderArr;
}

/**
 * Get router routing configuration
 * { path:{name,...param}}=>Array<{name,path ...param}>
 * @param {string} path
 * @param {routerData} routerData
 */
export function getRoutes(path, routerData) {
  let routes = Object.keys(routerData).filter(routePath => routePath.indexOf(path) === 0 && routePath !== path);
  // Replace path to '' eg. path='user' /user/name => name
  routes = routes.map(item => item.replace(path, ''));
  // Get the route to be rendered to remove the deep rendering
  const renderArr = getRenderArr(routes);
  // Conversion and stitching parameters
  const renderRoutes = renderArr.map(item => {
    const exact = !routes.some(route => route !== item && getRelation(route, item) === 1);
    return {
      exact,
      ...routerData[`${path}${item}`],
      key: `${path}${item}`,
      path: `${path}${item}`,
    };
  });
  return renderRoutes;
}

export function getPageQuery() {
  return parse(window.location.href.split('?')[1]);
}

export function getQueryPath(path = '', query = {}) {
  const search = stringify(query);
  if (search.length) {
    return `${path}?${search}`;
  }
  return path;
}

/* eslint no-useless-escape:0 */
const reg = /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;

export function isUrl(path) {
  return reg.test(path);
}

export function formatWan(val) {
  const v = val * 1;
  if (!v || Number.isNaN(v)) return '';

  let result = val;
  if (val > 10000) {
    result = Math.floor(val / 10000);
    result = (
      <span>
        {result}
        <span
          style={{
            position: 'relative',
            top: -2,
            fontSize: 14,
            fontStyle: 'normal',
            lineHeight: 20,
            marginLeft: 2,
          }}
        >
          万
        </span>
      </span>
    );
  }
  return result;
}

export function isJSON(str) {
  if (typeof str === 'string') {
    try {
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  }
  return false;
}

/**
 * IE浏览器检测
 *
 * 返回值     值类型    值说明
 *    -1      Number    不是ie浏览器
 *    6       Number    ie版本<=6
 *    7       Number    ie7
 *    8       Number    ie8
 *    9       Number    ie9
 *   10       Number    ie10
 *   11       Number    ie11
 *  'edge'    String    ie的edge浏览器
 */
export function IEVersion() {
  const { userAgent } = navigator; // 取得浏览器的userAgent字符串
  const isIE = userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1; // 判断是否IE<11浏览器
  const isEdge = userAgent.indexOf('Edge') > -1 && !isIE; // 判断是否IE的Edge浏览器
  const isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf('rv:11.0') > -1;
  if (isIE) {
    const fIEVersion = userAgent.match(/MSIE (\d+)/)[1];
    if (fIEVersion === '7') {
      return 7;
    }
    if (fIEVersion === '8') {
      return 8;
    }
    if (fIEVersion === '9') {
      return 9;
    }
    if (fIEVersion === '10') {
      return 10;
    }
    return 6; // IE版本<=7
  }
  if (isEdge) {
    return 'edge'; // edge
  }
  if (isIE11) {
    return 11; // IE11
  }
  return -1; // 不是ie浏览器
}

const _fixPlaceholderForIE = () => {
  const isIE8 = IEVersion() === 8;
  const isIE9 = IEVersion() === 9;
  // fix html5 placeholder attribute for ie7 & ie8
  if (isIE8 || isIE9) {
    // ie8 & ie9
    // this is html5 placeholder fix for inputs, inputs with placeholder-no-fix class will be skipped(e.g: we need this for password fields)
    $('input[placeholder]:not(.placeholder-no-fix), textarea[placeholder]:not(.placeholder-no-fix)').each(() => {
      const input = $(this);

      if (input.val() === '' && input.attr('placeholder') !== '') {
        input.addClass('placeholder').val(input.attr('placeholder'));
      }

      input.focus(() => {
        if (input.val() === input.attr('placeholder')) {
          input.val('');
        }
      });

      input.blur(() => {
        if (input.val() === '' || input.val() === input.attr('placeholder')) {
          input.val(input.attr('placeholder'));
        }
      });
    });
  }
};

// Fix input placeholder issue for IE8 and IE9
export function fixPlaceholderForIE() {
  setTimeout(() => {
    _fixPlaceholderForIE();
  }, 600);
}

/**
 * 从 www.abc.com?id=1&name=soon 字符串中提取 对应key的值
 * @ paramName {String}
 * @ searchString {String}
 */
export function getURLParameter(paramName, searchString) {
  const str = searchString.split('?')[1];
  let val;
  const params = str.split('&');

  for (let i = 0; i < params.length; i += 1) {
    val = params[i].split('=');
    if (val[0] === paramName) {
      return decodeURIComponent(val[1]);
    }
  }
  return null;
}

/**
 * 将URL中query部分(即?后面的)的参数转化为对象，重复的参数作为数组。
 * 如果？后面没有参数返回空对象{}
 *
 *  样例一：
 *  parseQuery('http:www.baidu.com/index?name=username&city=%E5%8C%97%E4%BA%AC&id=123&id=456#flag=66')
 *  输出：
 *  {
 *   name: 'username',
 *   city: '北京', // 中文需解码
 *   id: [ 123, 456 ], // 重复出现的 key 要组装成数组，能被转成数字的就转成数字类型
 *  }
 *
 * 样例二：parseQuery('http://localhost:8000/#/demo/curd')  => {}
 *
 * @param {String} url
 */
export function parseQuery(url) {
  const result = {};
  // eslint-disable-next-line no-shadow
  const reg = /[?&]([^=&#]+)=([^&#]*)/g;
  const querys = url.match(reg);
  if (querys) {
    for (let i = 0; i < querys.length; i += 1) {
      const query = querys[i].split('=');
      const key = query[0].substring(1);
      const value = /^\d+$/.test(query[1]) ? parseFloat(query[1]) : decodeURIComponent(query[1]);
      if (result[key]) {
        result[key] = [].concat(result[key], value);
      } else {
        result[key] = value;
      }
    }
  }
  return result;
}

/**
 * 获取视窗宽高
 * 关于视窗的各种尺寸可参考：
 * https://www.w3cplus.com/sites/default/files/blogs/2017/1707/vw-layout-4.png
 * @includeScollbar   {Boolean}  true为包含滚动条的宽度，false反之
 * @return            {Object}   返回一个包含width和height2个属性的对象。
 *                    width：浏览器视窗的宽度，height为窗口的高度
 */
export function getViewPort(includeScollbar) {
  const isInclude = includeScollbar || false;
  if (isInclude) {
    let e = window;
    let a = 'inner';
    if (!('innerWidth' in window)) {
      a = 'client';
      e = document.documentElement || document.body;
    }
    return {
      width: e[`${a}Width`],
      height: e[`${a}Height`],
    };
  }
  const de = document.documentElement;
  const db = document.body;
  const viewW = de.clientWidth === 0 ? db.clientWidth : de.clientWidth;
  const viewH = de.clientHeight === 0 ? db.clientHeight : de.clientHeight;
  return {
    width: viewW,
    height: viewH,
  };
}

// "a=2&b=3&c=4" 序列化成 {a:2,b:3,c:4}
export function serialize(str) {
  // 修复 jquery.serialize() 会把空格转成'+'的坑
  const s = str.replace(/\+/g, ' ');
  const obj = {};
  const params = s.split('&');
  for (let i = 0; i < params.length; i += 1) {
    const val = params[i].split('=');
    // 多选的select，在jquery.serialize()的时候名称都是相同的，如右：rules=1&rules=3
    // 这个时候需要把值以数组的形式保存，如右：rules：[1,3]
    if (obj[val[0]]) {
      const arr = [];
      arr.push(obj[val[0]]); // 读取已存在的，保存到临时数组
      arr.push(unescape(val[1]));
      obj[val[0]] = arr;
    } else {
      obj[val[0]] = unescape(val[1]);
    }
  }
  return obj;
}

/**
 * encrypt 加密
 * https://github.com/brix/crypto-js#object-encryption
 * @param {any} value 任意合法值，object,string,array等
 * @param {string} secretKey 秘钥
 * @return {string} 字符串
 */
export function encrypt(value, secretKey) {
  return CryptoJS.AES.encrypt(JSON.stringify(value), secretKey).toString();
}

/**
 * encrypt 解密
 * https://github.com/brix/crypto-js#object-encryption
 * @param {string} ciphertext 由encrypt()加密后生成的字符串
 * @param {string} secretKey 秘钥
 * @return {any}
 */
export function decrypt(ciphertext, secretKey) {
  if (isJSON(ciphertext)) {
    return JSON.parse(ciphertext);
  }
  const result = CryptoJS.AES.decrypt(ciphertext, secretKey);
  return JSON.parse(result.toString(CryptoJS.enc.Utf8));
}

/**
 * encrypt 解密
 * https://github.com/brix/crypto-js#object-encryption
 * @param {string} ciphertext 由encrypt()加密后生成的字符串
 * @param {string} secretKey 秘钥
 * @return {any}
 */
export function encryptUtf8(word) {
  const key = CryptoJS.enc.Utf8.parse('abcdefgabcdefg12');
  const srcs = CryptoJS.enc.Utf8.parse(word);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
}

/**
 * 本地存储 - 存
 * @param {string} key 必填
 * @param {any} value 必填
 * @param {string} mode ' 非必填，默认是sessionStorage。'sessionStorage | localStorage'
 */
export function setItem(key, value, mode = 'sessionStorage') {
  if (_storageMode.indexOf(mode) !== -1) {
    if (value !== undefined) {
      window[mode].setItem(`${SESSION_PREFIX}${key}`, encrypt(value, key));
    }
  }
}

/**
 * 本地存储 - 取
 * @param {string} key 必填
 * @param {string} mode  非必填，默认是sessionStorage。'sessionStorage | localStorage'
 */
export function getItem(key, mode = 'sessionStorage') {
  let result;
  if (key && _storageMode.indexOf(mode) !== -1) {
    result = window[mode].getItem(`${SESSION_PREFIX}${key}`);
    if (result !== null) {
      return decrypt(result, key);
    }
    return null;
  }
  return null;
}

// /**
//  * 本地存储 - 存
//  * @param {string} key 必填
//  * @param {any} value 必填
//  * @param {string} mode ' 非必填，默认是sessionStorage。'sessionStorage | localStorage'
//  */
// export function setItem(key, value, mode = 'sessionStorage') {
//   if (_storageMode.indexOf(mode) !== -1) {
//     if (value !== undefined) {
//       window[mode].setItem(`${SESSION_PREFIX}${key}`, JSON.stringify(value));
//     }
//   }
// }

// /**
//  * 本地存储 - 取
//  * @param {string} key 必填
//  * @param {string} mode  非必填，默认是sessionStorage。'sessionStorage | localStorage'
//  */
// export function getItem(key, mode = 'sessionStorage') {
//   let result;
//   if (key !== undefined && key !== '' && _storageMode.indexOf(mode) !== -1) {
//     result = window[mode].getItem(`${SESSION_PREFIX}${key}`);
//     if (result !== null) {
//       return isJSON(result) ? JSON.parse(result) : result;
//     }
//     return null;
//   }
//   return false;
// }

/**
 * 本地存储 - 删
 * @param {string} key 必填
 * @param {string} mode  非必填，默认是sessionStorage。'sessionStorage | localStorage'
 */
export function removeItem(key, mode = 'sessionStorage') {
  if (_storageMode.indexOf(mode) !== -1) {
    if (key !== undefined) {
      window[mode].removeItem(`${SESSION_PREFIX}${key}`);
    }
  }
}

/**
 * 清除带项目前缀的storage
 * @param {string} mode  非必填，默认是sessionStorage。'sessionStorage | localStorage'
 */
export function storageClear(mode = 'sessionStorage') {
  if (_storageMode.indexOf(mode) !== -1) {
    const storage = window[mode];
    const targets = [];
    for (let i = 0, len = storage.length; i < len; i += 1) {
      const key = storage.key(i);
      if (`${key}`.indexOf(`${SESSION_PREFIX}`) >= 0) {
        targets.push(key);
      }
    }
    targets.forEach(value => {
      window[mode].removeItem(value);
    });
  }
}

/**
 * 首字母转大写
 * @param {*} str
 */
export function firstUpperCase(str) {
  return str.replace(/( |^)[a-z]/g, L => L.toUpperCase());
}

/* eslint-disable */
/**
 * js密码加密函数，对应java端用com.ztesoft.bss.orgauth.util.JsPasswordUtil.decodePwd来解密.
 * @param pwd
 * @returns {*}
 */
export function jsEncodePwd(pwd) {
  var bytes = strToUtf8Bytes(pwd);
  return encodeBytes(bytes);
}

/**
 * 转化字节数组为字符串，每个字节的高4位和低4位+字符a组成2个ascii字符.
 * @param bytes
 * @returns {string}
 */
export function encodeBytes(bytes) {
  var re = [];
  for (var i = 0; i < bytes.length; i++) {
    var ch1 = String.fromCharCode(((bytes[i] >> 4) & 0xf) + 0x61);
    var ch2 = String.fromCharCode((bytes[i] & 0xf) + 0x61);
    re.push(ch1);
    re.push(ch2);
  }
  return re.join('');
}

/**
 * 字符串转化为utf8字节数组
 * @param str
 * @returns {Array}
 */
export function strToUtf8Bytes(str) {
  // TODO(user): Use native implementations if/when available
  var out = [],
    p = 0;
  for (var i = 0; i < str.length; i++) {
    var c = str.charCodeAt(i);
    if (c < 128) {
      out[p++] = c;
    } else if (c < 2048) {
      out[p++] = (c >> 6) | 192;
      out[p++] = (c & 63) | 128;
    } else if ((c & 0xfc00) == 0xd800 && i + 1 < str.length && (str.charCodeAt(i + 1) & 0xfc00) == 0xdc00) {
      // Surrogate Pair
      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);
      out[p++] = (c >> 18) | 240;
      out[p++] = ((c >> 12) & 63) | 128;
      out[p++] = ((c >> 6) & 63) | 128;
      out[p++] = (c & 63) | 128;
    } else {
      out[p++] = (c >> 12) | 224;
      out[p++] = ((c >> 6) & 63) | 128;
      out[p++] = (c & 63) | 128;
    }
  }
  return out;
}

/**
 * https://github.com/leon-good-life/wheel-react
 * 监听鼠标滚轮
 */
export function mouseWheel() {
  const WheelReact = {};

  WheelReact.pauseWheelEvent = false;

  WheelReact._config = {};

  WheelReact.config = config => {
    const options = ['right', 'left', 'up', 'down'];
    for (const option of options) {
      if (config.hasOwnProperty(option)) {
        WheelReact._config[option] = config[option];
      }
    }
  };

  WheelReact.events = {
    onWheel: e => {
      if (WheelReact.pauseWheelEvent) {
        return;
      }
      if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
        if (e.deltaX > 0 && WheelReact._config.hasOwnProperty('left')) {
          WheelReact._config.left();
        } else if (e.deltaX < 0 && WheelReact._config.hasOwnProperty('right')) {
          WheelReact._config.right();
        }
      } else if (e.deltaY > 0 && WheelReact._config.hasOwnProperty('up')) {
        WheelReact._config.up();
      } else if (e.deltaY < 0 && WheelReact._config.hasOwnProperty('down')) {
        WheelReact._config.down();
      }
      WheelReact.pauseWheelEvent = true;
      WheelReact.timeout = setTimeout(() => {
        WheelReact.pauseWheelEvent = false;
      }, 200);
    },
  };

  WheelReact.clearTimeout = () => {
    if (WheelReact.timeout) {
      clearTimeout(WheelReact.timeout);
    }
  };
  return WheelReact;
}

/* eslint-enable */

// 获取终端类型
export function getTerminalType() {
  /**
   *终端类型-PC
   */
  const TERMINAL_TYPE_PC = '1000';

  /**
   *终端类型-PAD
   */
  const TERMINAL_TYPE_PAD = '1100';

  /**
   *终端类型-MOBILE
   */
  const TERMINAL_TYPE_MOBILE = '1200';
  const u = navigator.userAgent;
  // 移动终端浏览器版本信息
  const agent = {
    trident: u.indexOf('Trident') > -1, // IE内核
    presto: u.indexOf('Presto') > -1, // opera内核
    webKit: u.indexOf('AppleWebKit') > -1, // 苹果、谷歌内核
    gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') === -1, // 火狐内核
    mobile: !!u.match(/AppleWebKit.*Mobile.*/) || !!u.match(/AppleWebKit/), // 是否为移动终端
    ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), // ios终端
    android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, // android终端或者uc浏览器
    iPhone: u.indexOf('iPhone') > -1, //  || u.indexOf('Mac') > -1  是否为iPhone或者QQHD浏览器
    iPad: u.indexOf('iPad') > -1, // 是否iPad
    google: u.indexOf('Chrome') > -1,
  };

  if (agent.iPad) {
    return TERMINAL_TYPE_PAD;
  }
  if (agent.android || agent.iPhone) {
    return TERMINAL_TYPE_MOBILE;
  }
  return TERMINAL_TYPE_PC;
}

/**
 * 根据提供的card高度，计算card-body内最多可以放多少行，行高定位33
 * 38 是.ant-card-head高度
 * 32 是ant-card-body上下内边距的高度
 * 40 是thead高度
 * 40 是table-pagination.mini高度
 * @param {number} card  card高度
 */
export function getPageSizeByCardHeight(height) {
  return Math.floor((height - 46 - 32 - 40 - 40) / 40);
}

/**
 * 根据url中是否存在viewMode来判断当前视图模式
 * @return {string} 返回 "normal | inner"; normal表示有header和tab-nav; inner相反
 */
export function getViewMode() {
  const { viewMode, lowcodePopup } = parseQuery(window.location.href);

  if (typeof viewMode === 'string' && viewMode.toLocaleLowerCase() === 'inner') {
    return 'inner';
  }

  // 低代码平台弹窗展示时需要inner模式
  if (typeof viewMode === 'string' && viewMode.toLocaleLowerCase() === 'inner' && lowcodePopup) {
    return 'inner';
  }

  return 'normal';
}

/**
 * var actionLog = {
 * actionType: ACTION_TYPE_FUNC,   动作类型
 * actionModule: self.menuId,    菜单id
 * action: ACTION_D,  对应动作
 * handResult：RESULT_SUCCESS  操作结果
 * actionMsg： msg     错误信息（操作失败时有值）
 * 记录菜单日志和按钮操作日志
 */
export function operActionLog(params) {
  request('orgauth/SystemActionLogController/collectSystemActionLog.do', { data: params });
}

/**
 * 任何int%1 都是0，而float%1 就不等于0
 * @param {*} val
 */
export function isFloat(val) {
  return parseFloat(val) % 1 !== 0;
}

function Expand({ text = '更多详情', content }) {
  const [show, setShow] = useState(false);

  const wrapStyle = show ? { background: '#f3f3f4', border: '1px solid #e8e8e8' } : {};

  const detailStyle = show ? { display: 'block' } : { display: 'none' };

  const iconType = show ? 'down' : 'right';

  return (
    <div
      style={{
        padding: '8px',
        borderRadius: '3px',
        border: '1px solid transparent',
        ...wrapStyle,
      }}
    >
      <p>
        <a onClick={() => setShow(!show)}>
          {text} <Icon type={iconType} />
        </a>
      </p>
      <div style={detailStyle}>{content}</div>
    </div>
  );
}

/**
 *
 * @param {string} pathname 如：/notice 首字母必须带斜杆
 */
export const Portal = {
  close: pathname =>
    window.top.postMessage(
      JSON.stringify({
        to: 'portal',
        action: 'close',
        pathname,
      }),
      '*'
    ),
  open: (pathname, directopen) =>
    window.top.postMessage(
      JSON.stringify({
        to: 'portal',
        action: 'open',
        pathname,
        directopen,
      }),
      '*'
    ),

  closeAll: pathname =>
    window.self.postMessage(
      JSON.stringify({
        to: 'portal',
        action: 'closeAll',
        pathname,
      }),
      '*'
    ),

  /**
   * 错误弹窗
   * 用法：Portal.error({message, detail});
   * @param {string} 错误信息
   * @param {string | React.ReactNode} 非必填，有值时显示详情展开
   */
  // eslint-disable-next-line no-shadow
  error: ({ width = 500, okText = '知道了', title = '操作异常', message, detail }) => {
    Modal.error({
      className: 'myModal',
      width,
      title,
      okText,
      content: (
        <div>
          <p>{message}</p>
          {detail && <Expand content={detail} />}
        </div>
      ),
    });
  },
};

export const isEmptyArray = value => !Array.isArray(value) || value.length === 0;

export const isNumber = value => !Number.isNaN(parseFloat(value)) && !Number.isNaN(Number(value));

export const toNumber = value => (isNumber(value) ? Number(value) : 0);

export const transformToArray = val => (Array.isArray(val) ? val : []);

export const toArray = val => (Array.isArray(val) ? val : []);

export const isEmptyStr = val => val === undefined || val === null || val === '';

export const deleteByIndex = (list, index) => {
  if (isEmptyArray(list)) {
    return [];
  }

  return [...transformToArray(list).slice(0, index), ...transformToArray(list).slice(index + 1)];
};

export const getIframeFinalUrl = async (url, systemCode) => {
  let address = url;
  if (!address) return null;

  const { sessionId, userInfo } = getItem('user');

  // 去除菜单配置中的[iframe]
  if (/^\[iframe\]+/.test(address)) {
    const [, iframeUrl] = address.split('[iframe]');
    address = iframeUrl;
  }

  // 如果配置的url不带域名，则直接取当前域名
  if (!address.startsWith('http')) {
    // 去掉address头部的/
    address = address.startsWith('/') ? address.slice(1) : address;

    const { origin } = window.location;
    // const origin = 'http://************:8906';
    address = `${origin}/${address}`;
  }

  if (address.indexOf('bss3SessionId') === -1) {
    address = `${address}${address.includes('?') ? '&' : '?'}bss3SessionId=${sessionId}`;
  }

  if (address.indexOf('{SYS_USER_ID}') !== -1) {
    address = address.replace('{SYS_USER_ID}', userInfo.userCode);
  }
  if (address.indexOf('{SYS_ORG_ID}') !== -1) {
    address = address.replace('{SYS_ORG_ID}', userInfo.userOrgId);
  }

  // 采用SSO单点校验方式进行菜单打开
  if (address.indexOf('ssoType=1') !== -1 && systemCode) {
    // let urlParams = parseUrlParams(menuUrl);
    const _urlParams = {
      bssSessionId: sessionId,
    };

    // 菜单地址SSO加密，拼接返回的signString和singTimestamp
    const response = await request(`orgauth/SystemInfoController/calcSign.do?systemInfoId=${systemCode}`, {
      method: 'POST',
      data: _urlParams,
    });
    if (response && response.resultCode === '0') {
      address = `${address}&signString=${response.signString}&signTimestamp=${response.signTimestamp}`;
    }
  }

  // 通过4A的机制，跟其他外部系统进行单点
  if (address.indexOf('ssoType=4A') !== -1) {
    const resNum = getURLParameter('resNum', address);
    const response = await request('portal/LocalLogin4aController/get4AToken.do', {
      method: 'GET',
      data: {
        resNum,
      },
    });
    if (response?.resultCode === 'TRUE') {
      address = `${address}&iamcaspticket=${encodeURIComponent(response.resultObject)}`;
    } else if (response?.resultCode === '-1') {
      message.error(response.resultMsg || '4A登录过期');
      return null;
    }
  }

  return address;
};

export const getFinalUrl = (menu = {}, _key = 'urlAddr', menuOpenMode, urlParams = '') => {
  const key = typeof _key === 'string' ? _key : 'urlAddr';

  const { menuCode } = menu;
  // 有些场景url并不是放在urlAddr里，这里做个适配
  const urlAddr = menu[key];

  if (!urlAddr) {
    return '';
  }

  if (/^\[iframe\]+/.test(urlAddr)) {
    if (menuOpenMode === '2' || menuOpenMode === '3' || menuOpenMode === '4') {
      return urlAddr.replace(/^\[iframe\]/, '');
    }

    return `/iframe/${menuCode}${urlParams}`;
  }

  // 对于micro，例如[micro]http://*******:8800/#/testMicro/list 会返回 /testMicro/list
  // 即从/xxxMicro/开始取，一直到结尾
  if (/^\[micro\]http+/.test(urlAddr)) {
    const microIndex = urlAddr.toLocaleLowerCase().indexOf('micro/');
    if (microIndex === -1) {
      return '/403';
    }

    const beginIndex = urlAddr.slice(0, microIndex).lastIndexOf('/');

    if (menuOpenMode === '2' || menuOpenMode === '3' || menuOpenMode === '4') {
      const endIndexLength = urlAddr.slice(beginIndex + 1).indexOf('/');
      return `${urlAddr.slice(0, beginIndex)}${urlAddr.slice(beginIndex + 1 + endIndexLength)}${urlParams}`.replace(/^\[micro\]/, '');
    }

    return `${urlAddr}${urlParams}`.slice(beginIndex);
  }

  return `${urlAddr}${urlParams}`;
};

/* eslint-disable no-await-in-loop */
// 对于无依赖关系的多个请求，可使用此函数实现await效果的同时,避免相互阻塞,导致延长请求时间
export const moreRequest = async requestList => {
  const resList = [];

  for (let i = 0; i < requestList.length; i += 1) {
    const res = await requestList[i];
    resList.push(res);
  }

  return resList;
};

export const openWindowTab = url => {
  if (IEVersion() !== -1) {
    const a = document.createElement('a');
    a.href = url;
    a.setAttribute('target', '_blank');
    document.body.appendChild(a);
    a.click();
  } else {
    window.open(url);
  }
};

// 用于通用导航信息页面打开，与commonNavIframeBox搭配使用
export const openMenuByValue = async (value, allMenu, urlParams = '') => {
  const menuCode = value;
  let menuOpenMode = null;
  const urlAddr = null;
  let menu = null;

  // 如果menuOpenMode和urlAddr为空，需要从allMenu中获取menu信息
  if (!menuOpenMode || !urlAddr) {
    menu = allMenu.find(item => item.menuCode === menuCode);

    if (!menu) {
      Portal.open('/403', true);
      return;
    }
    // eslint-disable-next-line prefer-destructuring
    menuOpenMode = menu.menuOpenMode;
  }

  getIframeFinalUrl(urlParams, menu.systemCode).then(url => {
    if (url) {
      const menuUrl = getFinalUrl(menu, null, menuOpenMode, `?iframeUrl=${encodeURIComponent(url)}`);
      // var MENU_OPEN_MODE_WORK_BENCH_LABEL = "1"; //工作台标签页打开
      // var MENU_OPEN_MODE_POPUP = "2"; //弹出框打开
      // var MENU_OPEN_MODE_NEW_BROWSER = "3"; //新开浏览器打开
      // var MENU_OPEN_MODE_BROWSER_LABEL = "4"; //浏览器新标签打开
      // var MENU_OPEN_MODE_WORK_BENCH_LABEL_COMPLEX = "5" || "6"// 工作台标签页打开，同页面多标签，用于同页面不同参数，如详情页
      if (!menuOpenMode || menuOpenMode === '1' || menu.menuOpenMode === '5' || menu.menuOpenMode === '6') {
        // 普通打开方式，不需要特殊处理，iframe组件页面会处理
        Portal.open(menuUrl, true);
      }
    }
  });
};

export const openMenu = async (value, allMenu, dispatch, urlParams = '') => {
  // systemCode用于单点登录信息
  // menuId、showFlag用于非"工作台标签页"打开方式的菜单记录最近访问历史记录
  let menuCode = value;
  let menuOpenMode = null;
  let urlAddr = null;
  let systemCode = null;
  let menuId = null;
  let showFlag = null;
  let menu = null;

  if (typeof value === 'object') {
    menuCode = value?.menuCode;
    menuOpenMode = value?.menuOpenMode;
    urlAddr = value?.urlAddr;
    systemCode = value?.systemCode;
    menuId = value?.menuId;
    showFlag = value?.showFlag;
    menu = value;
  }

  // 如果menuOpenMode、urlAddr、systemCode为空，需要从allMenu中获取menu信息
  if (!menuOpenMode || !urlAddr || !systemCode || !menuId || !showFlag) {
    menu = allMenu.find(item => item.menuCode === menuCode);

    if (!menu) {
      // Portal.open('/403', true);
      message.error('无权限，请联系管理员授权');
      return;
    }
    // eslint-disable-next-line prefer-destructuring
    menuOpenMode = menu.menuOpenMode;
    // eslint-disable-next-line prefer-destructuring
    systemCode = menu.systemCode;
    // eslint-disable-next-line prefer-destructuring
    menuId = menu.menuId;
    // eslint-disable-next-line prefer-destructuring
    showFlag = menu.showFlag;
  }

  let menuUrl = getFinalUrl(menu, null, menuOpenMode, urlParams);

  // var MENU_OPEN_MODE_WORK_BENCH_LABEL = "1"; //工作台标签页打开
  // var MENU_OPEN_MODE_POPUP = "2"; //弹出框打开
  // var MENU_OPEN_MODE_NEW_BROWSER = "3"; //新开浏览器打开
  // var MENU_OPEN_MODE_BROWSER_LABEL = "4"; //浏览器新标签打开

  if (!menuOpenMode || menuOpenMode === '1' || menu.menuOpenMode === '5' || menu.menuOpenMode === '6') {
    // 普通打开方式，不需要特殊处理，iframe组件页面会处理
    Portal.open(menuUrl, true);
    return;
  }

  const { sessionId, userInfo } = getItem('user');

  // 判断所属系统是否需要单点登录，如需要，则添加单点信息
  const { systemList = [], ssoSystem = '' } = getItem('systemInfo');
  if (systemList.length > 0 && ssoSystem) {
    const systemNbr = systemList.find(item => String(item.systemInfoId) === systemCode)?.systemNbr || '';

    if (ssoSystem.toUpperCase().includes(systemNbr.toUpperCase())) {
      const response = await request('portal/SsoLoginStrategyController/ssoLogin.do', {
        method: 'POST',
        data: {
          systemInfo: systemNbr,
          menuCode,
          menuUrl,
        },
      });
      if (response && response.resultCode === 'TRUE') {
        menuUrl = response.resultObject;
      }
    }
  }

  // CRM待办
  if (menuCode && (menuCode.startsWith('RWGL_MENU_TASK080') || systemCode === '727023')) {
    // crm待办菜单编码为RWGL_MENU_TASK080，跳转地址需特殊处理
    // http://10.174.59.103/zfcrmsso?menu_url=/approvalcentre/approvalcentre?service=page/cs.core.approve.FlowViewQuery&listener=myInitialize&source=ztdd&ACCESS_NUM=18814127729&userInfo=TESTKM06&sign=5e9e42e327405c13f81707685f6de69a&timestamp=202307011638013
    if (!menu.urlAddr || menu.urlAddr.indexOf('menu_url') === -1) {
      message.warning('链接未配置统一任务视图路径menu_url');
      return;
    }
    if (!menu.urlAddr || menu.urlAddr.indexOf('source') === -1) {
      message.warning('链接未配置参数source');
      return;
    }

    if (!userInfo || !userInfo.externalUserInfos) {
      message.warning('无CRM用户信息');
      return;
    }
    let externalUserInfo = userInfo.externalUserInfos.find(item => item.systemNbr === 'CRM' && item.currentFlag === '1000');
    if (!externalUserInfo) {
      externalUserInfo = userInfo.externalUserInfos.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000');
    }
    if (!externalUserInfo) {
      message.warning('无可用CRM用户信息');
      return;
    }

    const crmParams = {
      ACCESS_NUM: userInfo.mobilePhone,
      source: 'ztdd',
      userInfo: userInfo.userCode,
      // staffId: externalUserInfo.externalUserCode,
    };
    const crmResponse = await request('orgauth/SystemInfoController/md5Sign.do', {
      method: 'POST',
      data: crmParams,
    });
    if (crmResponse && crmResponse.resultCode === '0') {
      if (menuUrl.indexOf('?') !== -1) {
        menuUrl = `${menuUrl}&userInfo=${userInfo.userCode}&ACCESS_NUM=${userInfo.mobilePhone}&timestamp=${crmResponse.signTimestamp}&sign=${crmResponse.signString}&staffId=${externalUserInfo.externalUserCode}`;
      } else {
        menuUrl = `${menuUrl}?userInfo=${userInfo.userCode}&ACCESS_NUM=${userInfo.mobilePhone}&timestamp=${crmResponse.signTimestamp}&sign=${crmResponse.signString}&staffId=${externalUserInfo.externalUserCode}`;
      }
    } else {
      message.warning('鉴权信息获取失败，跳转失败');
      return;
    }
  }

  // 稽核待办
  if (menuCode && menuCode.startsWith('RWGL_MENU_TASK090')) {
    // 稽核系统菜单编码为RWGL_MENU_TASK090
    // 点击稽核系统单点登录要进行字符串拼接处理http://10.174.25.75:8080/cloudaudit/baseserver?eventId=12&auditMenu=unauditedTask&sysUserId=TESTKM06&signTimestamp=20200610144120&signString=a74a52c1b77e440f138eb3623fb935b7
    // 地址写死【eventId：单点登录调用方区分标识】 【auditMenu=unauditedTask】  前端获取【sysUserId:CRM工号】   后端返回【signTimestamp：时间戳   signString：加密字符串】
    if (!menu.urlAddr || menu.urlAddr.indexOf('eventId') === -1) {
      message.warning('链接未配置参数区分标识eventId');
      return;
    }
    if (!userInfo || !userInfo.externalUserInfos) {
      message.warning('无CRM用户信息');
      return;
    }
    let externalUserInfo = userInfo.externalUserInfos.find(item => item.systemNbr === 'CRM' && item.currentFlag === '1000');
    if (!externalUserInfo) {
      externalUserInfo = userInfo.externalUserInfos.find(item => item.systemNbr === 'CRM' && item.defaultFlag === '1000');
    }
    if (!externalUserInfo) {
      message.warning('无可用CRM用户信息');
      return;
    }

    const jhxtParams = {
      sysUserId: externalUserInfo.externalUserCode,
      eventId: '12',
      datePattern: 'yyyyMMddHHmmss',
      auditMenu: 'unauditedTask',
    };
    const jhxtResponse = await request('orgauth/SystemInfoController/md5Sign.do', {
      method: 'POST',
      data: jhxtParams,
    });
    if (jhxtResponse && jhxtResponse.resultCode === '0') {
      if (menuOpenMode === '4') {
        menuUrl = `${menuUrl}&sysUserId=${externalUserInfo.externalUserCode}&signTimestamp=${jhxtResponse.signTimestamp}&signString=${jhxtResponse.signString}`;
      } else {
        menuUrl = `${menuUrl}?sysUserId=${externalUserInfo.externalUserCode}&signTimestamp=${jhxtResponse.signTimestamp}&signString=${jhxtResponse.signString}`;
      }
    } else {
      message.warning('鉴权信息获取失败，跳转失败');
      return;
    }
  }

  if (menuUrl.indexOf('?') === -1) {
    menuUrl += `?bss3SessionId=${sessionId}`;
  } else {
    menuUrl += `&bss3SessionId=${sessionId}`;
  }

  if (menuUrl.indexOf('{SYS_USER_ID}') !== -1) {
    menuUrl = menuUrl.replace('{SYS_USER_ID}', userInfo.userCode);
  }
  if (menuUrl.indexOf('{SYS_ORG_ID}') !== -1) {
    menuUrl = menuUrl.replace('{SYS_ORG_ID}', userInfo.userOrgId);
  }

  // 采用SSO单点校验方式进行菜单打开
  if (menuUrl.indexOf('ssoType=1') !== -1) {
    // let urlParams = parseUrlParams(menuUrl);
    const _urlParams = {
      bssSessionId: sessionId,
    };

    // 菜单地址SSO加密，拼接返回的signString和singTimestamp
    const response = await request(`orgauth/SystemInfoController/calcSign.do?systemInfoId=${systemCode}`, {
      method: 'POST',
      data: _urlParams,
    });
    if (response && response.resultCode === '0') {
      menuUrl = `${menuUrl}&signString=${response.signString}&signTimestamp=${response.signTimestamp}`;
    }
  }

  // 通过4A的机制，跟其他外部系统进行单点
  if (menuUrl.indexOf('ssoType=4A') !== -1) {
    const resNum = getURLParameter('resNum', menuUrl);
    const response = await request('portal/LocalLogin4aController/get4AToken.do', {
      method: 'GET',
      data: {
        resNum,
      },
    });
    if (response?.resultCode === 'TRUE') {
      menuUrl = `${menuUrl}&iamcaspticket=${encodeURIComponent(response.resultObject)}`;
    } else if (response?.resultCode === '-1') {
      message.error(response.resultMsg || '4A登录过期');
      return;
    }
  }

  if (menu.menuOpenMode === '4') {
    openWindowTab(menuUrl);
  } else if (menu.menuOpenMode === '3') {
    window.open(menuUrl, menu.menuName, 'width=800,height=600,location=yew,toolbar=yes');
  } else {
    typeof dispatch === 'function' &&
      dispatch({
        type: 'menu/saveInitData',
        payload: {
          openMenuModal: {
            visible: true,
            url: menuUrl,
          },
        },
      });
  }

  if (menu.menuOpenMode !== '1') {
    // 在菜单有展示而非隐藏时
    if (showFlag === '1000') {
      // 更新历史记录菜单
      window.g_app._store.dispatch({
        type: 'menu/updateRecentMenu',
        payload: menuId,
      });
    }
  }

  // 用于头部菜单栏打开菜单，弹窗打开的情况
  if (menu.menuOpenMode === '2') {
    // eslint-disable-next-line consistent-return
    return menuUrl;
  }
};

// 获取系统参数（直接返回参数值）
export const getValidValueByCode = params => {
  const { groupCode, paramCode } = params;
  return request('portal/DataDictController/getValidValueByCode.do', {
    data: { groupCode, paramCode },
    method: 'GET',
  });
};

// 获取系统参数（返回参数对象）
export const getValueByCode = params => {
  const { groupCode, paramCode } = params;
  return request('portal/DataDictController/getValueByCode.do', {
    data: { groupCode, paramCode },
    method: 'get',
  });
};

// 获取系统参数（批量）
export async function getDataDictsByList(params) {
  return request('portal/DataDictController/getDataDictsByList.do', {
    method: 'post',
    data: params,
  });
}

export const exportExcelFile = async ({ url, title = 'export', method = 'POST', exportFileType = 'xls', sendParams, setLoading }) => {
  // 参数验证
  if (!url || typeof url !== 'string') {
    message.error('请求地址有误');
    return;
  }
  if (!['xls', 'xlsx'].includes(exportFileType)) {
    message.error('文件类型有误');
    return;
  }

  // 设置加载状态
  if (typeof setLoading === 'function') setLoading(true);

  try {
    const xhr = new XMLHttpRequest();
    xhr.open(method, url, true);

    // 设置请求头
    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
    xhr.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');
    xhr.responseType = 'blob';

    // 处理响应
    xhr.onload = () => {
      if (xhr.status === 200) {
        const blob = new Blob([xhr.response], { type: 'application/vnd.ms-excel' });
        const newUrl = window.URL.createObjectURL(blob);
        if ('download' in document.createElement('a')) {
          const a = document.createElement('a');
          a.href = newUrl;
          a.download = `${title}.${exportFileType}`;
          a.click();
          a.remove();
        } else {
          navigator.msSaveBlob(blob, `${title}.${exportFileType}`);
        }
        window.URL.revokeObjectURL(url);
      } else {
        message.error('导出失败');
      }
      if (typeof setLoading === 'function') setLoading(false);
    };

    xhr.onerror = () => {
      message.error('请求发生错误');
      if (typeof setLoading === 'function') setLoading(false);
    };

    xhr.send(JSON.stringify(sendParams));
  } catch (error) {
    // console.error(error);
    message.error('未知错误');
    if (typeof setLoading === 'function') setLoading(false);
  }
};

export const toCamelCase = str =>
  str
    .toLowerCase() // 先全部转为小写
    .replace(/(_[a-z])/g, match =>
      // 对于每个匹配到的下划线加上其后跟的字符，将匹配到的部分首字母大写，其余部分去掉
      match.toUpperCase().replace('_', '')
    );

export const downloadFile = (fileName, url) => {
  if (fileName === undefined) {
    // console.log('附件名称为空');
    return;
  }
  const a = document.createElement('a');
  a.style.display = 'none';
  a.download = fileName;
  a.innerHTML = fileName;
  a.href = url;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

export const isLeapYear = year => (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
// 是否为纯数字
export const isPInteger = code => {
  const isNumeric = /^\d+$/.test(code);
  return code === '' || (isNumeric && code !== '0');
};
// 是否递增或递减的序列号
export const isSerialCode = code => {
  if (!isPInteger(code)) return false;
  const flag = true;
  for (let i = 0; i < code.length - 1; i += 1) {
    const step = parseInt(code.charAt(i + 1), 10) - parseInt(code.charAt(i), 10);
    if (step !== 1 && step !== -1) {
      return false; // 直接返回false，无需继续检查
    }
  }
  return flag; // 如果循环完成，说明所有相邻字符都符合要求
};
// 判断是否数字和字母
export const isNumberLetter = val => {
  const model = /^[a-zA-Z0-9]+$/;
  return model.test(val);
};

export const isRepeatCode = code => {
  for (let i = 0; i < code.length - 1; i += 1) {
    const diff = parseInt(code.charAt(i + 1), 10) - parseInt(code.charAt(i), 10);
    // 检查两个相邻数字之间的差是否为1或-1
    if (diff !== 1 && diff !== -1) {
      return false;
    }
  }
  return true;
};
// 判断是否数字
export const isNumberCode = code => {
  const model = /^[0-9]+$/;
  return model.test(code);
};
// 判断是否大写字母
export const isUpCharCode = code => {
  const model = /^[A-Z]+$/;
  return model.test(code);
};
// 校验是否大写字母和数字的组合
export const isUpperCaseAndNumber = code => {
  const model = /^[A-Z0-9()]+$/;
  return model.test(code);
};

// 判断是否包含特殊字符
export const isSpecialCharacter = val => {
  const specialStr = "`￥$~!@%^&*,;'?><[]{}|:=+“”‘’，《》 ";
  for (let i = 0;i < specialStr.length;i += 1) {
    if (val.indexOf(specialStr.charAt(i)) > -1) {
      return true;
    }
  }
  return false;
};
// 判读是否为空
export const isBlank = val => {
  if (val == null || (typeof val === 'string' && val.trim().length === 0)) {
    return true;
  }
  return false;
};
// 判断是否憨厚空格
export const isBlankSpace = val => {
  const model = /\s/;
  return model.test(val);
};

/**
 * 工具类函数，用于将表单数据转换为接口参数
 * @param {Object} formData - 表单数据
 * @param {Object} mappingConfig - 字段映射配置，格式为 { 目标路径: [字段名数组] }
 * @returns {Object} 转换后的接口参数
 * @example
 * // 使用工具函数转换表单数据
 * const apiParams = transformFormData(
 *   currentForm,
 *   {
 *     'dc': ['SERV_LEVEL', 'AGENT_LICENCE_TYPE', 'OSS_PROVINCE_NAME'],
 *     'ab': ['GROUP_BUSI_LICENCE_TYPE'],
 *     'ce.aa': ['GROUP_HALF_LEVEL']
 *   }
 * );
 */
export const transformFormData = (formData, mappingConfig = {}) => {
  // 创建接口参数对象
  const apiParams = {
    param: {},
  };

  // 处理每个映射配置
  Object.entries(mappingConfig).forEach(([targetPath, fieldNames]) => {
    // 提取指定字段
    const pickedData = pick(formData, fieldNames);

    if (Object.keys(pickedData).length > 0) {
      // 将目标路径拆分为数组
      const pathParts = targetPath.split('.');

      // 构建嵌套对象
      let current = apiParams.param;
      for (let i = 0; i < pathParts.length; i += 1) {
        const part = pathParts[i];
        if (i === pathParts.length - 1) {
          // 最后一级，设置提取的数据
          current[part] = { ...current[part], ...pickedData };
        } else {
          // 中间层级，确保对象存在
          if (!current[part]) {
            current[part] = {};
          }
          current = current[part];
        }
      }
    }
  });

  return apiParams;
};
