import { Card, Carousel, Empty } from 'antd';
import React, { useEffect } from 'react';
import classNames from 'classnames';
import { connect } from "dva";
import style from './banner.less';

const Banner = ({ bannerList, dispatch }) => {

  useEffect(() => {
    dispatch({
      type: 'banner/getBannerList',
    });
  }, [])

  return (
    <Card
      bordered={false}
      className={style.banner}
      bodyStyle={{ height: '236px', overflow: 'hidden' }}
    >
      <Carousel autoplay>
        {
          bannerList.length > 0 ? bannerList.map((item) => (
            <div
              className={classNames(style.content, {
                [style.pointer]: item.linkUrl
              })}
              key={item.confId}
              onClick={() => {
                if (item.linkUrl) {
                  window.open(item.linkUrl, 'blank')
                }
              }}
            >
              <div style={{backgroundImage: `url(${item.imageUrl})`}} />
            </div>
          )) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无数据"
            />
          )
        }
      </Carousel>
    </Card>
  )
}

export default connect(({ banner }) => ({
  bannerList: banner.bannerList,
}))(Banner);
