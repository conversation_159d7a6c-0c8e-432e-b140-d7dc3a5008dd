import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Row, Col, Card, Form, Input, Icon, Button, Table } from 'antd';
import SlickTable from '@/components/SlickTable';
import useSlickTable from '@/hooks/UseSlickTable';
const namespace = 'memberManage';

// 空对象校验
const judgeObj = obj => {
  if (Object.keys(obj).length == 0) return true;
  return false;
};

function RelSystemsUser(props) {
  // 初始化变量
  const [variable, setVariable] = useState(false);
  // 入参
  const [params, setParams] = useState({
    staffId: props.tableData.staffId,
  });
  const { tableProps } = useSlickTable(
    { pageSize: 5 },
    'orgauth/SystemUserController/selectGridData.do',
    params
  );

  useEffect(() => {
    if (!judgeObj(props.tableData) && variable) {
      setParams({
        staffId: props.tableData.staffId,
        gb_reset: true,
      });
    }
  }, [props.tableData]);

  useEffect(() => {
    setVariable(true);
  }, []);

  const columns = [
    {
      title: '账号',
      dataIndex: 'loginCode',
    },
    {
      title: '状态',
      dataIndex: 'statusCdName',
    },
    {
      title: '用户名称',
      dataIndex: 'userName',
    },
    {
      title: '用户描述',
      dataIndex: 'sysUserDesc',
    },
  ];
  return (
    <SlickTable rowKey={record => record.id} columns={columns} {...tableProps} pick="checkbox" />
  );
}

export default connect(({ memberManage }) => ({
  tableData: memberManage.tableData,
}))(RelSystemsUser);
