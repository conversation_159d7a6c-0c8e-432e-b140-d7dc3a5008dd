import React, { useState, useEffect } from 'react';
import { Tree, Skeleton, Spin, Input, Icon, Tooltip, Modal } from 'antd';
import { connect } from 'dva';
import ScrollBar from '@/components/ScrollBar';
import styles from '../styles.less';

const { TreeNode } = Tree;
const { Search } = Input;

const DeptTree = ({ deptTree, loading, dispatch, deptBehavior, currentCust }) => {
  const [selectedKeysArr, setSelectedKeysArr] = useState(['']);

  const [expandedKeys, setExpandedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  const [searchValue, setSearchValue] = useState('');

  // 选择树节点
  const selectTreeNode = selectedKeys => {
    if (selectedKeys.length > 0) {
      const deptId = selectedKeys[0];
      setSelectedKeysArr(selectedKeys);
      dispatch({ type: 'deptManage/setDeptBehavior', payload: 'view' });
      dispatch({ type: 'deptManage/selectDept', payload: deptId });
      dispatch({ type: 'deptManage/fetchDeptDetail', payload: deptId });
      dispatch({ type: 'deptManage/fetchDeptMembers', payload: { deptId, groupId: currentCust.GROUP_ID } });
    } else {
      // 如果没有选中任何节点，清空选择状态
      setSelectedKeysArr([]);
      dispatch({ type: 'deptManage/selectDept', payload: null });
    }
  };

  // 展开/收起节点
  const onExpand = keys => {
    setExpandedKeys(keys);
    setAutoExpandParent(false);
  };

  // 编辑部门
  const edit = (event, item) => {
    event.stopPropagation();
    if (deptBehavior !== 'view') {
      Modal.confirm({
        title: '提示',
        content: '当前有未保存的编辑内容，是否继续？',
        onOk: () => {
          dispatch({ type: 'deptManage/selectDept', payload: item.DIVISION_ID });
          dispatch({ type: 'deptManage/fetchDeptDetail', payload: item.DIVISION_ID });
          dispatch({ type: 'deptManage/fetchDeptMembers', payload: { deptId: item.DIVISION_ID, groupId: currentCust.GROUP_ID } });

          dispatch({ type: 'deptManage/setDeptBehavior', payload: 'edit' });
        },
      });
    } else {
      dispatch({ type: 'deptManage/selectDept', payload: item.DIVISION_ID });
      dispatch({ type: 'deptManage/fetchDeptDetail', payload: item.DIVISION_ID });
      dispatch({ type: 'deptManage/fetchDeptMembers', payload: { deptId: item.DIVISION_ID, groupId: currentCust.GROUP_ID } });

      dispatch({ type: 'deptManage/setDeptBehavior', payload: 'edit' });
    }
  };

  // 删除部门
  const removeTree = (event, item) => {
    event.stopPropagation();
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除部门 "${item.DIVISION_NAME}" 吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const success = await dispatch({
          type: 'deptManage/deleteDept',
          payload: {
            divisionId: item.DIVISION_ID,
            groupId: currentCust.GROUP_ID,
            groupName: currentCust.GROUP_NAME,
            orgaEnterpriseId: currentCust.ORGA_ENTERPRISE_ID,
          },
        });
        if (success) {
          // 如果删除的是当前选中的部门，清空选择
          if (selectedKeysArr.includes(item.DIVISION_ID)) {
            setSelectedKeysArr([]);
            dispatch({ type: 'deptManage/selectDept', payload: null });
          }
        }
      },
    });
  };

  const onSearch = value => {
    if (!value) {
      setExpandedKeys(['']);
      setAutoExpandParent(false);
      setSearchValue('');
      return;
    }
    const searchArr = []; // 被选中项
    const parentArr = []; // 当前选中项的父节点
    const getAllparentId = parentId => {
      deptTree.forEach(item => {
        if (item.id === parentId) {
          parentArr.push(item.id);
          if (item.parentId !== 0) {
            getAllparentId(item.parentId);
          }
        }
      });
    };

    // 查找匹配的部门并收集它们的父节点ID
    deptTree.forEach(item => {
      if (item.DIVISION_NAME.indexOf(value) > -1) {
        searchArr.push(item.id);
        getAllparentId(item.parentId);
      }
    });

    // 去重并转换为字符串
    const strParentArr = Array.from(new Set(parentArr)).map(item => `${item}`);
    const strSearchArr = Array.from(new Set(searchArr)).map(item => `${item}`);

    // 展开包含搜索结果的父节点
    setExpandedKeys([...strParentArr, ...strSearchArr]);
    setAutoExpandParent(true);
    setSearchValue(value);

    // 将搜索词保存到Context中
    dispatch({ type: 'SEARCH_DEPT', payload: value });
  };
  // 递归渲染树节点
  const renderTreeNode = () => {
    const loop = data => {
      if (!data || !Array.isArray(data)) return null;
      return data.map(item => {
        const index = item.DIVISION_NAME.indexOf(searchValue);
        const beforeStr = item.DIVISION_NAME.substr(0, index);
        const afterStr = item.DIVISION_NAME.substr(index + searchValue.length);
        const title =
          index > -1 ? (
            <span>
              {beforeStr}
              <span style={{ color: '#f50' }}>{searchValue}</span>
              {afterStr}
            </span>
          ) : (
            <span>{item.DIVISION_NAME}</span>
          );

        const nodeTitle = (
          <span>
            <span>{title}</span>
            <span className={styles.system_menu_tree_edit}>
              <Tooltip title="编辑">
                <Icon
                  type="edit"
                  className={styles.icon}
                  onClick={event => {
                    edit(event, item);
                  }}
                />
              </Tooltip>
              <Tooltip title="删除">
                <Icon
                  type="delete"
                  className={styles.icon}
                  onClick={event => {
                    removeTree(event, item);
                  }}
                />
              </Tooltip>
            </span>
          </span>
        );

        return <TreeNode key={item.DIVISION_ID} title={nodeTitle} dataRef={item} isLeaf={item.isLeaf} />;
      });
    };

    return loop(deptTree);
  };

  useEffect(() => {
    if (currentCust.GROUP_ID) {
      dispatch({
        type: 'deptManage/fetchDeptTree',
        payload: {
          groupId: currentCust.GROUP_ID,
          groupName: currentCust.GROUP_NAME,
          orgaEnterpriseId: currentCust.ORGA_ENTERPRISE_ID,
        },
      });
    }
  }, [currentCust.GROUP_ID]);

  return (
    <div className={styles.system_menu}>
      <Skeleton loading={loading}>
        <Search style={{ marginBottom: 8 }} placeholder="搜索关键字" onSearch={onSearch} />
        <ScrollBar autoHide autoHeight>
          <Spin spinning={loading}>
            <Tree
              showIcon
              expandAction={false}
              className={styles.system_menu_tree}
              onSelect={selectTreeNode}
              selectedKeys={selectedKeysArr}
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
            >
              {renderTreeNode()}
            </Tree>
          </Spin>
        </ScrollBar>
      </Skeleton>
    </div>
  );
};

const mapStateToProps = ({ deptManage, loading, groupCustView }) => ({
  deptTree: deptManage.deptTree,
  deptBehavior: deptManage.deptBehavior,
  loading: loading.effects['deptManage/fetchDeptTree'],
  currentCust: groupCustView.currentCust,
});

export default connect(mapStateToProps)(DeptTree);
