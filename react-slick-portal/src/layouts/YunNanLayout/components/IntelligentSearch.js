import React, { useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'dva';
import { Input, Empty, Icon } from 'antd';
import classNames from 'classnames';
import memoizeOne from 'memoize-one';
import PubSub from 'pubsub-js';
import { useDebounceFn } from '@umijs/hooks';
import Link from 'umi/link';
import isEqual from 'lodash/isEqual';
import { getFinalUrl, openMenu } from '@/utils/utils';
import { quickSearch } from './utils';
import sendBtn from '@/layouts/YunNanLayout/img/AIBadge.gif';
import styles from '../less/intelligentSearch.less';

function renderName(menuName, searchText) {
  const index = menuName.indexOf(searchText);
  const beforeStr = menuName.substr(0, index);
  const afterStr = menuName.substr(index + searchText.length);
  return (
    <span>
      {index > -1 ? (
        <span>
          {beforeStr}
          <span style={{ color: '#f50' }}>{searchText}</span>
          {afterStr}
        </span>
      ) : (
        <span>{menuName}</span>
      )}
    </span>
  );
}

const _quickSearch = memoizeOne(quickSearch, isEqual);

const IntelligentSearch = props => {
  const {
   menu: { all, appSysCode, taskSysCode },
  } = props;

  const [searchValue, setSearchValue] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  // 按类别排序的所有菜单
  const [allByCategory, setAllByCategory] = useState(_quickSearch(all, '', appSysCode, taskSysCode)); // 搜索结果
  const { run: search } = useDebounceFn(value => {
    setAllByCategory(_quickSearch(all, value, appSysCode, taskSysCode));
  }, 1000);

  const searchBoxRefBox = useRef(null);
  const inputRef = useRef(null);

  // 负责渲染级联菜单
  function renderMenu(menu) {
    const keyword = searchValue;

    // 1100 表示叶子节点
    if (parseInt(menu.menuType, 10) === 1100) {
      if (menu.menuOpenMode !== '1') {
        return (
          <a onClick={() => openMenu(menu)} className={classNames('text-ellipsis')} key={menu.menuId}>
            {renderName(menu.menuName, keyword)}
          </a>
        );
      }
      return (
        <Link to={getFinalUrl(menu)} onClick={() => setShowSearch(false)} title={menu.menuName} className="text-ellipsis">
          {renderName(menu.menuName, keyword)}
        </Link>
      );
    }
    return (
      <a title={menu.menuName} href="#" className="text-ellipsis">
        {menu.menuName}
        <Icon type="right" />
      </a>
    );
  }

  function renderAllByCategory(_all) {
    return _all.length === 0 ? (
      <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ margin: '150px 0 32px' }} />
    ) : (
      <div style={{ columnGap: 24, columnCount: 1 }}>
        {_all.map((group, n) => (
          <div key={`${group.title}_${n}`} style={{ breakInside: 'avoid' }}>
            {/**
             * TODO: 测试环境配置了一样的一级菜单导致key重复，暂时使用索引区分
             */}
            <dl>
              <dt className={styles['letter-title']}>
                <span>{group.title}</span>
              </dt>
              <dd>
                <ul>
                  {group.menus.map((menu, i) => (
                    <li className={styles['ng-item']} key={`${menu.menuId}_${i}`}>
                      {renderMenu(menu)}
                    </li>
                  ))}
                </ul>
              </dd>
            </dl>
          </div>
        ))}
      </div>
    );
  }

  const menuList = useMemo(() => renderAllByCategory(allByCategory), [allByCategory]);

  const onClickOutsideHandle = e => {
    if (!searchBoxRefBox.current.contains(e?.target)) {
      setShowSearch(false);
    }
  };

  useEffect(() => {
    window.addEventListener('click', onClickOutsideHandle);
    return () => {
      window.removeEventListener('click', onClickOutsideHandle);
    };
  }, []);

  const submitSearch = () => {
    // 打开智能助手
    PubSub.publish('systemOpt_openAIModal', {
      keyword: searchValue,
      isCenter: true,
    });
    setShowSearch(false);
    setSearchValue('');
  };

  return (
    <div className={styles.searchInputBox}>
      <div ref={searchBoxRefBox} className={`${styles.searchBox} ${showSearch ? styles.searchFocusBox : ''}`}>
        <Input
          ref={inputRef}
          className={styles.input}
          allowClear
          value={searchValue}
          placeholder="搜索全门户内容"
          suffix={<img src={sendBtn} style={{ cursor: 'pointer', width: '50px', height: '40px' }} onClick={() => submitSearch(searchValue)} alt="" />}
          onFocus={() => {
            setShowSearch(true);
          }}
          onPressEnter={e => {
            const getValue = e.target.value;
            submitSearch(getValue);
            inputRef.current?.blur();
          }}
          onChange={e => {
            setSearchValue(e?.target?.value || '');
            search(e?.target?.value || '');
          }}
        />
        <div style={{ padding: '0 0 8px 8px' }}>
          {
            showSearch && (
              <div className={styles['letter-panel']}>
                <div className={classNames(styles['letter-category'], 'clearfix')}>{menuList}</div>
              </div>
            )
          }
        </div>
      </div>
      <img className={styles.Icon} src={sendBtn} style={{ cursor: 'pointer', width: '50px', height: '40px' }} onClick={() => submitSearch(searchValue)} alt="" />
    </div>
  );
};
export default connect(({ menu }) => ({
  menu,
}))(IntelligentSearch);
