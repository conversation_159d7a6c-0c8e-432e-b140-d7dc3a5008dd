import React, { useMemo } from 'react';
import { Col, Form, message } from 'antd';
import { connect } from 'dva';
import styles from '@/pages/GroupCust/GroupCustManage/components/Edit/index.less';
import { queryDisticts, queryQxArea, queryPianQu } from '@/pages/GroupCust/GroupCustManage/services';
import AsyncCascader from '@/components/AsyncCascader';

const FormItem = Form.Item;
const formItemLayout = {
  // labelAlign: 'left',
  // labelCol: {
  //   xs: { span: 24 },
  //   // sm: { span: 4 },
  // },
  // wrapperCol: {
  //   xs: { span: 24 },
  //   sm: { span: 16 },
  // },
};
const valiArea = () => (_, value = [], callback) => {
  if (value.length === 0) {
    callback('请选择地市！');
    return;
  }
  if (value.length === 1) {
    callback('请选择区县！');
    return;
  }
  if (value.length === 2) {
    callback('请选择片区！');
    return;
  }
  callback();
};
const RegionInfo = props => {
  const { form, currentFormFieldState, viewMode } = props;
  const { getFieldDecorator } = form;

  // const [disInfos, setDisInfos] = useState([]);
  // const [countyInfos, setCountyInfos] = useState([]);
  // const [regionDetails, setRegionDetails] = useState([]);
  // const [openCard, setOpenCard] = useState(false); // 是否展开卡片

  // const initRegion = () => {
  //   queryDisticts('400').then(res => {
  //     const { resultCode, resultObject, resultMsg } = res;
  //     if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.resultInfo) {
  //       console.log('区域信息的省', resultObject?.rspParam?.busiInfo?.resultInfo);

  //       setDisInfos(resultObject?.rspParam?.busiInfo?.resultInfo.map(item => ({
  //         label: item.DISTRICT_NAME,
  //         value: item.DISTRICT_ID,
  //       })) || []);
  //       // 清除市-县
  //       const formValue = {
  //         MGMT_COUNTY: undefined,
  //         CHIP_AREA_CODE: undefined,
  //       };
  //       form.setFieldsValue(formValue);
  //       onValuesChange(
  //         {
  //           'BaseInfo.MGMT_COUNTY': '',
  //           'BaseInfo.CHIP_AREA_CODE': '',
  //         },
  //         true
  //       );
  //     } else {
  //       message.error(resultMsg);
  //     }
  //   });
  // };

  // 查询区域信息
  const getLocation = (selected = []) => {
    const level = selected.length; // 当前层级
    let service;
    if (level === 0) {
      // 查询省（第一级）
      service = queryDisticts('400');
    }
    if (level === 1) {
      // （第2级或第3级）
      const areaCode = selected[selected.length - 1].value;
      service = queryQxArea(areaCode);
    }
    if (level === 2) {
      const areaCode = selected[selected.length - 1].value;
      service = queryPianQu(areaCode);
    }

    return service
      .then(res => {
        const { resultCode, resultObject, resultMsg } = res;
        if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.resultInfo) {
          return (
            resultObject?.rspParam?.busiInfo?.resultInfo.map(item => ({
              label: item.DISTRICT_NAME,
              value: item.DISTRICT_ID,
              isLeaf: level === 2, // 第三级是叶子节点
            })) || []
          );
        }
        message.error(resultMsg || '获取省份数据失败');
        return [];
      })
      .catch(() => {
        // console.error(err); // 可选：打印错误以便调试
        message.error('获取省份数据失败');
        return [];
      });
  };

  // 判断是否禁用
  // const handleDisabled = e => {
  //   const BelongingAreaInfo = form.getFieldValue('BelongingAreaInfo');
  //   console.log('获取的BelongingAreaInfo', BelongingAreaInfo);
  //   // if (e === 0) {
  //   //   return BelongingAreaInfo[0];
  //   // }
  //   // if (e === 1) {
  //   //   return BelongingAreaInfo[1];
  //   // }
  //   // if (e === 2) {
  //   //   return BelongingAreaInfo[2];
  //   // }
  //   return '';
  // };

  // console.log('BelongingAreaInfo==', BelongingAreaInfo);

  const disableFunc = current => opt => opt.value !== current;

  const disableOption = useMemo(() => {
    const BelongingAreaInfo = form.getFieldValue('BelongingAreaInfo');
    if (BelongingAreaInfo && BelongingAreaInfo.length !== 0) {
      return BelongingAreaInfo.map(item => disableFunc(item));
    }
    return [];
  }, [form.getFieldValue('BelongingAreaInfo')]);

  // 查询区县
  // const queryCounty = regionId => {
  //   queryQxArea(regionId).then(res => {
  //     const { resultCode, resultObject, resultMsg } = res;
  //     if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.resultInfo) {
  //       setCountyInfos(resultObject?.rspParam?.busiInfo?.resultInfo);
  //       // 清除市-县
  //       const formValue = {
  //         MGMT_COUNTY: undefined,
  //       };
  //       form.setFieldsValue(formValue);
  //       onValuesChange(
  //         {
  //           'BaseInfo.CHIP_AREA_CODE': '',
  //         },
  //         true
  //       );
  //     } else {
  //       message.error(resultMsg);
  //     }
  //   });
  // };
  // // 查询片区
  // const queryRegionDetails = async regionId => {
  //   const resp = await queryPianQu(regionId);
  //   const { resultCode, resultObject, resultMsg } = resp;
  //   if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.resultInfo) {
  //     setRegionDetails(resultObject?.rspParam?.busiInfo?.resultInfo);
  //   } else {
  //     message.error(resultMsg);
  //   }
  // };
  // useEffect(() => {
  //   initRegion();
  // }, []);
  // const updateFormField = useCallback(
  //   data => {
  //     if (!data.field) return;
  //     form.setFieldsValue({
  //       [data.field]: data.value,
  //     });
  //   },
  //   [form]
  // );
  // useEffect(() => {
  //   dispatch({
  //     type: 'AddGroupCust/registerMethods',
  //     payload: {
  //       componentName: 'RegionInfo',
  //       methods: { queryCounty, queryRegionDetails, updateFormField },
  //     },
  //   });

  //   return () => {
  //     dispatch({
  //       type: 'AddGroupCust/unregisterMethods',
  //       payload: { componentName: 'RegionInfo' },
  //     });
  //   };
  // }, [dispatch]);
  return (
    <>
      {/*! hasPriv('52000042') disabled */}
      <Col span={8}>
        {/* 地市：MGMT_DISTRICT 区县：MGMT_COUNTY 片区：CHIP_AREA_CODE */}
        <FormItem label="归属区域" {...formItemLayout} className={styles.textAreaFormItem}>
          {getFieldDecorator('BelongingAreaInfo', {
            // initialValue: ['471', '7104', '510101'], // 举例写死内蒙 ['150000']
            rules: [
              {
                required: true,
                message: '归属区域不能为空！',
              },
              { validator: valiArea(form.getFieldValue('BelongingAreaInfo')) },
            ],
          })(
            <AsyncCascader
              title="归属区域"
              loadData={getLocation}
              placeholder="请选择区域信息"
              displayRender={labels => labels.join('-')}
              className={styles.inputStyle}
              disableOption={currentFormFieldState?.BelongingAreaInfo?.disabled && disableOption}
              allowClear={false}
              disabledAll={viewMode === 'view'}
            />
          )}
        </FormItem>
      </Col>
      <Col span={8} hidden>
        <FormItem label="地市" {...formItemLayout} className={styles.textAreaFormItem}>
          {getFieldDecorator('MGMT_DISTRICT', {
            // rules: [{ required: true, message: '地市不能为空！' }],
          })(
            <input disabled={viewMode === 'view'} />
            // <Select placeholder="请选择" disabled={!editable} onChange={e => queryCounty(e)} className={styles.inputStyle}>
            //   {disInfos.map(({ REGION_ID, DISTRICT_NAME }) => (
            //     <Select.Option value={REGION_ID} key={REGION_ID}>
            //       {DISTRICT_NAME}
            //     </Select.Option>
            //   ))}
            // </Select>
          )}
        </FormItem>
      </Col>
      <Col span={8} hidden>
        <FormItem label="区县" {...formItemLayout} className={styles.textAreaFormItem}>
          {getFieldDecorator('MGMT_COUNTY', {
            // rules: [{ required: true, message: '区县不能为空！' }],
          })(
            <input disabled={viewMode === 'view'} />
            // <Select placeholder="请选择" disabled={!editable} onChange={e => queryRegionDetails(e)} className={styles.inputStyle}>
            //   {countyInfos.map(({ REGION_ID, DISTRICT_NAME }) => (
            //     <Select.Option value={REGION_ID} key={REGION_ID}>
            //       {DISTRICT_NAME}
            //     </Select.Option>
            //   ))}
            // </Select>
          )}
        </FormItem>
      </Col>
      <Col span={8} hidden>
        <FormItem label="片区" {...formItemLayout} className={styles.textAreaFormItem}>
          {getFieldDecorator('CHIP_AREA_CODE', {
            // rules: [{ required: true, message: '片区不能为空！' }],
          })(
            <input disabled={viewMode === 'view'} />
            // <Select placeholder="请选择" disabled={!editable} className={styles.inputStyle}>
            //   {regionDetails.map(({ REGION_ID, DISTRICT_NAME }) => (
            //     <Select.Option value={REGION_ID} key={REGION_ID}>
            //       {DISTRICT_NAME}
            //     </Select.Option>
            //   ))}
            // </Select>
          )}
        </FormItem>
      </Col>
    </>
  );
};

export default connect(({ AddGroupCust }) => ({
  initialData: AddGroupCust.initialData,
  currentFormFieldState: AddGroupCust.currentFormFieldState,
}))(Form.create()(RegionInfo));
