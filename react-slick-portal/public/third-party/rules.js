function getDataType(data) {
  var str = Object.prototype.toString.call(data);
  return str.split(' ')[1].substring(0, str.split(' ')[1].length - 1);
}

function isString(value) {
  return Object.prototype.toString.call(value) == '[object String]';
}

function isDate(obj) {
  return Object.prototype.toString.call(obj) == '[object Date]';
}

$(document).ajaxSuccess(function(event, jqXHR, ajaxOptions) {
  var data = ajaxOptions.data == undefined ? null : ajaxOptions.data;
  // 只能打印出get类型的传参
  console.group('===================');
  console.info('请求地址：%s', ajaxOptions.url);
  console.log('提交数据 [%s]: %s', getDataType(data), decodeURIComponent(data));
  if (jqXHR.responseJSON) {
    console.log(jqXHR.responseJSON);
  }
  console.groupEnd();
});

Mock.setup({
  timeout: '600',
});

// Mock.mock(/portal\/LoginController\/getVcodeFlag.do/, function(options) {
//   return Mock.mock(false);
// });

// Mock.mock(/portal\/LoginController\/login.do/, function(options) {
//   return Mock.mock({
//     resultCode: '0000',
//     userCode: 'admin',
//     sessionId: '63ba2e7c-84b5-4afe-84da-e69ef1f8e90d',
//     errNum: 0,
//     leftNum: 0,
//     userInfo: {
//       userId: 1,
//       userCode: 'admin',
//       loginCode: 'admin',
//       userOrgId: 10008,
//       password: 'XHId4XZ14vYQ82wb1b8ZAA==',
//       userName: '管思坤',
//       roleId: 92549,
//       roleName: '运营管理',
//       pwdErrCnt: 0,
//       pwdNewtime: '2019-11-06 10:13:00',
//       pwdEffectDays: 3600,
//       sysCode: '727007',
//       pwdSmsTel: 12321324329,
//       pwdStatus: '1100',
//       statusCd: '1000',
//       extParams: { userOrgId: 10008 },
//       mobilePhone: '***********',
//     },
//     orgInfo: {
//       orgId: 10008,
//       orgName: '四川省',
//       orgCode: '10008',
//       parentOrgId: -1,
//       orgLevel: 1,
//       lanId: -1,
//     },
//     staffInfo: {
//       staffId: 3044336,
//       staffCode: 'admin',
//       staffName: '王兰军',
//       partyId: 3,
//       staffType: '1000',
//       staffTypeName: '自建员工',
//       staffAccount: 'wlj',
//       statusCd: '1000',
//       saleStaffCode: 'Y62010073906',
//       regionId: 731,
//     },
//     depInfos: [
//       {
//         depId: 142,
//         parentDepId: '127',
//         departmentName: '成都分公司',
//         depLevel: 2,
//         pathCode: '142',
//         isMainDpt: '0',
//       },
//     ],
//     portalRoles: [
//       {
//         sysRoleId: 92548,
//         sysRoleName: '公司领导',
//         sysRoleCode: 'TYMH0081',
//         sysRoleType: '1100',
//         sysRoleDesc: '公司领导',
//         sysCode: 'TYMH',
//         statusCd: '1000',
//         defaultFlag: false,
//       },
//       {
//         sysRoleId: 92549,
//         sysRoleName: '运营管理',
//         sysRoleCode: 'TYMH0082',
//         sysRoleType: '1100',
//         sysRoleDesc: '运营管理',
//         sysCode: 'TYMH',
//         statusCd: '1000',
//         defaultFlag: true,
//       },
//       {
//         sysRoleId: 92550,
//         sysRoleName: '一线营销',
//         sysRoleCode: 'TYMH0083',
//         sysRoleType: '1000',
//         sysRoleDesc: '一线营销',
//         sysCode: 'TYMH',
//         statusCd: '1000',
//         defaultFlag: false,
//       },
//       {
//         sysRoleId: 92791,
//         sysRoleName: '系统管理员',
//         sysRoleCode: 'XTGLY001',
//         sysRoleType: '1100',
//         sysRoleDesc: '系统管理员',
//         sysCode: 'TYMH',
//         statusCd: '1000',
//         defaultFlag: true,
//       },
//     ],
//     changePwdHintFlag: false,
//     pwdLeftDays: 3570,
//     loginIp: '*************',
//   });
// });

// Mock.mock(/orgauth\/SysuserRecentMenuController\/getRecentMenuInfo.do/, function(options) {
//   return Mock.mock([
//     {
//       id: 2727371,
//       sysUserId: 1,
//       postId: 92550,
//       recentContent: '276104',
//       recentType: '1',
//       accessDate: '2019-12-08 11:44:48',
//       iconUrl: 'icon-gene-view-module',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuName: '角色模板配置',
//       urlAddr: 'portal/modules/templet/views/TempletConfigView?confType=1',
//       menuCode: 'TYMH_MENU_025',
//       recentMenuId: 2727371,
//     },
//     {
//       id: 2727123,
//       sysUserId: 1,
//       postId: 92550,
//       recentContent: '276105',
//       recentType: '1',
//       accessDate: '2019-11-03 19:04:24',
//       iconUrl: 'icon-gene-person-border-',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuName: '用户模板配置',
//       urlAddr: 'portal/modules/templet/views/UserTempletConfigView?confType=1',
//       menuCode: 'TYMH_MENU_901',
//       recentMenuId: 2727123,
//     },
//     {
//       id: 2727097,
//       sysUserId: 1,
//       postId: 92550,
//       recentContent: '276980',
//       recentType: '1',
//       accessDate: '2019-11-01 16:13:13',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuName: '订单查询',
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3022',
//       menuCode: 'TYMH_MENU_DDCX',
//       recentMenuId: 2727097,
//     },
//     {
//       id: 2727096,
//       sysUserId: 1,
//       postId: 92550,
//       recentContent: '2769888',
//       recentType: '1',
//       accessDate: '2019-11-01 16:08:23',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuName: '订单模板管理',
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3122',
//       menuCode: 'TYMH_MENU_DDMB',
//       recentMenuId: 2727096,
//     },
//     {
//       id: 2727095,
//       sysUserId: 1,
//       postId: 92550,
//       recentContent: '2769886',
//       recentType: '1',
//       accessDate: '2019-11-01 16:07:46',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuName: '订单超时监控',
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3123',
//       menuCode: 'TYMH_MENU_DDCSJK',
//       recentMenuId: 2727095,
//     },
//     {
//       id: 2727075,
//       sysUserId: 1,
//       postId: 92550,
//       recentContent: '2769682',
//       recentType: '1',
//       accessDate: '2019-11-01 13:56:38',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuName: '主题管理',
//       urlAddr: '[iframe]http://10.45.47.16:8491/basic/themeManage/list',
//       menuCode: 'TYMH_MENU_SPIDERCONF',
//       recentMenuId: 2727075,
//     },
//     {
//       id: 2726974,
//       sysUserId: 1,
//       postId: 92550,
//       recentContent: '276116',
//       recentType: '1',
//       accessDate: '2019-11-01 12:24:01',
//       iconUrl: 'icon-gene-radio-button-un',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuName: '菜单管理',
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       menuCode: 'TYMH_MENU_022',
//       recentMenuId: 2726974,
//     },
//     {
//       id: 2727006,
//       sysUserId: 1,
//       postId: 92550,
//       recentContent: '2769936',
//       recentType: '1',
//       accessDate: '2019-11-01 10:25:59',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuName: '沙盘地图',
//       urlAddr:
//         '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/operations/views/OperationsView',
//       menuCode: 'MKT0_MENU_ZZZX',
//       recentMenuId: 2727006,
//     },
//     {
//       id: 2727007,
//       sysUserId: 1,
//       postId: 92550,
//       recentContent: '2769946',
//       recentType: '1',
//       accessDate: '2019-10-31 21:27:14',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuName: '行客视图',
//       urlAddr:
//         '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/gov-geer-region-portrait-day/views/IndexView',
//       menuCode: 'MKT0_MENU_HANGKE',
//       recentMenuId: 2727007,
//     },
//     {
//       id: 2726996,
//       sysUserId: 1,
//       postId: 92550,
//       recentContent: '2769963',
//       recentType: '1',
//       accessDate: '2019-10-31 17:53:42',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuName: '划配规则管理',
//       urlAddr:
//         '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/locrule/views/LocRuleView',
//       menuCode: 'TYMH_MENU_RULE_MGR',
//       recentMenuId: 2726996,
//     },
//   ]);
// });

// Mock.mock(/orgauth\/FuncMenuController\/selectMenuFromSession.do/, function(options) {
//   return Mock.mock([
//     {
//       privId: 991762,
//       menuId: 2769963,
//       menuName: '划配规则管理2222',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769961,
//       menuIndex: 1,
//       menuDesc: '划配规则管理',
//       urlAddr:
//         '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/locrule/views/LocRuleView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'HPGZGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_RULE_MGR',
//     },
//     {
//       privId: 88963,
//       menuId: 276742,
//       menuName: '任务',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 10,
//       urlAddr: 'none',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'RW',
//       iconUrl: 'main_task',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_TASKMENU',
//     },
//     {
//       privId: 992002,
//       menuId: 2770222,
//       menuName: 'APP卡片-任务概要',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 139,
//       menuDesc: 'APP卡片-任务概要',
//       urlAddr: 'modules/routinetask/views/NoTaskView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPKPRWGY',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_CARD002',
//     },
//     {
//       privId: 89172,
//       menuId: 276992,
//       menuName: '查勘',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 29,
//       urlAddr: 'none',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'CK',
//       iconUrl: 'reverse_file_icon',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_EXPLORATION',
//     },
//     {
//       privId: 88958,
//       menuId: 276737,
//       menuName: '客户',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 5,
//       menuDesc: '客户',
//       urlAddr: 'none',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'KH',
//       iconUrl: 'main_cust',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_CUST',
//     },
//     {
//       privId: 992003,
//       menuId: 2770223,
//       menuName: 'APP卡片-关键指标',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 140,
//       menuDesc: 'APP卡片-关键指标',
//       urlAddr: 'modules/routinetask/views/NoTaskView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPKPGJZB',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_CARD003',
//     },
//     {
//       privId: 88959,
//       menuId: 276738,
//       menuName: '消息中心', //TODO:原来是公告 需要改成消息中心
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 6,
//       urlAddr: '/notice', //TODO:原来是none 需要改成/notice
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'GG',
//       iconUrl: 'main_notice',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_NOTICE',
//     },
//     {
//       privId: 992001,
//       menuId: 2770221,
//       menuName: 'APP卡片-待办任务',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 138,
//       menuDesc: 'APP卡片-待办任务',
//       urlAddr: 'modules/routinetask/views/NoTaskView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPKPDBRW',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_CARD001',
//     },
//     {
//       privId: 88969,
//       menuId: 276748,
//       menuName: '业务办理',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 16,
//       urlAddr: 'none',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'YWBL',
//       iconUrl: 'quick_order_btn',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_QUICKORDER',
//     },
//     {
//       privId: 88965,
//       menuId: 276744,
//       menuName: '消息',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 12,
//       urlAddr: 'none',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'XX',
//       iconUrl: 'ic_home_message',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_MESSAGE',
//     },
//     {
//       privId: 88966,
//       menuId: 276745,
//       menuName: '合同',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 13,
//       urlAddr: 'http://gz.iwhalecloud.com:10064/dist/#/contractList',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'HT',
//       iconUrl: 'home_toker_list_archiving',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_CONTRACT',
//     },
//     {
//       privId: 88968,
//       menuId: 276747,
//       menuName: '网络测速',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 15,
//       urlAddr: 'none',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'WLCS',
//       iconUrl: 'home_toker_test_speed',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_SPEEDTEST',
//     },
//     {
//       privId: 89194,
//       menuId: 277012,
//       menuName: '渠道运营位管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770043,
//       menuIndex: 8,
//       urlAddr: '[iframe]http://10.45.47.18:3430/ire/channelOperation/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'QDYYWGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_QUDAO',
//     },
//     {
//       privId: 89195,
//       menuId: 277013,
//       menuName: '商品展示',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770043,
//       menuIndex: 3,
//       urlAddr: '[iframe]http://10.45.47.18:3430/ire/commodityManage/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'SPZS',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_OFFER',
//     },
//     {
//       privId: 89196,
//       menuId: 277014,
//       menuName: '创意话术管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770043,
//       menuIndex: 4,
//       urlAddr: '[iframe]http://10.45.47.18:3430/ire/creativeIdeaManage/?sso=1',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'CYHSGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'KGXT_MENU_HUASHU',
//     },
//     {
//       privId: 991781,
//       menuId: 2769982,
//       menuName: '产商品配置',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769981,
//       menuIndex: 1,
//       menuDesc: '产商品配置',
//       urlAddr:
//         '[iframe]http://172.21.96.65:13280/portal/iframe.html?pc/modules/offerConfig/views/OfferConfigView&user_name=admin',
//       systemCode: '756142',
//       statusCd: '1000',
//       firstLetter: 'CSPPZ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'CPC_MENU_0001',
//     },
//     {
//       privId: 89191,
//       menuId: 277009,
//       menuName: '政企稽核',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769882,
//       menuIndex: 3,
//       menuDesc: '政企稽核',
//       urlAddr: 'audit-web/audit/modules/core/views/manual-audit/EnterpriseManualAuditMainView.js',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZQJH',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_ENTERPRISE_AUDTI_NEW',
//     },
//     {
//       privId: 89192,
//       menuId: 277010,
//       menuName: '活动配置',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770043,
//       menuIndex: 1,
//       urlAddr:
//         '[iframe]http://10.45.47.18:3430/ire/activityConfigManage/marketingActivityList/?sso=1',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'HDPZ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_HUODONG',
//     },
//     {
//       privId: 89193,
//       menuId: 277011,
//       menuName: '业务办理',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276975,
//       menuIndex: 4,
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3042',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'YWBL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_YWBL',
//     },
//     {
//       privId: 992006,
//       menuId: 2770226,
//       menuName: 'APP卡片-业务视图',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 143,
//       menuDesc: 'APP卡片-业务视图',
//       urlAddr: 'modules/routinetask/views/NoTaskView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPKPYWST',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_CARD006',
//     },
//     {
//       privId: 992007,
//       menuId: 2770227,
//       menuName: 'APP卡片-发展趋势',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 144,
//       menuDesc: 'APP卡片-发展趋势',
//       urlAddr: 'modules/routinetask/views/NoTaskView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPKPFZQS',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_CARD007',
//     },
//     {
//       privId: 992004,
//       menuId: 2770224,
//       menuName: 'APP卡片-业绩排行',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 141,
//       menuDesc: 'APP卡片-业绩排行',
//       urlAddr: 'modules/routinetask/views/NoTaskView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPKPYJPX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_CARD004',
//     },
//     {
//       privId: 89203,
//       menuId: 277023,
//       menuName: '标签管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770043,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.47.18:3430/ire/labelManage/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'BQGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_LABEL',
//     },
//     {
//       privId: 992005,
//       menuId: 2770225,
//       menuName: 'APP卡片-区域排行',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 142,
//       menuDesc: 'APP卡片-区域排行',
//       urlAddr: 'modules/routinetask/views/NoTaskView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPKPQYPX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_CARD005',
//     },
//     {
//       privId: 88981,
//       menuId: 276761,
//       menuName: '客户任务',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274651,
//       parMenuName: '客户中心',
//       menuIndex: 2,
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/operator/dist/index.html#/cust/auditManage/custTask?viewMode=inner&language=zh-CN',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'KHRW',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'KGXT_MENU_115',
//     },
//     {
//       privId: 992008,
//       menuId: 2770228,
//       menuName: 'APP卡片-知识专区',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 145,
//       menuDesc: 'APP卡片-知识专区',
//       urlAddr: 'modules/routinetask/views/NoTaskView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPKPZSZQ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_CARD008',
//     },
//     {
//       privId: 89200,
//       menuId: 277019,
//       menuName: '查勘任务',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276890,
//       menuIndex: 4,
//       menuDesc: '查勘任务',
//       urlAddr: '[iframe]http://10.45.46.210:9000/bsc-web/ssoLogin?menuCode=2881',
//       systemCode: '727007',
//       statusCd: '1000',
//       firstLetter: 'CKRW',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'RWGL_MENU_TASK009-01',
//     },
//     {
//       privId: 991964,
//       menuId: 2770186,
//       menuName: '产品管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770182,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=39',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'CPGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_DDCPGL',
//     },
//     {
//       privId: 89209,
//       menuId: 277029,
//       menuName: '我的查勘',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 277020,
//       menuIndex: 1,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bsc-web/ssoLogin?menuCode=2901',
//       systemCode: '727006',
//       statusCd: '1000',
//       firstLetter: 'WDCK',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'SALES_MENU_01',
//     },
//     {
//       privId: 991962,
//       menuId: 2770184,
//       menuName: '环节岗位管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770182,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3010',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'HJGWGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_DD_HJGWGL',
//     },
//     {
//       privId: 991737,
//       menuId: 2769928,
//       menuName: '特殊分群管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770043,
//       menuIndex: 5,
//       menuDesc: '特殊分群管理',
//       urlAddr: '[iframe]http://10.45.47.18:3430/ire/specialGroup/?sso=1',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'TSFQGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_TeShuFenQun',
//     },
//     {
//       privId: 991963,
//       menuId: 2770185,
//       menuName: '属性规格管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770182,
//       menuIndex: 3,
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=38',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'SXGGGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_DD_SXGGGL',
//     },
//     {
//       privId: 991743,
//       menuId: 2769934,
//       menuName: '工单列表',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770044,
//       menuIndex: 1,
//       menuDesc: '工单汇总列表',
//       urlAddr: '[iframe]http://10.45.47.18:3430/ire/workOrder/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'GDLB',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_GongDanHuiZong',
//     },
//     {
//       privId: 991744,
//       menuId: 2769935,
//       menuName: '活动视图管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770044,
//       menuIndex: 2,
//       menuDesc: '活动视图管理',
//       urlAddr: '[iframe]http://10.45.47.18:3430/ire/activityWork/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'HDSTGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_HuoDongShiTu',
//     },
//     {
//       privId: 89205,
//       menuId: 277025,
//       menuName: '稽核目录',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769881,
//       menuIndex: 5,
//       menuDesc: '稽核目录',
//       urlAddr: 'audit-web/audit/modules/core/views/AuditCatalogMainView.js',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'JHML',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_AUDIT_CATALOG',
//     },
//     {
//       privId: 991741,
//       menuId: 2769932,
//       menuName: '活动模板管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770043,
//       menuIndex: 7,
//       menuDesc: '活动模板管理',
//       urlAddr: '[iframe]http://10.45.47.18:3430/ire/activityBase/activityTemplate/?sso=1',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'HDMBGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_HuoDongMuBan',
//     },
//     {
//       privId: 991742,
//       menuId: 2769933,
//       menuName: '活动评估',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770044,
//       menuIndex: 3,
//       menuDesc: '活动评估',
//       urlAddr: '[iframe]http://10.45.47.18:3430/ire/activityScheduling/?sso=1',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'HDPG',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_HuoDongPingGu',
//     },
//     {
//       privId: 991961,
//       menuId: 2770183,
//       menuName: '订单分拣策略管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770182,
//       menuIndex: 5,
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3282',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'DDFJCLGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_DDFJCLGL',
//     },
//     {
//       privId: 991736,
//       menuId: 2769927,
//       menuName: '活动模板要素管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770043,
//       menuIndex: 6,
//       urlAddr: '[iframe]http://10.45.47.18:3430/ire/templateElement/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'HDMBYSGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_HDYS',
//     },
//     {
//       privId: 991754,
//       menuId: 2769946,
//       menuName: '行客视图',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276669,
//       menuIndex: 9,
//       urlAddr:
//         '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/gov-geer-region-portrait-day/views/IndexView',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'XKST',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_HANGKE',
//     },
//     {
//       privId: 991755,
//       menuId: 2769947,
//       menuName: '商客视图',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276669,
//       menuIndex: 10,
//       urlAddr:
//         '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/gov-gebr-region-portrait-day/views/IndexView',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'SKST',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_SHANGKE',
//     },
//     {
//       privId: 991752,
//       menuId: 2769944,
//       menuName: '招标分析',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769926,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.47.16:8491/analysis/tender',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZBFX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_ZBFX',
//     },
//     {
//       privId: 991753,
//       menuId: 2769945,
//       menuName: '资产分析',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769926,
//       menuIndex: 3,
//       urlAddr: '[iframe]http://10.45.47.16:8491/analysis/assets',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZCFX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_ZCFX',
//     },
//     {
//       privId: 991981,
//       menuId: 2770201,
//       menuName: '任务概述',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 137,
//       menuDesc: '任务概述',
//       urlAddr: 'modules/workbench/views/TaskSummaryView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'RWGS',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_0109',
//     },
//     {
//       privId: 991756,
//       menuId: 2769948,
//       menuName: '校园视图',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276669,
//       menuIndex: 11,
//       urlAddr:
//         '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/gov-gesr-school-portrait-day/views/IndexView',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'XYST',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_XIAOYUAN',
//     },
//     {
//       privId: 991757,
//       menuId: 2769949,
//       menuName: '政企全视图',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276669,
//       menuIndex: 12,
//       urlAddr:
//         '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/gov-ger-region-portrait-day/views/IndexView',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'ZQQST',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_ZHENGQIALL',
//     },
//     {
//       privId: 991747,
//       menuId: 2769938,
//       menuName: 'APP版本配置',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276649,
//       menuIndex: 2,
//       menuDesc: 'APP版本配置',
//       urlAddr: 'modules/appversion/views/appVersionView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPBBPZ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_081',
//     },
//     {
//       privId: 891502,
//       menuId: 2769682,
//       menuName: '主题管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769925,
//       menuIndex: 3,
//       urlAddr: '[iframe]http://10.45.47.16:8491/basic/themeManage/list',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZTGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_SPIDERCONF',
//     },
//     {
//       privId: 891503,
//       menuId: 2769683,
//       menuName: '主题执行详情',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769925,
//       menuIndex: 4,
//       urlAddr: '[iframe]http://10.45.47.16:8491/basic/runDetail/list',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZTZXXQ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_TIMINGTASK',
//     },
//     {
//       privId: 991748,
//       menuId: 2769939,
//       menuName: 'APP参数配置',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276649,
//       menuIndex: 3,
//       menuDesc: 'APP参数配置',
//       urlAddr: 'portal/modules/sys/views/AppDataDictView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPCSPZ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_082',
//     },
//     {
//       privId: 991745,
//       menuId: 2769936,
//       menuName: '沙盘地图',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276669,
//       menuIndex: 8,
//       urlAddr:
//         '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/operations/views/OperationsView',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'SPDT',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_ZZZX',
//     },
//     {
//       privId: 991746,
//       menuId: 2769937,
//       menuName: 'APP信息配置',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276649,
//       menuIndex: 1,
//       menuDesc: 'APP信息配置',
//       urlAddr: 'portal/modules/appinfoconf/views/AppInfoConfView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPXXPZ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_080',
//     },
//     {
//       privId: 86430,
//       menuId: 274744,
//       menuName: 'APP菜单',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 4,
//       menuDesc: 'APP000001',
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'APPCD',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'APP000001',
//     },
//     {
//       privId: 991751,
//       menuId: 2769943,
//       menuName: '企业分析',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769926,
//       menuIndex: 1,
//       urlAddr: '[iframe]http://10.45.47.16:8491/analysis/enterprise',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'QYFX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_QYFX',
//     },
//     {
//       privId: 891504,
//       menuId: 2769684,
//       menuName: '站点模板',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769925,
//       menuIndex: 5,
//       urlAddr: '[iframe]http://10.45.47.16:8491/basic/site',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZDMB',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_RULE',
//     },
//     {
//       privId: 991749,
//       menuId: 2769940,
//       menuName: 'APP操作日志管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276649,
//       menuIndex: 4,
//       menuDesc: 'APP操作日志管理',
//       urlAddr: 'orgauth/modules/appactionlog/views/AppActionLogView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPCZRZGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_083',
//     },
//     {
//       privId: 991750,
//       menuId: 2769941,
//       menuName: '吐槽信息管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276649,
//       menuIndex: 5,
//       menuDesc: '吐槽信息管理',
//       urlAddr: 'modules/appfeedback/views/AppFeedbackView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'TCXXGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_084',
//     },
//     {
//       privId: 89021,
//       menuId: 276813,
//       menuName: '潜在客户',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274651,
//       parMenuName: '客户中心',
//       menuIndex: 1,
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/operator/dist/index.html#/cust/manage/potentialcustInfo?viewMode=inner&language=zh-CN',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'QZKH',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'KGXT_MENU_11',
//     },
//     {
//       privId: 991821,
//       menuId: 2770021,
//       menuName: '产商品审批',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769981,
//       menuIndex: 5,
//       menuDesc: '待办待阅',
//       urlAddr:
//         '[iframe]http://172.21.96.65:13280/portal/iframe.html?so/modules/todocenter/todoandread/views/OrderTabView&user_name=admin',
//       systemCode: '756142',
//       statusCd: '1000',
//       firstLetter: 'CSPSP',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'CPC_MENU_0005',
//     },
//     {
//       privId: 991561,
//       menuId: 2769741,
//       menuName: '协议续签提醒',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276954,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bcm-web/ssoLogin?menuCode=2120',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'XYXQTX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_CONTRACT_REMIND',
//     },
//     {
//       privId: 992021,
//       menuId: 2770241,
//       menuName: '任务统计',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 30,
//       menuDesc: '任务统计',
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'RWTJ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_TASK',
//     },
//     {
//       privId: 89050,
//       menuId: 276849,
//       menuName: '环节处理配置',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276848,
//       menuIndex: 1,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bsc-web/ssoLogin?menuCode=396',
//       systemCode: '727006',
//       statusCd: '1000',
//       firstLetter: 'HJCLPZ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'SALES_MENU_396',
//     },
//     {
//       privId: 991802,
//       menuId: 2770002,
//       menuName: '网格管理',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769961,
//       menuIndex: 3,
//       menuDesc: '网格管理',
//       urlAddr:
//         '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/grid-management/views/gridManageView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'WGGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_GRID',
//     },
//     {
//       privId: 991801,
//       menuId: 2770001,
//       menuName: '划配规则配置',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769961,
//       menuIndex: 2,
//       menuDesc: '划配规则配置',
//       urlAddr:
//         '[iframe]http://10.45.47.18:8088/mst-pc/web/main.html?page=modules/locrule/views/LocRuleCfgView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'HPGZPZ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_RULE_CFG',
//     },
//     {
//       privId: 991905,
//       menuId: 2770090,
//       menuName: '企业分析',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770083,
//       menuIndex: 1,
//       urlAddr: '[iframe]http://10.45.47.16:8493/analysis/enterprise',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'QYFX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_ORQY',
//     },
//     {
//       privId: 991906,
//       menuId: 2770091,
//       menuName: '招标分析',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770083,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.47.16:8493/analysis/tender',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZBFX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_ORZHFX',
//     },
//     {
//       privId: 991904,
//       menuId: 2770089,
//       menuName: '计费属性',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770088,
//       menuIndex: 1,
//       menuDesc: '计费属性',
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/chgc-web/iframe.html?chgc/modules/chargingcenter/attr/views/AttrView&lang=zh&version=firekylin',
//       systemCode: '727011',
//       statusCd: '1000',
//       firstLetter: 'JFSX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'BILLING_MENU_BILL002',
//     },
//     {
//       privId: 991909,
//       menuId: 2770094,
//       menuName: '企业信息库',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770084,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.47.16:8493/marketOpportunity/enterpriseInfoBase',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'QYXXK',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_ORQYXX',
//     },
//     {
//       privId: 991910,
//       menuId: 2770095,
//       menuName: '计费事件',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770088,
//       menuIndex: 2,
//       menuDesc: '计费事件',
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/chgc-web/iframe.html?chgc/modules/chargingcenter/event/views/ReView&lang=zh&version=firekylin',
//       systemCode: '727011',
//       statusCd: '1000',
//       firstLetter: 'JFSJ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'BILLING_MENU_BILL003',
//     },
//     {
//       privId: 991907,
//       menuId: 2770092,
//       menuName: '资产分析',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770083,
//       menuIndex: 3,
//       urlAddr: '[iframe]http://10.45.47.16:8493/analysis/assets',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZCFX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_ORZBFX',
//     },
//     {
//       privId: 991908,
//       menuId: 2770093,
//       menuName: '招标信息库',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770084,
//       menuIndex: 1,
//       urlAddr: '[iframe]http://10.45.47.16:8493/marketOpportunity/BiddingMsg',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZBXXK',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_ZBXX',
//     },
//     {
//       privId: 991645,
//       menuId: 2769826,
//       menuName: '客户维护记录',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274651,
//       parMenuName: '客户中心',
//       menuIndex: 4,
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/operator/dist/index.html#/cust/manage/custModRec?viewMode=inner&language=zh-CN',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'KHWHJL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'KGXT_MENU_MODIF_REC',
//     },
//     {
//       privId: 88350,
//       menuId: 276108,
//       menuName: '待办',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 9,
//       menuDesc: '待办',
//       urlAddr: 'modules/workbench/views/WaitToDoView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'DB',
//       iconUrl: 'icon-gene-redpacket',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_005',
//     },
//     {
//       privId: 88351,
//       menuId: 276109,
//       menuName: '系统公告',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 7,
//       menuDesc: '公告',
//       urlAddr: '/bulletin',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'XTGG',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_001',
//     },
//     {
//       privId: 991646,
//       menuId: 2769827,
//       menuName: '审批人设置',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274651,
//       parMenuName: '客户中心',
//       menuIndex: 5,
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/operator/dist/index.html#/cust/auditManage/auditorSetting?viewMode=inner&language=zh-CN',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'SPRSZ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'KGXT_MENU_AUDIT_SETTING',
//     },
//     {
//       privId: 991643,
//       menuId: 2769824,
//       menuName: '成员调配',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769821,
//       menuIndex: 3,
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/operator/dist/index.html#/cust/corpAllocate/MemberAllocate?viewMode=inner&language=zh-CN',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'CYDP',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'KGXT_MENU_ALLOC_MEM',
//     },
//     {
//       privId: 991644,
//       menuId: 2769825,
//       menuName: '成员调配记录',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769821,
//       menuIndex: 4,
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/operator/dist/index.html#/cust/corpAllocate/MemberAllocateRecord?viewMode=inner&language=zh-CN',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'CYDPJL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'KGXT_MENU_ALLOC_MEM_REC',
//     },
//     {
//       privId: 88352,
//       menuId: 276111,
//       menuName: '员工管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276110,
//       menuIndex: 7,
//       menuDesc: '员工管理',
//       urlAddr: '/orgManage/memberManage',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'YGGL',
//       iconUrl: 'icon-gene-perm-identity',
//       paramEncryptType: '1000',
//       menuOpenMode: '3',
//       menuCode: 'TYMH_MENU_019',
//     },
//     {
//       privId: 991902,
//       menuId: 2770086,
//       menuName: '主题执行详情',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770082,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.47.16:8493/basic/runDetail/list',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZTZXXQ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_ORZX',
//     },
//     {
//       privId: 88346,
//       menuId: 276104,
//       menuName: '角色模板配置',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 1,
//       menuDesc: '模板配置',
//       urlAddr: 'portal/modules/templet/views/TempletConfigView?confType=1',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'JSMBPZ',
//       iconUrl: 'icon-gene-view-module',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_025',
//     },
//     {
//       privId: 88347,
//       menuId: 276105,
//       menuName: '用户模板配置',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 3,
//       menuDesc: '用户模板配置',
//       urlAddr: '/customize/user',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'YHMBPZ',
//       iconUrl: 'icon-gene-person-border-',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_901',
//     },
//     {
//       privId: 991903,
//       menuId: 2770087,
//       menuName: '站点模板',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770082,
//       menuIndex: 3,
//       urlAddr: '[iframe]http://10.45.47.16:8491/basic/site',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZDMB',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_ORZD',
//     },
//     {
//       privId: 88348,
//       menuId: 276106,
//       menuName: '用户功能模块配置',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 4,
//       menuDesc: '用户功能模块配置',
//       urlAddr: 'portal/modules/templet/views/UserModuleConfigView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'YHGNMKPZ',
//       iconUrl: 'icon-gene-man-client',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_902',
//     },
//     {
//       privId: 991901,
//       menuId: 2770085,
//       menuName: '主题管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770082,
//       menuIndex: 1,
//       urlAddr: '[iframe]http://10.45.47.16:8493/basic/themeManage/list',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZTGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_ORZT',
//     },
//     {
//       privId: 88349,
//       menuId: 276107,
//       menuName: 'APP模板配置',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 128,
//       menuDesc: 'APP模板配置',
//       urlAddr: 'portal/modules/apptemplet/views/AppTempView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'APPMBPZ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_903',
//     },
//     {
//       privId: 88356,
//       menuId: 276116,
//       menuName: '菜单管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276729,
//       menuIndex: 1,
//       menuDesc: '菜单管理',
//       urlAddr: '/authManage/menuManage',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'CDGL',
//       iconUrl: 'icon-gene-radio-button-un',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_022',
//     },
//     {
//       privId: 991668,
//       menuId: 2769850,
//       menuName: '我的考核',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 135,
//       menuDesc: '我的考核',
//       urlAddr: 'modules/workbench/views/MyAssessmentView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'WDKH',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_0106',
//     },
//     {
//       privId: 991669,
//       menuId: 2769851,
//       menuName: '任务统计',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 136,
//       menuDesc: '任务统计',
//       urlAddr: 'modules/workbench/views/TaskStatisticalView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'RWTJ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_0107',
//     },
//     {
//       privId: 88890,
//       menuId: 276629,
//       menuName: '商机',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 2,
//       urlAddr: 'none',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'SJ',
//       iconUrl: 'tool_menu_business',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_MY_BUSINESS',
//     },
//     {
//       privId: 991666,
//       menuId: 2769848,
//       menuName: '客户增长趋势',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 133,
//       menuDesc: '客户增长趋势',
//       urlAddr: 'modules/workbench/views/GrowthTrendView2',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'KHZZQS',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_0104',
//     },
//     {
//       privId: 88358,
//       menuId: 276118,
//       menuName: '公告管理',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276102,
//       menuIndex: 8,
//       menuDesc: '公告管理',
//       urlAddr: '/bulletin/manage',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'GGGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_801',
//     },
//     {
//       privId: 991667,
//       menuId: 2769849,
//       menuName: '订单增长趋势',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 134,
//       menuDesc: '订单增长趋势',
//       urlAddr: 'modules/workbench/views/GrowthTrendView3',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'DDZZQS',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_0105',
//     },
//     {
//       privId: 88359,
//       menuId: 276119,
//       menuName: '系统公告',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276102,
//       menuIndex: 111,
//       urlAddr: '/notice',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'XTGG',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_008',
//     },
//     {
//       privId: 88353,
//       menuId: 276112,
//       menuName: '用户管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276110,
//       menuIndex: 8,
//       urlAddr: 'orgauth/modules/systemuser/views/SysUserView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'YHGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_020',
//     },
//     {
//       privId: 88354,
//       menuId: 276113,
//       menuName: '区域管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276110,
//       menuIndex: 9,
//       menuDesc: '部门管理',
//       urlAddr: 'orgauth/modules/org/views/OrgManageView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'QYGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_030',
//     },
//     {
//       privId: 88355,
//       menuId: 276114,
//       menuName: '组织管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276110,
//       menuIndex: 12,
//       menuDesc: '部门管理',
//       urlAddr: 'orgauth/modules/department/views/DepManageView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZZGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_060',
//     },
//     {
//       privId: 88364,
//       menuId: 276124,
//       menuName: '下载中心',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276102,
//       menuIndex: 109,
//       menuDesc: '下载中心',
//       urlAddr: 'modules/downloadcenter/views/DownLoadCenterManageView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'XZZX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_009',
//     },
//     {
//       privId: 88897,
//       menuId: 276636,
//       menuName: '线索',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 9,
//       menuDesc: '\n',
//       urlAddr: 'none',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'XS',
//       iconUrl: 'order_trace_icon',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_CLUE',
//     },
//     {
//       privId: 991913,
//       menuId: 2770098,
//       menuName: '分拣流程',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770088,
//       menuIndex: 5,
//       menuDesc: '分拣流程',
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/chgc-web/iframe.html?chgc/modules/chargingcenter/sorting/views/SortingView&lang=zh&version=firekylin',
//       systemCode: '727011',
//       statusCd: '1000',
//       firstLetter: 'FJLC',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'BILLING_MENU_BILL6',
//     },
//     {
//       privId: 88898,
//       menuId: 276637,
//       menuName: '发现',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 10,
//       urlAddr: 'none',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'FX',
//       iconUrl: 'main_discovery',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_DISCOVERY',
//     },
//     {
//       privId: 991914,
//       menuId: 2770099,
//       menuName: '话单源',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770088,
//       menuIndex: 6,
//       menuDesc: '话单源',
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/chgc-web/iframe.html?chgc/modules/chargingcenter/event/views/EventSourceView&lang=zh&version=firekylin',
//       systemCode: '727011',
//       statusCd: '1000',
//       firstLetter: 'HDY',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'BILLING_MENU_BILL007',
//     },
//     {
//       privId: 991911,
//       menuId: 2770096,
//       menuName: '账目类型',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770088,
//       menuIndex: 3,
//       menuDesc: '账目类型',
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/chgc-web/iframe.html?chgc/modules/chargingcenter/acctitem/views/AccountItemTypeView&lang=zh&version=firekylin',
//       systemCode: '727011',
//       statusCd: '1000',
//       firstLetter: 'ZMLX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'BILLING_MENU_BILL004',
//     },
//     {
//       privId: 991912,
//       menuId: 2770097,
//       menuName: '计费流程',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770088,
//       menuIndex: 4,
//       menuDesc: '计费流程',
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/chgc-web/iframe.html?chgc/modules/chargingcenter/ratingflow/views/RatingFlowView&lang=zh&version=firekylin',
//       systemCode: '727011',
//       statusCd: '1000',
//       firstLetter: 'JFLC',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'BILLING_MENU_BILL005',
//     },
//     {
//       privId: 88360,
//       menuId: 276120,
//       menuName: '系统参数配置',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276766,
//       menuIndex: 20,
//       menuDesc: '系统参数配置',
//       urlAddr: 'portal/modules/sys/views/DataDictView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'XTCSPZ',
//       iconUrl: 'icon-gene-account-balance1',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_002',
//     },
//     {
//       privId: 991664,
//       menuId: 2769846,
//       menuName: '状态分布图',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 131,
//       menuDesc: '状态分布图',
//       urlAddr: 'modules/workbench/views/StateMapView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZTFBT',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_0102',
//     },
//     {
//       privId: 991665,
//       menuId: 2769847,
//       menuName: '增长趋势',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 132,
//       menuDesc: '增长趋势',
//       urlAddr: 'modules/workbench/views/GrowthTrendView1',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZZQS',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_0103',
//     },
//     {
//       privId: 88361,
//       menuId: 276121,
//       menuName: '登录状态管理',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276102,
//       menuIndex: 124,
//       menuDesc: '4G新装',
//       urlAddr: '/account', // TODO
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'DLZTGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_026',
//     },
//     {
//       privId: 991662,
//       menuId: 2769844,
//       menuName: '关键指标',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 129,
//       menuDesc: '关键指标',
//       urlAddr: 'modules/workbench/views/KeyIndicatorsView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'GJZB',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_0100',
//     },
//     {
//       privId: 991915,
//       menuId: 2770100,
//       menuName: '资费计划',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770088,
//       menuIndex: 7,
//       menuDesc: '资费计划',
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/chgc-web/iframe.html?chgc/modules/chargingcenter/price/views/OfferView&lang=zh&version=firekylin',
//       systemCode: '727011',
//       statusCd: '1000',
//       firstLetter: 'ZFJH',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'BILLING_MENU_BILL008',
//     },
//     {
//       privId: 88362,
//       menuId: 276122,
//       menuName: '系统信息配置',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276766,
//       menuIndex: 101,
//       menuDesc: '系统信息配置归属系统',
//       urlAddr: 'portal/modules/sys/views/SystemInfoView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'XTXXPZ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_201',
//     },
//     {
//       privId: 88895,
//       menuId: 276634,
//       menuName: '营销沙盘',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 7,
//       urlAddr: 'none',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'YXSP',
//       iconUrl: 'main_indicator',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_INDICATOR',
//     },
//     {
//       privId: 88363,
//       menuId: 276123,
//       menuName: '门户缓存刷新',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276102,
//       menuIndex: 103,
//       menuDesc: '门户缓存刷新',
//       urlAddr: 'modules/refreshcache/views/cacheView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'MHHCSX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_003',
//     },
//     {
//       privId: 991663,
//       menuId: 2769845,
//       menuName: '客户经理排名',
//       menuLevel: -1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276103,
//       menuIndex: 130,
//       menuDesc: '客户经理排名',
//       urlAddr: 'modules/workbench/views/IndicatorsRankView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'KHJLPM',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_0101',
//     },
//     {
//       privId: 88896,
//       menuId: 276635,
//       menuName: '分享',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 8,
//       urlAddr: 'none',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'FX',
//       iconUrl: 'main_share',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_SHARE',
//     },
//     {
//       privId: 991916,
//       menuId: 2770101,
//       menuName: '账期',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770088,
//       menuIndex: 8,
//       menuDesc: '账期',
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/chgc-web/iframe.html?chgc/modules/chargingcenter/billingcycle/views/BillingCycleView&lang=zh&version=firekylin',
//       systemCode: '727011',
//       statusCd: '1000',
//       firstLetter: 'ZQ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'BILLING_MENU_BILL009',
//     },
//     {
//       privId: 88590,
//       menuId: 276388,
//       menuName: '角色管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276729,
//       menuIndex: 2,
//       urlAddr: '/authManage/roleManage',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'JSGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_023',
//     },
//     {
//       privId: 991861,
//       menuId: 2770063,
//       menuName: '活动配置-oracle',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770062,
//       menuIndex: 1,
//       urlAddr:
//         '[iframe]http://10.45.47.18:3428/ire/activityConfigManage/marketingActivityList/?sso=1',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'HDPZORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_HUODONGO',
//     },
//     {
//       privId: 89120,
//       menuId: 276908,
//       menuName: '集团营销',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276890,
//       menuIndex: 8,
//       menuDesc: '集团营销',
//       urlAddr: 'modules/routinetask/views/NoTaskView',
//       systemCode: '727007',
//       statusCd: '1000',
//       firstLetter: 'JTYX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'RWGL_MENU_TASK002_1',
//     },
//     {
//       privId: 89122,
//       menuId: 276910,
//       menuName: '驻点拜访',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276890,
//       menuIndex: 2,
//       menuDesc: '驻点拜访',
//       urlAddr: 'modules/routinetask/views/NoTaskView',
//       systemCode: '727007',
//       statusCd: '1000',
//       firstLetter: 'ZDBF',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'RWGL_MENU_TASK003_1',
//     },
//     {
//       privId: 89123,
//       menuId: 276911,
//       menuName: '生日关怀',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276890,
//       menuIndex: 9,
//       menuDesc: '生日关怀',
//       urlAddr: 'modules/routinetask/views/NoTaskView',
//       systemCode: '727007',
//       statusCd: '1000',
//       firstLetter: 'SRGH',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'RWGL_MENU_TASK004_1',
//     },
//     {
//       privId: 991871,
//       menuId: 2770074,
//       menuName: '活动评估-oracle',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770071,
//       menuIndex: 3,
//       urlAddr: '[iframe]http://10.45.47.18:3428/ire/activityScheduling/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'HDPGORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_HDPGO',
//     },
//     {
//       privId: 991869,
//       menuId: 2770072,
//       menuName: '工单列表-oracle',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770071,
//       menuIndex: 1,
//       urlAddr: '[iframe]http://10.45.47.18:3428/ire/workOrder/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'GDLBORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_GDLBO',
//     },
//     {
//       privId: 991870,
//       menuId: 2770073,
//       menuName: '活动视图管理-oracle',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770071,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.47.18:3428/ire/activityWork/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'HDSTGLORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_HDSTO',
//     },
//     {
//       privId: 89124,
//       menuId: 276912,
//       menuName: '客户任务',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276890,
//       menuIndex: 1,
//       menuDesc: '客户任务',
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/operator/dist/index.html#/cust/auditManage/custTask?viewMode=inner&language=zh-CN',
//       systemCode: '727007',
//       statusCd: '1000',
//       firstLetter: 'KHRW',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'RWGL_MENU_TASK005_1',
//     },
//     {
//       privId: 991641,
//       menuId: 2769822,
//       menuName: '集团调配',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769821,
//       menuIndex: 1,
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/operator/dist/index.html#/cust/corpAllocate/corpAllocate?viewMode=inner&language=zh-CN',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'JTDP',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'KGXT_MENU_ALLOC_GRP',
//     },
//     {
//       privId: 89125,
//       menuId: 276913,
//       menuName: '商机任务',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276890,
//       menuIndex: 3,
//       menuDesc: '商机任务',
//       urlAddr: '[iframe]http://10.45.46.210:9000/bsc-web/ssoLogin?menuCode=2900',
//       systemCode: '727007',
//       statusCd: '1000',
//       firstLetter: 'SJRW',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'RWGL_MENU_TASK006_1',
//     },
//     {
//       privId: 991642,
//       menuId: 2769823,
//       menuName: '集团调配记录',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769821,
//       menuIndex: 2,
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/operator/dist/index.html#/cust/corpAllocate/CorpAllocateRecord?viewMode=inner&language=zh-CN',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'JTDPJL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'KGXT_MENU_ALLOC_GRP_REC',
//     },
//     {
//       privId: 89126,
//       menuId: 276914,
//       menuName: '合同任务',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276890,
//       menuIndex: 5,
//       menuDesc: '合同任务',
//       urlAddr: '[iframe]http://10.45.46.210:9000/bcm-web/ssoLogin?menuCode=162',
//       systemCode: '727007',
//       statusCd: '1000',
//       firstLetter: 'HTRW',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'RWGL_MENU_TASK007_1',
//     },
//     {
//       privId: 89127,
//       menuId: 276915,
//       menuName: '订单任务',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276890,
//       menuIndex: 7,
//       menuDesc: '订单任务',
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3009',
//       systemCode: '727007',
//       statusCd: '1000',
//       firstLetter: 'DDRW',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'RWGL_MENU_TASK008_1',
//     },
//     {
//       privId: 991864,
//       menuId: 2770066,
//       menuName: '创意话术管理-oracle',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770062,
//       menuIndex: 4,
//       urlAddr: '[iframe]http://10.45.47.18:3428/ire/creativeIdeaManage/?sso=1',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'CYHSGLORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'KGXT_MENU_HUASHUO',
//     },
//     {
//       privId: 991865,
//       menuId: 2770067,
//       menuName: '特殊分群管理-oracle',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770062,
//       menuIndex: 5,
//       urlAddr: '[iframe]http://10.45.47.18:3428/ire/specialGroup/?sso=1',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'TSFQGLORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_TESFQO',
//     },
//     {
//       privId: 991862,
//       menuId: 2770064,
//       menuName: '标签管理-oracle',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770062,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.47.18:3428/ire/labelManage/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'BQGLORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_LABELO',
//     },
//     {
//       privId: 991863,
//       menuId: 2770065,
//       menuName: '商品展示-oracle',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770062,
//       menuIndex: 3,
//       urlAddr: '[iframe]http://10.45.47.18:3428/ire/commodityManage/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'SPZSORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_OFFERO',
//     },
//     {
//       privId: 991868,
//       menuId: 2770070,
//       menuName: '渠道运营位管理-oracle',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770062,
//       menuIndex: 8,
//       urlAddr: '[iframe]http://10.45.47.18:3428/ire/channelOperation/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'QDYYWGLORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_QUDAOO',
//     },
//     {
//       privId: 991866,
//       menuId: 2770068,
//       menuName: '活动模板要素管理-oracle',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770062,
//       menuIndex: 6,
//       urlAddr: '[iframe]http://10.45.47.18:3428/ire/templateElement/?sso=1',
//       systemCode: '727005',
//       statusCd: '1000',
//       firstLetter: 'HDMBYSGLORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'MKT0_MENU_HDYSO',
//     },
//     {
//       privId: 991867,
//       menuId: 2770069,
//       menuName: '活动模板管理-oracle',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770062,
//       menuIndex: 7,
//       urlAddr: '[iframe]http://10.45.47.18:3428/ire/activityBase/activityTemplate/?sso=1',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'HDMBGLORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_HDMBO',
//     },
//     {
//       privId: 89131,
//       menuId: 276930,
//       menuName: '协议起草',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276949,
//       menuIndex: 1,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bcm-web/ssoLogin?menuCode=2023',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'XYQC',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_XYGL_1',
//     },
//     {
//       privId: 89132,
//       menuId: 276931,
//       menuName: '协议全视图',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276949,
//       menuIndex: 3,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bcm-web/ssoLogin?menuCode=1981',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'XYQST',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_XYGL_3',
//     },
//     {
//       privId: 991708,
//       menuId: 2769891,
//       menuName: '企业信息库',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769942,
//       menuIndex: 7,
//       urlAddr: '[iframe]http://10.45.47.16:8491/marketOpportunity/enterpriseInfoBase',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'QYXXK',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_EN',
//     },
//     {
//       privId: 991705,
//       menuId: 2769888,
//       menuName: '订单模板管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770182,
//       menuIndex: 5,
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3122',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'DDMBGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_DDMB',
//     },
//     {
//       privId: 89143,
//       menuId: 276950,
//       menuName: '协议草稿',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276949,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bcm-web/ssoLogin?menuCode=2024',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'XYCG',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_XYGL_2',
//     },
//     {
//       privId: 89144,
//       menuId: 276951,
//       menuName: '协议模板',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276949,
//       menuIndex: 4,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bcm-web/ssoLogin?menuCode=2041',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'XYMB',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_XYGL_4',
//     },
//     {
//       privId: 86152,
//       menuId: 274652,
//       menuName: '审批管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 275245,
//       urlAddr: '[iframe]http://172.21.96.21:9061/operator/',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'SPGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'KGXT_10004',
//     },
//     {
//       privId: 89148,
//       menuId: 276958,
//       menuName: '产品属性管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276956,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bcm-web/ssoLogin?menuCode=822',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'CPSXGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_DTSXGL_2',
//     },
//     {
//       privId: 89145,
//       menuId: 276953,
//       menuName: '我的任务单',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276952,
//       menuIndex: 1,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bcm-web/ssoLogin?menuCode=162',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'WDRWD',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_GDGL_1',
//     },
//     {
//       privId: 86452,
//       menuId: 274651,
//       menuName: '客户中心',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 2,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'KHZX',
//       iconUrl: 'icon-user',
//       menuCode: 'KGXT_MENU_MAIN',
//     },
//     {
//       privId: 86157,
//       menuId: 274660,
//       menuName: '导出操作日志',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274652,
//       parMenuName: '审批管理',
//       urlAddr: '[iframe]http://172.21.96.21:9061/operator/cust/exportOperationLog.html',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'DCCZRZ',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'KGXT_10019',
//     },
//     {
//       privId: 991921,
//       menuId: 2770122,
//       menuName: '合同信息管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 2770121,
//       menuIndex: 1,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'HTXXGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '3',
//       menuCode: 'TYMH_MENU_342434',
//     },
//     {
//       privId: 89151,
//       menuId: 276965,
//       menuName: '招标信息库',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769942,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.47.16:8491/marketOpportunity/BiddingMsg',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'ZBXXK',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_ANALYSIS_ZB',
//     },
//     {
//       privId: 991924,
//       menuId: 2770126,
//       menuName: '产品查询',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770125,
//       menuIndex: 1,
//       urlAddr: 'BPP/modules/productManager/productInfo/views/productQueryView.js',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'CPCX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_55544',
//     },
//     {
//       privId: 991923,
//       menuId: 2770124,
//       menuName: '合同查询',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770122,
//       parMenuName: '合同信息管理',
//       menuIndex: 1,
//       urlAddr: 'BPP/modules/contractManagement/contractQuery/views/contractQueryView.js',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'HTCX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_22333',
//     },
//     {
//       privId: 86152,
//       menuId: 274659,
//       menuName: '我的客户',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274651,
//       parMenuName: '客户中心',
//       menuIndex: 1,
//       urlAddr:
//         '[iframe]http://10.45.46.210:9000/operator/dist/index.html#/cust/manage/custInfo?viewMode=inner&language=zh-CN',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'WDKH',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'KGXT_MENU_1005',
//     },
//     {
//       privId: 89158,
//       menuId: 276974,
//       menuName: '稽核点管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769881,
//       menuIndex: 1,
//       menuDesc: '稽核点管理',
//       urlAddr: 'audit-web/audit/modules/core/views/AuditPointMainView2.js',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'JHDGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_AUDIT_POINT',
//     },
//     {
//       privId: 991681,
//       menuId: 2769861,
//       menuName: '订单录入',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769885,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=19',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'DDLR',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_DDLR',
//     },
//     {
//       privId: 89161,
//       menuId: 276980,
//       menuName: '订单查询',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2769885,
//       menuIndex: 1,
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3022',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'DDCX',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_DDCX',
//     },
//     {
//       privId: 89164,
//       menuId: 276983,
//       menuName: '订单任务',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276975,
//       menuIndex: 3,
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3009',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'DDRW',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_DDRWD',
//     },
//     {
//       privId: 86157,
//       menuId: 274672,
//       menuName: '审批人设置',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274652,
//       parMenuName: '审批管理',
//       urlAddr: '[iframe]http://172.21.96.21:9061/operator/cust/approveSet.html',
//       systemCode: '727002',
//       statusCd: '1000',
//       firstLetter: 'SPRSZ',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'KGXT_10006',
//     },
//     {
//       privId: 991703,
//       menuId: 2769886,
//       menuName: '订单超时监控',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770181,
//       menuIndex: 3,
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3123',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'DDCSJK',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_DDCSJK',
//     },
//     {
//       privId: 991704,
//       menuId: 2769887,
//       menuName: '环节超时管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770182,
//       menuIndex: 4,
//       urlAddr: '[iframe]http://10.45.46.210:9000/order-web/ssoLogin?menuCode=3124',
//       systemCode: '727001',
//       statusCd: '1000',
//       firstLetter: 'HJCSGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_HJCSPZ',
//     },
//     {
//       privId: 991941,
//       menuId: 2770141,
//       menuName: '要素管理',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 2770088,
//       menuIndex: 9,
//       urlAddr: 'mrss/modules/factor/eventattr/views/EventAttrView',
//       systemCode: '727011',
//       statusCd: '1000',
//       firstLetter: 'YSGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'BILLING_MENU_EVENT_ATTR',
//     },
//     {
//       privId: 89169,
//       menuId: 276989,
//       menuName: '属性规格管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276988,
//       menuIndex: 1,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bsc-web/ssoLogin?menuCode=821',
//       systemCode: '727006',
//       statusCd: '1000',
//       firstLetter: 'SXGGGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'SALES_MENU_8',
//     },
//     {
//       privId: 89170,
//       menuId: 276990,
//       menuName: '产品属性管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276988,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bsc-web/ssoLogin?menuCode=822',
//       systemCode: '727006',
//       statusCd: '1000',
//       firstLetter: 'CPSXGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'SALES_MENU_9',
//     },
//     {
//       privId: 89171,
//       menuId: 276991,
//       menuName: '订单',
//       menuLevel: 1,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 274744,
//       parMenuName: 'APP菜单',
//       menuIndex: 28,
//       urlAddr: 'http://gz.iwhalecloud.com:10064/dist/#/myOrder',
//       systemCode: '727004',
//       statusCd: '1000',
//       firstLetter: 'DD',
//       iconUrl: 'main_money',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'APP0_MENU_ORDER',
//     },
//     {
//       privId: 89165,
//       menuId: 276984,
//       menuName: '我的线索',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276789,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bsc-web/ssoLogin?menuCode=2820',
//       systemCode: '727006',
//       statusCd: '1000',
//       firstLetter: 'WDXS',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'SALES_MENU_2',
//     },
//     {
//       privId: 89166,
//       menuId: 276985,
//       menuName: '我的商机',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276789,
//       menuIndex: 3,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bsc-web/ssoLogin?menuCode=2920',
//       systemCode: '727006',
//       statusCd: '1000',
//       firstLetter: 'WDSJ',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'SALES_MENU_3',
//     },
//     {
//       privId: 89168,
//       menuId: 276987,
//       menuName: '虚拟团队管理',
//       menuLevel: 2,
//       menuType: '1100',
//       menuTypeName: '叶子菜单',
//       parMenuId: 276848,
//       menuIndex: 2,
//       urlAddr: '[iframe]http://10.45.46.210:9000/bsc-web/ssoLogin?menuCode=356',
//       systemCode: '727006',
//       statusCd: '1000',
//       firstLetter: 'XNTDGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'SALES_MENU_6',
//     },
//     {
//       menuId: 276848,
//       menuName: '运营管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276816,
//       menuIndex: 138,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'YYGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'SALES_MENU_139',
//       ifMain: false,
//     },
//     {
//       menuId: 276816,
//       menuName: '销售中心',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 3,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'XSZX',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'SALES_MENU_138',
//       ifMain: false,
//     },
//     {
//       menuId: 276789,
//       menuName: '商机管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276816,
//       menuIndex: 137,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'SJGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'SALES_MENU_137',
//       ifMain: false,
//     },
//     {
//       menuId: 276988,
//       menuName: '动态属性管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276816,
//       menuIndex: 139,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'DTSXGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'SALES_MENU_7',
//       ifMain: false,
//     },
//     {
//       menuId: 2770088,
//       menuName: '计费系统',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 11,
//       menuDesc: '计费系统',
//       statusCd: '1000',
//       firstLetter: 'JFXT',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'BILLING_MENU_BILL001',
//       ifMain: false,
//     },
//     {
//       menuId: 2770182,
//       menuName: '订单配置',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276975,
//       menuIndex: 6,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'DDPZ',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_DD_DDPZ',
//       ifMain: false,
//     },
//     {
//       menuId: 276975,
//       menuName: '订单中心',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 5,
//       menuDesc: 'sss',
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'DDZX',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_0010',
//       ifMain: false,
//     },
//     {
//       menuId: 2770181,
//       menuName: '订单监控',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276975,
//       menuIndex: 5,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'DDJK',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_DD_DDJK',
//       ifMain: false,
//     },
//     {
//       menuId: 2769885,
//       menuName: '订单管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276975,
//       menuIndex: 1,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'DDGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_DDGL',
//       ifMain: false,
//     },
//     {
//       menuId: 2769881,
//       menuName: '稽核规则配置',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276973,
//       menuIndex: 9,
//       menuDesc: '稽核规则配置',
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'JHGZPZ',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_AUDIT_RULE',
//       ifMain: false,
//     },
//     {
//       menuId: 276973,
//       menuName: '稽核管理',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 6,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'JHGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_JHGL001',
//       ifMain: false,
//     },
//     {
//       menuId: 2770125,
//       menuName: '产品信息管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 2770121,
//       menuIndex: 2,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'CPXXGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_344533',
//       ifMain: false,
//     },
//     {
//       menuId: 2770121,
//       menuName: '青海政企',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 11,
//       menuDesc: '政企合作伙伴平台-测试',
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'QHZQ',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_111',
//       ifMain: false,
//     },
//     {
//       menuId: 2769942,
//       menuName: '市场商机',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276963,
//       menuIndex: 10,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'SCSJ',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_SJ',
//       ifMain: false,
//     },
//     {
//       menuId: 276963,
//       menuName: '商情分析',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 1,
//       urlAddr: 'iframe',
//       statusCd: '1000',
//       firstLetter: 'SQFX',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_ANALYSIS',
//       ifMain: false,
//     },
//     {
//       menuId: 276952,
//       menuName: '工单管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276928,
//       menuIndex: 2,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'GDGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_GDGL',
//       ifMain: false,
//     },
//     {
//       menuId: 276928,
//       menuName: '合同中心',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 4,
//       menuDesc: 'stest\n',
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'HTZX',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_0001',
//       ifMain: false,
//     },
//     {
//       menuId: 276956,
//       menuName: '动态属性管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276928,
//       menuIndex: 4,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'DTSXGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_DTSXGL',
//       ifMain: false,
//     },
//     {
//       menuId: 276949,
//       menuName: '协议管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276928,
//       menuIndex: 1,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'XYGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_XYGL',
//       ifMain: false,
//     },
//     {
//       menuId: 2770062,
//       menuName: '活动管理-oracle',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 2770061,
//       menuIndex: 1,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'HDGLORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'KGXT_MENU_HDGULO',
//       ifMain: false,
//     },
//     {
//       menuId: 2770061,
//       menuName: '智能营销-oracle',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 9,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'ZNYXORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'KGXT_MENU_ZQYXO',
//       ifMain: false,
//     },
//     {
//       menuId: 276890,
//       menuName: '任务管理',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 20,
//       menuDesc: '管理营业员日常任务信息',
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'RWGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'RWGL_MENU_TASK_MANAGE',
//       ifMain: false,
//     },
//     {
//       menuId: 2769821,
//       menuName: '客户调配',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 274651,
//       menuIndex: 3,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'KHDP',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'KGXT_MENU_ALLOC',
//       ifMain: false,
//     },
//     {
//       menuId: 2770071,
//       menuName: '活动工单展示-oracle',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 2770061,
//       menuIndex: 2,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'HDGDZSORACLE',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'MKT0_MENU_GDZSO',
//       ifMain: false,
//     },
//     {
//       menuId: 276729,
//       menuName: '权限管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276102,
//       menuIndex: 6,
//       menuDesc: '权限管理',
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'QXGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_TYMH133',
//       ifMain: false,
//     },
//     {
//       menuId: 276102,
//       menuName: '系统管理',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 11,
//       menuDesc: '系统管理',
//       urlAddr: 'about:blank',
//       statusCd: '1000',
//       firstLetter: 'XTGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_013',
//       ifMain: false,
//     },
//     {
//       menuId: 276103,
//       menuName: '工作台管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276102,
//       menuIndex: 4,
//       menuDesc: '工作台管理',
//       urlAddr: 'about:blank',
//       statusCd: '1000',
//       firstLetter: 'GZTGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_016',
//     },
//     {
//       menuId: 276766,
//       menuName: '配置管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276102,
//       menuIndex: 7,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'PZGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_SYSCFG',
//       ifMain: false,
//     },
//     {
//       menuId: 276110,
//       menuName: '组织管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276102,
//       menuIndex: 5,
//       menuDesc: '组织管理',
//       urlAddr: 'about:blank',
//       statusCd: '1000',
//       firstLetter: 'ZZGL',
//       iconUrl: 'icon-gene-man-manager',
//       paramEncryptType: '1000',
//       menuOpenMode: '1',
//       menuCode: 'TYMH_MENU_014',
//       ifMain: false,
//     },
//     {
//       menuId: 2770082,
//       menuName: '爬虫管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 2770081,
//       menuIndex: 1,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'PCGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_MYGL',
//       ifMain: false,
//     },
//     {
//       menuId: 2770081,
//       menuName: '商情分析my_test',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 11,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'SQFXMY_TEST',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_MY',
//       ifMain: false,
//     },
//     {
//       menuId: 2770084,
//       menuName: '市场商机',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 2770081,
//       menuIndex: 3,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'SCSJ',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_ORSJ',
//       ifMain: false,
//     },
//     {
//       menuId: 2770083,
//       menuName: '统计分析',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 2770081,
//       menuIndex: 2,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'TJFX',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_ORFX',
//       ifMain: false,
//     },
//     {
//       menuId: 2769961,
//       menuName: '网格管理',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 10,
//       menuDesc: '网格管理',
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'WGGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'MKT0_MENU_GRID',
//       ifMain: false,
//     },
//     {
//       menuId: 276954,
//       menuName: '续签及提醒',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276928,
//       menuIndex: 3,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'XQJTX',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_XQJTX',
//       ifMain: false,
//     },
//     {
//       menuId: 2769981,
//       menuName: '产商品中心',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 9,
//       menuDesc: '产商品中心',
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'CSPZX',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'CPC_MENU_001',
//       ifMain: false,
//     },
//     {
//       menuId: 276649,
//       menuName: 'APP信息管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276102,
//       menuIndex: 132,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'APPXXGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_APP_CFG',
//       ifMain: false,
//     },
//     {
//       menuId: 2769925,
//       menuName: '爬虫管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276963,
//       menuIndex: 8,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'PCGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_GL',
//       ifMain: false,
//     },
//     {
//       menuId: 2769926,
//       menuName: '统计分析',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276963,
//       menuIndex: 9,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'TJFX',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_FX',
//       ifMain: false,
//     },
//     {
//       menuId: 276669,
//       menuName: '政企沙盘',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 7,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'ZQSP',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'MKT0_MENU_GL',
//       ifMain: false,
//     },
//     {
//       menuId: 2770043,
//       menuName: '活动管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276653,
//       menuIndex: 1,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'HDGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'MKT0_MENU_HDGUL',
//       ifMain: false,
//     },
//     {
//       menuId: 276653,
//       menuName: '智能营销',
//       menuLevel: 0,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 0,
//       menuIndex: 8,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'ZNYX',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'KGXT_MENU_ZQYX',
//       ifMain: false,
//     },
//     {
//       menuId: 2770044,
//       menuName: '活动工单展示',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276653,
//       menuIndex: 2,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'HDGDZS',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'MKT0_MENU_GDZS',
//       ifMain: false,
//     },
//     {
//       menuId: 277020,
//       menuName: '查勘管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276816,
//       menuIndex: 140,
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'CKGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'SALES_MENU_ckgl',
//       ifMain: false,
//     },
//     {
//       menuId: 2769882,
//       menuName: '稽核业务单管理',
//       menuLevel: 1,
//       menuType: '1000',
//       menuTypeName: '目录菜单',
//       parMenuId: 276973,
//       menuIndex: 10,
//       menuDesc: '稽核业务单管理',
//       urlAddr: 'modules/menu/views/MenuManagementView',
//       statusCd: '1000',
//       firstLetter: 'JHYWDGL',
//       iconUrl: 'icon-gene-man-manager',
//       menuCode: 'TYMH_MENU_AUDIT_TASK',
//       ifMain: false,
//     },
//   ]);
// });

Mock.mock(/sso\/login.do/, function(options) {
  return Mock.mock({
    resultCode: '0',
    resultObject: {
      userCode: 'admin',
      sessionId: '63ba2e7c-84b5-4afe-84da-e69ef1f8e90d',
      errNum: 0,
      leftNum: 0,
      userInfo: {
        userId: 1,
        userCode: 'admin',
        loginCode: 'admin',
        userOrgId: 10008,
       // password: 'XHId4XZ14vYQ82wb1b8ZAA==',
        userName: '管思坤',
        roleId: 92549,
        roleName: '运营管理',
       // pwdErrCnt: 0,
       // pwdNewtime: '2019-11-06 10:13:00',
       // pwdEffectDays: 3600,
        sysCode: '727007',
       // pwdSmsTel: 12321324329,
       // pwdStatus: '1100',
        statusCd: '1000',
        extParams: { userOrgId: 10008 },
        mobilePhone: '***********',
      },
      orgInfo: {
        orgId: 10008,
        orgName: '四川省',
        orgCode: '10008',
        parentOrgId: -1,
        orgLevel: 1,
        lanId: -1,
      },
      staffInfo: {
        staffId: 3044336,
        staffCode: 'admin',
        staffName: '王兰军',
        partyId: 3,
        staffType: '1000',
        staffTypeName: '自建员工',
        staffAccount: 'wlj',
        statusCd: '1000',
        saleStaffCode: 'Y62010073906',
        regionId: 731,
      },
      portalRoles: [
        {
          sysRoleId: 92548,
          sysRoleName: '公司领导',
          sysRoleCode: 'TYMH0081',
        },
        {
          sysRoleId: 92549,
          sysRoleName: '运营管理',
          sysRoleCode: 'TYMH0082',
        },
        {
          sysRoleId: 92550,
          sysRoleName: '一线营销',
          sysRoleCode: 'TYMH0083',
        },
        {
          sysRoleId: 92791,
          sysRoleName: '系统管理员',
          sysRoleCode: 'XTGLY001',
        },
      ],
    },
  });
});
Mock.mock(/portal\/TempletController\/getTempletModule.do/, function(options) {
  if (options.type == 'POST') {
    return Mock.mock({
      id: 68045,
      templetName: '管思坤-一线营销-PC卡片',
      layoutType: 3,
      sysPostId: 92550,
      sysUserId: 1,
      moduleRels: [
        {
          id: 714777,
          moduleId: 11721174,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 0,
          module: {
            id: 11721174,
            moduleName: '系统公告-68045',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11750994,
                moduleId: 11721174,
                moduleIndex: 0,
                funcMenu: {
                  id: 276109,
                  menuId: 276109,
                  menuName: '系统公告',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 7,
                  parMenuId: 276103,
                  menuDesc: '公告',
                  urlAddr: '[portlet]Bulletin',
                  x: 0,
                  y: 0,
                  w: 24,
                  h: 1.35,
                  minW: 12,
                  maxW: 24,
                  minH: 1.35,
                  maxH: 1.35,
                  sysCode: '727001',
                  firstLetter: 'XTGG',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_001',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11750994,
              },
            ],
            moduleId: 11721174,
          },
          templetModuleRelId: 714777,
        },
        {
          id: 714689,
          moduleId: 11721421,
          templetId: 68043,
          sysPostId: 92548,
          relType: '1',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 1,
          module: {
            id: 11721421,
            moduleName: '关键指标',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11751241,
                moduleId: 11721421,
                moduleIndex: 0,
                funcMenu: {
                  id: 2769844,
                  menuId: 2769844,
                  menuName: '关键指标',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 129,
                  parMenuId: 276103,
                  menuDesc: '关键指标',
                  urlAddr: '[portlet]KeyIndex',
                  x: 0,
                  y: 1.35,
                  w: 16,
                  h: 2.16,
                  minW: 12,
                  maxW: 24,
                  minH: 2.16,
                  maxH: 2.16,
                  sysCode: '727001',
                  firstLetter: 'GJZB',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_0100',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11751241,
              },
            ],
            moduleId: 11721421,
          },
          templetModuleRelId: 714689,
        },
        {
          id: 714778,
          moduleId: 11721175,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 1,
          module: {
            id: 11721175,
            moduleName: '待办-68045',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11750995,
                moduleId: 11721175,
                moduleIndex: 0,
                funcMenu: {
                  id: 276108,
                  menuId: 276108,
                  menuName: '待办',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 9,
                  parMenuId: 276103,
                  menuDesc: '待办',
                  urlAddr: '[portlet]Undo',
                  x: 16,
                  y: 1.35,
                  w: 8,
                  h: 2.16,
                  minW: 8,
                  maxW: 24,
                  minH: 2.16,
                  maxH: 2.16,
                  sysCode: '727001',
                  firstLetter: 'DB',
                  iconUrl: 'icon-gene-redpacket',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_005',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11750995,
              },
            ],
            moduleId: 11721175,
          },
          templetModuleRelId: 714778,
        },
        {
          id: 7147781,
          moduleId: 117211751,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 1,
          module: {
            id: 117211751,
            moduleName: '增长趋势',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11750995,
                moduleId: 117211751,
                moduleIndex: 0,
                funcMenu: {
                  id: 27610822,
                  menuId: 27610822,
                  menuName: '增长趋势',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 9,
                  parMenuId: 276103,
                  menuDesc: '增长趋势',
                  urlAddr: '[portlet]GrowthRate',
                  x: 0,
                  y: 3.51,
                  w: 24,
                  h: 3.64,
                  minW: 12,
                  maxW: 24,
                  minH: 3.64,
                  maxH: 3.64,
                  sysCode: '727001',
                  firstLetter: 'DB',
                  iconUrl: 'icon-gene-redpacket',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_005',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11750995,
              },
            ],
            moduleId: 117211751,
          },
          templetModuleRelId: 7147781,
        },
        {
          id: 714780,
          moduleId: 11721449,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 3,
          module: {
            id: 11721449,
            moduleName: '客户状态分布',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11751269,
                moduleId: 11721449,
                moduleIndex: 0,
                funcMenu: {
                  id: 2770201,
                  menuId: 2770201,
                  menuName: '客户状态分布',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 137,
                  parMenuId: 276103,
                  menuDesc: '客户状态分布',
                  urlAddr: '[portlet]CustomerDistribution',
                  x: 0,
                  y: 7.15,
                  w: 12,
                  h: 3.7,
                  minW: 8,
                  maxW: 24,
                  minH: 3.7,
                  maxH: 3.7,
                  sysCode: '727001',
                  firstLetter: 'RWGS',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_0109',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11751269,
              },
            ],
            moduleId: 11721449,
          },
          templetModuleRelId: 714780,
        },
        {
          id: 714780,
          moduleId: 117214491,
          templetId: 68045,
          sysUserId: 1,
          sysPostId: 92550,
          relType: '2',
          terminalType: '1000',
          confType: '1',
          layoutCol: '1',
          moduleIndex: 3,
          module: {
            id: 117214491,
            moduleName: '商机状态分布',
            moduleType: '1',
            moduleLevel: 1,
            details: [
              {
                id: 11751269,
                moduleId: 117214491,
                moduleIndex: 0,
                funcMenu: {
                  id: 2770202,
                  menuId: 2770202,
                  menuName: '商机状态分布',
                  menuType: '1100',
                  menuLevel: -1,
                  menuIndex: 137,
                  parMenuId: 276103,
                  menuDesc: '商机状态分布',
                  urlAddr: '[portlet]BusinessDistribution',
                  x: 12,
                  y: 7.15,
                  w: 12,
                  h: 3.7,
                  minW: 8,
                  maxW: 24,
                  minH: 3.7,
                  maxH: 3.7,
                  sysCode: '727001',
                  firstLetter: 'RWGS',
                  iconUrl: 'icon-gene-man-manager',
                  paramEncryptType: '1000',
                  menuOpenMode: '1',
                  menuCode: 'TYMH_MENU_0109',
                  ifMainName: '',
                  menuTypeName: '叶子菜单',
                },
                moduleDetailId: 11751269,
              },
            ],
            moduleId: 117214491,
          },
          templetModuleRelId: 714780,
        },
      ],
      terminalType: '1000',
      confType: '1',
      templetId: 68045,
    });
  }
});

Mock.mock(/portal\/ModuleController\/getTempletById.do/, function(options) {
  var terminalType = parseInt(options.body.terminalType, 10);
  if (options.type == 'POST' && terminalType === 1000) {
    return Mock.mock({
      resultCode: '0',
      resultObject: {
        templetId: 468001,
        portlets: [
          {
            menuId: 3610505,
            menuName: 'Portlet - 代办',
            urlAddr: '[portlet]Undo',
            x: 16,
            y: 1.35,
          },
          {
            menuId: 3610503,
            menuName: 'Portlet - 系统公告',
            urlAddr: '[portlet]Bulletin',
            x: 0,
            y: 0,
          },
          {
            menuId: 3610504,
            menuName: 'Portlet - 关键指标',
            urlAddr: '[portlet]KeyIndex',
            x: 0,
            y: 1.35,
          },
          {
            menuId: 3610506,
            menuName: 'Portlet - 增长趋势',
            urlAddr: '[portlet]GrowthRate',
            x: 0,
            y: 3.51,
          },
          {
            menuId: 3610508,
            menuName: 'Portlet - 商机状态分布',
            urlAddr: '[portlet]BusinessDistribution',
            x: 12,
            y: 7.15,
          },
          {
            menuId: 3610507,
            menuName: 'Portlet - 客户状态分布',
            urlAddr: '[portlet]CustomerDistribution',
            x: 0,
            y: 7.15,
          },
        ],
      },
    });
  } else if (options.type == 'POST' && terminalType === 1200) {
    return Mock.mock({
      resultCode: '0',
      resultObject: {
        templetId: 468001,
        portlets: [
          {
            menuId: 2770282,
            parMenuId: 276103,
            parMenuName: '工作台管理',
            urlAddr: 'modules/menu/views/MenuManagementView',
            systemCode: '727001',
            menuName: 'APP卡片-增长趋势',
            menuDesc: 'APP卡片-增长趋势',
            menuLevel: -1,
            iconUrl: 'icon-gene-man-manager',
            menuCode: 'TYMH_MENU_CARD007',
          },
        ],
      },
    });
  } else {
    return Mock.mock({
      resultCode: '0',
      resultObject: {
        portlets: [],
      },
    });
  }
});

Mock.mock(/portal\/TempletController\/saveTemplet.do/, function(options) {
  return Mock.mock({
    resultCode: '0',
    resultObject: {
      templetId: options.body.templetId === '-1' ? 123 : options.body.templetId,
      templetName: options.body.templetName,
      templetDesc: options.body.templetDesc,
      portlets: options.body.portlets,
    },
  });
});

// Mock.mock(/orgauth\/BulletinRcvObjRelController\/getUnReadBulletin.do/, function(options) {
//   if (
//     options.type == 'GET' &&
//     options.body.bulletinType == '3000' &&
//     options.body.statusCd == '1000'
//   ) {
//     return Mock.mock([]);
//   } else {
//     return Mock.mock([
//       {
//         id: 14278743,
//         createDate: '2020-02-06 19:33:00',
//         createStaff: 1,
//         updateDate: '2020-03-12 15:02:21',
//         updateStaff: 1,
//         bulletinId: 864516,
//         rcvType: '1200',
//         rcvId: 1,
//         statusCd: '1100',
//         bulletin: {
//           id: 864516,
//           createDate: '2020-02-04 14:50:04',
//           createStaff: 1,
//           updateDate: '2020-02-06 19:31:44',
//           updateStaff: 1,
//           bulletinId: 864516,
//           bulletinTitle: '火麒麟系统演示通知3.0修',
//           bulletinContent:
//             '系统将于2019-12-26进行演示，请确保系统的正常运行。对于新申报产品，6月30日前，允许企业使用研发验证测试结果替代第三方检测机构检测报告。',
//           bulletinType: '3000',
//           bulletinLevel: '1200',
//           isTop: 0,
//           launchStaff: 1,
//           launchOrg: 10008,
//           effDate: '2020-02-04 14:47:11',
//           expDate: '2037-01-01 00:00:00',
//           statusCd: '1000',
//           launchStaffName: '管理员',
//           launchOrgName: '四川省',
//           realTime: false,
//           statusCdName: '有效',
//           bulletinTypeName: '系统公告',
//           bulletinLevelName: '低',
//         },
//         statusCdName: '已读',
//         bulletinRcvObjRelId: 14278743,
//         rcvTypeName: '用户',
//       },
//       {
//         id: 14268743,
//         createDate: '2020-02-06 19:33:00',
//         createStaff: 1,
//         updateDate: '2020-03-12 15:02:21',
//         updateStaff: 1,
//         bulletinId: 864516,
//         rcvType: '1200',
//         rcvId: 1,
//         statusCd: '1100',
//         bulletin: {
//           id: 864516,
//           createDate: '2020-02-04 14:50:04',
//           createStaff: 1,
//           updateDate: '2020-02-06 19:31:44',
//           updateStaff: 1,
//           bulletinId: 864516,
//           bulletinTitle: '疫情防控-系统公告',
//           bulletinContent:
//             '针对新冠肺炎疫情给车辆生产企业在履行《公告》管理中带来的实际困难，按照工业和信息化部有关部署，现就实施疫情防控期间《公告》管理便企服务措施有关事项',
//           bulletinType: '3000',
//           bulletinLevel: '1200',
//           isTop: 0,
//           launchStaff: 1,
//           launchOrg: 10008,
//           effDate: '2020-02-04 14:47:11',
//           expDate: '2037-01-01 00:00:00',
//           statusCd: '1000',
//           launchStaffName: '管理员',
//           launchOrgName: '四川省',
//           realTime: false,
//           statusCdName: '有效',
//           bulletinTypeName: '系统公告',
//           bulletinLevelName: '低',
//         },
//         statusCdName: '已读',
//         bulletinRcvObjRelId: 14268743,
//         rcvTypeName: '用户',
//       },
//       {
//         id: 14258743,
//         createDate: '2020-02-06 19:33:00',
//         createStaff: 1,
//         updateDate: '2020-03-12 15:02:21',
//         updateStaff: 1,
//         bulletinId: 864516,
//         rcvType: '1200',
//         rcvId: 1,
//         statusCd: '1100',
//         bulletin: {
//           id: 864516,
//           createDate: '2020-02-04 14:50:04',
//           createStaff: 1,
//           updateDate: '2020-02-06 19:31:44',
//           updateStaff: 1,
//           bulletinId: 864516,
//           bulletinTitle: '关于开展车联网融合套餐营销活动的公告',
//           bulletinContent:
//             '关于开展车联网融合套餐营销活动，希望所有客户经理积极执行，争取业绩再创新篇章~',
//           bulletinType: '3000',
//           bulletinLevel: '1200',
//           isTop: 0,
//           launchStaff: 1,
//           launchOrg: 10008,
//           effDate: '2020-02-04 14:47:11',
//           expDate: '2037-01-01 00:00:00',
//           statusCd: '1000',
//           launchStaffName: '管理员',
//           launchOrgName: '四川省',
//           realTime: false,
//           statusCdName: '有效',
//           bulletinTypeName: '系统公告',
//           bulletinLevelName: '低',
//         },
//         statusCdName: '已读',
//         bulletinRcvObjRelId: 14258743,
//         rcvTypeName: '用户',
//       },
//     ]);
//   }
// });
