import React, { useState, useEffect } from 'react';
import { I<PERSON>, Divider, List, Switch, Tooltip, Button, Alert, Modal } from 'antd';
import classNames from 'classnames';
import { connect } from 'dva';
import delay from 'lodash/delay';
import { removeItem } from '@/utils/utils';
import styles from '../less/settingDrawer.less';
import ThemeColor from './ThemeColor';
import CacheRefresh from './CacheRefresh';
import request from '@/utils/request';

const Body = ({ children, title, style }) => (
  <div
    style={{
      ...style,
      marginBottom: 24,
    }}
  >
    <h3 className={styles.title}>{title}</h3>
    {children}
  </div>
);

const renderLayoutSettingItem = item => {
  const action = React.cloneElement(item.action, {
    disabled: item.disabled,
  });
  return (
    <Tooltip title={item.disabled ? item.disabledReason : ''} placement="left">
      <List.Item actions={[action]}>
        <span style={{ opacity: item.disabled ? '0.5' : '' }}>{item.title}</span>
      </List.Item>
    </Tooltip>
  );
};

function triggerResizeEvent() {
  const event = document.createEvent('HTMLEvents');
  event.initEvent('resize', true, false);
  window.dispatchEvent(event);
}

function SettingDrawer({
  dispatch,
  login: {
    user: {
      userInfo: { userId },
    },
  },
  setting: { showLeftMenu, menuDrawerActive, gutter },
}) {
  const [disbaled1, setDisabled1] = useState(false);
  const [alertTip, setAlertTip] = useState('');

  const initData = async () => {
    request('portal/DataDictController/getValueByCode.do', {
      data: { groupCode: 'SYSTEM_VAR', paramCode: 'CURRENT_VERSION' },
      method: 'GET',
    }).then(result => {
      setAlertTip(result);
    });
  };

  useEffect(() => {
    initData();
  }, []);

  return (
    <div className={classNames('setting-drawer')}>
      <span className="setting-drawer-close">
        <Icon
          type="close"
          onClick={() => {
            dispatch({
              type: 'setting/toggleSettingDrawer',
              payload: false,
            });
            // 配置项的改变会引起容器宽高的变化，比如'收起侧边栏'、'容器间距'等，
            // 另外展示settingDrawer时会隐藏垂直滚动条，也会引起宽度变化
            // 所以需要主动触发resize，重新layout
            delay(triggerResizeEvent, 100);
          }}
        />
      </span>
      <div className={styles.content}>
        <ThemeColor title="主题色" value="#1890FF" />
        <Divider />
        <CacheRefresh />
        <Body title="风格设置">
          <List
            split={false}
            renderItem={renderLayoutSettingItem}
            dataSource={[
              {
                title: '开启左边栏',
                action: (
                  <Switch
                    size="small"
                    checked={showLeftMenu}
                    onChange={checked => {
                      // 不显示时，收起左边栏，同时禁用“展开左边栏”选项
                      if (checked) {
                        setDisabled1(false);
                      } else {
                        setDisabled1(true);
                        dispatch({
                          type: 'setting/toggleMenuDrawer',
                          payload: false,
                        });
                        dispatch({
                          type: 'setting/expandLeftMenu',
                          payload: false,
                        });
                      }
                      dispatch({
                        type: 'setting/showLeftMenu',
                        payload: checked,
                      });
                    }}
                  />
                ),
              },
              {
                title: '默认展开左边栏',
                disabled: disbaled1,
                disabledReason: '只在显示左侧边栏时有效',
                action: (
                  <Switch
                    size="small"
                    checked={menuDrawerActive}
                    onChange={checked => {
                      dispatch({
                        type: 'setting/expandLeftMenu',
                        payload: checked,
                      });
                    }}
                  />
                ),
              },
              // {
              //   title: '容器边距',
              //   action: (
              //     <Select
              //       value={gutter}
              //       size="small"
              //       onSelect={value => {
              //         dispatch({
              //           type: 'setting/gutter',
              //           payload: value,
              //         });
              //       }}
              //       style={{ width: 80 }}
              //     >
              //       <Select.Option value="8">8px</Select.Option>
              //       <Select.Option value="10">10px</Select.Option>
              //       <Select.Option value="16">16px</Select.Option>
              //     </Select>
              //   ),
              // },
            ]}
          />
        </Body>

        <Alert
          type="warning"
          className={styles.productionHint}
          message={(
            <div>
              {alertTip}，以上配置将改变应用的初始状态，并将保存于您的客户端。如果需要清除，请选择
              <strong>“重置设置”</strong>
            </div>
          )}
        />
        <Button
          type="primary"
          block
          size="large"
          onClick={() => {
            Modal.confirm({
              title: '确定要恢复默认配置吗',
              onOk() {
                if (userId) {
                  removeItem(`user${userId}:settings`, 'localStorage');
                }
              },
            });
          }}
        >
          重置设置
        </Button>
      </div>
    </div>
  );
}

export default connect(({ setting, login }) => ({
  setting,
  login,
}))(SettingDrawer);
