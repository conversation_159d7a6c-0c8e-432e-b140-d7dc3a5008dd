import React from 'react';
import { connect } from 'dva';
import { Form } from 'antd';
import dynamic from 'umi/dynamic';
import StepWizard from '@/components/StepWizard';
import PageLoading from '@/components/PageLoading';

const List = dynamic({
  loader: () => import(`@/pages/Customize/AppShortcutMenu/List`),
  loading: PageLoading,
});

const MobileTemplet = dynamic({
  loader: () => import(`@/pages/Customize/AppShortcutMenu/MobileTemplet`),
  loading: PageLoading,
});

function Index() {
  return (
    <StepWizard isLazyMount>
      <List />
      <MobileTemplet destroy />
    </StepWizard>
  );
}

export default connect(({ setting, common, login }) => ({
  size: setting.size,
  user: login.user,
  terminalTypeMap: common.terminalTypeMap,
}))(Form.create()(Index));
