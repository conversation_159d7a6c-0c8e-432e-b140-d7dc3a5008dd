/* eslint-disable global-require */
import React, { useEffect, useState, useRef } from 'react';
import { connect } from 'dva';
import {
  Form,
  Table,
  Spin,
  Modal,
  Radio,
  message,
  Divider,
  Icon,
} from 'antd';
import { openMenuByValue, openMenu } from '@/utils/utils';
import MessageCard from './MessageCard';
import style from './ToDoItems.less';
import caretLeft from '../img/toDoItems/caret-left.png';
import caretRight from '../img/toDoItems/caret-right.png';

const colorsMap = {
  1: { color: '#8FC31F', label: '低' },
  2: { color: '#0085D0', label: '中' },
  3: { color: '#FAAF0C', label: '高' },
  4: { color: '#E40077', label: '极高' },
};

function ToDOItems(props) {
  const { dispatch, modelLoading, user, form, menu } = props;

  const [dataList, setDataList] = useState([]); // 待办数据
  const [activeFirst, setActiveFirst] = useState(null); // 选中的任务类型1
  const [activeTwo, setActiveTwo] = useState(null); // 选中的任务类型2
  const [activeThree, setActiveThree] = useState(null); // 选中的任务类型3
  const [visible, setVisible] = useState(false); // 优先级弹窗
  const [modalLoading, setModalLoading] = useState(false); // 优先级弹窗loading

  const [modalData, setModalData] = useState(false); // 优先级弹窗

  const [tableParams, setTableParams] = useState({
    list: [],
    total: 0,
    pageSize: 6,
    pageNum: 1,
  });

  const [pageInfo, setPageInfo] = useState({
    total: 0,
    pageSize: 6,
    pageNum: 1,
  });

  const containerRef = useRef(null);
  const scrollViewRef = useRef(null);
  const [leftDisabled, setLeftDisabled] = useState(false);
  const [rightDisabled, setRightDisabled] = useState(false);
  const [isShowScroll, setIsShowScroll] = useState(false);
  const [scrollLeftDistance, setScrollLeftDistance] = useState(0);
  const handleRightScroll = () => {
    scrollViewRef.current.scrollTo({
      left: scrollLeftDistance + 200,
      behavior: 'smooth',
    });
  };
  const handleLeftScroll = () => {
    scrollViewRef.current.scrollTo({
      left: scrollLeftDistance - 200,
      behavior: 'smooth',
    });
  };
  const handleScroll = event => {
    const { scrollLeft } = event.target;
    setScrollLeftDistance(scrollLeft);

    setRightDisabled(scrollViewRef.current.scrollWidth - containerRef.current.scrollWidth + 46 > scrollLeft);
    setLeftDisabled(scrollLeft > 0);
  };
  useEffect(() => {
    // 添加滚动事件监听器
    const scrollContainer = scrollViewRef.current;
    if (scrollContainer) {
      setRightDisabled(scrollViewRef.current.scrollWidth - containerRef.current.scrollWidth + 46 > 0);
      setIsShowScroll(scrollViewRef.current.scrollWidth - containerRef.current.scrollWidth > 0);
      scrollContainer.addEventListener('scroll', handleScroll);
    }
    // 清理事件监听器
    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
      }
    };
  }, [scrollViewRef.current]);

  // 处理url携带参数
  const dealUrl = url => {
    const { sessionId, userCode, orgInfo, userInfo } = user;
    let _url = url;
    if (url.indexOf('?') === -1) {
      _url += `?bss3SessionId=${sessionId}&sysUserCode=${userCode}`;
    } else if (url.indexOf('?') === url.length - 1) {
      _url += `bss3SessionId=${sessionId}&sysUserCode=${userCode}`;
    } else {
      _url += `&bss3SessionId=${sessionId}&sysUserCode=${userCode}`;
    }
    if (orgInfo?.orgCode) _url += `&orgNbr=${orgInfo.orgCode}`;
    if (userInfo?.postRegionId) _url += `&regionNbr=${userInfo?.postRegionId}`;
    return _url;
  };

  const [columns, setColumns] = useState([]);
  const [filterField, setFilterField] = useState([]);
  useEffect(() => {
    if (activeThree?.tableHeaderJson) {
      const col = activeThree?.tableHeaderJson.map((item, index) => {
        const { tHeaderName, tHeaderCode, width = 100, tHeaderType, operationValue, sort = false, filter = false, color, bgColor } = item;
        let tableColumn = {
          dataIndex: tHeaderCode,
          title: tHeaderName,
          width,
          ellipsis: true,
        };
        if (sort) {
          tableColumn = {
            ...tableColumn,
            sorter: (a, b) => a[tHeaderCode] - b[tHeaderCode],
            sortDirections: ['descend', 'ascend'],
          };
        }
        if (filter) {
          tableColumn = {
            ...tableColumn,
            filters: [],
          };
          setFilterField(pre => [...pre, tHeaderCode]);
        }

        if (tHeaderCode === 'priority') {
          tableColumn = {
            ...tableColumn,
            render: (text, record) => (
              <a
                onClick={() => {
                  setVisible(true);
                  setModalData(record);
                }}
                style={{ color: colorsMap[text || '1']?.color }}
              >
                {colorsMap[text || '1']?.label || '低'}
              </a>
            ),
          };
          return tableColumn;
        }
        if (index === 0) {
          tableColumn = {
            ...tableColumn,
            render: text => (
              <div className={style.idStyle}>
                <img src={require('../img/idTag.png')} alt="" className={style.idImg} />
                <span>{text}</span>
              </div>
            ),
          };
          return tableColumn;
        }
        if (color && bgColor) {
          tableColumn = {
            ...tableColumn,
            render: text => (
              <a className={style.currentFlow} style={{ color, backgroundColor: bgColor }}>
                {text}
              </a>
            ),
          };
          return tableColumn;
        }
        if (tHeaderType === '1') {
          return tableColumn;
        }

        tableColumn = {
          ...tableColumn,
          fixed: 'right',
          render: (text, record) => (
            <div>
              {operationValue.map((i, index1) => (
                <>
                  {index1 !== 0 ? <Divider type="vertical" /> : null}
                  <a
                    onClick={() => {
                      const { buttonUrl, buttonUrlParam = {} } = i;
                      const keys = Object.keys(buttonUrlParam) || [];
                      const params = keys.map(key => `${buttonUrlParam[key].slice(1, buttonUrlParam[key].length - 1)}=${record[key]}`).join('&');
                      const url = `${dealUrl(buttonUrl)}&${params}`;
                      openMenuByValue('TYMH_MENU_XTGL_IFRAMEBOX', props?.menu, url);
                    }}
                  >
                    {i.buttonName}
                  </a>
                </>
              ))}
            </div>
          ),
        };
        return tableColumn;
      });
      setColumns(col);
    }
  }, [activeThree]);
  const handleFilterOption = list => {
    const tmp = columns;
    filterField.forEach(key => {
      const option = list.map(rowItem => rowItem[key]);
      const uniqueArray = [...new Set(option)];
      tmp.forEach(colItem => {
        if (colItem.filters) {
          colItem.filters = uniqueArray.map(item => ({ text: item, value: item }));
        }
      });
    });
    setColumns(tmp);
  };

  // 查询智能表格数据
  const [dataSource, setDataSource] = useState([]);
  const getAggregationThreeeInfo = (pageNum = 1, pageSize = 99) => {
    const formValues = form.getFieldsValue();
    // 过滤时间组件
    const datePickerItems = activeThree?.queryAreaConditionJson?.attr?.filter(i => i?.condCode === 'createDate').map(i => i.condCode);
    const requestObject = {
      ...formValues,
      pageNum,
      pageSize,
    };
    // 格式化时间组件返回值
    datePickerItems.forEach(i => {
      requestObject[i] = requestObject[i] ? requestObject[i].format('YYYY-MM-DD') : requestObject[i];
    });
    const params = {
      aggregationThreeId: activeThree?.aggregationThreeId,
      requestObject,
    };
    const type = activeFirst?.aggregationFirstCode === 'myConcern' ? 'toDoItems/getAggregationConcernfo' : 'toDoItems/getAggregationThreeeInfo';
    dispatch({
      type,
      payload: params,
    }).then(res => {
      setTableParams({ ...res.resultObject });
      setDataSource((res.resultObject.list || []).slice(0, 6));
      handleFilterOption((res.resultObject.list || []));
      setPageInfo({
        ...pageInfo,
        total: (res.resultObject.list || []).length,
      });
    });
  };

  const handleTableChange = (pagination, filters, sorter) => {
    let pageNum = 0;
    const { current, pageSize } = pagination;
    const { list = [] } = tableParams;
    let handleData = list;
    if (Object.keys(filters).length > 0) {
      Object.keys(filters).forEach(key => {
        if (filters[key].length > 0) {
          handleData = handleData.filter(item => {
            if (filters[key].includes(item[key])) return true;
            return false;
          });
        }
      });
    }

    if (sorter.order) {
      handleData = handleData.sort((a, b) => {
        if (sorter.order === 'ascend') {
          return a[sorter.field] > b[sorter.field] ? 1 : -1;
        } if (sorter.order === 'descend') {
          return a[sorter.field] < b[sorter.field] ? 1 : -1;
        }
        return 0;
      });
    }
    if (pageInfo.pageNum === current) {
      pageNum = 1;
    }
    setPageInfo(pre => ({
      ...pre,
      total: handleData.length,
      pageNum: pageNum || current,
    }));
    setDataSource(handleData.slice(((pageNum || current) - 1) * pageSize, (pageNum || current) * pageSize));
  };

  // 渲染表格
  const renderTable = () => (
    <Table
      rowKey="id"
      columns={columns}
      dataSource={dataSource}
      pagination={{
        total: pageInfo.total,
        pageSize: pageInfo.pageSize,
        current: pageInfo.pageNum,
        showTotal: () => `共${pageInfo.total}条`,
        pageSizeOptions: ['6', '10', '20', '30', '40'],
        // onChange: (a, b) => {
        //   // handlePage(a, b);
        //   // getAggregationThreeeInfo(a, b);
        // },
      }}
      onChange={handleTableChange}
    />
  );

  const toObjectPage = () => {
    openMenu(activeTwo?.aggregationTwoCode, menu, dispatch);
  };

  // 初始化页面
  useEffect(() => {
    dispatch({
      type: 'toDoItems/getAggregationInfo',
      payload: { statusCd: ' ' },
    }).then(res => {
      if (Array.isArray(res?.resultObject)) {
        setDataList([...res.resultObject]);
        setActiveFirst(res?.resultObject[0]);
      } else {
        message.error(res?.resultMsg);
      }
    });
  }, []);

  // 一级变化，默认你选中二级第一个
  useEffect(() => {
    if (activeFirst && activeFirst?.aggregationFirstCode !== 'messageList') {
      for (let i = 0; i < activeFirst?.childAggregationTwoInfo?.length || 0; i += 1) {
        if (activeFirst.childAggregationTwoInfo[i].aggregationTwoCount > 0) {
          setActiveTwo(activeFirst.childAggregationTwoInfo[i]);
          return;
        }
      }
    }
  }, [JSON.stringify(activeFirst)]);

  // 二级变化，默认你选中三级第一个
  useEffect(() => {
    if (activeTwo) {
      setActiveThree(activeTwo?.childAggregationThreeInfo?.[0]);
    }
  }, [JSON.stringify(activeTwo)]);

  // 三级变化，查询表格数据
  useEffect(() => {
    if (activeThree) {
      getAggregationThreeeInfo();
    }
  }, [columns]);
  return (
    <div
      className={style.wrap}
    >
      <Spin spinning={modelLoading}>
        {/* 第一层 */}
        <div className={style.aggregationFirstBox}>
          <div style={{ flex: 1, display: 'flex' }}>
            {dataList.map(item => {
              const { aggregationFirstId, aggregationFirstName } = item;
              return (
                <div
                  className={style.firstTab}
                >
                  <div
                    className={style.aggregationFirstName}
                    style={{ color: aggregationFirstId === activeFirst?.aggregationFirstId ? '#000000' : '' }}
                    onClick={() => {
                      if (aggregationFirstId !== activeFirst?.aggregationFirstId) {
                        setActiveFirst(item);
                      }
                    }}
                  >
                    {aggregationFirstName}
                  </div>
                </div>
              );
            })}
          </div>
          <div className={style.showMore} onClick={() => toObjectPage()}>
            <div>查看更多</div>
            <Icon style={{ verticalAlign: 'middle' }} type="right" />
          </div>
        </div>
        <div className={style.content}>
          {/* 第二层 */}
          {
            activeFirst?.aggregationFirstCode === 'messageList' ? <MessageCard /> : (
              <>
                {activeFirst?.childAggregationTwoInfo && (
                  <div style={{ display: 'flex', width: '100%' }}>
                    <div className={style.twoBox} id="twoBox" ref={containerRef}>
                      {
                        isShowScroll ? (
                          <img
                            src={leftDisabled ? caretRight : caretLeft}
                            style={{ transform: leftDisabled ? 'rotate(180deg)' : null, padding: '10px 0', display: 'none', width: '24px' }}
                            alt=""
                            onClick={() => handleLeftScroll()}
                            className={style.scrollImg}
                          />
                        ) : null
                      }
                      <div className={style.twoBoxChildrens} id="twoBoxChildrens" ref={scrollViewRef}>
                        {activeFirst?.childAggregationTwoInfo?.map(item => {
                          const { aggregationTwoId, aggregationTwoName, aggregationTwoCount } = item;
                          return (
                            <div
                              onClick={() => {
                                if (aggregationTwoId !== activeTwo?.aggregationTwoId) {
                                  setActiveTwo(item);
                                }
                              }}
                              className={aggregationTwoId === activeTwo?.aggregationTwoId ? style.activeTwoTab : style.twoTab}
                            >
                              <div
                                className={style.twoTabName}
                              >
                                {/* <img
                                  src={sceneNodeIcon.indexOf('data:image/png;base64,') >= 0 ? sceneNodeIcon : `data:image/png;base64,${sceneNodeIcon}`}
                                  alt=""
                                  style={{ width: '16px', height: '16px', marginRight: '4px' }}
                                /> */}
                                <div>
                                  {aggregationTwoName}
                                </div>
                              </div>
                              <div className={style.aggregationTwoCount}>
                                {aggregationTwoCount || 0}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      {
                        isShowScroll ? (
                          <img
                            src={rightDisabled ? caretRight : caretLeft}
                            style={{ transform: rightDisabled ? null : 'rotate(180deg)', padding: '10px 0', display: 'none', width: '24px' }}
                            alt=""
                            onClick={() => handleRightScroll()}
                            className={style.scrollImg}
                          />
                        ) : null
                      }
                    </div>
                  </div>
                )}
              </>
            )
          }

          {
            activeFirst?.aggregationFirstCode !== 'messageList' ? (
              <>
                {/* 第三层 */}
                <div className={style.threeBox}>
                  {columns.length > 0 && renderTable()}
                  { visible && (
                    <Modal
                      title="优先级设置"
                      onCancel={() => {
                        setVisible(false);
                        setModalData(null);
                      }}
                      confirmLoading={modalLoading}
                      open={visible}
                      onOk={() => {
                        setModalLoading(true);
                        dispatch({
                          type: 'toDoItems/AggregationConcernControllerSave',
                          payload: {
                            aggregationThreeId: activeThree?.aggregationThreeId,
                            priority: modalData?.priority,
                            primaryCode: modalData?.contractCode,
                            primaryName: 'contractCode',
                          },
                        }).then(() => {
                          setModalLoading(false);
                          setVisible(false);
                          setModalData(null);
                          getAggregationThreeeInfo();
                        });
                      }}
                    >
                      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                        <Radio.Group
                          onChange={e => {
                            setModalData({ ...modalData, priority: e.target.value });
                          }}
                          defaultValue={modalData?.priority || '1'}
                        >
                          <Radio.Button value="1">低</Radio.Button>
                          <Radio.Button value="2">中</Radio.Button>
                          <Radio.Button value="3">高</Radio.Button>
                          <Radio.Button value="4">极高</Radio.Button>
                        </Radio.Group>
                      </div>
                    </Modal>
                  )}
                </div>
              </>
            ) : null
          }
        </div>
      </Spin>
    </div>
  );
}

export default connect(store => ({
  ...store,
  ...store.ToDOItems,
  menu: store.menu.all,
  user: store.login?.user,
  modelLoading: store.loading.models?.toDoItems,
}))(Form.create()(ToDOItems));
