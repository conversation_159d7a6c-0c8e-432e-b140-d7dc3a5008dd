import request from '@/utils/request';

const bulletinPath = 'orgauth/BulletinController/';
const bulletinRcvObjRelPath = 'orgauth/BulletinRcvObjRelController/';

/**
 *
 * @param {object} params
 * {
 *  bulletinType: 3000,
 *  bulletinLevel: 1000,
 *  statusCd: 1000 // 未读
 * }
 */
export async function getUnReadBulletin(params) {
  return request('orgauth/BulletinRcvObjRelController/getUnReadBulletin.do', {
    method: 'get',
    data: params,
  });
}

export async function saveBulletin(params) {
  if (params.bulletinId === '') {
    return request(`${bulletinPath}createBulletin.do`, {
      data: params,
    });
  }

  return request(`${bulletinPath}updateBulletin.do`, {
    method: 'put',
    data: params,
  });
}

/**
 * 获取系统公告表格数据
 * @param {object} params  {"page":1,"rowNum":5,"sortName":"","sortOrder":"asc","pageNum":1,"pageSize":5}
 */
export async function selectBulletinManageGridData(params) {
  return request(`${bulletinPath}selectBulletinManageGridData.do`, {
    data: params,
  });
}

export async function deleteBulletin(params) {
  return request(`${bulletinPath}removeBulletin.do`, {
    method: 'DELETE',
    data: params,
  });
}

export async function makeAsRead(params) {
  return request(`${bulletinRcvObjRelPath}makeAsRead.do`, {
    method: 'put',
    data: params,
  });
}

// 生效
export async function effectiveBulletin(params) {
  return request(`${bulletinPath}effectiveBulletin.do`, {
    method: 'put',
    data: params,
  });
}

/**
 * 失效，入参格式如下：
 * {
    "id": 344494,
    "createDate": "2019-11-03 20:21:31",
    "createStaff": 1,
    "updateDate": "2019-11-03 20:21:31",
    "bulletinId": 344494,
    "bulletinTitle": "火麒麟系统演示通知",
    "bulletinContent": "系统将于2019/11/04日进行功能演示，请确保各系统环境运行稳定。",
    "bulletinType": "3000",
    "bulletinLevel": "1200",
    "isTop": "0",
    "launchStaff": 1,
    "launchOrg": 10008,
    "effDate": "2019-11-03 20:20:52",
    "expDate": "2037-01-01 00:00:00",
    "statusCd": "1000",
    "launchStaffName": "管思坤",
    "rcvType": "1200",
    "realTime": false
}
 */

export async function invalidateBulletin(params) {
  return request(`${bulletinPath}invalidateBulletin.do`, {
    method: 'put',
    data: params,
  });
}

// 清除草稿
export async function cleanBulletin4Draft(params) {
  return request(`${bulletinPath}cleanBulletin4Draft.do`, {
    method: 'remove',
    data: params,
  });
}

/**
 * 获取接收对象
 * @param {object} params {bulletinId:344494,rcvType:1200}
 */
export async function getBulletinRcvObjRels(params) {
  return request(`${bulletinRcvObjRelPath}getBulletinRcvObjRels.do`, {
    method: 'get',
    data: params,
  });
}

/**
 *
 * @param {object} params
 * {
 *   groupCode:""
 *   paramCode:""
 *   defValue:""
 * }
 * @param success
 * @returns {*}
 */
export async function getValueByCode(params) {
  return request('portal/DataDictController/getValueByCode.do', {
    method: 'get',
    data: params,
  });
}

/**
 * 系统公告 /bulletin 界面获取表格数据
 * @param {object} params  {"page":1,"rowNum":5,"sortName":"","sortOrder":"asc","pageNum":1,"pageSize":5}
 */
export async function selectBulletinGridData(params) {
  return request('orgauth/BulletinRcvObjRelController/selectBulletinGridData.do', {
    data: params,
  });
}

/**
 *
 * @param {object} params { busiNbr: 'Bulletin', propertyName: 'bulletinType' }
 * @param success
 * @returns {array} [{"value":"3000","name":"系统公告","desc":"系统维护通知","nodeList":[]}]
 */
export async function getValuesList(params) {
  return request('portal/DomainDataController/getValuesList.do', {
    data: params,
  });
}

/**
 * 获取公告详情里的附件信息
 * @param {object} params { objId: '344533', objType: '1000' }
 * @param success
 * @returns {object[]}
 * [{
    docId: 128771
    docNbr: "4qYf_ofHHYyGm_aGz8u1m2"
    fileGetUrl: "/portal/portal/FileStoreController/download.do?docNbr=4qYf_ofHHYyGm_aGz8u1m2"
    fileName: "盖章.png"
    resultCode: "0000"
    resultDesc: "success"
}]

 */
export async function queryByObjId(params) {
  return request('portal/FileStoreController/queryByObjId.do', {
    method: 'get',
    data: params,
  });
}

/**
 * 获取首页公告推荐
 * @param {*} params
 * @returns
 */
export async function qryConMessageUsersGridData(params) {
  return request('portal/ConMessageUsersController/qryConMessageUsersGridData.do', {
    method: 'post',
    data: params,
  });
}
