import React, { useEffect } from 'react';
import dynamic from 'umi/dynamic';
import { connect } from 'dva';
import StepWizard from '@/components/StepWizard';
import PageLoading from '@/components/PageLoading';
import Edit from './components/Edit';

const List = dynamic({
  loader: () => import('./components/List'),
  loading: PageLoading,
});

function Index(props) {
  useEffect(() => {
    /* 初始化 */
    props.dispatch({
      type: 'GroupCustManage/initStaticData',
    });
  }, []);
  return (
    <StepWizard isLazyMount>
      <List />
      <Edit destroy />
    </StepWizard>
  );
}

export default connect(() => ({}))(Index);
