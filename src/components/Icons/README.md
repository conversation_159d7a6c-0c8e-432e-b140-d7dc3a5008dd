# 图标组件系统

基于React的SVG图标组件库，支持动态颜色、大小调整和统一管理。

## 特性

- 🎨 **动态颜色**：支持通过props动态修改图标颜色
- 📏 **灵活尺寸**：支持数字或字符串形式的尺寸设置
- 🔧 **统一接口**：所有图标组件具有一致的API
- 📦 **按需导入**：支持按需导入，减少打包体积
- 🎯 **TypeScript友好**：完整的类型定义支持
- ♿ **无障碍支持**：内置aria-label和title支持

## 安装使用

```javascript
// 按需导入具体图标
import { AddIcon, DeleteIcon, SearchIcon } from '@/components/Icons';

// 导入常量
import { ICON_SIZES, ICON_COLORS } from '@/components/Icons';

// 导入动态图标组件
import { DynamicIcon } from '@/components/Icons';
```

## 基础用法

### 1. 直接使用图标组件

```jsx
import { AddIcon, DeleteIcon } from '@/components/Icons';

function MyComponent() {
  return (
    <div>
      <AddIcon size={24} color="#1890ff" />
      <DeleteIcon size={20} color="#f5222d" onClick={handleDelete} />
    </div>
  );
}
```

### 2. 使用预设常量

```jsx
import { AddIcon, ICON_SIZES, ICON_COLORS } from '@/components/Icons';

function MyComponent() {
  return (
    <AddIcon 
      size={ICON_SIZES.LARGE} 
      color={ICON_COLORS.PRIMARY} 
    />
  );
}
```

### 3. 动态图标使用

```jsx
import { DynamicIcon } from '@/components/Icons';

function MyComponent({ iconName }) {
  return (
    <DynamicIcon 
      name={iconName} 
      size={24} 
      color="#1890ff" 
    />
  );
}
```

## API

### 通用Props

所有图标组件都支持以下props：

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| size | number \| string | 16 | 图标大小，数字时单位为px |
| color | string | 'currentColor' | 图标颜色，支持任何CSS颜色值 |
| className | string | - | 自定义CSS类名 |
| style | object | - | 自定义样式对象 |
| onClick | function | - | 点击事件处理函数 |
| title | string | - | 图标标题，用于无障碍访问 |
| viewBox | string | '0 0 24 24' | SVG viewBox属性 |

### 预设常量

#### ICON_SIZES
```javascript
{
  SMALL: 12,
  DEFAULT: 16,
  MEDIUM: 20,
  LARGE: 24,
  XLARGE: 32,
}
```

#### ICON_COLORS
```javascript
{
  PRIMARY: '#1890ff',
  SUCCESS: '#52c41a',
  WARNING: '#faad14',
  ERROR: '#f5222d',
  INFO: '#1890ff',
  DEFAULT: 'currentColor',
  DISABLED: '#d9d9d9',
}
```

## 可用图标

### 基础操作图标
- `AddIcon` - 添加
- `DeleteIcon` - 删除
- `EditIcon` - 编辑
- `SearchIcon` - 搜索
- `FilterIcon` - 过滤
- `RefreshIcon` - 刷新
- `CloseIcon` - 关闭
- `MoreIcon` - 更多
- `SaveIcon` - 保存

### 导航图标
- `HomeIcon` - 首页
- `BackIcon` - 返回
- `ForwardIcon` - 前进
- `UpIcon` - 向上
- `DownIcon` - 向下
- `LeftIcon` - 向左
- `RightIcon` - 向右
- `MenuIcon` - 菜单
- `ExpandIcon` - 展开
- `CollapseIcon` - 收起

### 状态图标
- `SuccessIcon` - 成功
- `ErrorIcon` - 错误
- `WarningIcon` - 警告
- `InfoIcon` - 信息
- `LoadingIcon` - 加载
- `CheckIcon` - 检查
- `QuestionIcon` - 问号
- `NotificationIcon` - 通知

### 文件和用户图标
- `FileIcon` - 文件
- `FolderIcon` - 文件夹
- `UploadIcon` - 上传
- `DownloadIcon` - 下载
- `UserIcon` - 用户
- `TeamIcon` - 团队
- `SettingIcon` - 设置
- `LogoutIcon` - 退出

## 高级用法

### 自定义图标

如需添加新图标，可以基于`IconBase`组件创建：

```jsx
import IconBase from '@/components/Icons/IconBase';

export const CustomIcon = (props) => (
  <IconBase viewBox="0 0 24 24" {...props}>
    <path d="your-svg-path-data" />
  </IconBase>
);
```

### 主题适配

图标默认使用`currentColor`，会自动继承父元素的文字颜色：

```jsx
<div style={{ color: '#1890ff' }}>
  <AddIcon /> {/* 自动使用蓝色 */}
</div>
```

## 迁移指南

### 从Antd Icon迁移

```jsx
// 旧的Antd Icon用法
import { Icon } from 'antd';
<Icon type="plus" />

// 新的图标组件用法
import { AddIcon } from '@/components/Icons';
<AddIcon />
```

### 从IconFont迁移

```jsx
// 旧的IconFont用法
import IconFont from '@/components/IconFont';
<IconFont type="icon-add" />

// 新的图标组件用法
import { AddIcon } from '@/components/Icons';
<AddIcon />
```

## 注意事项

1. 图标组件基于SVG实现，在IE9+浏览器中有良好支持
2. 建议使用预设的颜色常量保持设计一致性
3. 大尺寸图标建议设置合适的`title`属性提升无障碍体验
4. 动态图标组件会在控制台警告未找到的图标名称
