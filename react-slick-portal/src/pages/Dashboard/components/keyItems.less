.card {
  .container {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    margin-top: 16px;
    .keyItem {
      > div:first-child {
        color: #555864;
        font-size: 14px;
        cursor: pointer;
      }
      > div:nth-child(2) {
        color: #141828;
        font-weight: 600;
        font-size: 22px;
        cursor: pointer;
      }
      > div:nth-child(3) {
        margin-top: 8px;
      }
    }
  }

  img {
    position: absolute;
    bottom: 0;
    width: 100%;
  }

  :global(.ant-card-head-wrapper) {
    height: 48px;
  }
  :global(.ant-card-head-title) {
    font-size: 16px;
  }
}
