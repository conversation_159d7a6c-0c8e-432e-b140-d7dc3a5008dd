import mockjs from 'mockjs';
import { delay } from 'roadhog-api-doc';
import defaultSettings from '@/defaultSettings';

const proxy = {
  'POST /portal/GroupLegalPersionController/legalPersonInfo.do': (req, res) => {
    const {
      body: { operType },
    } = req;
    let result = {};
    if(operType==='QUERY') {
      result ={"resultCode":"TRUE","resultMsg":"操作成功","resultObject":{"rspParam":{"busiInfo":{"code":"200","data":[{"REGION_ID":"471","CREATE_OP_ID":"74030002","DONE_DATE":"2025-02-26 11:24:58","LEGAL_PERSON_STATE":"1","OP_ID":"74030002","PERSON_SCAN_ID_CARD":"","LEGAL_PERSON_IDEN":"111","LEGAL_PERSON_SEX":"","END_DATE":"","LEGAL_PERSON_ID_CARD_F":"","LEGAL_PERSON_ID":"100449","LEGAL_PERSON_PHONE":"111","REMARKS":"","START_DATE":"","ORGA_ENTERPRISE_ID":"111","GROUP_ID":"471710052745","CREATE_DATE":"2025-02-26 11:24:58","LEGAL_PERSON_ID_CARD_Z":"","APPLY_ID":"","LEGAL_PERSON_NAME":"111"},{"REGION_ID":"471","CREATE_OP_ID":"74030002","DONE_DATE":"2025-02-26 11:31:01","LEGAL_PERSON_STATE":"1","OP_ID":"74030002","PERSON_SCAN_ID_CARD":"","LEGAL_PERSON_IDEN":"222","LEGAL_PERSON_SEX":"","END_DATE":"","LEGAL_PERSON_ID_CARD_F":"","LEGAL_PERSON_ID":"100450","LEGAL_PERSON_PHONE":"222","REMARKS":"","START_DATE":"","ORGA_ENTERPRISE_ID":"222","GROUP_ID":"471710052745","CREATE_DATE":"2025-02-26 11:31:01","LEGAL_PERSON_ID_CARD_Z":"","APPLY_ID":"","LEGAL_PERSON_NAME":"222"},{"REGION_ID":"471","CREATE_OP_ID":"71000385","DONE_DATE":"2024-12-26 11:04:54","LEGAL_PERSON_STATE":"1","OP_ID":"71000385","PERSON_SCAN_ID_CARD":"11328552.jpg","LEGAL_PERSON_IDEN":"341222198502036818","LEGAL_PERSON_SEX":"","END_DATE":"","LEGAL_PERSON_ID_CARD_F":"","LEGAL_PERSON_ID":"100443","LEGAL_PERSON_PHONE":"15618466625","REMARKS":"","START_DATE":"","ORGA_ENTERPRISE_ID":"***************","GROUP_ID":"471710052745","CREATE_DATE":"2024-12-26 11:04:54","LEGAL_PERSON_ID_CARD_Z":"","APPLY_ID":"","LEGAL_PERSON_NAME":"王大大"}],"count":"0","message":"查询成功"},"pubInfo":{"message":"success","code":"0","reqSerialNo":"2015060611223320345443"}}},"success":true}
    } else if (operType==='DELETE') {
        result = {"resultCode":"TRUE","resultMsg":"操作成功","resultObject":{"rspParam":{"busiInfo":{"code":"200","data":{},"count":"0","message":"删除成功"},"pubInfo":{"message":"success","code":"0","reqSerialNo":"2015060611223320345443"}}},"success":true}
    } else if (operType==='ADD') {
      result = {"resultCode":"TRUE","resultMsg":"操作成功","resultObject":{"rspParam":{"busiInfo":{"code":"200","data":100452,"count":"0","message":"添加成功"},"pubInfo":{"message":"success","code":"0","reqSerialNo":"2015060611223320345443"}}},"success":true}
    }
    res.status(200).send(result);
  },
  'POST /portal/GroupFileController/fileUpload.do': (req, res) => {
    res.status(200).send({"resultCode":"TRUE","resultMsg":"操作成功","resultObject":{"rspParam":{"busiInfo":{"fileId":"11331586"},"pubInfo":{"message":"success","code":"0","reqSerialNo":"2015060611223320345443"}}},"success":true});
  },
};

export default delay(proxy, defaultSettings.delay);
