import React, { useState, useEffect } from 'react';
import { Form, Modal, Row, Col, Select, Input } from 'antd';
import BraftEditor from 'braft-editor';
import { ImageUploader } from '@/components/Uploader';
import stringToHtml from '@/utils/string';
import { ITEM_LABEL } from '@/pages/OfferManage/Offers/const'

const EDITOR_CONFIGS = [
  'undo', 'redo', 'font-size', 'indent', 'text-color', 'bold', 'italic', 'text-align', 'headings', 'link',
];

const { Item } = Form;
const { Option } = Select;
const AddPicture = props => {
  const { passData, visible, attrs } = props;
  const { getFieldDecorator, validateFields } = props.form;
  const [images, setImages] = useState([]);
  const [title, setTitle] = useState('');


  const commitData = () => {
    validateFields((err, value) => {
      if (!err) {
        let params = {
          // ...value,
          desc: value.desc,
          title: value.title,
          url: (value.images && value.images.length > 0 && value.images[0].url) || "",
        };
        if (['funcDescs'].includes(passData.model)) {
          delete params.url;
        }
        if (['applications'].includes(passData.model)) {
          params = {
            ...params,
            desc: params.desc.toHTML(),
          }
        }
        if (['pictureInfo'].includes(passData.model)) {
          params = {
            docSubType: value.docSubType,
            statusCd: value.statusCd,
            remark: value.remark,
            docLink: value.images && value.images[0].url,
            docName: value.images[0].name,
            docNbr: value.images[0].docNbr || value.images[0].response[0].docNbr,
            docId: value.images[0].docId || value.images[0].response[0].docId,
            docType: "1",
            id: value.images[0].docId || value.images[0].response[0].docId,
          };
        }
        if (['posterRecords'].includes(passData.model)) {
          params = {
            title: value.title,
            desc: params.desc.toHTML(),
            pic: {
              docName: value.images[0].name,
              docNbr: value.images[0].docNbr || value.images[0].response[0].docNbr,
              docLink: value.images && value.images[0].url,
              docType: "2",
              docSubType: "3",
              docId: value.images[0].docId || value.images[0].response[0].docId
            }
          };
        }
        props.onOk(params)
      }
    });
  }

  useEffect(() => {
    if (visible && passData.item && (passData.item.docNbr || passData.item.url)) {
      if (passData.model === 'pictureInfo') {
        setImages([{
          uid: '-1',
          name: 'image.png',
          status: 'done',
          type: "image/jpeg",
          url: `portal/FileStoreController/download.do?docNbr=${passData.item.docNbr}`,
          docNbr: passData.item.docNbr,
          docId: passData.item.docId,
        }])
        props.form.setFieldsValue({
          images: [{
            uid: '-1',
            name: 'image.png',
            status: 'done',
            type: "image/jpeg",
            url: `portal/FileStoreController/download.do?docNbr=${passData.item.docNbr}`,
            docNbr: passData.item.docNbr,
            docId: passData.item.docId,
          }],
        });
      } else {
        setImages([{
          uid: '-1',
          name: 'image.png',
          type: "image/jpeg",
          status: 'done',
          url: passData.item.url,
        }])
        props.form.setFieldsValue({
          images: [{
            uid: '-1',
            name: 'image.png',
            type: "image/jpeg",
            status: 'done',
            url: passData.item.url,
          }],
        });
      }
    } else if (visible && passData.item && passData.item.pic) {
      setImages([{
        uid: '-1',
        name: 'image.png',
        status: 'done',
        type: "image/jpeg",
        url: `portal/FileStoreController/download.do?docNbr=${passData.item.pic.docNbr}`,
        docNbr: passData.item.pic.docNbr,
        docId: passData.item.pic.docId,
      }])
      props.form.setFieldsValue({
        images: [{
          uid: '-1',
          name: 'image.png',
          status: 'done',
          type: "image/jpeg",
          url: `portal/FileStoreController/download.do?docNbr=${passData.item.pic.docNbr}`,
          docNbr: passData.item.pic.docNbr,
          docId: passData.item.pic.docId,
        }],
      });
    }
    Object.keys(ITEM_LABEL).forEach(key => {
      if (key === passData.model) {
        setTitle(ITEM_LABEL[key])
      }
    })
  }, [visible])

  const handleFileChange = file => {
    setImages(file.fileList);
    props.form.setFieldsValue({
      images: file.fileList,
    });
  };

  // 产品图片
  const PictureInfo = (data = {}) => (
    <>
      <Row gutter={16}>
        <Col span={12}>
          <Item label="图片类型" required>
            {getFieldDecorator('docSubType', {
              initialValue: data.docSubType || '',
              rules: [
                { required: true, message: '不能为空' },
              ],
            })(
              <Select>
                {
                  attrs.docSubType.map(item => <Option key={item.value} value={item.value}>{item.name}</Option>)
                }
              </Select>
            )}
          </Item>
        </Col>
        <Col span={12}>
          <Item label="状态" required>
            {getFieldDecorator('statusCd', {
              initialValue: data.statusCd || '',
              rules: [
                { required: true, message: '不能为空' },
              ],
            })(
              <Select>
                {
                  attrs.statusCd.map(item => <Option key={item.value} value={item.value}>{item.name}</Option>)
                }
              </Select>
            )}
          </Item>
        </Col>
        <Col span={24}>
          <Item label='图片' required>
            {getFieldDecorator('images', {
              initialValue: data.images || '',
              rules: [
                {
                  required: true,
                  message: '图片不能为空',
                },
              ],
            })(
              <ImageUploader
                title="上传图片"
                maximum={1}
                uploadedFileList={images}
                onFileListChange={handleFileChange}
              />
            )}
          </Item>
        </Col>
        <Col span={24}>
          <Item label='备注'>
            {getFieldDecorator('remark', {
              initialValue: data.remark || '',
              rules: [
                { required: true, message: '不能为空' },
              ],
            })(
              <Input.TextArea rows={3} />
            )}
          </Item>
        </Col>
      </Row>
    </>
  );

  // 海报信息
  const PosterRecords = (data = {}) => (
    <>
      <Row gutter={16}>
        <Col span={24}>
          <Item label="海报标题" required>
            {getFieldDecorator('title', {
              initialValue: data.title || '',
              rules: [
                { required: true, message: '不能为空' },
              ],
            })(<Input />)}
          </Item>
        </Col>
        <Col span={24}>
          <Item label='图片' required>
            {getFieldDecorator('images', {
              rules: [
                {
                  required: true,
                  message: '图片不能为空',
                },
              ],
            })(
              <ImageUploader
                title="上传图片"
                maximum={1}
                uploadedFileList={images}
                onFileListChange={handleFileChange}
              />
            )}
          </Item>
        </Col>
        <Col span={24}>
          <Item label='海报描述'>
            {getFieldDecorator('desc', {
              initialValue: data.desc ? BraftEditor.createEditorState(stringToHtml(data.desc)) : '',
              rules: [
                { required: true, message: '不能为空' },
              ],
            })(
              <BraftEditor controls={EDITOR_CONFIGS} />
            )}
          </Item>
        </Col>
      </Row>
    </>
  );

  // 优势
  const Advantages = (data = {}) => (
    <>
      <Row gutter={16}>
        <Col span={24}>
          <Item label="优势标题" required>
            {getFieldDecorator('title', {
              initialValue: data.title || '',
              rules: [
                { required: true, message: '不能为空' },
              ],
            })(
              <Input />
            )}
          </Item>
        </Col>
        <Col span={24}>
          <Item label='图片'>
            {getFieldDecorator('images', {
              initialValue: data.images || '',
              // rules: [
              //   { required: true, message: '不能为空' },
              // ],
            })(
              <ImageUploader
                title="上传图片"
                maximum={1}
                uploadedFileList={images}
                onFileListChange={handleFileChange}
              />
            )}
          </Item>
        </Col>
        <Col span={24}>
          <Item label='描述'>
            {getFieldDecorator('desc', {
              initialValue: data.desc || '',
              rules: [
                { required: true, message: '不能为空' },
              ],
            })(
              <Input.TextArea rows={3} />
            )}
          </Item>
        </Col>
      </Row>
    </>
  );

  // 功能描述
  const FuncDescs = (data = {}) => (
    <>
      <Row gutter={16}>
        <Col span={24}>
          <Item label="标题" required>
            {getFieldDecorator('title', {
              initialValue: data.title || '',
              rules: [
                { required: true, message: '不能为空' },
              ],
            })(
              <Input />
            )}
          </Item>
        </Col>
        <Col span={24}>
          <Item label='描述' required>
            {getFieldDecorator('desc', {
              initialValue: data.desc || '',
              rules: [
                { required: true, message: '不能为空' },
              ],
            })(
              <Input.TextArea rows={3} />
            )}
          </Item>
        </Col>
      </Row>
    </>
  );

  // 应用场景
  const Applications = (data = {}) => (
    <>
      <Row gutter={16}>
        <Col span={24}>
          <Item label="标题" required>
            {getFieldDecorator('title', {
              initialValue: data.title || '',
              rules: [
                { required: true, message: '不能为空' },
              ],
            })(
              <Input />
            )}
          </Item>
        </Col>
        <Col span={24}>
          <Item label='图片'>
            {getFieldDecorator('images', {
              initialValue: data.images || '',
            })(
              <ImageUploader
                title="上传图片"
                maximum={1}
                uploadedFileList={images}
                onFileListChange={handleFileChange}
              />
            )}
          </Item>
        </Col>
        <Col span={24}>
          <Item label='描述'>
            {getFieldDecorator('desc', {
              initialValue: data.desc ? BraftEditor.createEditorState(stringToHtml(data.desc)) : '',
              rules: [
                { required: true, message: '不能为空' },
              ],
            })(
              <BraftEditor
                controls={EDITOR_CONFIGS}
              />
            )}
          </Item>
        </Col>
      </Row>
    </>
  )


  const renderComp = () => {
    switch (passData.model) {
      case 'pictureInfo':
        return PictureInfo(passData.item);
      case 'posterRecords':
        return PosterRecords(passData.item);
      case 'advantages':
        return Advantages(passData.item);
      case 'funcDescs':
        return FuncDescs(passData.item);
      case 'applications':
        return Applications(passData.item);
      default:
        return null
    }
  }

  return (
    <Modal
      width="50%"
      bodyStyle={{ height: '400px', overflowY: 'auto' }}
      visible={visible}
      title={(passData.index || passData.index === 0) ? `编辑${title}` : `新增${title}`}
      onCancel={() => props.onCancle()}
      onOk={() => { commitData() }}
      destroyOnClose
    >
      <Form>
        {renderComp()}
      </Form>
    </Modal>
  );
}

export default Form.create()(AddPicture)
