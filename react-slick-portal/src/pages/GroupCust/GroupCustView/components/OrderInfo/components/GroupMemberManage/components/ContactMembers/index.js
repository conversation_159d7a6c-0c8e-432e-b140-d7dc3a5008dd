import React, { useState } from 'react';
import { Button, Card, Col, Form, Input, message, Modal, Row, Select } from 'antd';
import { connect } from 'dva';
import { useFormTable } from '@umijs/hooks';
import SlickTable from '@/components/SlickTable';
import styles from './index.less';
import {
  convertMemberInfo,
  deleteMemberInfo,
  queryDisticts,
  queryPianQu,
  queryQxArea,
} from '@/pages/GroupCust/GroupCustManage/services';
import request from '@/utils/request';

const { Option } = Select;

const ContactMembers = ({ form, currentCust }) => {
  const [loading, setLoading] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [disInfos, setDisInfos] = useState([]);
  const [countyInfos, setCountyInfos] = useState([]);
  const [regionDetails, setRegionDetails] = useState([]);

  // 获取表格数据
  const getTableData = async pageParams => {
    setLoading(true);
    try {
      const { current, pageSize } = pageParams;
      const values = await form.validateFields();

      // 处理查询参数
      const params = {
        ...values,
      };
      Object.keys(params).forEach(key => {
        if (params[key] === undefined || params[key] === '') {
          delete params[key];
        }
      });

      const res = await request('portal/GroupMemberController/queryMemberInfo.do', {
        method: 'POST',
        data: {
          ...params,
          QRY_TYPE: '2', // 联系人类型为2
          pageInfo: {
            currentPage: current,
            pageSize,
          },
        },
      });

      if (res.resultCode === 'TRUE') {
        return {
          list: res.resultObject?.data || [],
          total: res.resultObject?.total || 0,
          success: true,
        };
      }
      message.error(res.resultMsg || '查询失败');
      return {
        list: [],
        total: 0,
        success: false,
      };
    } catch (error) {
      message.error('查询失败');
      return {
        list: [],
        total: 0,
        success: false,
      };
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    // eslint-disable-next-line
    getDisInfo();
  }, []);

  const {
    tableProps,
    search: { submit, reset },
  } = useFormTable(getTableData, {
    defaultPageSize: 10,
    form,
    manual: true,
  });

  const { pagination, ...restTableProps } = tableProps;

  const handleReset = () => {
    reset();
    setCountyInfos([]);
    setRegionDetails([]);
  };

  const toggleAdvanced = () => {
    setShowAdvanced(!showAdvanced);
  };

  // 新增联系人
  const handleAdd = () => {
    // TODO: 实现新增联系人功能
  };

  // 导出
  const handleExport = () => {
    // TODO: 实现导出功能
  };

  // 获取地市
  const getDisInfo = async () => {
    const resp = await queryDisticts();
    if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.resultInfo) {
      setDisInfos(resp.resultObject.rspParam.busiInfo.resultInfo);
    }
  };

  // 获取区县
  const queryCounty = val => {
    // 清空区县和片区的值
    form.setFieldsValue({
      MGMT_COUNTY: undefined,
      AREA: undefined,
    });
    setRegionDetails([]);

    if (val) {
      queryQxArea(val).then(resp => {
        if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.resultInfo) {
          setCountyInfos(resp.resultObject.rspParam.busiInfo.resultInfo);
        }
      });
    } else {
      setCountyInfos([]);
    }
  };

  // 获取片区
  const queryRegionDetails = val => {
    form.setFieldsValue({
      AREA: undefined,
    });

    if (val) {
      queryPianQu(val).then(resp => {
        if (resp.resultCode === 'TRUE' && resp.resultObject.rspParam.busiInfo.resultInfo) {
          setRegionDetails(resp.resultObject.rspParam.busiInfo.resultInfo);
        }
      });
    } else {
      setRegionDetails([]);
    }
  };

  // 刷新列表数据
  const refresh = () => {
    form.validateFields((err, values) => {
      if (!err) {
        const params = {
          ...values,
        };
        Object.keys(params).forEach(key => {
          if (params[key] === undefined || params[key] === '') {
            delete params[key];
          }
        });
        submit(params);
      }
    });
  };

  // 修改联系人
  const handleModify = record => {
    Modal.confirm({
      title: '确认操作',
      content: `是否确认修改 ${record.CUST_NAME}(${record.ACCESS_NUM}) 的信息？`,
      okText: '确认',
      cancelText: '取消',
      onOk() {
        // TODO: 调用修改接口
        message.success('操作成功');
      },
    });
  };

  // 设为主要联系人
  const transCotactToMainPserson = record => {
    Modal.confirm({
      title: '确认操作',
      content: '您确定设置吗?',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        convertMemberInfo({ MODE: 'contactMember_convert',
          GROUP_MEB_REL_ID: record.GROUP_MEB_REL_ID,
          ORGA_ENTERPRISE_ID: record.ORGA_ENTERPRISE_ID }).then(res => {
          const { resultCode, resultMsg } = res;
          if (resultCode !== 'TRUE') {
            message.error(resultMsg);
          } else {
            message.success('操作成功！');
            // eslint-disable-next-line
            refresh();
          }
        });
      },
    });
  };


  // 下拉框通用样式
  const selectStyle = {
    width: '100%',
    dropdownStyle: {
      maxHeight: 400,
      overflow: 'auto',
    },
    dropdownMatchSelectWidth: false,
    style: {
      width: '100%',
      minWidth: '180px',
    },
  };

  // 删除联系人
  const handleDelete = record => {
    Modal.confirm({
      title: '确认删除',
      content: '您确定删除吗?',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        const params = {
          hide_MODE: 'contactMember_del',
          GROUP_MEB_ID: record.GROUP_MEB_ID,
          ACCESS_NUM: record.ACCESS_NUM,
          MODE: 'contactMember_del',
        };
        deleteMemberInfo(params).then(res => {
          const { resultCode, resultMsg } = res;
          if (resultCode !== 'TRUE') {
            message.error(resultMsg);
          } else {
            message.success('删除成功！');
            // eslint-disable-next-line
            refresh();
          }
        });
      },
    });
  };

  return (
    <div className={styles.container}>
      <Card bordered={false}>
        <Form layout="inline" className={styles.form}>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label="集团编码">
                {form.getFieldDecorator('GROUP_ID', {
                  initialValue: currentCust.GROUP_ID,
                })(<Input placeholder="请输入集团编码" />)}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="手机号码">
                {form.getFieldDecorator('ACCESS_NUM')(<Input placeholder="请输入手机号码" />)}
              </Form.Item>
            </Col>
            <Col span={8} style={{ textAlign: 'right' }}>
              <a style={{ marginLeft: 8 }} onClick={toggleAdvanced}>
                {showAdvanced ? '收起' : '高级搜索'} <span className={showAdvanced ? 'anticon-up' : 'anticon-down'} />
              </a>
            </Col>
          </Row>
          {showAdvanced && (
            <div style={{ marginTop: 16 }}>
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item label="归属地市">
                    {form.getFieldDecorator('MGMT_DISTRICT')(
                      <Select
                        {...selectStyle}
                        placeholder="请选择归属地市"
                        onChange={queryCounty}
                        allowClear
                      >
                        {disInfos.map(item => (
                          <Option key={item.REGION_ID} value={item.REGION_ID}>
                            {item.DISTRICT_NAME}
                          </Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="归属区县">
                    {form.getFieldDecorator('MGMT_COUNTY')(
                      <Select
                        {...selectStyle}
                        placeholder="请选择归属区县"
                        onChange={queryRegionDetails}
                        allowClear
                        disabled={!form.getFieldValue('MGMT_DISTRICT')}
                      >
                        {countyInfos.map(item => (
                          <Option key={item.REGION_ID} value={item.REGION_ID}>
                            {item.DISTRICT_NAME}
                          </Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="归属片区">
                    {form.getFieldDecorator('AREA')(
                      <Select
                        {...selectStyle}
                        placeholder="请选择归属片区"
                        allowClear
                        disabled={!form.getFieldValue('MGMT_COUNTY')}
                      >
                        {regionDetails.map(item => (
                          <Option key={item.REGION_ID} value={item.REGION_ID}>
                            {item.DISTRICT_NAME}
                          </Option>
                        ))}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </div>
          )}
        </Form>

        <div style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={submit}>
            查询
          </Button>
          <Button style={{ marginLeft: 8 }} onClick={handleReset}>
            重置
          </Button>
          <Button type="primary" style={{ marginLeft: 8 }} onClick={handleAdd}>新增</Button>
          <Button style={{ marginLeft: 8 }} onClick={handleExport}>全部导出</Button>
        </div>

        <SlickTable
          {...restTableProps}
          loading={loading}
          scroll={{ x: 'max-content' }}
          columns={[
            {
              title: '手机号码',
              dataIndex: 'ACCESS_NUM',
              key: 'accessNum',
              width: 120,
            },
            {
              title: '客户名称',
              dataIndex: 'CUST_NAME',
              key: 'custName',
              width: 120,
            },
            {
              title: '集团编号',
              dataIndex: 'GROUP_ID',
              key: 'groupId',
              width: 140,
            },
            {
              title: '所属集团',
              dataIndex: 'GROUP_NAME',
              key: 'groupName',
              width: 150,
            },
            {
              title: '是否联系人',
              dataIndex: 'IS_LINK_MAN',
              key: 'isLinkMan',
              width: 120,
            },
            {
              title: '是否主要联系人',
              dataIndex: 'IS_MAIN_LINK_MAN',
              key: 'isMainLinkMan',
              width: 140,
            },
            {
              title: '是否关键人物',
              dataIndex: 'IS_KEYMAN',
              key: 'isKeyman',
              width: 120,
            },
            {
              title: '是否普通成员',
              dataIndex: 'IS_MAN',
              key: 'isMan',
              width: 120,
            },
            {
              title: '加入集团时间',
              dataIndex: 'JOIN_DATE',
              key: 'joinDate',
              width: 200,
            },
            {
              title: '职位',
              dataIndex: 'DUTY',
              key: 'duty',
              width: 120,
            },
            {
              title: '说明',
              dataIndex: 'REMARKS',
              key: 'remarks',
              width: 150,
            },
            {
              title: '操作',
              key: 'action',
              fixed: 'right',
              width: 220,
              render: (text, record) => (
                <>
                  <a onClick={() => transCotactToMainPserson(record)}>设为主要联系人</a>
                  <a style={{ marginLeft: 8 }} onClick={() => handleModify(record)}>修改</a>
                  <a style={{ marginLeft: 8 }} onClick={() => handleDelete(record)}>删除</a>
                </>
              ),
            },
          ]}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: total => `共 ${total} 条记录`,
          }}
        />
      </Card>
    </div>
  );
};

export default connect(({ groupCustView }) => ({
  currentCust: groupCustView.currentCust,
}))(Form.create()(ContactMembers));
