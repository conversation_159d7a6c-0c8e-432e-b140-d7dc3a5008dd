// 获取属性管理里配置的属性数据
import { useState, useEffect } from 'react';
import request from '@/utils/request';

const getValue = ({ groupCode, paramCode, ...rest }) =>
  request('portal/DataDictController/getValueByCode.do', {
    data: {
      groupCode,
      paramCode,
      ...rest,
    },
  });
const getGroup = ({ groupCode, ...rest }) =>
  request('portal/DataDictController/getDataDictByGroup.do', {
    data: {
      groupCode,
      ...rest,
    },
  });

const Index = ({ groupCode, paramCode, ...rest }) => {
  const [list, setList] = useState([]);

  useEffect(() => {
    if (paramCode) {
      getValue({ groupCode, paramCode, ...rest })
      .then(res => {
        setList(Array.isArray(res) ? res : []);
      });
    } else {
      getGroup({ groupCode, ...rest })
      .then(res => {
        setList(Array.isArray(res) ? res : []);
      });
    }
  }, []);

  return list;
};

export default Index;
