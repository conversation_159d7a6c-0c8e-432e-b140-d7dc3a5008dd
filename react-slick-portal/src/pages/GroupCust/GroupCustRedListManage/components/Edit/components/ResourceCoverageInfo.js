import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Form, message } from 'antd';
import { connect } from 'dva';
import { ResourceCoverageInfoFields } from '@/pages/GroupCust/GroupCustRedListManage/components/Edit/const';
import { queryCmResourceCoverageInfo } from '@/pages/GroupCust/GroupCustRedListManage/services';
import { queryCommonStaticDataEnumMap } from '@/services/commonStaticData';

const ResourceCoverageInfo = props => {
  const { baseInfo } = props;
  const [resourceCoverageInfo, setResourceCoverageInfo] = useState({});
  const [openCard, setOpenCard] = useState(true); // 是否展开非必填字段

  useEffect(() => {
    if (!baseInfo.GROUP_ID) {
      // eslint-disable-next-line no-console
      console.log('groupId is null!');
      return;
    }
    queryCmResourceCoverageInfo({ groupId: baseInfo.GROUP_ID }).then(res => {
      const { resultCode, resultObject, resultMsg } = res;
      if (resultCode === 'TRUE' && resultObject?.rspParam?.busiInfo?.outData) {
        const data = Array.isArray(resultObject?.rspParam?.busiInfo?.outData) ? resultObject.rspParam.busiInfo.outData[0] : {};
        setResourceCoverageInfo(data);
      } else {
        message.error(resultMsg);
      }
    });
  }, [baseInfo.GROUP_ID]);

  useEffect(() => {
    const fetchQueryCommonStaticDataEnumMap = async () => {
      // Use map + Promise.all to properly handle async operations
      const promises = ResourceCoverageInfoFields.map(async item => {
        if (item.enumeration) {
          item.options = await queryCommonStaticDataEnumMap(item.enumeration);
        }
      });
      await Promise.all(promises); // Wait for all async operations to complete
    };
    fetchQueryCommonStaticDataEnumMap();
  }, []);

  return (
    <Card
      title="客户资源覆盖情况查询"
      id="panel-0"
      className="cute3"
      extra={!openCard ? <a onClick={() => setOpenCard(true)}>隐藏</a> : <a onClick={() => setOpenCard(false)}>展开</a>}
    >
      {openCard ? (
        <Descriptions className="cuteLabelBold" column={{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }}>
          {ResourceCoverageInfoFields.map(({ name, key, span, options }) => (
            <Descriptions.Item label={name} span={span} key={key}>
              {!options ? resourceCoverageInfo?.[key] : options[resourceCoverageInfo?.[key]] ?? resourceCoverageInfo?.[key]}
            </Descriptions.Item>
          ))}
        </Descriptions>
      ) : (
        <></>
      )}
    </Card>
  );
};

export default connect()(Form.create()(ResourceCoverageInfo));
