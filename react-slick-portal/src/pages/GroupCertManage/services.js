import request from '@/utils/request';

// 集团证件查询
export function queryIdentInfos(data) {
  return request('portal/GroupIdentificationsController/queryIdentificationByCond.do', {
    method: 'POST',
    data,
  });
}

// 删除集团证件

export function delIdenInfo() {
  return request('orgauth/PermissionsRelController/deletePermissionsRelList.do', {});
}

// 地市查询
export function queryDisticts() {
  return request('portal/EnterpriseAddressController/queryDisticts.do', {
    data: {
      parentDisrictId: 400,
    },
  });
}
