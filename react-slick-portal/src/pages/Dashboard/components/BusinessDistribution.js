import React, { useState, useEffect } from 'react';

import { Card, Empty } from 'antd';
import { Pie } from '@/components/Charts';
import request from '@/utils/request';

const cache = {};

function BusinessDistribution() {
  const [loading, setLoading] = useState(false);
  const [total1, setTotal1] = useState(0);
  const [chart1, setChart1] = useState([]);

  useEffect(() => {
    async function getInitData() {
      setLoading(true);

      // 商机状态分布
      const response1 = await request(
        'orgauth/WorkbenchController/qryStatisticsIncreaseCurrentMonth.do',
        {
          method: 'get',
          data: { objType: 'OPP' },
        }
      );

      if (
        response1 !== null &&
        response1.resultCode === '0' &&
        response1.resultObject.data.resultCode === '0'
      ) {
        const rstData = response1.resultObject.data.resultObject;

        if (rstData !== undefined) {
          const mapData = [];
          let totalCount = 0;
          rstData.forEach(function(rankData) {
            totalCount += rankData.num;

            mapData.push({
              x: rankData.statusCdName,
              y: rankData.num,
              name: rankData.statusCdName,
            });
          });
          cache.chart1 = mapData;
          cache.total1 = totalCount;
          setTotal1(totalCount);
          setChart1(mapData);
        }
      }

      setLoading(false);
    }
    if (Object.hasOwnProperty.call(cache, 'chart1') === false) {
      getInitData();
    }
  }, []);

  let _chart1;
  let _total1;
  if (cache.chart1 === undefined) {
    _chart1 = chart1;
    _total1 = total1;
  } else {
    _chart1 = cache.chart1;
    _total1 = cache.total1;
  }
  return (
    <Card bordered title="商机状态分布（本月）" loading={loading} bodyStyle={{ height: '356px' }}>
      {_total1 === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="本月增长0个商机"
          style={{ marginTop: 120 }}
        />
      ) : (
        <Pie
          hasLegend
          title="商机增长数"
          subTitle="商机增长数"
          total={<span>{_total1}个</span>}
          // colors={['#8543e0', '#13c2c2', '#facc14']}
          data={_chart1}
          tooltipFormat={[
            'x*y*percent',
            (x, y, p) => ({
              name: `${x} ${y}个`,
              value: `${(p * 100).toFixed(2)}%`,
            }),
          ]}
          valueFormat={val => <span dangerouslySetInnerHTML={{ __html: `${val}个` }} />}
          height={294}
        />
      )}
    </Card>
  );
}

export default BusinessDistribution;
