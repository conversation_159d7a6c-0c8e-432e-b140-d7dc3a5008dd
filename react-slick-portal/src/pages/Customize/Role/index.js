import React from 'react';
import { connect } from 'dva';
import { Form } from 'antd';
import dynamic from 'umi/dynamic';
import StepWizard from '@/components/StepWizard';
import PageLoading from '@/components/PageLoading';

const List = dynamic({
  loader: () => import('@/pages/Customize/Role/List'),
  loading: PageLoading,
});

const PcTemplet = dynamic({
  loader: () => import('@/pages/Customize/Role/PcTemplet'),
  loading: PageLoading,
});

const MobileTemplet = dynamic({
  loader: () => import('@/pages/Customize/Role/MobileTemplet'),
  loading: PageLoading,
});

function Index() {
  return (
    <StepWizard isLazyMount>
      <List />
      <PcTemplet destroy />
      <MobileTemplet destroy />
    </StepWizard>
  );
}

export default connect(({ setting, common, login }) => ({
  size: setting.size,
  user: login.user,
  terminalTypeMap: common.terminalTypeMap,
}))(Form.create()(Index));
